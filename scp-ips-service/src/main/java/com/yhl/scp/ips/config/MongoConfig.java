package com.yhl.scp.ips.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

/**
 * <AUTHOR>
 */
@Configuration
public class MongoConfig {

    @Value("${spring.mongodb.index.create-on-startup:false}")
    private boolean createIndexOnStartup;

    private final MongoTemplate mongoTemplate;

    public MongoConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Bean
    public void createIndexesForDataChangeRecords() {
        if (createIndexOnStartup) {
            IndexOperations indexOps = mongoTemplate.indexOps("log_data_change_record");
            Index index = new Index()
                    .on("dataTableName", Sort.Direction.ASC)
                    .on("primaryId", Sort.Direction.ASC)
                    .on("createTime", Sort.Direction.DESC)
                    .named("log_data_change_record_index");
            indexOps.ensureIndex(index.background());
        }
    }

    @Bean
    public void createIndexesForDataChangeRecordIndex() {
        if (createIndexOnStartup) {
            IndexOperations indexOps = mongoTemplate.indexOps("log_data_change_record");
            Index index = new Index()
                    .on("createTime", Sort.Direction.DESC)
                    .on("dataTableName", Sort.Direction.ASC)
                    .on("primaryId", Sort.Direction.ASC)
                    .on("operateUser", Sort.Direction.ASC)
                    .named("index_log_data_change_record_time_table_id_user");
            indexOps.ensureIndex(index.background());
        }
    }

    @Bean
    public void createIndexesForDataChangeRecordIndex2() {
        if (createIndexOnStartup) {
            IndexOperations indexOps = mongoTemplate.indexOps("log_data_change_record");
            Index index = new Index()
                    .on("dataTableName", Sort.Direction.ASC)
                    .on("createTime", Sort.Direction.DESC)
                    .on("primaryId", Sort.Direction.ASC)
                    .on("operateUser", Sort.Direction.ASC)
                    .named("index_log_data_change_record_table_time_id_user");
            indexOps.ensureIndex(index.background());
        }
    }

    @Bean
    public void createIndexesForRequestLog() {
        if (createIndexOnStartup) {
            IndexOperations indexOps = mongoTemplate.indexOps("log_request_info");
            Index index = new Index()
                    .on("requestTime", Sort.Direction.DESC)
                    .on("requestUri", Sort.Direction.ASC)
                    .on("userId", Sort.Direction.ASC)
                    .on("responseStatus", Sort.Direction.ASC)
                    .on("userAgent", Sort.Direction.ASC)
                    .named("index_log_request_info");
            indexOps.ensureIndex(index.background());
        }
    }

}
