package com.yhl.scp.ips.requestLog.service;

import com.yhl.platform.common.Pagination;
import com.yhl.scp.ips.mongo.entity.RequestLogMongoDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <AUTHOR>
 */
public interface RequestLogService {

    public void insert(RequestLogDTO requestLogDTO);

    public void saveData(RequestLogMongoDTO requestLogDTO);

    Page<RequestLogMongoDTO> selectByPage(Pagination pagination, RequestLogQueryDTO queryParams);
}
