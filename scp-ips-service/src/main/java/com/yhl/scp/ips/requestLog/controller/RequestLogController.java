package com.yhl.scp.ips.requestLog.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.mongo.entity.RequestLogMongoDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <code>RequestLogController</code>
 * <p>
 * requestLog管理控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:10
 */
@Slf4j
@Api(tags = "requestLog管理控制器")
@RestController
@RequestMapping("requestLog")
public class RequestLogController extends BaseController {

    @Resource
    private RequestLogService requestLogService;

    /**
     * 新增数据
     *
     * @param requestLogDTO 实体
     */
    @PostMapping("/create")
    @ApiOperation("创建网关日志")
    public void create(@RequestBody RequestLogDTO requestLogDTO) {
        requestLogService.insert(requestLogDTO);
    }

    @ApiOperation(value = "分页查询")
    @PostMapping(value = "page")
    public BaseResponse<Page<RequestLogMongoDTO>> page(@RequestBody RequestLogQueryDTO queryParams) {
        Page<RequestLogMongoDTO> requestLogs = requestLogService.selectByPage(getPagination(), queryParams);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, requestLogs);
    }
}
