<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.rbac.dao.UserDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.rbac.entity.User">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="staff_code" jdbcType="VARCHAR" property="staffCode"/>
        <result column="cn_name" jdbcType="VARCHAR" property="cnName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="grey_type" jdbcType="VARCHAR" property="greyType"/>
        <result column="user_status" jdbcType="VARCHAR" property="userStatus"/>
        <result column="erp_user" jdbcType="VARCHAR" property="erpUser"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.rbac.vo.UserVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id, user_name, staff_code, cn_name, en_name, user_type, org_code, password, email, phone, avatar,
        grey_type, user_status,erp_user, remark, enabled, creator, create_time, modifier, modify_time
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.userName != null and params.userName != ''">
                and user_name = #{params.userName,jdbcType=VARCHAR}
            </if>
            <if test="params.userNames != null and params.userNames.size() > 0">
                and user_name in
                <foreach collection="params.userNames" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.userNameLike != null and params.userNameLike != ''">
                and user_name like concat('%',#{params.userNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.staffCode != null and params.staffCode != ''">
                and staff_code = #{params.staffCode,jdbcType=VARCHAR}
            </if>
            <if test="params.staffCodeLike != null and params.staffCodeLike != ''">
                and staff_code like concat('%',#{params.staffCodeLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.staffCodes != null and params.staffCodes.size() > 0">
                and staff_code in
                <foreach collection="params.staffCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.cnName != null and params.cnName != ''">
                and cn_name = #{params.cnName,jdbcType=VARCHAR}
            </if>
            <if test="params.cnNames != null and params.cnNames.size() > 0">
                and cn_name in
                <foreach collection="params.cnNames" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.cnNameLike != null and params.cnNameLike != ''">
                and cn_name like concat('%',#{params.cnNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.enName != null and params.enName != ''">
                and en_name = #{params.enName,jdbcType=VARCHAR}
            </if>
            <if test="params.userType != null and params.userType != ''">
                and user_type = #{params.userType,jdbcType=VARCHAR}
            </if>
            <if test="params.userTypes != null and params.userTypes.size() > 0">
                and user_type in
                <foreach collection="params.userTypes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.email != null and params.email != ''">
                and email = #{params.email,jdbcType=VARCHAR}
            </if>
            <if test="params.emailLike != null and params.emailLike != ''">
                and email like concat('%',#{params.emailLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.phone != null and params.phone != ''">
                and phone = #{params.phone,jdbcType=VARCHAR}
            </if>
            <if test="params.phoneLike != null and params.phoneLike != ''">
                and phone like concat('%',#{params.phoneLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.greyType != null and params.greyType != ''">
                and grey_type = #{params.greyType,jdbcType=VARCHAR}
            </if>
            <if test="params.userStatus != null and params.userStatus != ''">
                and user_status = #{params.userStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.erpUser != null and params.erpUser != ''">
                and erp_user = #{params.erpUser,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        where enabled = 'YES'
    </select>
    <select id="selectByUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        where user_name = #{userName,jdbcType=VARCHAR}
    </select>
    <select id="selectByCnName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        where cn_name = #{cnName,jdbcType=VARCHAR}
    </select>
    <select id="selectByPage" parameterType="java.lang.String" resultMap="BaseResultMap">
        select distinct u.*
        from auth_rbac_user u
        left join auth_rbac_tenant_user tu on u.id = tu.user_id
        <where>
            1=1
            <if test="enabled!=null and enabled!=''">
                and u.enabled = #{enabled,jdbcType=VARCHAR}
            </if>
            <if test="tenantId!=null and tenantId!=''">
                and tu.tenant_id = #{tenantId}
            </if>
            <if test="queryCriteriaParam!=null and queryCriteriaParam!=''">
                and ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam!=null and sortParam!=''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByCondition" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        <where>
            <if test="queryCriteriaParam!=null and queryCriteriaParam!=''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam!=null and sortParam!=''">
            order by ${sortParam}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_rbac_user
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.yhl.scp.ips.rbac.entity.User">
        insert into auth_rbac_user (id, user_name, staff_code,
                                    cn_name, en_name, user_type, org_code,
                                    password, email, phone,
                                    avatar, grey_type, user_status,erp_user, remark, enabled,
                                    creator, create_time, modifier,
                                    modify_time)
        values (#{id,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{staffCode,jdbcType=VARCHAR},
                #{cnName,jdbcType=VARCHAR}, #{enName,jdbcType=VARCHAR},
                #{userType,jdbcType=VARCHAR}, #{orgCode,jdbcType=VARCHAR},
                #{password,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
                #{avatar,jdbcType=VARCHAR}, #{greyType,jdbcType=VARCHAR}, #{userStatus,jdbcType=VARCHAR},
                #{erpUser,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into auth_rbac_user(
        id,
        user_name,
        staff_code,
        cn_name,
        en_name,
        user_type,
        org_code,
        password,
        email,
        phone,
        avatar,
        grey_type,
        user_status,
        erp_user,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.userName,jdbcType=VARCHAR},
            #{entity.staffCode,jdbcType=VARCHAR},
            #{entity.cnName,jdbcType=VARCHAR},
            #{entity.enName,jdbcType=VARCHAR},
            #{entity.userType,jdbcType=VARCHAR},
            #{entity.orgCode,jdbcType=VARCHAR},
            #{entity.password,jdbcType=VARCHAR},
            #{entity.email,jdbcType=VARCHAR},
            #{entity.phone,jdbcType=VARCHAR},
            #{entity.avatar,jdbcType=VARCHAR},
            #{entity.greyType,jdbcType=VARCHAR},
            #{entity.userStatus,jdbcType=VARCHAR},
            #{entity.erpUser,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.yhl.scp.ips.rbac.entity.User">
        update auth_rbac_user
        set user_name   = #{userName,jdbcType=VARCHAR},
            staff_code  = #{staffCode,jdbcType=VARCHAR},
            cn_name     = #{cnName,jdbcType=VARCHAR},
            en_name     = #{enName,jdbcType=VARCHAR},
            user_type   = #{userType,jdbcType=VARCHAR},
            org_code    = #{orgCode,jdbcType=VARCHAR},
            password    = #{password,jdbcType=VARCHAR},
            email       = #{email,jdbcType=VARCHAR},
            phone       = #{phone,jdbcType=VARCHAR},
            avatar      = #{avatar,jdbcType=VARCHAR},
            grey_type   = #{greyType,jdbcType=VARCHAR},
            user_status = #{userStatus,jdbcType=VARCHAR},
            erp_user    = #{erpUser,jdbcType=VARCHAR},
            remark      = #{remark,jdbcType=VARCHAR},
            enabled     = #{enabled,jdbcType=VARCHAR},
            creator     = #{creator,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            modifier    = #{modifier,jdbcType=VARCHAR},
            modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="deleteUserRelative" parameterType="java.lang.String">
        delete from component_user_default where user_id = #{userId,jdbcType=VARCHAR};
        delete from component_user_default_language where user_id = #{userId,jdbcType=VARCHAR};
        delete from auth_ps_rfu_user where user_id = #{userId,jdbcType=VARCHAR};
        delete from auth_rbac_dept_user where user_id = #{userId,jdbcType=VARCHAR};
        delete from auth_rbac_tenant_user where user_id = #{userId,jdbcType=VARCHAR};
        delete from auth_rbac_user_role where user_id = #{userId,jdbcType=VARCHAR};
        delete from auth_rbac_user where id = #{userId,jdbcType=VARCHAR}
    </delete>
    <delete id="doBatchDelete"  parameterType="java.util.List">
        delete from component_user_default where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from component_user_default_language where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from auth_ps_rfu_user where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from auth_rbac_dept_user where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from auth_rbac_tenant_user where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from auth_rbac_user_role where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;

        delete from auth_rbac_user where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">#{id}</foreach>;
    </delete>

    <select id="selectOrderPlannerDropDown" resultMap="BaseResultMap">
        SELECT
			c.id,
			c.cn_name 
		FROM
			auth_rbac_user_role a
			LEFT JOIN auth_rbac_role b ON a.role_id = b.id
			LEFT JOIN auth_rbac_user c ON a.user_id = c.id 
		WHERE
			b.role_name = '量产计划员' 
			AND c.id IS NOT NULL
    </select>
    <select id="selectUserByParamDAC" resultMap="VOResultMap">
            select
            <include refid="Base_Column_List"/>
            from auth_rbac_user
            where enabled = 'YES'
    </select>
    <select id="selectByCurrentScenario" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_rbac_user
        where enabled ='YES' and id in (
            select distinct a.user_id
            from auth_rbac_user_scenario a
            inner join auth_scenario b on b.id =a.scenario_id
            where b.data_base_name = #{scenario1,jdbcType=VARCHAR}
        )
    </select>
</mapper>