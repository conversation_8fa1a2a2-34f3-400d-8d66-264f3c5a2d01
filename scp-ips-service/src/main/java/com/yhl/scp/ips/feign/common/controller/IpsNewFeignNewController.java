package com.yhl.scp.ips.feign.common.controller;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.ips.algorithm.service.AlgorithmServerService;
import com.yhl.scp.ips.algorithm.vo.AlgorithmServerVO;
import com.yhl.scp.ips.api.service.ExtApiConfigService;
import com.yhl.scp.ips.api.service.LlmConfigService;
import com.yhl.scp.ips.api.vo.ExtApiConfigVO;
import com.yhl.scp.ips.api.vo.LlmConfigVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.DeptDTO;
import com.yhl.scp.ips.rbac.dto.UserDTO;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.entity.Dept;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.entity.Tenant;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.rbac.service.*;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.ScenarioBusinessRangeService;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.warning.service.WarningSqlSettingService;
import com.yhl.scp.ips.warning.vo.WarningSqlSettingVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>DcpFeignController</code>
 * <p>
 * DcpFeign控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-27 08:55:14
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class IpsNewFeignNewController implements IpsNewFeign {
    @Resource
    private ScenarioService scenarioService;

    @Resource
    private TenantService tenantService;

    @Resource
    private UserService userService;

    @Resource
    private DeptService deptService;

    @Resource
    private RoleService roleService;

    @Resource
    private WarningSqlSettingService warningSqlSettingService;

    @Resource
    private AlgorithmServerService algorithmServerService;

    @Resource
    private UserMessageService userMessageService;

    @Resource
    private ScenarioBusinessRangeService scenarioBusinessRangeService;

    @Resource
    private LlmConfigService llmConfigService;

    @Resource
    private ExtApiConfigService extApiConfigService;

    @Override
    public BaseResponse<String> getDefaultScenario(String moduleCode, String tenantId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, "scp_fysh");
        /*String userId = SystemHolder.getUserId();
        return BaseResponse.success(BaseResponse.OP_SUCCESS, scenarioService.selectDefaultScenario(tenantId, userId,
                moduleCode));*/
    }

    @Override
    public List<Scenario> selectDefaultByTenantId(String tenantId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenantId", tenantId);
        map.put("master", YesOrNoEnum.YES.getCode());
        return scenarioService.selectByParams(map);
    }

    @Override
    public BaseResponse<String> getScenarioByTenantCode(String moduleCode, String tenantCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, "scp_fysh");
        /*List<Tenant> tenants = tenantService.selectAll();
        String tenantId = CollectionUtils.isEmpty(tenants) ? "" : tenants.stream().filter(x -> tenantCode.equals(x.getTenantCode())).map(Tenant::getId).findFirst().orElse(null);
        if (StringUtils.isBlank(tenantId)) {
            return BaseResponse.error("租户信息不存在");
        }
        String userId = SystemHolder.getUserId();
        return BaseResponse.success(BaseResponse.OP_SUCCESS, scenarioService.selectDefaultScenario(tenantId,
                userId, moduleCode));*/
    }

    @Override
    public BaseResponse<List<Scenario>> getScenariosByModuleCode(String moduleCode) {
        List<Scenario> scenarios = scenarioService.selectByModuleCode(moduleCode);
        if (CollectionUtils.isEmpty(scenarios)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        return BaseResponse.success(scenarios);
    }

    @Override
    public List<User> userList() {
        return userService.selectAll();
    }

    @Override
    public User userById(String id) {
        return userService.selectById(id);
    }

    @Override
    public List<Scenario> selectByScenario(String scenario) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("dataBaseName", scenario);
        map.put("master", YesOrNoEnum.YES.getCode());
        return scenarioService.selectByParams(map);
    }

    @Override
    public BaseResponse<String> syncDeptData(String scenario, List<DeptDTO> list) {
        return deptService.syncDeptData(list);
    }

    @Override
    public BaseResponse<String> syncUserData(String scenario, List<UserDTO> list) {
        return userService.syncUserData(list);
    }

    @Override
    public String getIpAddress(String moduleCode) {
        // 根据模块获取算法RPC服务地址
        List<AlgorithmServerVO> algorithmServerList = algorithmServerService.selectAll();
        List<AlgorithmServerVO> candidateServerList = algorithmServerList.stream()
                .filter(t -> t.getModuleCode().contains(moduleCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(candidateServerList)) {
            return null;
        }
        return candidateServerList.get(0).getIpAddress();
    }

    @Override
    public User userNameById(String id) {
        return userService.selectById(id);
    }

    @Override
    public User getUserByUserName(String userName) {
        return userService.getUserByUserName(userName);
    }

    @Override
    public List<User> selectUserByParams(String scenario, Map<String, Object> params) {
        return userService.selectByParams(params);
    }

    @Override
    public BaseResponse<ScenarioBusinessRangeVO> getScenarioBusinessRange(String scenario, String rangeType, String rangeCategory, String apiConfigId) {
        Map<String, Object> params = new HashMap<>();
        params.put("scenario", scenario);
        params.put("rangeType", rangeType);
        params.put("rangeCategory", rangeCategory);
        if (StringUtils.isNotBlank(apiConfigId)) {
            params.put("apiConfigId", apiConfigId);
        }
        List<ScenarioBusinessRangeVO> scenarioBusinessRangeVOS = scenarioBusinessRangeService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(scenarioBusinessRangeVOS)) {
            return BaseResponse.success(scenarioBusinessRangeVOS.get(0));
        }
        return BaseResponse.success(null);
    }

    @Override
    public List<Dept> getDeptByCurrentUserId(String userId) {
        return deptService.selectByCurrentUserId(userId);
    }

    @Override
    public List<Role> selectRoleListByUserId(String userId) {
        return roleService.selectByUserId(userId);
    }

    @Override
    public List<WarningSqlSettingVO> getWarningSql(String code) {
        return warningSqlSettingService.selectByParams(ImmutableMap.of("warningCode", code));
    }

    @Override
    public List<UserMessageVO> selectUserMessage(String userId) {
        return userMessageService.selectAll();
    }

    @Override
    public BaseResponse<String> syncAllUserData(String scenario, List<UserDTO> list) {
        return userService.syncAllUserData(list);
    }

    /**
     * 查询大模型配置
     *
     * @param llmConfigId 配置ID
     * @return
     */
    @Override
    public BaseResponse<LlmConfigVO> getLlmConfig(String llmConfigId) {
        return BaseResponse.success(llmConfigService.selectByPrimaryKey(llmConfigId));
    }

    @Override
    public void saveUserMessage(List<UserMessageDTO> list) {
        userMessageService.doCreateBatch(list);
    }

    @Override
    public List<ExtApiConfigVO> selectALLApiConfig() {
        return extApiConfigService.selectAll() ;
    }

    @Override
    public List<ScenarioBusinessRangeVO> selectScenarioBusinessByParams(Map<String, Object> params) {
        return scenarioBusinessRangeService.selectByParams(params);
    }
}