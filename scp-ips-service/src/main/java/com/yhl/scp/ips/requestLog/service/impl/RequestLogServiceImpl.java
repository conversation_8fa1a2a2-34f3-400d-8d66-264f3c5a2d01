package com.yhl.scp.ips.requestLog.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.Pagination;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.mongo.entity.RequestLogMongoDTO;
import com.yhl.scp.ips.requestLog.dao.RequestLogRepository;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    private static final String FIELD_REQUEST_TIME = "requestTime";
    @Resource
    private RequestLogRepository requestLogRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void insert(RequestLogDTO requestLogDTO) {
        // 发送MQ
        String profile = System.getProperty("spring.profiles.active") + ".";
        rabbitTemplate.convertAndSend(profile + MqConstants.BPIM_EVENT_EXCHANGE, profile + MqConstants.REQUEST_LOG_ROUTING_KEY, JSONObject.toJSONString(requestLogDTO));
    }

    @Override
    public void saveData(RequestLogMongoDTO requestLogMongoDTO) {
        requestLogRepository.save(requestLogMongoDTO);
    }

    @Override
    public Page<RequestLogMongoDTO> selectByPage(Pagination pagination, RequestLogQueryDTO queryParams) {
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();
        if (queryParams != null) {
            if (StringUtils.isNotEmpty(queryParams.getUserId())) {
                criteriaList.add(Criteria.where("userId").is(queryParams.getUserId()));
            }
            if (Objects.nonNull(queryParams.getResponseStatus())) {
                criteriaList.add(Criteria.where("responseStatus").is(queryParams.getResponseStatus()));
            }
            if (StringUtils.isNotEmpty(queryParams.getRequestUri())) {
                criteriaList.add(Criteria.where("requestUri").regex(queryParams.getRequestUri(), "i"));
            }
            if (StringUtils.isNotEmpty(queryParams.getUserAgent())) {
                criteriaList.add(Criteria.where("userAgent").regex(queryParams.getUserAgent(), "i"));
            }
            if (queryParams.getBeginTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).gte(queryParams.getBeginTime()));
            }
            if (queryParams.getEndTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).lte(queryParams.getEndTime()));
            }
        }
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }
        Query mongoQuery = new Query(criteria);
        long total = mongoTemplate.count(mongoQuery, RequestLogMongoDTO.class);
        mongoQuery.with(Sort.by(Sort.Direction.DESC, FIELD_REQUEST_TIME));
        mongoQuery.skip((long) (pagination.getPageNum() - 1) * pagination.getPageSize())
                .limit(pagination.getPageSize());
        List<RequestLogMongoDTO> result = mongoTemplate.find(mongoQuery, RequestLogMongoDTO.class);
        return new PageImpl<>(result, PageRequest.of(pagination.getPageNum() - 1, pagination.getPageSize()), total);
    }
}
