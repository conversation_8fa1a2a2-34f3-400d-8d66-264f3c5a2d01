package com.yhl.scp.ips.rbac.controller;

import com.google.common.collect.Lists;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.rbac.entity.TenantUser;
import com.yhl.scp.ips.rbac.service.TenantUserService;
import io.seata.common.util.CollectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <code>TenantUserController</code>
 * <p>
 * TenantUserController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29 15:38:14
 */
@Api(tags = "租户用户管理")
@RestController
@RequestMapping("tenantUser")
public class TenantUserController extends BaseController {

    @Resource
    private TenantUserService tenantUserService;

    @ApiOperation("根据租户ID获取用户ID列表")
    @GetMapping(value = "byTenantId")
    public BaseResponse<List<String>> getByTenantId(@RequestParam("tenantId") String tenantId) {
        List<String> userIds = tenantUserService.selectByTenantIds(Lists.newArrayList(tenantId))
                .stream().map(TenantUser::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return BaseResponse.success(userIds);
    }

    @ApiOperation("根据用户ID获取租户ID列表")
    @GetMapping(value = "byUserId")
    public BaseResponse<List<String>> getByUserId(@RequestParam("userId") String userId) {
        List<String> tenantIds = tenantUserService.selectByUserId(userId)
                .stream().map(TenantUser::getTenantId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return BaseResponse.success(tenantIds);
    }

    @ApiOperation("保存")
    @PostMapping(value = "save")
    public BaseResponse<Void> save(@RequestParam("tenantId") String tenantId, @RequestBody List<TenantUser> list) {
        tenantUserService.doDeleteByTenantId(tenantId);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> x.setId(UUIDUtil.getUUID()));
            tenantUserService.doCreateBatch(list);
        }
        return BaseResponse.success();
    }

}