package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpMaterialTransactions;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName MaterialTransactionsHandler
 * @Description TODO
 * @Date 2025-06-18 11:10:53
 * <AUTHOR>
 * @Copyright 物料事务处理查询
 * @Version 1.0
 */
@Component
@Slf4j
public class MaterialTransactionsHandler extends SyncDataHandler<List<ErpMaterialTransactions>> {
    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private AuthHandler authHandler;

    @Override
    protected List<ErpMaterialTransactions> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取_物料事务处理为空");
            return null;
        }
        ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
        Object data = erpResponse.getData();
        return JSON.parseArray(data.toString(), ErpMaterialTransactions.class);
    }


    /**
     * 获取最新的更新时间
     *
     * @param list
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<ErpMaterialTransactions> list) {
        Date lastUpdateDate = list.stream().map(ErpMaterialTransactions::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    /**
     * 获取同步分组
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("orgId");
    }

    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param list
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<ErpMaterialTransactions> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("物料事务处理为空");
            return null;
        }
        list = list.stream().collect(Collectors.collectingAndThen(
                Collectors.toMap(
                        ErpMaterialTransactions::getTransactionId,
                        dto -> dto,
                        (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                ),
                map -> new ArrayList<>(map.values())
        ));
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        mrpFeign.handleMaterialTransactions(scenario.getData(), list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步ERP_物料事务处理:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDate, DateUtils.COMMON_DATE_STR3);
            lastUpdateDate = DateUtils.dateToString(calculateDate);
            BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            List<NewStockPointVO> stockPointVOList =
                    newMdsFeign.selectStockPointByParams(scenario.getData(),
                            ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),"organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()));
            Map<String, NewStockPointVO> stockPointVOListMap = stockPointVOList.stream()
                    .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                            (value1, value2) -> value1));
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                    ipsNewFeign.getScenarioBusinessRange(scenario.getData(),
                            StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode(), "INTERNAL", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            NewStockPointVO newStockPointVO = stockPointVOListMap.get(rangeData);

            params.put("orgId", newStockPointVO.getOrganizeId());
            String url = apiUri  + "?token=" + erpToken
                    + "&lastUpdateDate=" + lastUpdateDate
                    + "&orgId=" + params.get("orgId");;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);


            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", erpToken, apiUri
                        , systemNumber, lastUpdateDate, url, bodyStr);
            }

            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP_物料事务处理请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("同步ERP_物料事务处理处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.MATERIAL_TRANSACTIONS.getCode());
    }
}
