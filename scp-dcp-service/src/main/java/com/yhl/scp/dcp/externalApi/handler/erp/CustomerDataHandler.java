package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpCustomer;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CustomerDataHandler
 * @Description 客户基础表
 * @Date 2025-01-03 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class CustomerDataHandler extends SyncDataHandler<List<ErpCustomer>> {
    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private AuthHandler authHandler;
    @Override
    protected List<ErpCustomer> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取外部客户数据为空");
            return null;
        }
        ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);
        Object data = erpResponse.getData();
        List<ErpCustomer> erpCustomers = JSONObject.parseArray(data.toString(), ErpCustomer.class);
        return erpCustomers;
    }


    /**
     * 获取最新的更新时间
     *
     * @param erpCustomers
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpCustomer> erpCustomers) {
        Date lastUpdateDate = erpCustomers.stream().map(ErpCustomer::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    /**
     * 获取同步分组
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("ebsOuId");
    }

    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param erpCustomers
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpCustomer> erpCustomers) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(erpCustomers)) {
            log.error("客户数据为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        newMdsFeign.handleCustomer(scenario.getData(), erpCustomers);
        this.saveSyncCtrl(apiConfigVO, params, erpCustomers);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步ERP客户:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},lastUpdateDate={},url={},bodyStr={}", erpToken, apiUri
                        ,  lastUpdateDate, url, bodyStr);
            }
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP客户请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("同步ERP客户处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.CUSTOMER_DATA.getCode());
    }
}
