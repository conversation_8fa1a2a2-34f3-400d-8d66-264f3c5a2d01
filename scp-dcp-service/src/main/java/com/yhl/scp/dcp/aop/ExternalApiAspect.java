package com.yhl.scp.dcp.aop;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import com.yhl.scp.dcp.message.NotifyEvent;
import com.yhl.scp.dcp.token.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 外部接口API请求切面
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RefreshScope
@Scope(value = "singleton", proxyMode = ScopedProxyMode.NO)
public class ExternalApiAspect {
    @Value("${message.notify.enabled:false}")
    private boolean isNotify;

    @Value("#{'${message.notify.ignoreMsgs:}'.split(',')}")
    private String[] ignoreNotifyMsgs;

    @Autowired
    private TokenManager tokenManager;

    @Pointcut("execution(* com.yhl.scp.dcp.externalApi.handler..handle(..)) && " +
            "!within(com.yhl.scp.dcp.externalApi.handler.wx.MessageHandler)")
    public void pointCut() {
    }

    /**
     * 请求方法环绕处理
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("pointCut()")
    public Object aroundMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = null;
        if (log.isInfoEnabled()) {
            log.info("==> 收到API调用请求，处理方法:{}", joinPoint.getTarget() + "." + joinPoint.getSignature().getName());
            printRequestLog(joinPoint);
        }
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            log.error("外部接口调用异常:{}", e.getMessage(), e);
            if (e instanceof HttpClientErrorException && ((HttpClientErrorException) e).getRawStatusCode() == 401) {
                log.info("Token过期，开始重新获取:");
                Handler handler = (Handler) joinPoint.getTarget();
                String[] splitCommands = handler.getCommand().split(Handler.CMD_DELIMITER);
                String authCommand = String.join(Handler.CMD_DELIMITER, splitCommands[0], splitCommands[1], ApiCategoryEnum.AUTH.getCode());
                this.tokenManager.revoke(authCommand);
                result = joinPoint.proceed();
            } else {
                if (log.isTraceEnabled()) {
                    log.trace("其他异常，是否发送消息：{}，需要忽略的消息：{}", isNotify, ignoreNotifyMsgs);
                }
                if (isNotify) {
                    sendNotifyMsg(joinPoint, e);
                }
                throw e;
            }
        } finally {
            DynamicDataSourceContextHolder.clearDataSource();
        }
        if (log.isInfoEnabled()) {
            log.info("<== API调用返回，处理方法:{}，返回数据:{}，耗时:{}ms!", joinPoint.getTarget() + "." + joinPoint.getSignature().getName(), result, (System.currentTimeMillis() - start));
        }
        return result;
    }

    /**
     * 发送通知消息
     *
     * @param joinPoint 切入点
     * @param e         异常
     */
    private void sendNotifyMsg(ProceedingJoinPoint joinPoint, Throwable e) {
        // 校验该通知消息是否在过滤清单中
        for (String ignoreNotifyMsg : ignoreNotifyMsgs) {
            if (StrUtil.isNotBlank(e.getMessage())
                    && StrUtil.isNotBlank(ignoreNotifyMsg)
                    && e.getMessage().indexOf(ignoreNotifyMsg) > 0) {
                return;
            }
        }
        Map<String, Object> paramMap = new HashMap<>(3);
        String envPrefix = SpringUtil.getActiveProfile().equalsIgnoreCase("prod") ? "" : "【测试】";
        String jsonString = JSONObject.toJSONString(joinPoint.getArgs());
        if (jsonString.length() > 3072) {
            jsonString = jsonString.substring(0, 3072);
        }
        paramMap.put("msgtype", "markdown");
        paramMap.put("markdown", MapUtil.builder()
                .put("content", String.format(envPrefix + "API接口调用异常，请及时处理。\n" +
                                ">接口类：<font color='warning'>%s</font>\n" +
                                ">请求时间：<font color='warning'>%s</font>\n" +
                                ">请求参数：<font color='warning'>%s</font>\n" +
                                ">异常信息：<font color='warning'>%s</font>",
                        joinPoint.getTarget().getClass(), DateUtil.now(),
                        jsonString, e.getMessage()))
                .build());
        SpringUtil.getApplicationContext().publishEvent(new NotifyEvent(paramMap));
    }

    /**
     * 打印请求日志
     *
     * @param joinPoint
     */
    private void printRequestLog(ProceedingJoinPoint joinPoint) {
        int paramCnt = 0;
        for (Object param : joinPoint.getArgs()) {
            try {
                log.debug("==> 传入参数[{}]:{}", (paramCnt + 1), JSONObject.toJSONString(param));
            } catch (Exception e) {
                log.debug("==> 传入参数[{}]:{}", (paramCnt + 1), param);
            }
            paramCnt++;
        }

    }
}
