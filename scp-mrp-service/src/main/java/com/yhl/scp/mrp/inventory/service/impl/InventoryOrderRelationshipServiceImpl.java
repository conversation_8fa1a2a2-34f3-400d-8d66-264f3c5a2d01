package com.yhl.scp.mrp.inventory.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ArrivalTrackingDataSourceEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.halfsubinventory.service.WarehouseHalfSubinventoryService;
import com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO;
import com.yhl.scp.mrp.inventory.convertor.InventoryAlternativeRelationshipConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryAlternativeRelationshipDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryAlternativeRelationshipDomainService;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryOrderRelationshipDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryAlternativeRelationshipDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAlternativeRelationshipPO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryOrderRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOrderRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanVersionService;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPublishedPageVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO;
import com.yhl.scp.mrp.supplier.infrastructure.dao.MaterialSupplierPurchaseDao;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryOrderRelationshipServiceImpl</code>
 * <p>
 * 库存与订单关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-25 10:25:16
 */
@Slf4j
@Service
public class InventoryOrderRelationshipServiceImpl implements InventoryOrderRelationshipService {

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private WarehouseHalfSubinventoryService warehouseHalfSubInventoryService;

    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public PageInfo<InventoryOrderRelationshipVO> pageCustom(InventoryOrderRelationshipDTO dto) {
        log.info("开始查询材料与订单关系");

        // TODO：只会查出来一条（先用list）
        // 查询产品需求库存
        List<HighRiskProductOrderVO> highRiskProductOrderVOList =
                dfpFeign.selectHighRiskProductOrderVOListByParams(SystemHolder.getScenario(), ImmutableMap.of(
                        "versionId", dto.getVersionId(),
                        "stockPointCode", dto.getStockPointCode(),
                        "vehicleModelCode", dto.getVehicleModelCode(),
                        "productCode", dto.getProductCode(),
                        "oemCode", dto.getOemCode(),
                        "partNumber", dto.getPartNumber()));

        String stockPointCodeScenario = getRangeData();

        if (CollectionUtils.isEmpty(highRiskProductOrderVOList)) return new PageInfo<>();

        HighRiskProductOrderVO highRiskProductOrderVO = highRiskProductOrderVOList.get(0);

        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(),
                ImmutableMap.of("productCode", highRiskProductOrderVO.getProductCode()));
        List<String> bomVersionIds = productBomVersionVOList.stream().map(BaseVO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bomVersionIds)) return new PageInfo<>();

        // 根据本厂编码查询BOM明细
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("bomVersionIds", bomVersionIds));
        PageInfo<ProductBomVO> productBomVOPageInfo = new PageInfo<>(productBomVOList);

        if (CollectionUtils.isEmpty(productBomVOList)) return new PageInfo<>();

        // 收集物料编码
        List<String> productCodes = productBomVOList.stream().map(ProductBomVO::getProductCode).distinct().collect(Collectors.toList());

        // 查询实时库存
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList =
                dfpFeign.selectInventoryBatchDetailByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodes", productCodes));
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMap = inventoryBatchDetailVOList.stream()
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        // 查询材料与供应商关系
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList =
                materialSupplierPurchaseService.selectVOByParams(ImmutableMap.of("materialCodeList", productCodes));
        Map<String, List<MaterialSupplierPurchaseVO>> materialSupplierPurchaseVOMap = materialSupplierPurchaseVOList.stream()
                .collect(Collectors.groupingBy(MaterialSupplierPurchaseVO::getMaterialCode));

        // 查询到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("materialCodes", productCodes));
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingVOMap = materialArrivalTrackingVOList.stream()
                .filter(data -> !data.getArrivalStatus().equals(ArrivalStatusEnum.CANCEL.getCode()))
                .filter(data -> !data.getArrivalStatus().equals(ArrivalStatusEnum.CLOSE.getCode()))
                .collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getMaterialCode));

        // 查询浮法库存批次明细
        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOList = inventoryFloatGlassDetailService.selectByParams(
                ImmutableMap.of("productCodes", productCodes));
        Map<String, List<InventoryFloatGlassDetailVO>> inventoryFloatGlassDetailMap = inventoryFloatGlassDetailVOList.stream()
                .collect(Collectors.groupingBy(InventoryFloatGlassDetailVO::getProductCode));

        // 查询半品辅料
        List<WarehouseHalfSubinventoryVO> warehouseHalfSubInventoryVOList = warehouseHalfSubInventoryService.selectAll();
        Map<String, List<WarehouseHalfSubinventoryVO>> warehouseHalfSubInventoryVOMap =
                warehouseHalfSubInventoryVOList.stream().collect(Collectors.groupingBy(WarehouseHalfSubinventoryVO::getOrganization));

        List<InventoryOrderRelationshipVO> result = productBomVOList.stream().map(bomData -> {
            InventoryOrderRelationshipVO vo = new InventoryOrderRelationshipVO();
            vo.setProductCode(bomData.getProductCode());
            vo.setProductName(bomData.getProductName());

            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "PRODUCT_ORGANIZATION", "INTERNAL", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            List<String> rangeList = Arrays.asList(rangeData.split(","));
            // 获取相应的实时库存
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = inventoryBatchDetailVOMap.get(bomData.getProductCode());
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {

                // 工厂库存（取库存点是 S1 或 S2 的库存）
                BigDecimal factoryInventory = inventoryBatchDetailVOS.stream()
                        .filter(data -> rangeList.contains(data.getStockPointCode()))
                        .map(data -> new BigDecimal(data.getCurrentQuantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setFactoryInventory(factoryInventory);

                // 中转库库存（半品辅料中 辅料库存 + 半品库存）
                List<WarehouseHalfSubinventoryVO> warehouseHalfSubInventoryVOS = warehouseHalfSubInventoryVOMap.get(bomData.getStockPointCode());
                if (CollectionUtils.isNotEmpty(warehouseHalfSubInventoryVOS)) {
                    BigDecimal totalInventory = warehouseHalfSubInventoryVOS.stream()
                            .map(data -> {
                                try {
                                    // 处理辅助子库存
                                    String aux = data.getAuxiliarySubinventory();
                                    BigDecimal auxiliary = aux != null ? new BigDecimal(aux.trim()) : BigDecimal.ZERO;

                                    // 处理半成品子库存
                                    String half = data.getHalfFinishedSubinventory();
                                    BigDecimal halfFinished = half != null ? new BigDecimal(half.trim()) : BigDecimal.ZERO;

                                    return auxiliary.add(halfFinished);
                                } catch (NumberFormatException e) {
                                    // 记录错误日志（根据实际情况调整）
                                    throw new BusinessException("半品辅料库，库存数值格式错误: " + e.getMessage());
                                }
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setTransferWarehouseInventory(totalInventory);
                }

                // 仓库库存（取库存点是 SJG 的库存）
                BigDecimal warehouseInventory = inventoryBatchDetailVOS.stream()
                        .filter(data -> data.getStockPointCode().equals(stockPointCodeScenario))
                        .map(data -> new BigDecimal(data.getCurrentQuantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setWarehouseInventory(warehouseInventory);

                List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseVOMap.get(bomData.getProductCode());
                if (CollectionUtils.isNotEmpty(materialSupplierPurchaseVOS)) {
                    List<MaterialSupplierPurchaseVO> consignment = materialSupplierPurchaseVOS.stream()
                            .filter(data -> data.getStockPointCode().equals(stockPointCodeScenario))
                            .filter(data -> null != data.getConsignment() && YesOrNoEnum.YES.getCode().equals(data.getConsignment()))
                            .collect(Collectors.toList());
                    // 寄售库存（取库存点是 SJG 的库存，并且类型是寄售）
                    if (CollectionUtils.isNotEmpty(consignment)){
                        vo.setConsignmentWarehouseInventory(warehouseInventory);
                    }

                    // 供应商
                    vo.setSupplierCode(materialSupplierPurchaseVOS.stream()
                            .map(MaterialSupplierPurchaseVO::getSupplierCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(",")));
                }

                // 获取相应的到货跟踪
                List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = materialArrivalTrackingVOMap.get(bomData.getProductCode());
                if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOS)) {

                    // 在途（到货跟踪在途数量）
                    BigDecimal transitQuantity = materialArrivalTrackingVOS.stream()
                            .map(MaterialArrivalTrackingVO::getPredictArrivalQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setTransitQuantity(transitQuantity);

                    // 待发货（到货跟踪待发货数量）
                    BigDecimal waitDeliveryQuantity = materialArrivalTrackingVOS.stream()
                            .map(MaterialArrivalTrackingVO::getWaitDeliveryQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setWaitDeliveryQuantity(waitDeliveryQuantity);

                    // 要货计划（到货跟踪数据来源是要货计划的待发货数量）
                    BigDecimal requireQuantity = materialArrivalTrackingVOS.stream()
                            .filter(data -> StringUtils.isNotEmpty(data.getDataSource()) &&
                                    data.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()))
                            .map(MaterialArrivalTrackingVO::getWaitDeliveryQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    vo.setRequireQuantity(requireQuantity);
                }
            }

            // 半品库存（在产品库存总和）
            BigDecimal semiFinishedInventory = dto.getStepInventories().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setSemiFinishedInventory(semiFinishedInventory);

            // 缺口库存（缺口需求数量 + 半品库存 - 在途 - 要货）
            BigDecimal productGap = Optional.ofNullable(dto.getDemandGap()).orElse(BigDecimal.ZERO)
                    .add(vo.getSemiFinishedInventory())
                    .subtract(vo.getTransitQuantity())
                    .subtract(vo.getRequireQuantity());
            vo.setProductGap(productGap);

            vo.setVehicleModelCode(highRiskProductOrderVO.getVehicleModelCode());
            vo.setProductFactoryCode(highRiskProductOrderVO.getProductCode());
            vo.setProductFactoryName(highRiskProductOrderVO.getProductName());
            vo.setMaterialLoss(bomData.getIoFactor());

            // 未分配物料可生产片数（所有库存（工厂库存）+ 在途 + 待发 - 半品库存生产所需数量）÷ 单耗
            vo.setUndistributedProductionQuantity((vo.getFactoryInventory().add(vo.getTransitQuantity())
                    .add(vo.getWaitDeliveryQuantity())
                    .subtract(vo.getSemiFinishedInventory()))
                    .divide(vo.getMaterialLoss(), 2, BigDecimal.ROUND_HALF_UP));

            // 7天需求缺口（取上方缺口需求数量 + 半品库存生产所需物料 - 在途 - 待发）
            vo.setDemandGap7Days(dto.getDemandGap7Days().add(vo.getSemiFinishedInventory())
                    .subtract(vo.getTransitQuantity())
                    .subtract(vo.getWaitDeliveryQuantity()));
            // 15天需求缺口
            vo.setDemandGap15Days(dto.getDemandGap15Days().add(vo.getSemiFinishedInventory())
                    .subtract(vo.getTransitQuantity())
                    .subtract(vo.getWaitDeliveryQuantity()));
            // 30天需求缺口
            vo.setDemandGap30Days(dto.getDemandGap30Days().add(vo.getSemiFinishedInventory())
                    .subtract(vo.getTransitQuantity())
                    .subtract(vo.getWaitDeliveryQuantity()));

            // 供应商库存
            if (inventoryFloatGlassDetailMap.containsKey(bomData.getProductCode())){
                BigDecimal reduce = inventoryFloatGlassDetailMap.get(bomData.getProductCode()).stream()
                        .map(InventoryFloatGlassDetailVO::getQty)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setSupplierInventory(reduce);
            }
            return vo;
        }).collect(Collectors.toList());

        // 根据物料编码排序
        result = result.stream().sorted(Comparator.comparing(InventoryOrderRelationshipVO::getProductCode)).collect(Collectors.toList());

        log.info("结束查询材料与订单关系，数据量为{}", result.size());
        PageInfo<InventoryOrderRelationshipVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(productBomVOPageInfo.getTotal());
        pageInfo.setPageSize(productBomVOPageInfo.getPageSize());
        return pageInfo;
    }

    private String getRangeData() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "PURCHASE_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        return rangeData;
    }
}
