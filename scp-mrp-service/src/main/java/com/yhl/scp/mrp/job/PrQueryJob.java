package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryOceanFreightJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-08 20:33:57
 */
@Component
@Slf4j
public class PrQueryJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MaterialPurchaseRequestService materialPurchaseRequestService ;

    @XxlJob("prQueryJob")
    private ReturnT<String> prQueryJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步PR查询job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            materialPurchaseRequestService.syncMaterialPurchaseRequest(scenario.getTenantId());
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步PR查询job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
