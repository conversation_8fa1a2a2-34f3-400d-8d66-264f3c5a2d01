package com.yhl.scp.mrp.material.purchase.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.DemandPatternEnum;
import com.yhl.scp.mrp.enums.MaterialPurchaseReviewFixedEnum;
import com.yhl.scp.mrp.enums.MaterialPurchaseReviewTypeEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseReviewDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialStatisticsVO;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseReviewDataConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseReviewDataDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseReviewDataDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDataDTO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewDetailDTO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseReviewVersionDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseReviewDataDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseReviewDataPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDataService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewDetailService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseReviewVersionService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseReviewDataVO;
import com.yhl.scp.mrp.material.transactions.service.MaterialTransactionsService;
import com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Arrays.stream;

/**
 * <code>MaterialPurchaseReviewDataServiceImpl</code>
 * <p>
 * 材料采购评审应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19 14:46:25
 */
@Slf4j
@Service
public class MaterialPurchaseReviewDataServiceImpl extends AbstractService implements MaterialPurchaseReviewDataService {

    @Resource
    private MaterialPurchaseReviewDataDao materialPurchaseReviewDataDao;

    @Resource
    private MaterialPurchaseReviewDataDomainService materialPurchaseReviewDataDomainService;

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialTransactionsService materialTransactionsService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private MaterialPurchaseReviewVersionService materialPurchaseReviewVersionService;

    @Resource
    private MaterialPurchaseReviewDetailService materialPurchaseReviewDetailService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private RedisUtil redisUtil;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {
        // 0.数据转换
        MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Do(materialPurchaseReviewDataDTO);
        MaterialPurchaseReviewDataPO materialPurchaseReviewDataPO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Po(materialPurchaseReviewDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewDataDomainService.validation(materialPurchaseReviewDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseReviewDataPO);
        materialPurchaseReviewDataDao.insert(materialPurchaseReviewDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {
        // 0.数据转换
        MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Do(materialPurchaseReviewDataDTO);
        MaterialPurchaseReviewDataPO materialPurchaseReviewDataPO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Po(materialPurchaseReviewDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseReviewDataDomainService.validation(materialPurchaseReviewDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseReviewDataPO);
        materialPurchaseReviewDataDao.update(materialPurchaseReviewDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdateSelective(MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO) {

        // 修改历史版本
        if (null != materialPurchaseReviewDataDTO.getMaterialPurchaseReviewVersionId()) {
            MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO = new MaterialPurchaseReviewDetailDTO();
            BeanUtils.copyProperties(materialPurchaseReviewDataDTO, materialPurchaseReviewDetailDTO);
            materialPurchaseReviewDetailDTO.setMaterialPurchaseReviewVersionId(null);
            materialPurchaseReviewDetailService.doUpdateSelective(materialPurchaseReviewDetailDTO);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }

        // 0.数据转换
        MaterialPurchaseReviewDataDO materialPurchaseReviewDataDO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Do(materialPurchaseReviewDataDTO);
        MaterialPurchaseReviewDataPO materialPurchaseReviewDataPO = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Po(materialPurchaseReviewDataDTO);
        // 1.数据校验
        materialPurchaseReviewDataDomainService.validation(materialPurchaseReviewDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseReviewDataPO);
        materialPurchaseReviewDataDao.updateSelective(materialPurchaseReviewDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> release() {
        // 查询材料采购评审
        List<MaterialPurchaseReviewDataVO> reviewDataVOList = this.selectByParams(new HashMap<>());

        // 生成版本
        MaterialPurchaseReviewVersionDTO materialPurchaseReviewVersionDTO = new MaterialPurchaseReviewVersionDTO();
        String version = materialPurchaseReviewVersionService.getVersionCode();
        materialPurchaseReviewVersionDTO.setVersionCode(version);
        materialPurchaseReviewVersionDTO.setVersionName(version);
        String versionId = UUID.randomUUID().toString();
        materialPurchaseReviewVersionDTO.setId(versionId);

        // 生成明细
        List<MaterialPurchaseReviewDetailDTO> addDetailList = reviewDataVOList.stream().map(data -> {
            MaterialPurchaseReviewDetailDTO materialPurchaseReviewDetailDTO = new MaterialPurchaseReviewDetailDTO();
            BeanUtils.copyProperties(data, materialPurchaseReviewDetailDTO);
            materialPurchaseReviewDetailDTO.setMaterialPurchaseReviewVersionId(versionId);
            return materialPurchaseReviewDetailDTO;
        }).collect(Collectors.toList());

        // 版本落库
        materialPurchaseReviewVersionService.doCreateWithPrimaryKey(materialPurchaseReviewVersionDTO);

        // 批量落库（明细）
        if (!addDetailList.isEmpty()) {
            Lists.partition(addDetailList, 1000).forEach(materialPurchaseReviewDetailService::doCreateBatch);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, String versionId) {

        List<MaterialPurchaseReviewDataVO> reviewDataVOList = new ArrayList<>();

        if (StringUtils.isEmpty(versionId)) {
            reviewDataVOList = this.selectAll();
        } else {
            reviewDataVOList = materialPurchaseReviewDetailService.selectByParams(ImmutableMap.of("materialPurchaseReviewVersionId", versionId))
                    .stream().map(data -> {
                        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = new MaterialPurchaseReviewDataVO();
                        BeanUtils.copyProperties(data, materialPurchaseReviewDataVO);
                        return materialPurchaseReviewDataVO;
                    }).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(reviewDataVOList)) return;

        Map<String, List<MaterialPurchaseReviewDataVO>> reviewDataMap = reviewDataVOList.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getReviewType));

        // 获取计划员名称
        List<String> userIds = reviewDataVOList.stream().map(MaterialPurchaseReviewDataVO::getForecastProvider)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, String> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.IPS.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            userMap = ipsNewFeign.selectUserByParams(defaultScenario.getData(), ImmutableMap.of("ids", userIds))
                    .stream().collect(Collectors.toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheetInventoryAndDemand = workbook.createSheet("材料采购需求");
            Sheet sheetModelHistory = workbook.createSheet("车型历史");
            Sheet sheetModelForecast = workbook.createSheet("车型预测");

            exportInventoryAndDemand(response, workbook, sheetInventoryAndDemand, reviewDataMap.get(MaterialPurchaseReviewTypeEnum.INVENTORY_AND_DEMAND.getCode()));
            exportModelHistory(response, workbook, sheetModelHistory, reviewDataMap.get(MaterialPurchaseReviewTypeEnum.MODEL_HISTORY.getCode()));
            exportModelForecast(response, workbook, sheetModelForecast, reviewDataMap.get(MaterialPurchaseReviewTypeEnum.MODEL_FORECAST.getCode()), userMap);

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("风险材料采购评审.xlsx", "UTF-8"));

            // 获取输出流并写入
            try (ServletOutputStream os = response.getOutputStream()) {
                workbook.write(os);
                // 刷新输出流
                os.flush();
            }
        }
    }

    @SneakyThrows
    private void exportInventoryAndDemand(HttpServletResponse response, Workbook workbook, Sheet sheet, List<MaterialPurchaseReviewDataVO> list) {
        // 排序
        sortReviewDataVOList(list);

        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = list.get(0);

        Map<String, List<MaterialPurchaseReviewDataVO>> reviewDataMap = list.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductCode));

        List<String> lineDescriptionList = list.stream()
                .map(MaterialPurchaseReviewDataVO::getLineDescription)
                .distinct()
                .collect(Collectors.toList());

        // 默认12个月维度（前后6个月）
        List<String> monthRangeList = getMonthRange(null, null,
                materialPurchaseReviewDataVO.getAppointYearMonth(), false);

        // 1. 创建标题
        createTitle(workbook, sheet, materialPurchaseReviewDataVO.getAppointYearMonth());

        // 2. 创建表头
        createHeadersInventoryAndDemand(workbook, sheet, materialPurchaseReviewDataVO, monthRangeList);

        // 3. 创建数据行
        int startRow = 4;
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry : reviewDataMap.entrySet()) {
            List<MaterialPurchaseReviewDataVO> value = entry.getValue();
            createMaterialDataInventoryAndDemand(workbook, sheet, startRow, value, lineDescriptionList);
            startRow = startRow + 20;
        }

        // 4. 创建评审意见
        createReviewComments(workbook, sheet);

        // 5. 创建审批区域
        createApprovalArea(workbook, sheet);
    }

    private void exportModelHistory(HttpServletResponse response,
                                    Workbook workbook,
                                    Sheet sheet, List<MaterialPurchaseReviewDataVO> list) {

        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = list.get(0);

        Map<String, List<MaterialPurchaseReviewDataVO>> reviewDataMap = list.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductCode));

        List<String> lineDescriptionList = list.stream()
                .map(MaterialPurchaseReviewDataVO::getLineDescription)
                .distinct()
                .collect(Collectors.toList());

        // 默认12个月维度
        List<String> monthRangeList = getMonthRange(null, null,
                materialPurchaseReviewDataVO.getAppointYearMonth(), true);

        // 1. 创建标题
        createTitle(workbook, sheet, materialPurchaseReviewDataVO.getAppointYearMonth());

        // 2. 创建表头
        createHeadersMaterialHistory(workbook, sheet, materialPurchaseReviewDataVO, monthRangeList);

        // 3. 创建数据行
        int startRow = 4;
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry : reviewDataMap.entrySet()) {
            List<MaterialPurchaseReviewDataVO> value = entry.getValue();
            Map<String, List<MaterialPurchaseReviewDataVO>> materialPurchaseReviewDataVOMap = value.stream()
                    .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductFactoryCode));
            for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry1 : materialPurchaseReviewDataVOMap.entrySet()) {
                createMaterialDataModelHistory(workbook, sheet, startRow, entry1.getValue(), lineDescriptionList);
                startRow = startRow + 5;
            }
        }
    }

    private void exportModelForecast(HttpServletResponse response,
                                     Workbook workbook,
                                     Sheet sheet,
                                     List<MaterialPurchaseReviewDataVO> list,
                                     Map<String, String> userMap) {
        // 排序
        sortReviewDataVOList(list);

        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = list.get(0);

        Map<String, List<MaterialPurchaseReviewDataVO>> reviewDataMap = list.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductCode));

        List<String> lineDescriptionList = list.stream()
                .map(MaterialPurchaseReviewDataVO::getLineDescription)
                .distinct()
                .collect(Collectors.toList());

        // 默认12个月维度（前后6个月）
        List<String> monthRangeList = getMonthRange(null, null,
                materialPurchaseReviewDataVO.getAppointYearMonth(), false);

        // 1. 创建标题
        createTitle(workbook, sheet, materialPurchaseReviewDataVO.getAppointYearMonth());

        // 2. 创建表头
        createHeadersMaterialForecast(workbook, sheet, materialPurchaseReviewDataVO, monthRangeList);

        // 3. 创建数据行
        int startRow = 4;
        for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry : reviewDataMap.entrySet()) {
            List<MaterialPurchaseReviewDataVO> value = entry.getValue();
            Map<String, List<MaterialPurchaseReviewDataVO>> materialPurchaseReviewDataVOMap = value.stream()
                    .collect(Collectors.groupingBy(MaterialPurchaseReviewDataVO::getProductFactoryCode));
            for (Map.Entry<String, List<MaterialPurchaseReviewDataVO>> entry1 : materialPurchaseReviewDataVOMap.entrySet()) {
                createMaterialDataModelForecast(workbook, sheet, startRow, entry1.getValue(), lineDescriptionList, userMap);
                startRow = startRow + 12;
            }
        }
    }

    /**
     * 对统计列表按LineDescription排序：含日期的按日期升序，无日期的放最后
     *
     * @param list 需要排序的列表
     */
    private void sortReviewDataVOList(List<MaterialPurchaseReviewDataVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 自定义排序规则
        list.sort((vo1, vo2) -> {
            String line1 = vo1.getLineDescription();
            String line2 = vo2.getLineDescription();

            // 解析日期
            YearMonth date1 = parseDateFromLineDescription(line1);
            YearMonth date2 = parseDateFromLineDescription(line2);

            // 排序逻辑：有日期的在前，按日期升序；无日期的在后，按自然顺序
            if (date1 != null && date2 != null) {
                return date1.compareTo(date2);
            } else if (date1 != null) {
                return -1;
            } else if (date2 != null) {
                return 1;
            } else {
                // 都无日期：按字符串自然排序
                return line1.compareTo(line2);
            }
        });
    }

    /**
     * 从LineDescription中提取日期（格式：YY.M月）
     */
    private YearMonth parseDateFromLineDescription(String lineDescription) {
        if (StringUtils.isEmpty(lineDescription)) {
            return null;
        }
        // 匹配"数字.数字月"格式（如"26.1月"）
        Pattern pattern = Pattern.compile("(\\d+)\\.(\\d+)月");
        Matcher matcher = pattern.matcher(lineDescription);

        if (matcher.find()) {
            try {
                // 转换为4位年份（26→2026）
                int year = 2000 + Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));
                if (month >= 1 && month <= 12) {
                    return YearMonth.of(year, month);
                }
            } catch (NumberFormatException e) {
                // 数字解析失败，视为无日期
                return null;
            }
        }
        return null;
    }

    private static void createTitle(Workbook workbook, Sheet sheet, String appointYearMonth) {
        // 标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        // 水平居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);

        // 创建标题行
        Row titleRow = sheet.createRow(0);
        // 设置行高，使垂直居中效果更明显
        titleRow.setHeightInPoints(50);

        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("风险材料采购评审表（" + appointYearMonth + "）");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 0, 20));
    }

    private static void createHeadersInventoryAndDemand(Workbook workbook,
                                                        Sheet sheet,
                                                        MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO,
                                                        List<String> monthRangeList) {
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 列标题
        Row headerRow = sheet.createRow(3);
        List<String> headersList = new ArrayList<>();
        Collections.addAll(headersList,
                "本厂编码", "名称", "车型", "主机厂名称", "供应商", "采购周期", "最小起订量", "情况说明");
        headersList.addAll(monthRangeList);
        headersList.add("合计");
        String[] headers = headersList.toArray(new String[0]);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private static void createHeadersMaterialHistory(Workbook workbook,
                                                     Sheet sheet,
                                                     MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO,
                                                     List<String> monthRangeList) {
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 列标题
        Row headerRow = sheet.createRow(3);
        List<String> headersList = new ArrayList<>();
        Collections.addAll(headersList,
                "物料编码", "物料名称", "本厂编码", "车型", "车型EOP时间", "情况说明");
        headersList.addAll(monthRangeList);
        headersList.add("合计");
        headersList.add("备注");
        String[] headers = headersList.toArray(new String[0]);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private static void createHeadersMaterialForecast(Workbook workbook,
                                                      Sheet sheet,
                                                      MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO,
                                                      List<String> monthRangeList) {
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 列标题
        Row headerRow = sheet.createRow(3);
        List<String> headersList = new ArrayList<>();
        Collections.addAll(headersList,
                "物料编码", "物料名称", "本厂编码", "单片面积", "情况说明");
        headersList.addAll(monthRangeList);
        headersList.add("合计");
        headersList.add("预测提供者");
        String[] headers = headersList.toArray(new String[0]);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void createMaterialDataInventoryAndDemand(Workbook workbook,
                                                      Sheet sheet,
                                                      int startRow,
                                                      List<MaterialPurchaseReviewDataVO> materialPurchaseReviewDataVOList,
                                                      List<String> lineDescriptionList) {
        MaterialPurchaseReviewDataVO purchaseReviewDataVO = materialPurchaseReviewDataVOList.get(0);
        Map<String, MaterialPurchaseReviewDataVO> materialPurchaseReviewDataMap = materialPurchaseReviewDataVOList.stream().collect(Collectors.toMap(MaterialPurchaseReviewDataVO::getLineDescription, Function.identity()));

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, purchaseReviewDataVO.getProductCode(), dataStyle);
        createCell(indexRow, 1, purchaseReviewDataVO.getProductName(), dataStyle);
        createCell(indexRow, 2, purchaseReviewDataVO.getVehicleModelCode(), dataStyle);
        createCell(indexRow, 3, purchaseReviewDataVO.getOemName(), dataStyle);
        createCell(indexRow, 4, purchaseReviewDataVO.getSupplierCode(), dataStyle);
        createCell(indexRow, 5, String.valueOf(purchaseReviewDataVO.getPurchaseLot()), dataStyle);
        createCell(indexRow, 6, String.valueOf(purchaseReviewDataVO.getMinOrderQuantity()), dataStyle);

        int rowNum = startRow;
        for (String lineDescription : lineDescriptionList) {
            Row row = (rowNum == startRow) ? indexRow : sheet.createRow(rowNum);
            createCell(row, 7, lineDescription, dataStyle);
            MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = materialPurchaseReviewDataMap.get(lineDescription);
            // 1-12年的数据列（8-19列），为空时显示0
            createCell(row, 8, getValueOrZero(materialPurchaseReviewDataVO.getOneYearMonthValue()), dataStyle);
            createCell(row, 9, getValueOrZero(materialPurchaseReviewDataVO.getTwoYearMonthValue()), dataStyle);
            createCell(row, 10, getValueOrZero(materialPurchaseReviewDataVO.getThreeYearMonthValue()), dataStyle);
            createCell(row, 11, getValueOrZero(materialPurchaseReviewDataVO.getFourYearMonthValue()), dataStyle);
            createCell(row, 12, getValueOrZero(materialPurchaseReviewDataVO.getFiveYearMonthValue()), dataStyle);
            createCell(row, 13, getValueOrZero(materialPurchaseReviewDataVO.getSixYearMonthValue()), dataStyle);
            createCell(row, 14, getValueOrZero(materialPurchaseReviewDataVO.getSevenYearMonthValue()), dataStyle);
            createCell(row, 15, getValueOrZero(materialPurchaseReviewDataVO.getEightYearMonthValue()), dataStyle);
            createCell(row, 16, getValueOrZero(materialPurchaseReviewDataVO.getNineYearMonthValue()), dataStyle);
            createCell(row, 17, getValueOrZero(materialPurchaseReviewDataVO.getTenYearMonthValue()), dataStyle);
            createCell(row, 18, getValueOrZero(materialPurchaseReviewDataVO.getElevenYearMonthValue()), dataStyle);
            createCell(row, 19, getValueOrZero(materialPurchaseReviewDataVO.getTwelveYearMonthValue()), dataStyle);

            // 计算12个数值的总和（处理可能的null值）
            double total = 0.0;
            // 假设值是数字类型（Double/Integer等），如果是字符串需要先转换
            total += getValueOrDefault(materialPurchaseReviewDataVO.getOneYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwoYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getThreeYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFourYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFiveYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSixYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getEightYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getNineYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getElevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwelveYearMonthValue());

            // 合计列（第20列）
            createCell(row, 20, String.valueOf(total), dataStyle);
            rowNum++;
        }
    }

    private void createMaterialDataModelHistory(Workbook workbook,
                                                Sheet sheet,
                                                int startRow,
                                                List<MaterialPurchaseReviewDataVO> materialPurchaseReviewDataVOList,
                                                List<String> lineDescriptionList) {
        MaterialPurchaseReviewDataVO purchaseReviewDataVO = materialPurchaseReviewDataVOList.get(0);
        Map<String, MaterialPurchaseReviewDataVO> materialPurchaseReviewDataMap = materialPurchaseReviewDataVOList.stream()
                .collect(Collectors.toMap(MaterialPurchaseReviewDataVO::getLineDescription, Function.identity()));

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, purchaseReviewDataVO.getProductCode(), dataStyle);
        createCell(indexRow, 1, purchaseReviewDataVO.getProductName(), dataStyle);
        createCell(indexRow, 2, purchaseReviewDataVO.getProductFactoryCode(), dataStyle);
        createCell(indexRow, 3, purchaseReviewDataVO.getVehicleModelCode(), dataStyle);
        createCell(indexRow, 4, purchaseReviewDataVO.getVehicleModelEopDate(), dataStyle);


        int rowNum = startRow;
        for (String lineDescription : lineDescriptionList) {
            Row row = (rowNum == startRow) ? indexRow : sheet.createRow(rowNum);
            createCell(row, 5, lineDescription, dataStyle);
            MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = materialPurchaseReviewDataMap.get(lineDescription);
            // 1-12年的数据列（8-19列），为空时显示0
            createCell(row, 6, getValueOrZero(materialPurchaseReviewDataVO.getOneYearMonthValue()), dataStyle);
            createCell(row, 7, getValueOrZero(materialPurchaseReviewDataVO.getTwoYearMonthValue()), dataStyle);
            createCell(row, 8, getValueOrZero(materialPurchaseReviewDataVO.getThreeYearMonthValue()), dataStyle);
            createCell(row, 9, getValueOrZero(materialPurchaseReviewDataVO.getFourYearMonthValue()), dataStyle);
            createCell(row, 10, getValueOrZero(materialPurchaseReviewDataVO.getFiveYearMonthValue()), dataStyle);
            createCell(row, 11, getValueOrZero(materialPurchaseReviewDataVO.getSixYearMonthValue()), dataStyle);
            createCell(row, 12, getValueOrZero(materialPurchaseReviewDataVO.getSevenYearMonthValue()), dataStyle);
            createCell(row, 13, getValueOrZero(materialPurchaseReviewDataVO.getEightYearMonthValue()), dataStyle);
            createCell(row, 14, getValueOrZero(materialPurchaseReviewDataVO.getNineYearMonthValue()), dataStyle);
            createCell(row, 15, getValueOrZero(materialPurchaseReviewDataVO.getTenYearMonthValue()), dataStyle);
            createCell(row, 16, getValueOrZero(materialPurchaseReviewDataVO.getElevenYearMonthValue()), dataStyle);
            createCell(row, 17, getValueOrZero(materialPurchaseReviewDataVO.getTwelveYearMonthValue()), dataStyle);

            // 计算12个数值的总和（处理可能的null值）
            double total = 0.0;
            // 假设值是数字类型（Double/Integer等），如果是字符串需要先转换
            total += getValueOrDefault(materialPurchaseReviewDataVO.getOneYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwoYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getThreeYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFourYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFiveYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSixYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getEightYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getNineYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getElevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwelveYearMonthValue());

            // 合计列（第20列）
            createCell(row, 17, String.valueOf(total), dataStyle);
            createCell(row, 18, materialPurchaseReviewDataVO.getRemark(), dataStyle);
            rowNum++;
        }
    }

    private void createMaterialDataModelForecast(Workbook workbook,
                                                 Sheet sheet,
                                                 int startRow,
                                                 List<MaterialPurchaseReviewDataVO> materialPurchaseReviewDataVOList,
                                                 List<String> lineDescriptionList,
                                                 Map<String, String> userMap) {
        MaterialPurchaseReviewDataVO purchaseReviewDataVO = materialPurchaseReviewDataVOList.get(0);
        Map<String, MaterialPurchaseReviewDataVO> materialPurchaseReviewDataMap = materialPurchaseReviewDataVOList.stream()
                .collect(Collectors.toMap(MaterialPurchaseReviewDataVO::getLineDescription, Function.identity()));

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, purchaseReviewDataVO.getProductCode(), dataStyle);
        createCell(indexRow, 1, purchaseReviewDataVO.getProductName(), dataStyle);
        createCell(indexRow, 2, purchaseReviewDataVO.getProductFactoryCode(), dataStyle);
        createCell(indexRow, 3, purchaseReviewDataVO.getSinglePieceArea(), dataStyle);

        int rowNum = startRow;
        for (String lineDescription : lineDescriptionList) {
            Row row = (rowNum == startRow) ? indexRow : sheet.createRow(rowNum);
            createCell(row, 4, lineDescription, dataStyle);
            MaterialPurchaseReviewDataVO materialPurchaseReviewDataVO = materialPurchaseReviewDataMap.get(lineDescription);
            // 1-12年的数据列（8-19列），为空时显示0
            createCell(row, 5, getValueOrZero(materialPurchaseReviewDataVO.getOneYearMonthValue()), dataStyle);
            createCell(row, 6, getValueOrZero(materialPurchaseReviewDataVO.getTwoYearMonthValue()), dataStyle);
            createCell(row, 7, getValueOrZero(materialPurchaseReviewDataVO.getThreeYearMonthValue()), dataStyle);
            createCell(row, 8, getValueOrZero(materialPurchaseReviewDataVO.getFourYearMonthValue()), dataStyle);
            createCell(row, 9, getValueOrZero(materialPurchaseReviewDataVO.getFiveYearMonthValue()), dataStyle);
            createCell(row, 10, getValueOrZero(materialPurchaseReviewDataVO.getSixYearMonthValue()), dataStyle);
            createCell(row, 11, getValueOrZero(materialPurchaseReviewDataVO.getSevenYearMonthValue()), dataStyle);
            createCell(row, 12, getValueOrZero(materialPurchaseReviewDataVO.getEightYearMonthValue()), dataStyle);
            createCell(row, 13, getValueOrZero(materialPurchaseReviewDataVO.getNineYearMonthValue()), dataStyle);
            createCell(row, 14, getValueOrZero(materialPurchaseReviewDataVO.getTenYearMonthValue()), dataStyle);
            createCell(row, 15, getValueOrZero(materialPurchaseReviewDataVO.getElevenYearMonthValue()), dataStyle);
            createCell(row, 16, getValueOrZero(materialPurchaseReviewDataVO.getTwelveYearMonthValue()), dataStyle);

            // 计算12个数值的总和（处理可能的null值）
            double total = 0.0;
            // 假设值是数字类型（Double/Integer等），如果是字符串需要先转换
            total += getValueOrDefault(materialPurchaseReviewDataVO.getOneYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwoYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getThreeYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFourYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getFiveYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSixYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getSevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getEightYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getNineYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getElevenYearMonthValue());
            total += getValueOrDefault(materialPurchaseReviewDataVO.getTwelveYearMonthValue());

            // 合计列（第20列）
            createCell(row, 17, String.valueOf(total), dataStyle);
            String cnName = null;
            if (userMap.containsKey(materialPurchaseReviewDataVO.getForecastProvider())) {
                cnName = userMap.get(materialPurchaseReviewDataVO.getForecastProvider());
            }
            createCell(row, 18, cnName, dataStyle);
            rowNum++;
        }
    }

    /**
     * 辅助方法：处理null值，将非数字类型转换为0
     *
     * @param value
     * @return
     */
    private double getValueOrDefault(Object value) {
        if (value == null) {
            return 0.0;
        }
        // 根据实际数据类型进行转换（这里以数字类型为例）
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        // 如果是字符串类型需要额外处理
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }

    /**
     * 辅助方法：处理null值，为空时返回0
     *
     * @param value
     * @return
     */
    private String getValueOrZero(Object value) {
        // 如果值为null，返回0
        if (value == null) {
            return String.valueOf(0);
        }
        // 非null值直接返回原始值
        return value.toString();
    }

    private static void createCell(Row row, int col, String value, CellStyle style) {
        Cell cell = row.createCell(col);
        cell.setCellValue(value);
        if (style != null) cell.setCellStyle(style);
    }

    private static void createReviewComments(Workbook workbook, Sheet sheet) {
        int lastRow = sheet.getLastRowNum() + 2;
        Row row1 = sheet.createRow(lastRow);
        createCell(row1, 0, "综述上述信息评审意见如下：", null);
        sheet.addMergedRegion(new CellRangeAddress(lastRow, lastRow, 0, 27));

        Row row2 = sheet.createRow(lastRow + 1);
        createCell(row2, 0, null, null);
        sheet.addMergedRegion(new CellRangeAddress(lastRow + 1, lastRow + 1, 0, 27));

        Row row3 = sheet.createRow(lastRow + 2);
        createCell(row3, 0, null, null);
        sheet.addMergedRegion(new CellRangeAddress(lastRow + 2, lastRow + 2, 0, 27));
    }

    private static void createApprovalArea(Workbook workbook, Sheet sheet) {
        int lastRow = sheet.getLastRowNum() + 2;
        Row row = sheet.createRow(lastRow);
        createCell(row, 1, "批准：", null);
        createCell(row, 5, "审核：", null);
        createCell(row, 8, "制表：", null);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseReviewDataDTO> list) {
        List<MaterialPurchaseReviewDataPO> newList = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseReviewDataDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseReviewDataDTO> list) {
        List<MaterialPurchaseReviewDataPO> newList = MaterialPurchaseReviewDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseReviewDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseReviewDataDao.deleteBatch(idList);
        }
        return materialPurchaseReviewDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseReviewDataVO selectByPrimaryKey(String id) {
        MaterialPurchaseReviewDataPO po = materialPurchaseReviewDataDao.selectByPrimaryKey(id);
        return MaterialPurchaseReviewDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_DATA")
    public List<MaterialPurchaseReviewDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_PURCHASE_REVIEW_DATA")
    public List<MaterialPurchaseReviewDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseReviewDataVO> dataList = materialPurchaseReviewDataDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseReviewDataServiceImpl target = SpringBeanUtils.getBean(MaterialPurchaseReviewDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseReviewDataVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseReviewDataPO> list = materialPurchaseReviewDataDao.selectByParams(params);
        return MaterialPurchaseReviewDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseReviewDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> calc(MaterialPurchaseReviewDTO dto) {
        String scenario = SystemHolder.getScenario();

        // 核心数据查询：非原片推移主数据
        List<NoGlassInventoryShiftDataVO> shiftDataList = noGlassInventoryShiftDataService
                .selectVOByParams(ImmutableMap.of("productCodeList", dto.getProductCodeList()));
        if (CollectionUtils.isEmpty(shiftDataList)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }

        // 查询材料供应商关联数据，并根据供应商代码过滤
        List<MaterialSupplierPurchaseVO> materialSupplierVOs = materialSupplierPurchaseService
                .selectVOByParams(ImmutableMap.of("materialCodeList", dto.getProductCodeList()));

        // 提取供应商代码过滤条件，避免重复判断
        boolean hasSupplierCode = StringUtils.isNotEmpty(dto.getSupplierCode());
        Stream<MaterialSupplierPurchaseVO> supplierStream = materialSupplierVOs.stream();

        // 有供应商代码时，直接在流中过滤，不创建中间列表
        if (hasSupplierCode) {
            String targetSupplierCode = dto.getSupplierCode();
            supplierStream = supplierStream.filter(vo -> targetSupplierCode.equals(vo.getSupplierCode()));
        }

        // 构建材料与供应商的映射关系（合并过滤和分组操作）
        Map<String, List<MaterialSupplierPurchaseVO>> supplierMap = supplierStream
                .filter(vo -> vo.getMaterialCode() != null)
                .collect(Collectors.groupingBy(MaterialSupplierPurchaseVO::getMaterialCode));

        // 按供应商过滤推移数据（提前判断空映射，避免无效流操作）
        if (hasSupplierCode && !supplierMap.isEmpty()) {
            // 只保留存在供应商关联的材料
            shiftDataList = shiftDataList.stream()
                    .filter(data -> supplierMap.containsKey(data.getProductCode()))
                    .collect(Collectors.toList());
        }

        // 2. 提取核心ID和编码
        List<String> shiftDataIds = shiftDataList.stream().map(BaseVO::getId).collect(Collectors.toList());
        Map<String, NoGlassInventoryShiftDataVO> shiftDataMap = shiftDataList.stream()
                .filter(data -> data.getProductFactoryCode() != null)
                .collect(Collectors.toMap(NoGlassInventoryShiftDataVO::getProductCode, Function.identity()));

        // 2.1 拆分并缓存 工厂编码 和 车型编码
        Map<String, List<String>> productFactoryCodeMap = new HashMap<>(shiftDataList.size());
        Map<String, List<String>> vehicleModeCodeMap = new HashMap<>(shiftDataList.size());
        List<String> allProductFactoryCodes = new ArrayList<>();
        List<String> allVehicleModeCodes = new ArrayList<>();
        for (NoGlassInventoryShiftDataVO data : shiftDataList) {
            // 处理工厂编码
            List<String> factoryCodes = data.getProductFactoryCode() != null ?
                    stream(data.getProductFactoryCode().split(",")).collect(Collectors.toList()) : Collections.emptyList();
            productFactoryCodeMap.put(data.getProductCode(), factoryCodes);
            allProductFactoryCodes.addAll(factoryCodes);

            // 处理车型编码
            List<String> vehicleCodes = data.getVehicleModeCode() != null ?
                    stream(data.getVehicleModeCode().split(",")).collect(Collectors.toList()) : Collections.emptyList();
            vehicleModeCodeMap.put(data.getProductCode(), vehicleCodes);
            allVehicleModeCodes.addAll(vehicleCodes);
        }
        // 去重
        allProductFactoryCodes = allProductFactoryCodes.stream().distinct().collect(Collectors.toList());
        allVehicleModeCodes = allVehicleModeCodes.stream().distinct().collect(Collectors.toList());

        // 3. 查询关联数据
        // 3.1 非原片推移明细
        Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailMap = noGlassInventoryShiftDetailService.selectVOByParams(
                        ImmutableMap.of("noGlassInventoryShiftDataIdList", shiftDataIds, "reviewVersionFlag", dto.getReviewVersionFlag())).stream()
                .filter(detail -> detail.getNoGlassInventoryShiftDataId() != null)
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId, HashMap::new, Collectors.toList()));

        // 3.2 需求预测版本
        List<ConsistenceDemandForecastVersionVO> allForecastVersions = dfpFeign.selectConsistenceDemandForecastVersion(scenario);
        // 版本1：目标月份范围的最新版本
        List<String> targetMonths = getMonthRange(dto.getStartYearMonthDimension(), dto.getEndYearMonthDimension(), dto.getAppointYearMonth(), false);
        Map<String, ConsistenceDemandForecastVersionVO> forecastVersionMap = allForecastVersions.stream()
                .filter(v -> targetMonths.contains(v.getPlanPeriod()))
                .collect(Collectors.groupingBy(ConsistenceDemandForecastVersionVO::getPlanPeriod))
                .values().stream()
                .map(vs -> vs.stream().max(Comparator.comparing(BaseVO::getCreateTime)).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ConsistenceDemandForecastVersionVO::getPlanPeriod, Function.identity()));
        // 版本2：上一年上月的最新版本
        String prevMonth = getPreviousYearMonth(dto.getAppointYearMonth());
        Map<String, ConsistenceDemandForecastVersionVO> forecastVersionMap02 = allForecastVersions.stream()
                .filter(v -> prevMonth.equals(v.getPlanPeriod()))
                .collect(Collectors.groupingBy(ConsistenceDemandForecastVersionVO::getPlanPeriod))
                .values().stream()
                .map(vs -> vs.stream().max(Comparator.comparing(BaseVO::getCreateTime)).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ConsistenceDemandForecastVersionVO::getPlanPeriod, Function.identity()));
        // 提取版本ID
        List<String> versionIds = forecastVersionMap.values().stream().map(BaseVO::getId).collect(Collectors.toList());
        List<String> versionIds02 = forecastVersionMap02.values().stream().map(BaseVO::getId).collect(Collectors.toList());

        // 3.3 需求预测数据及明细
        // 版本1的需求预测
        List<ConsistenceDemandForecastDataVO> forecastDataList = dfpFeign.selectDemandForecastDataByParams(
                scenario, ImmutableMap.of("versionIdList", versionIds, "productCodeList", allProductFactoryCodes));
        // 过滤需求类型
        if (StringUtils.isNotEmpty(dto.getDemandCategory())) {
            forecastDataList = forecastDataList.stream()
                    .filter(data -> dto.getDemandCategory().equals(data.getDemandCategory()))
                    .collect(Collectors.toList());
        }
        List<String> forecastDataIds = forecastDataList.stream().map(BaseVO::getId).collect(Collectors.toList());
        Map<String, List<ConsistenceDemandForecastDataVO>> forecastDataMap = forecastDataList.stream()
                .filter(d -> d.getVersionId() != null)
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataVO::getVersionId));
        List<ConsistenceDemandForecastDataDetailVO> forecastDetails = new ArrayList<>();
        // 版本1的需求明细
        if (CollectionUtils.isNotEmpty(forecastDataIds)) {
            forecastDetails = dfpFeign.selectDemandForecastByDataDetailVOByParams(
                    scenario, ImmutableMap.of("consistenceDemandForecastDataIdList", forecastDataIds));
        }
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> forecastDetailMap = forecastDetails.stream()
                .filter(d -> d.getProductCode() != null)
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getProductCode));

        // 版本2的需求预测
        List<ConsistenceDemandForecastDataVO> forecastDataList02 = dfpFeign.selectDemandForecastDataByParams(
                scenario, ImmutableMap.of("versionIdList", versionIds02, "productCodeList", allProductFactoryCodes));
        List<String> forecastDataIds02 = forecastDataList02.stream().map(BaseVO::getId).collect(Collectors.toList());
        Map<String, List<ConsistenceDemandForecastDataVO>> forecastDataMap02 = forecastDataList02.stream()
                .filter(d -> d.getVersionId() != null)
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataVO::getVersionId));
        List<ConsistenceDemandForecastDataDetailVO> forecastDetails02 = new ArrayList<>();
        // 版本2的需求明细
        if (CollectionUtils.isNotEmpty(forecastDataIds02)) {
            forecastDetails02 = dfpFeign.selectDemandForecastByDataDetailVOByParams(
                    scenario, ImmutableMap.of("consistenceDemandForecastDataIdList", forecastDataIds02));
        }
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> forecastDetailMap02 = forecastDetails02.stream()
                .filter(d -> d.getProductCode() != null)
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getProductCode));

        // 3.4 其他关联数据
        // 物料交易（投料）
        Map<String, List<MaterialTransactionsVO>> transactionMap = materialTransactionsService.selectByParams(
                        ImmutableMap.of("productCodes", dto.getProductCodeList(), "transactionType", "Inventory direct organization transfer"))
                .stream()
                .filter(d -> d.getProductCode() != null)
                .peek(vo -> {
                    // 处理transactionQty：负数转正数（取绝对值），确保为BigDecimal类型
                    BigDecimal transactionQty = vo.getTransactionQty();
                    if (transactionQty != null) {
                        // 若为负数，取绝对值；非负数保持不变
                        if (transactionQty.compareTo(BigDecimal.ZERO) < 0) {
                            vo.setTransactionQty(transactionQty.abs());
                        }
                    } else {
                        // 若为null，默认设为0（可选，根据业务需求调整）
                        vo.setTransactionQty(BigDecimal.ZERO);
                    }
                })
                .collect(Collectors.groupingBy(MaterialTransactionsVO::getProductCode));

        // 主机厂车型
        Map<String, OemVehicleModelVO> oemVehicleMap = dfpFeign.selectOemVehicleModelByVehicleModelCode(scenario, allVehicleModeCodes).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(OemVehicleModelVO::getOemVehicleModelCode, Function.identity(), (v1, v2) -> v1));

        // 到货跟踪（在途）
        Map<String, List<MaterialArrivalTrackingVO>> arrivalTrackingMap = materialArrivalTrackingService.selectByParams(
                        ImmutableMap.of("materialCodes", dto.getProductCodeList(), "arrivalStatus", ArrivalStatusEnum.DELIVERED.getCode())).stream()
                .filter(d -> d.getMaterialCode() != null)
                .collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getMaterialCode));

        // 主机厂信息
        List<String> oemCodes = oemVehicleMap.values().stream().map(OemVehicleModelVO::getOemCode).distinct().collect(Collectors.toList());
        Map<String, OemVO> oemMap = dfpFeign.selectOemByParams(scenario, ImmutableMap.of("oemCodes", oemCodes)).stream()
                .collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (v1, v2) -> v1));
        List<String> targetStockLocations = oemMap.values().stream().map(OemVO::getTargetStockLocation).distinct().collect(Collectors.toList());

        // 仓库发货记录
        Map<String, List<WarehouseReleaseRecordVO>> releaseRecordMap = dfpFeign.selectWarehouseReleaseRecordByParams(
                        scenario, ImmutableMap.of("shipmentLocatorCodes", targetStockLocations, "itemCodes", allProductFactoryCodes)).stream()
                .collect(Collectors.groupingBy(WarehouseReleaseRecordVO::getItemCode));
        // 仓库发货至中转库
        Map<String, List<WarehouseReleaseToWarehouseVO>> releaseToWarehouseMap = dfpFeign.selectWarehouseReleaseToWarehouseVOListByParams(
                        scenario, ImmutableMap.of("shipmentLocatorCodes", targetStockLocations, "itemCodes", allProductFactoryCodes)).stream()
                .collect(Collectors.groupingBy(WarehouseReleaseToWarehouseVO::getItemCode));

        // 乘用车销售
        Map<String, List<PassengerCarSaleVO>> passengerSaleMap = dfpFeign.selectPassengerCarSaleByParams(
                        scenario, ImmutableMap.of("vehicleModeCodeList", allVehicleModeCodes)).stream()
                .filter(d -> d.getVehicleModelCode() != null)
                .collect(Collectors.groupingBy(PassengerCarSaleVO::getVehicleModelCode));

        // 物料主数据
        FeignDynamicParam feignParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "vehicle_model_code", "product_eop", "product_length", "product_width", "product_area"))
                .queryParam(ImmutableMap.of("productCodes", allProductFactoryCodes))
                .build();
        Map<String, NewProductStockPointVO> productStockMap = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignParam).stream()
                .filter(d -> d.getProductCode() != null)
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));

        // 默认12个月维度
        List<String> monthRangeList = getMonthRange(null, null, dto.getAppointYearMonth(), false);

        // 默认12个月维度
        List<String> monthRangeList02 = getMonthRange(null, null, dto.getAppointYearMonth(), true);

        // 4. 核心计算逻辑
        List<MaterialPurchaseReviewDataDTO> addList = new ArrayList<>();
        for (Map.Entry<String, NoGlassInventoryShiftDataVO> entry : shiftDataMap.entrySet()) {
            String productCode = entry.getKey();
            NoGlassInventoryShiftDataVO shiftData = entry.getValue();

            // 当前物料的关联数据（从预处理的Map中直接获取）
            List<NoGlassInventoryShiftDetailVO> shiftDetails = shiftDetailMap.getOrDefault(shiftData.getId(), Collections.emptyList());
            List<MaterialTransactionsVO> transactions = transactionMap.getOrDefault(productCode, Collections.emptyList());
            List<MaterialSupplierPurchaseVO> suppliers = supplierMap.getOrDefault(productCode, Collections.emptyList());
            List<MaterialArrivalTrackingVO> arrivalTrackings = arrivalTrackingMap.getOrDefault(productCode, Collections.emptyList());
            List<String> factoryCodes = productFactoryCodeMap.getOrDefault(productCode, Collections.emptyList());
            List<String> vehicleCodes = vehicleModeCodeMap.getOrDefault(productCode, Collections.emptyList());

            // 过滤需求预测明细（根据工厂编码关联）
            List<ConsistenceDemandForecastDataDetailVO> relatedForecastDetails = factoryCodes.stream()
                    .flatMap(code -> forecastDetailMap.getOrDefault(code, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            // 过滤车型相关数据
            List<OemVehicleModelVO> relatedVehicles = vehicleCodes.stream()
                    .map(oemVehicleMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<PassengerCarSaleVO> relatedSales = vehicleCodes.stream()
                    .flatMap(code -> passengerSaleMap.getOrDefault(code, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            // 计算库存与需求
            calcInventoryAndDemandData(monthRangeList, addList, dto, shiftData, shiftDetails, forecastVersionMap,
                    forecastDataMap, relatedForecastDetails, transactions, relatedVehicles, suppliers, arrivalTrackings);

            // 处理每个工厂编码的车型历史和预测
            for (String factoryCode : factoryCodes) {
                // 过滤仓库发货记录（根据工厂编码和目标货位）
                List<WarehouseReleaseRecordVO> relatedReleases = relatedVehicles.stream()
                        .map(v -> oemMap.get(v.getOemCode()))
                        .filter(Objects::nonNull)
                        .flatMap(oem -> releaseRecordMap.getOrDefault(factoryCode, Collections.emptyList()).stream()
                                .filter(r -> oem.getTargetStockLocation().equals(r.getShipmentLocatorCode())))
                        .collect(Collectors.toList());
                // 过滤中转库发货记录
                List<WarehouseReleaseToWarehouseVO> relatedTransfers = relatedVehicles.stream()
                        .map(v -> oemMap.get(v.getOemCode()))
                        .filter(Objects::nonNull)
                        .flatMap(oem -> releaseToWarehouseMap.getOrDefault(factoryCode, Collections.emptyList()).stream()
                                .filter(r -> oem.getTargetStockLocation().equals(r.getShipmentLocatorCode())))
                        .collect(Collectors.toList());
                NewProductStockPointVO productStock = productStockMap.get(factoryCode);
                List<ConsistenceDemandForecastDataDetailVO> relatedForecastDetails02 = forecastDetailMap02.getOrDefault(factoryCode, Collections.emptyList());

                // 计算车型历史
                calcModelHistory(monthRangeList02, addList, factoryCode, dto, shiftData, forecastVersionMap02,
                        forecastDataMap02, relatedForecastDetails02, transactions, relatedSales,
                        relatedReleases, relatedTransfers, productStock, suppliers, relatedVehicles);
                // 计算车型预测
                calcModelForecast(monthRangeList, addList, factoryCode, dto, shiftData, forecastVersionMap,
                        forecastDataMap, relatedForecastDetails, suppliers, relatedVehicles, productStockMap);
            }
        }

        // 批量落库
        if (!addList.isEmpty()) {
            materialPurchaseReviewDataDao.deleteAll();
            Lists.partition(addList, 1000).forEach(this::doCreateBatch);
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 计算（库存与需求）
     *
     * @param monthRangeList                            年月维度
     * @param addList                                   材料采购评审
     * @param materialPurchaseReviewDTO                 材料采购评审DTO
     * @param noGlassInventoryShiftDataVO               非原片推移
     * @param noGlassInventoryShiftDetailVOList         非原片推移明细
     * @param consistenceDemandForecastVersionMap       需求预测版本
     * @param consistenceDemandForecastDataMap          需求预测
     * @param consistenceDemandForecastDataDetailVOList 需求预测明细
     * @param materialTransactionsVOList                投料（事务）
     * @param oemVehicleModelVOList                     主机厂车型
     * @param materialSupplierPurchaseVOList            材料与供应商关系
     * @param materialArrivalTrackingVOList             到货跟踪
     */
    private void calcInventoryAndDemandData(List<String> monthRangeList,
                                            List<MaterialPurchaseReviewDataDTO> addList,
                                            MaterialPurchaseReviewDTO materialPurchaseReviewDTO,
                                            NoGlassInventoryShiftDataVO noGlassInventoryShiftDataVO,
                                            List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftDetailVOList,
                                            Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionMap,
                                            Map<String, List<ConsistenceDemandForecastDataVO>> consistenceDemandForecastDataMap,
                                            List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOList,
                                            List<MaterialTransactionsVO> materialTransactionsVOList,
                                            List<OemVehicleModelVO> oemVehicleModelVOList,
                                            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList,
                                            List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList) {

        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 赋值日期数据
        setStatisticsDataDate(monthRangeList, statisticsList, materialPurchaseReviewDTO, consistenceDemandForecastVersionMap,
                consistenceDemandForecastDataMap, consistenceDemandForecastDataDetailVOList);
        // 赋值固定数据
        setStatisticsDataFixed(monthRangeList, statisticsList, materialPurchaseReviewDTO, noGlassInventoryShiftDetailVOList,
                materialTransactionsVOList, materialArrivalTrackingVOList);

        for (MaterialStatisticsVO statistics : statisticsList) {
            Map<String, ?> monthlyMap = statistics.getMonthlyMap();
            MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO = new MaterialPurchaseReviewDataDTO();
            materialPurchaseReviewDataDTO.setLineDescription(statistics.getLineDescription());
            setFoundationData(materialPurchaseReviewDataDTO, noGlassInventoryShiftDataVO, oemVehicleModelVOList,
                    materialSupplierPurchaseVOList, null, null, monthlyMap, monthRangeList);
            materialPurchaseReviewDataDTO.setReviewType(MaterialPurchaseReviewTypeEnum.INVENTORY_AND_DEMAND.getCode());
            materialPurchaseReviewDataDTO.setConsistenceDemandForecastVersionId(statistics.getConsistenceDemandForecastVersionId());

            // 定义月份对应的setter方法列表（顺序对应1-12月）
            List<Consumer<String>> monthSetters = Arrays.asList(
                    materialPurchaseReviewDataDTO::setOneYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwoYearMonthValue,
                    materialPurchaseReviewDataDTO::setThreeYearMonthValue,
                    materialPurchaseReviewDataDTO::setFourYearMonthValue,
                    materialPurchaseReviewDataDTO::setFiveYearMonthValue,
                    materialPurchaseReviewDataDTO::setSixYearMonthValue,
                    materialPurchaseReviewDataDTO::setSevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setEightYearMonthValue,
                    materialPurchaseReviewDataDTO::setNineYearMonthValue,
                    materialPurchaseReviewDataDTO::setTenYearMonthValue,
                    materialPurchaseReviewDataDTO::setElevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwelveYearMonthValue
            );

            // 循环处理12个月份（注意先确保monthRangeList至少有12个元素，避免越界）
            for (int i = 0; i < 12; i++) {
                // 避免索引越界（如果monthRangeList不足12个，跳过后续）
                if (i >= monthRangeList.size()) {
                    break;
                }
                String month = monthRangeList.get(i);
                Object value = monthlyMap.get(month);
                // 非空才设置值
                if (value != null) {
                    String valueStr;
                    // 判断是否为数字类型（单个数字）
                    if (value instanceof Number) {
                        // 是数字类型，只保留整数部分
                        Number number = (Number) value;
                        valueStr = String.valueOf(number.longValue());
                    }
                    // 判断是否为字符串类型（可能包含逗号分隔的多个数字）
                    else if (value instanceof String) {
                        String originalStr = (String) value;
                        // 按逗号分割字符串
                        String[] parts = originalStr.split(",");
                        StringBuilder processed = new StringBuilder();
                        for (String part : parts) {
                            part = part.trim();
                            try {
                                // 尝试转换为数字并取整数部分
                                double num = Double.parseDouble(part);
                                processed.append((long) num);
                            } catch (NumberFormatException e) {
                                // 如果不是数字格式，保留原字符串部分
                                processed.append(part);
                            }
                            // 添加逗号（最后一个元素后不添加）
                            if (processed.length() > 0 && !part.equals(parts[parts.length - 1])) {
                                processed.append(",");
                            }
                        }
                        valueStr = processed.toString();
                    }
                    // 其他类型直接转换为字符串
                    else {
                        valueStr = String.valueOf(value);
                    }
                    monthSetters.get(i).accept(valueStr);
                }
            }
            // 指定年月
            materialPurchaseReviewDataDTO.setAppointYearMonth(materialPurchaseReviewDTO.getAppointYearMonth());
            addList.add(materialPurchaseReviewDataDTO);
        }
    }

    /**
     * 计算（车型历史）
     *
     * @param monthRangeList                            年月维度
     * @param addList                                   材料采购评审
     * @param productFactoryCode                        本厂编码
     * @param materialPurchaseReviewDTO                 材料采购评审DTO
     * @param noGlassInventoryShiftDataVO               非原片推移
     * @param consistenceDemandForecastVersionMap       需求预测版本
     * @param consistenceDemandForecastDataMap          需求预测
     * @param consistenceDemandForecastDataDetailVOList 需求预测明细
     * @param materialTransactionsVOList                投料（事务）
     * @param passengerCarSaleVOList                    乘用车市场
     * @param warehouseReleaseRecordVOList              仓库发货记录
     * @param warehouseReleaseToWarehouseVOList         仓库发货至中转库
     * @param newProductStockPointVO                    物料
     * @param suppliers                                 供应商
     * @param relatedVehicles                           主机厂车型
     */
    private void calcModelHistory(List<String> monthRangeList,
                                  List<MaterialPurchaseReviewDataDTO> addList,
                                  String productFactoryCode,
                                  MaterialPurchaseReviewDTO materialPurchaseReviewDTO,
                                  NoGlassInventoryShiftDataVO noGlassInventoryShiftDataVO,
                                  Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionMap,
                                  Map<String, List<ConsistenceDemandForecastDataVO>> consistenceDemandForecastDataMap,
                                  List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOList,
                                  List<MaterialTransactionsVO> materialTransactionsVOList,
                                  List<PassengerCarSaleVO> passengerCarSaleVOList,
                                  List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList,
                                  List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseVOList,
                                  NewProductStockPointVO newProductStockPointVO,
                                  List<MaterialSupplierPurchaseVO> suppliers,
                                  List<OemVehicleModelVO> relatedVehicles) {

        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 赋值固定数据
        setStatisticsDataFixed02(monthRangeList, statisticsList, materialPurchaseReviewDTO, materialTransactionsVOList, consistenceDemandForecastVersionMap, consistenceDemandForecastDataMap,
                consistenceDemandForecastDataDetailVOList, passengerCarSaleVOList, warehouseReleaseRecordVOList, warehouseReleaseToWarehouseVOList);

        for (MaterialStatisticsVO statistics : statisticsList) {
            Map<String, ?> monthlyMap = statistics.getMonthlyMap();
            MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO = new MaterialPurchaseReviewDataDTO();
            materialPurchaseReviewDataDTO.setLineDescription(statistics.getLineDescription());
            setFoundationData(materialPurchaseReviewDataDTO, noGlassInventoryShiftDataVO,
                    null, suppliers, productFactoryCode, newProductStockPointVO, monthlyMap, monthRangeList);
            materialPurchaseReviewDataDTO.setReviewType(MaterialPurchaseReviewTypeEnum.MODEL_HISTORY.getCode());
            materialPurchaseReviewDataDTO.setConsistenceDemandForecastVersionId(statistics.getConsistenceDemandForecastVersionId());
            // 指定年月
            materialPurchaseReviewDataDTO.setAppointYearMonth(materialPurchaseReviewDTO.getAppointYearMonth());
            addList.add(materialPurchaseReviewDataDTO);
        }
    }

    /**
     * 计算（车型预测）
     *
     * @param monthRangeList                            年月维度
     * @param addList                                   材料采购评审
     * @param productFactoryCode                        本场编码
     * @param materialPurchaseReviewDTO                 材料采购评审DTO
     * @param noGlassInventoryShiftDataVO               非原片推移
     * @param consistenceDemandForecastVersionMap       需求预测版本
     * @param consistenceDemandForecastDataMap          需求预测
     * @param consistenceDemandForecastDataDetailVOList 需求预测明细
     * @param suppliers                                 供应商
     * @param relatedVehicles                           主机厂车型
     * @param productStockPointMap                      物料
     */
    private void calcModelForecast(List<String> monthRangeList,
                                   List<MaterialPurchaseReviewDataDTO> addList,
                                   String productFactoryCode,
                                   MaterialPurchaseReviewDTO materialPurchaseReviewDTO,
                                   NoGlassInventoryShiftDataVO noGlassInventoryShiftDataVO,
                                   Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionMap,
                                   Map<String, List<ConsistenceDemandForecastDataVO>> consistenceDemandForecastDataMap,
                                   List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOList,
                                   List<MaterialSupplierPurchaseVO> suppliers,
                                   List<OemVehicleModelVO> relatedVehicles,
                                   Map<String, NewProductStockPointVO> productStockPointMap) {

        List<MaterialStatisticsVO> statisticsList = new ArrayList<>();

        // 赋值日期数据
        setStatisticsDataDate(monthRangeList, statisticsList, materialPurchaseReviewDTO, consistenceDemandForecastVersionMap,
                consistenceDemandForecastDataMap, consistenceDemandForecastDataDetailVOList);

        for (MaterialStatisticsVO statistics : statisticsList) {
            Map<String, ?> monthlyMap = statistics.getMonthlyMap();
            MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO = new MaterialPurchaseReviewDataDTO();
            materialPurchaseReviewDataDTO.setLineDescription(statistics.getLineDescription());
            setFoundationData(materialPurchaseReviewDataDTO, noGlassInventoryShiftDataVO, relatedVehicles, suppliers,
                    productFactoryCode, null, monthlyMap, monthRangeList);
            materialPurchaseReviewDataDTO.setReviewType(MaterialPurchaseReviewTypeEnum.MODEL_FORECAST.getCode());
            materialPurchaseReviewDataDTO.setConsistenceDemandForecastVersionId(statistics.getConsistenceDemandForecastVersionId());
            materialPurchaseReviewDataDTO.setForecastProvider(statistics.getForecastProvider());
            if (productStockPointMap.containsKey(materialPurchaseReviewDataDTO.getProductFactoryCode())) {
                NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(materialPurchaseReviewDataDTO.getProductFactoryCode());
                if (null != newProductStockPointVO.getProductArea()) {
                    materialPurchaseReviewDataDTO.setSinglePieceArea(newProductStockPointVO.getProductArea().toString());
                } else if (null != newProductStockPointVO.getProductLength() && null != newProductStockPointVO.getProductWidth()) {
                    materialPurchaseReviewDataDTO.setSinglePieceArea(newProductStockPointVO.getProductLength()
                            .multiply(newProductStockPointVO.getProductWidth())
                            .subtract(new BigDecimal("1000000")).toString());
                }
            }

            // 定义月份对应的setter方法列表（顺序对应1-12月）
            List<Consumer<String>> monthSetters = Arrays.asList(
                    materialPurchaseReviewDataDTO::setOneYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwoYearMonthValue,
                    materialPurchaseReviewDataDTO::setThreeYearMonthValue,
                    materialPurchaseReviewDataDTO::setFourYearMonthValue,
                    materialPurchaseReviewDataDTO::setFiveYearMonthValue,
                    materialPurchaseReviewDataDTO::setSixYearMonthValue,
                    materialPurchaseReviewDataDTO::setSevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setEightYearMonthValue,
                    materialPurchaseReviewDataDTO::setNineYearMonthValue,
                    materialPurchaseReviewDataDTO::setTenYearMonthValue,
                    materialPurchaseReviewDataDTO::setElevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwelveYearMonthValue
            );

            // 循环处理12个月份（注意先确保monthRangeList至少有12个元素，避免越界）
            for (int i = 0; i < 12; i++) {
                // 避免索引越界（如果monthRangeList不足12个，跳过后续）
                if (i >= monthRangeList.size()) {
                    break;
                }
                String month = monthRangeList.get(i);
                Object value = monthlyMap.get(month);
                // 非空才设置值
                if (value != null) {
                    String valueStr;
                    // 判断是否为数字类型（单个数字）
                    if (value instanceof Number) {
                        // 是数字类型，只保留整数部分
                        Number number = (Number) value;
                        valueStr = String.valueOf(number.longValue());
                    }
                    // 判断是否为字符串类型（可能包含逗号分隔的多个数字）
                    else if (value instanceof String) {
                        String originalStr = (String) value;
                        // 按逗号分割字符串
                        String[] parts = originalStr.split(",");
                        StringBuilder processed = new StringBuilder();
                        for (String part : parts) {
                            part = part.trim();
                            try {
                                // 尝试转换为数字并取整数部分
                                double num = Double.parseDouble(part);
                                processed.append((long) num);
                            } catch (NumberFormatException e) {
                                // 如果不是数字格式，保留原字符串部分
                                processed.append(part);
                            }
                            // 添加逗号（最后一个元素后不添加）
                            if (processed.length() > 0 && !part.equals(parts[parts.length - 1])) {
                                processed.append(",");
                            }
                        }
                        valueStr = processed.toString();
                    }
                    // 其他类型直接转换为字符串
                    else {
                        valueStr = String.valueOf(value);
                    }
                    monthSetters.get(i).accept(valueStr);
                }
            }
            // 指定年月
            materialPurchaseReviewDataDTO.setAppointYearMonth(materialPurchaseReviewDTO.getAppointYearMonth());
            addList.add(materialPurchaseReviewDataDTO);
        }
    }


    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialPurchaseReviewDataVO> invocation(List<MaterialPurchaseReviewDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    /**
     * 赋值基础数据
     *
     * @param materialPurchaseReviewDataDTO  材料采购评审
     * @param noGlassInventoryShiftDataVO    非原片推移
     * @param oemVehicleModelVOList          主机厂车型
     * @param materialSupplierPurchaseVOList 材料与供应商关系
     * @param productFactoryCode             本场编码
     * @param newProductStockPointVO         物料
     * @param monthlyMap                     月份维度
     * @param monthRangeList                 月份维度
     */
    private void setFoundationData(MaterialPurchaseReviewDataDTO materialPurchaseReviewDataDTO,
                                   NoGlassInventoryShiftDataVO noGlassInventoryShiftDataVO,
                                   List<OemVehicleModelVO> oemVehicleModelVOList,
                                   List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList,
                                   String productFactoryCode,
                                   NewProductStockPointVO newProductStockPointVO,
                                   Map<String, ?> monthlyMap, List<String> monthRangeList) {
        // 物料
        materialPurchaseReviewDataDTO.setProductCode(noGlassInventoryShiftDataVO.getProductCode());
        materialPurchaseReviewDataDTO.setProductName(noGlassInventoryShiftDataVO.getProductName());
        if (StringUtils.isEmpty(productFactoryCode)) {
            materialPurchaseReviewDataDTO.setProductFactoryCode(noGlassInventoryShiftDataVO.getProductFactoryCode());
        } else {
            materialPurchaseReviewDataDTO.setProductFactoryCode(productFactoryCode);
        }

        // 主机厂车型
        materialPurchaseReviewDataDTO.setVehicleModelCode(noGlassInventoryShiftDataVO.getVehicleModeCode());

        if (CollectionUtils.isNotEmpty(oemVehicleModelVOList)) {
            // 使用集合去重
            Set<String> oemCodeSet = new HashSet<>();
            Set<String> oemNameSet = new HashSet<>();

            // 收集所有不重复的OEM代码和名称
            for (OemVehicleModelVO oemVehicleModelVO : oemVehicleModelVOList) {
                if (oemVehicleModelVO.getOemCode() != null) {
                    oemCodeSet.add(oemVehicleModelVO.getOemCode());
                }
                if (oemVehicleModelVO.getOemName() != null) {
                    oemNameSet.add(oemVehicleModelVO.getOemName());
                }
            }
            String oemCode = String.join(",", oemCodeSet);
            String oemName = String.join(",", oemNameSet);

            materialPurchaseReviewDataDTO.setOemCode(oemCode);
            materialPurchaseReviewDataDTO.setOemName(oemName);
        }

        if (CollectionUtils.isNotEmpty(materialSupplierPurchaseVOList)) {
            // 供应商（多个用逗号分割）
            String supplierCode = materialSupplierPurchaseVOList.stream()
                    .map(MaterialSupplierPurchaseVO::getSupplierCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
            materialPurchaseReviewDataDTO.setSupplierCode(supplierCode);

            // 采购周期
            BigDecimal purchaseLot = materialSupplierPurchaseVOList.stream()
                    .filter(data -> null != data.getDemandPattern())
                    .map(data -> {
                        if (data.getDemandPattern().equals(DemandPatternEnum.PLAN_NEED.getCode())) {
                            return data.getRequestCargoPlanLockDay();
                        }
                        if (data.getDemandPattern().equals(DemandPatternEnum.PO.getCode())) {
                            return data.getOrderPlacementLeadTimeDay();
                        }
                        return BigDecimal.ZERO;
                    }).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            materialPurchaseReviewDataDTO.setPurchaseLot(purchaseLot);

            // 最小起订量
            BigDecimal minOrderQuantity = materialSupplierPurchaseVOList.stream()
                    .map(MaterialSupplierPurchaseVO::getMinOrderQty)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            materialPurchaseReviewDataDTO.setMinOrderQuantity(minOrderQuantity);

            if (Objects.nonNull(newProductStockPointVO)) {
                materialPurchaseReviewDataDTO.setVehicleModelEopDate(DateUtils.dateToString(newProductStockPointVO.getProductEop()));
                // 主机厂车型
                materialPurchaseReviewDataDTO.setVehicleModel(newProductStockPointVO.getVehicleModelCode());
            } else {
                materialPurchaseReviewDataDTO.setVehicleModel(noGlassInventoryShiftDataVO.getVehicleModeCode());
            }

            // 定义月份对应的setter方法列表（顺序对应1-12月）
            List<Consumer<String>> monthSetters = Arrays.asList(
                    materialPurchaseReviewDataDTO::setOneYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwoYearMonthValue,
                    materialPurchaseReviewDataDTO::setThreeYearMonthValue,
                    materialPurchaseReviewDataDTO::setFourYearMonthValue,
                    materialPurchaseReviewDataDTO::setFiveYearMonthValue,
                    materialPurchaseReviewDataDTO::setSixYearMonthValue,
                    materialPurchaseReviewDataDTO::setSevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setEightYearMonthValue,
                    materialPurchaseReviewDataDTO::setNineYearMonthValue,
                    materialPurchaseReviewDataDTO::setTenYearMonthValue,
                    materialPurchaseReviewDataDTO::setElevenYearMonthValue,
                    materialPurchaseReviewDataDTO::setTwelveYearMonthValue
            );

            // 循环处理12个月份（注意先确保monthRangeList至少有12个元素，避免越界）
            for (int i = 0; i < 12; i++) {
                // 避免索引越界（如果monthRangeList不足12个，跳过后续）
                if (i >= monthRangeList.size()) {
                    break;
                }
                String month = monthRangeList.get(i);
                Object value = monthlyMap.get(month);
                // 非空才设置值
                if (value != null) {
                    String valueStr;
                    // 判断是否为数字类型（单个数字）
                    if (value instanceof Number) {
                        // 是数字类型，只保留整数部分
                        Number number = (Number) value;
                        valueStr = String.valueOf(number.longValue());
                    }
                    // 判断是否为字符串类型（可能包含逗号分隔的多个数字）
                    else if (value instanceof String) {
                        String originalStr = (String) value;
                        // 按逗号分割字符串
                        String[] parts = originalStr.split(",");
                        StringBuilder processed = new StringBuilder();
                        for (String part : parts) {
                            part = part.trim();
                            try {
                                // 尝试转换为数字并取整数部分
                                double num = Double.parseDouble(part);
                                processed.append((long) num);
                            } catch (NumberFormatException e) {
                                // 如果不是数字格式，保留原字符串部分
                                processed.append(part);
                            }
                            // 添加逗号（最后一个元素后不添加）
                            if (processed.length() > 0 && !part.equals(parts[parts.length - 1])) {
                                processed.append(",");
                            }
                        }
                        valueStr = processed.toString();
                    }
                    // 其他类型直接转换为字符串
                    else {
                        valueStr = String.valueOf(value);
                    }
                    monthSetters.get(i).accept(valueStr);
                }
            }
        }
    }

    /**
     * 赋值 统计（日期维度）
     *
     * @param monthRangeList                            年月维度
     * @param statisticsList                            统计
     * @param materialPurchaseReviewTableDTO            材料采购评审DTO
     * @param consistenceDemandForecastVersionMap       需求预测版本
     * @param consistenceDemandForecastDataMap          需求预测
     * @param consistenceDemandForecastDataDetailVOList 需求预测明细
     */
    private void setStatisticsDataDate(List<String> monthRangeList,
                                       List<MaterialStatisticsVO> statisticsList,
                                       MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO,
                                       Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionMap,
                                       Map<String, List<ConsistenceDemandForecastDataVO>> consistenceDemandForecastDataMap,
                                       List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOList) {
        // 获取日期列范围
        Map<String, String> yearMonthMap = generateMonthRange(materialPurchaseReviewTableDTO.getAppointYearMonth());
        for (Map.Entry<String, String> entry : yearMonthMap.entrySet()) {
            // 获取月份对应的需求预测版本
            ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO = consistenceDemandForecastVersionMap.get(entry.getValue());
            Map<String, Object> monthlyMap = new HashMap<>();
            if (Objects.nonNull(consistenceDemandForecastVersionVO) && consistenceDemandForecastDataMap.containsKey(consistenceDemandForecastVersionVO.getId())) {
                List<ConsistenceDemandForecastDataVO> consistenceDemandForecastDataVOList = consistenceDemandForecastDataMap.get(consistenceDemandForecastVersionVO.getId());
                List<String> consistenceDemandForecastDataIdList = consistenceDemandForecastDataVOList.stream().map(BaseVO::getId).collect(Collectors.toList());
                for (String monthRange : monthRangeList) {
                    BigDecimal reduce = consistenceDemandForecastDataDetailVOList.stream()
                            .filter(data -> consistenceDemandForecastDataIdList.contains(data.getConsistenceDemandForecastDataId()))
                            .filter(data -> null != data.getForecastTime() && null != data.getForecastQuantity())
                            .filter(data -> compareMonths(monthRange, data.getForecastTime()))
                            .map(ConsistenceDemandForecastDataDetailVO::getForecastQuantity)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    monthlyMap.put(monthRange, reduce);
                }
            }

            MaterialStatisticsVO statistics = new MaterialStatisticsVO();
            statistics.setLineDescription(entry.getKey());
            statistics.setMonthlyMap(monthlyMap);
            statistics.setAmount(monthlyMap.values().stream()
                    .map(data -> new BigDecimal(data.toString()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (CollectionUtils.isNotEmpty(consistenceDemandForecastDataDetailVOList)) {
                statistics.setForecastProvider(consistenceDemandForecastDataDetailVOList.get(0).getCreator());
            }
            statisticsList.add(statistics);
        }
    }

    private void setStatisticsDataFixed(List<String> monthRangeList, List<MaterialStatisticsVO> statisticsList,
                                        MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO,
                                        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftDetailVOList,
                                        List<MaterialTransactionsVO> materialTransactionsVOList,
                                        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList) {
        // 2. 预计算所有需要的分组数据（核心优化：一次遍历，多次使用）
        // 2.1 物料交易数据分组（实际消耗、物料消耗）
        Map<String, BigDecimal> transactionQtyByMonthForActual = preComputeTransactionQty(materialTransactionsVOList, monthRangeList);

        // 2.2 非玻璃库存数据分组（期初库存、库存差异、计划采购量、计划到货时间）
        Map<String, BigDecimal> openingInventoryByMonth = preComputeNoGlassOpeningInventory(noGlassInventoryShiftDetailVOList, monthRangeList);
        Map<String, BigDecimal> adjustQuantityByMonth = preComputeNoGlassAdjustQuantity(noGlassInventoryShiftDetailVOList, monthRangeList);
        Map<String, String> planPurchaseByMonth = preComputeNoGlassPlanPurchase(noGlassInventoryShiftDetailVOList, monthRangeList);
        Map<String, String> plannedArrivalTimeByMonth = preComputeNoGlassPlannedArrivalTime(noGlassInventoryShiftDetailVOList, monthRangeList);

        // 2.3 在途订单数据分组（在途订单量、到货时间）
        Map<String, BigDecimal> predictArrivalQtyByMonth = preComputeInTransitOrders(materialArrivalTrackingVOList, monthRangeList);
        Map<String, String> arrivalTimeByMonth = preComputeArrivalTime(materialArrivalTrackingVOList, monthRangeList);

        // 3. 处理各枚举值，使用预计算的Map构建统计数据
        for (MaterialPurchaseReviewFixedEnum value : MaterialPurchaseReviewFixedEnum.values()) {
            switch (value) {
                case ACTUAL_CONSUMPTION:
                    addStatisticsVO(statisticsList, value.getDesc(), transactionQtyByMonthForActual);
                    break;
                case BEGINNING_INVENTORY:
                    addStatisticsVO(statisticsList, value.getDesc(), openingInventoryByMonth);
                    break;
                case IN_TRANSIT_ORDERS:
                    addStatisticsVO(statisticsList, value.getDesc(), predictArrivalQtyByMonth);
                    break;
                case ARRIVAL_TIME:
                    addStatisticsVO(statisticsList, value.getDesc(), arrivalTimeByMonth, false);
                    break;
                case INVENTORY_GAP:
                    addStatisticsVO(statisticsList, value.getDesc(), adjustQuantityByMonth);
                    break;
                case PLANNED_PURCHASE_QUANTITY:
                    addStatisticsVO(statisticsList, value.getDesc(), planPurchaseByMonth);
                    break;
                case PLANNED_ARRIVAL_TIME:
                    addStatisticsVO(statisticsList, value.getDesc(), plannedArrivalTimeByMonth, false);
                    break;
                default:// 忽略未知枚举值
            }
        }
    }

    private void setStatisticsDataFixed02(List<String> monthRangeList,
                                          List<MaterialStatisticsVO> statisticsList,
                                          MaterialPurchaseReviewDTO materialPurchaseReviewTableDTO,
                                          List<MaterialTransactionsVO> materialTransactionsVOList,
                                          Map<String, ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionMap,
                                          Map<String, List<ConsistenceDemandForecastDataVO>> consistenceDemandForecastDataMap,
                                          List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOList,
                                          List<PassengerCarSaleVO> passengerCarSaleVOList,
                                          List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList,
                                          List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseVOList) {

        // 2. 预计算所有需要的分组数据（核心优化：一次遍历，多次使用）
        // 2.1 物料交易数据分组（实际消耗、物料消耗）
        Map<String, BigDecimal> transactionQtyByMonthForConsume = preComputeTransactionQty(materialTransactionsVOList, monthRangeList);

        // 2.4 物料预测数据分组（物料预测、交付预测占比）
        Map<String, BigDecimal> materialForecastByMonth = preComputeMaterialForecast(consistenceDemandForecastVersionMap, consistenceDemandForecastDataMap, consistenceDemandForecastDataDetailVOList, monthRangeList);

        // 2.5 车辆销售数据分组（车型消耗量）
        Map<String, BigDecimal> vehicleSaleByMonth = preComputeVehicleSale(passengerCarSaleVOList, monthRangeList);

        // 2.6 仓库发货数据分组（消耗交付占比、交付预测占比）
        Map<String, BigDecimal> warehouseReleaseRecordByMonth = preComputeWarehouseRelease(warehouseReleaseRecordVOList, monthRangeList);
        Map<String, BigDecimal> warehouseReleaseToWarehouseByMonth = preComputeWarehouseReleaseToWarehouse(warehouseReleaseToWarehouseVOList, monthRangeList);

        // 3. 处理各枚举值，使用预计算的Map构建统计数据
        for (MaterialPurchaseReviewFixedEnum value : MaterialPurchaseReviewFixedEnum.values()) {
            switch (value) {
                case MATERIAL_FORECAST:
                    addStatisticsVO(statisticsList, value.getDesc(), materialForecastByMonth);
                    break;
                case MATERIAL_CONSUME:
                    addStatisticsVO(statisticsList, value.getDesc(), transactionQtyByMonthForConsume);
                    break;
                case VEHICLE_MODEL_CONSUME:
                    addStatisticsVO(statisticsList, value.getDesc(), vehicleSaleByMonth);
                    break;
                case CONSUME_DELIVERY_PROPORTION:
                    Map<String, String> consumeDeliveryProportionMap = computeConsumeDeliveryProportion(monthRangeList, vehicleSaleByMonth, warehouseReleaseRecordByMonth, warehouseReleaseToWarehouseByMonth);
                    addStatisticsVO(statisticsList, value.getDesc(), consumeDeliveryProportionMap, false);
                    break;
                case DELIVERY_FORECAST_PROPORTION:
                    Map<String, String> deliveryForecastProportionMap = computeDeliveryForecastProportion(monthRangeList, materialForecastByMonth, warehouseReleaseRecordByMonth, warehouseReleaseToWarehouseByMonth);
                    addStatisticsVO(statisticsList, value.getDesc(), deliveryForecastProportionMap, false);
                    break;
                default:// 忽略未知枚举值
            }
        }
    }


    /**
     * 预计算物料交易按月份的数量总和
     */
    private Map<String, BigDecimal> preComputeTransactionQty(List<MaterialTransactionsVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getTransactionDate() != null && data.getTransactionQty() != null,
                MaterialTransactionsVO::getTransactionDate,
                MaterialTransactionsVO::getTransactionQty);
    }

    /**
     * 预计算非玻璃库存期初库存按月份的总和
     */
    private Map<String, BigDecimal> preComputeNoGlassOpeningInventory(List<NoGlassInventoryShiftDetailVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getInventoryDate() != null && data.getOpeningInventory() != null,
                NoGlassInventoryShiftDetailVO::getInventoryDate,
                NoGlassInventoryShiftDetailVO::getOpeningInventory);
    }

    /**
     * 预计算非玻璃库存调整量按月份的总和
     */
    private Map<String, BigDecimal> preComputeNoGlassAdjustQuantity(List<NoGlassInventoryShiftDetailVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getInventoryDate() != null && data.getAdjustQuantity() != null,
                NoGlassInventoryShiftDetailVO::getInventoryDate,
                NoGlassInventoryShiftDetailVO::getAdjustQuantity);
    }

    /**
     * 预计算非玻璃库存计划采购量按月份的总和
     */
    private Map<String, String> preComputeNoGlassPlanPurchase(List<NoGlassInventoryShiftDetailVO> list, List<String> months) {
        // 按 inventoryDate 正序排序（从早到晚）
        list = list.stream()
                .sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate))
                .collect(Collectors.toList());
        return preComputeMonthStringJoin(list, months,
                data -> data.getInventoryDate() != null && data.getPlanPurchase() != null && data.getPlanPurchase().compareTo(BigDecimal.ZERO) > 0,
                NoGlassInventoryShiftDetailVO::getInventoryDate,
                data -> String.valueOf(data.getPlanPurchase()));
    }

    /**
     * 预计算非玻璃库存计划到货时间按月份的字符串拼接
     */
    private Map<String, String> preComputeNoGlassPlannedArrivalTime(List<NoGlassInventoryShiftDetailVO> list, List<String> months) {
        // 按 inventoryDate 正序排序（从早到晚）
        list = list.stream()
                .sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate))
                .collect(Collectors.toList());
        return preComputeMonthStringJoin(list, months,
                data -> data.getInventoryDate() != null && data.getPlanPurchase() != null && data.getPlanPurchase().compareTo(BigDecimal.ZERO) > 0,
                NoGlassInventoryShiftDetailVO::getInventoryDate,
                data -> DateUtils.dateToString(data.getInventoryDate(), "MM-dd"));
    }

    /**
     * 预计算在途订单量按月份的总和
     */
    private Map<String, BigDecimal> preComputeInTransitOrders(List<MaterialArrivalTrackingVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getPredictArrivalDate() != null && data.getPredictArrivalQuantity() != null,
                MaterialArrivalTrackingVO::getPredictArrivalDate,
                MaterialArrivalTrackingVO::getPredictArrivalQuantity);
    }

    /**
     * 预计算到货时间按月份的字符串拼接
     */
    private Map<String, String> preComputeArrivalTime(List<MaterialArrivalTrackingVO> list, List<String> months) {
        return preComputeMonthStringJoin(list, months,
                data -> data.getPredictArrivalDate() != null,
                MaterialArrivalTrackingVO::getPredictArrivalDate,
                data -> DateUtils.dateToString(data.getPredictArrivalDate(), "MM-dd"));
    }

    /**
     * 预计算物料预测按月份的总和
     */
    private Map<String, BigDecimal> preComputeMaterialForecast(Map<String, ConsistenceDemandForecastVersionVO> versionMap,
                                                               Map<String, List<ConsistenceDemandForecastDataVO>> dataMap,
                                                               List<ConsistenceDemandForecastDataDetailVO> detailList,
                                                               List<String> months) {
        Map<String, BigDecimal> result = new HashMap<>(months.size());
        for (String month : months) {
            ConsistenceDemandForecastVersionVO version = versionMap.get(month);
            if (version == null || !dataMap.containsKey(version.getId())) {
                result.put(month, BigDecimal.ZERO);
                continue;
            }
            // 使用Set优化contains判断
            Set<String> dataIdSet = dataMap.get(version.getId()).stream()
                    .map(BaseVO::getId)
                    .collect(Collectors.toSet());
            BigDecimal sum = detailList.stream()
                    .filter(detail -> dataIdSet.contains(detail.getConsistenceDemandForecastDataId()))
                    .filter(detail -> detail.getForecastTime() != null && detail.getForecastQuantity() != null)
                    .filter(detail -> month.equals(DateUtils.dateToString(detail.getForecastTime(), "yyyy-MM")))
                    .map(ConsistenceDemandForecastDataDetailVO::getForecastQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            result.put(month, sum);
        }
        return result;
    }

    /**
     * 预计算车辆销售量按月份的总和（转为BigDecimal）
     */
    private Map<String, BigDecimal> preComputeVehicleSale(List<PassengerCarSaleVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getSaleTime() != null && data.getSaleQuantity() != null,
                PassengerCarSaleVO::getSaleTime,
                data -> new BigDecimal(data.getSaleQuantity()));
    }

    /**
     * 预计算仓库发货记录按月份的总和
     */
    private Map<String, BigDecimal> preComputeWarehouseRelease(List<WarehouseReleaseRecordVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getInWarehouseTime() != null && data.getSumQty() != null,
                WarehouseReleaseRecordVO::getInWarehouseTime,
                WarehouseReleaseRecordVO::getSumQty);
    }

    /**
     * 预计算仓库发货至中转库按月份的总和
     */
    private Map<String, BigDecimal> preComputeWarehouseReleaseToWarehouse(List<WarehouseReleaseToWarehouseVO> list, List<String> months) {
        return preComputeMonthSum(list, months,
                data -> data.getInWarehouseTime() != null && data.getSumQty() != null,
                WarehouseReleaseToWarehouseVO::getInWarehouseTime,
                WarehouseReleaseToWarehouseVO::getSumQty);
    }

    /**
     * 计算消耗交付占比
     */
    private Map<String, String> computeConsumeDeliveryProportion(List<String> months,
                                                                 Map<String, BigDecimal> vehicleSaleMap,
                                                                 Map<String, BigDecimal> releaseRecordMap,
                                                                 Map<String, BigDecimal> releaseToWarehouseMap) {
        Map<String, String> result = new HashMap<>(months.size());
        for (String month : months) {
            BigDecimal sale = vehicleSaleMap.getOrDefault(month, BigDecimal.ZERO);
            BigDecimal releaseTotal = releaseRecordMap.getOrDefault(month, BigDecimal.ZERO)
                    .add(releaseToWarehouseMap.getOrDefault(month, BigDecimal.ZERO));
            String proportion = (sale.compareTo(BigDecimal.ZERO) > 0 && releaseTotal.compareTo(BigDecimal.ZERO) > 0)
                    ? sale.divide(releaseTotal, 2, RoundingMode.HALF_UP) + "%"
                    : null;
            result.put(month, proportion);
        }
        return result;
    }

    /**
     * 计算交付预测占比
     */
    private Map<String, String> computeDeliveryForecastProportion(List<String> months,
                                                                  Map<String, BigDecimal> forecastMap,
                                                                  Map<String, BigDecimal> releaseRecordMap,
                                                                  Map<String, BigDecimal> releaseToWarehouseMap) {
        Map<String, String> result = new HashMap<>(months.size());
        for (String month : months) {
            BigDecimal forecast = forecastMap.getOrDefault(month, BigDecimal.ZERO);
            BigDecimal releaseTotal = releaseRecordMap.getOrDefault(month, BigDecimal.ZERO)
                    .add(releaseToWarehouseMap.getOrDefault(month, BigDecimal.ZERO));
            String proportion = (forecast.compareTo(BigDecimal.ZERO) > 0 && releaseTotal.compareTo(BigDecimal.ZERO) > 0)
                    ? releaseTotal.divide(forecast, 2, RoundingMode.HALF_UP) + "%"
                    : null;
            result.put(month, proportion);
        }
        return result;
    }

    /**
     * 通用工具方法：按月份分组计算总和
     */
    private <T> Map<String, BigDecimal> preComputeMonthSum(List<T> list, List<String> months,
                                                           Predicate<T> filter,
                                                           Function<T, Date> dateExtractor,
                                                           Function<T, BigDecimal> valueExtractor) {
        if (CollectionUtils.isEmpty(list)) {
            return months.stream().collect(Collectors.toMap(m -> m, m -> BigDecimal.ZERO));
        }
        // 先过滤有效数据并分组
        Map<String, BigDecimal> monthSumMap = list.stream()
                .filter(filter)
                .collect(Collectors.groupingBy(
                        data -> DateUtils.dateToString(dateExtractor.apply(data), "yyyyMM"),
                        Collectors.reducing(BigDecimal.ZERO, valueExtractor, BigDecimal::add)
                ));
        // 确保所有月份都有值（补0）
        return months.stream()
                .collect(Collectors.toMap(
                        m -> m,
                        m -> monthSumMap.getOrDefault(m, BigDecimal.ZERO),
                        (a, b) -> a,
                        () -> new HashMap<>(months.size())
                ));
    }

    /**
     * 通用工具方法：按月份分组拼接字符串
     */
    private <T> Map<String, String> preComputeMonthStringJoin(List<T> list, List<String> months,
                                                              Predicate<T> filter,
                                                              Function<T, Date> dateExtractor,
                                                              Function<T, String> stringExtractor) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        // 分组并拼接字符串，同时确保stringExtractor的结果不为null
        Map<String, List<String>> monthStrListMap = list.stream()
                .filter(filter)
                .filter(data -> dateExtractor.apply(data) != null)
                .collect(Collectors.groupingBy(
                        data -> DateUtils.dateToString(dateExtractor.apply(data), "yyyyMM"),
                        Collectors.mapping(
                                data -> {
                                    String str = stringExtractor.apply(data);
                                    return str != null ? str : "";
                                },
                                Collectors.toList()
                        )
                ));

        // 拼接字符串并过滤掉空值（没有数据的月份）
        return monthStrListMap.entrySet().stream()
                .filter(entry -> !entry.getValue().isEmpty())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> String.join(",", entry.getValue()),
                        (a, b) -> a,
                        LinkedHashMap::new
                ));
    }

    /**
     * 添加统计VO到列表（封装重复逻辑）
     */
    private void addStatisticsVO(List<MaterialStatisticsVO> statisticsList, String description, Map<String, ?> monthlyMap) {
        addStatisticsVO(statisticsList, description, monthlyMap, true);
    }

    private void addStatisticsVO(List<MaterialStatisticsVO> statisticsList, String description, Map<String, ?> monthlyMap, boolean calculateAmount) {
        MaterialStatisticsVO statistics = new MaterialStatisticsVO();
        statistics.setLineDescription(description);
        statistics.setMonthlyMap(monthlyMap);
        if (calculateAmount && !monthlyMap.isEmpty()) {
            // 计算总和（仅当值为BigDecimal时）
            BigDecimal amount = monthlyMap.values().stream()
                    .filter(BigDecimal.class::isInstance)
                    .map(v -> (BigDecimal) v)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setAmount(amount);
        }
        statisticsList.add(statistics);
    }


    /**
     * 比较两个对象表示的日期的月份是否相同（忽略日和时间部分）
     * 支持 String（多种格式）和 Date 类型
     *
     * @param obj1 第一个日期对象
     * @param obj2 第二个日期对象
     * @return 如果两个日期的年份和月份都相同，返回 true；否则返回 false
     */
    public static boolean compareMonths(String obj1, Date obj2) {
        try {
            // 处理第一个参数（支持多种格式）
            Date date1 = convertToDate(obj1);
            // 处理第二个参数
            Date date2 = obj2;

            // 创建仅包含年和月的格式化器
            SimpleDateFormat yearMonthFormat = new SimpleDateFormat("yyyy-MM");

            // 比较两个日期的年月部分
            return date1 != null && date2 != null
                    && yearMonthFormat.format(date1).equals(yearMonthFormat.format(date2));
        } catch (ParseException e) {
            log.error("日期解析错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 将对象转换为 Date 类型
     * 支持多种 String 格式和 Date 类型
     */
    private static Date convertToDate(Object obj) throws ParseException {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Date) {
            return (Date) obj;
        }

        if (obj instanceof String) {
            String dateStr = (String) obj;

            // 定义支持的日期格式列表（按优先级排序）
            List<SimpleDateFormat> formatters = Arrays.asList(
                    new SimpleDateFormat("yyyyMM"),     // 202009 格式
                    new SimpleDateFormat("yyyy-MM-dd"), // 标准格式
                    new SimpleDateFormat("yyyy-MM"),    // 年月格式
                    new SimpleDateFormat("yyyy/MM/dd"), // 斜杠分隔格式
                    new SimpleDateFormat("yyyy/MM")     // 年月斜杠格式
            );

            // 尝试按顺序解析日期
            for (SimpleDateFormat formatter : formatters) {
                try {
                    // 严格解析，避免模糊匹配
                    formatter.setLenient(false);
                    return formatter.parse(dateStr);
                } catch (ParseException e) {
                    // 忽略错误，尝试下一个格式
                }
            }

            throw new ParseException("不支持的日期格式: " + dateStr, 0);
        }

        throw new IllegalArgumentException("不支持的参数类型: " + obj.getClass().getName());
    }

    /**
     * 生成月份范围集合
     * 指定中心点年月，自动生成前后各6个月的范围
     *
     * @param appointYearMonth 指定年月，格式：yyyy-MM（如：2024-10）
     * @return LinkedHashMap，键为月份显示名称（如：24.7月预测量），值为原始格式年月（如：202407）
     * @throws IllegalArgumentException 当输入参数不合法时抛出异常
     */

    public static Map<String, String> generateMonthRange(String appointYearMonth) {
        Map<String, String> resultMap = new LinkedHashMap<>();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yy.M");

        LocalDate startDate;
        LocalDate endDate;

        if (appointYearMonth != null && !appointYearMonth.isEmpty()) {
            // 使用 YearMonth 解析年月字符串
            YearMonth appointedYearMonth = YearMonth.parse(appointYearMonth, inputFormatter);
            startDate = appointedYearMonth.minusMonths(5).atDay(1);
            endDate = appointedYearMonth.plusMonths(6).atDay(1);
        } else {
            throw new IllegalArgumentException("请提供有效的 appointYearMonth 或同时提供 startMonthDimension 和 endMonthDimension");
        }

        long monthsBetween = ChronoUnit.MONTHS.between(startDate, endDate);
        for (int i = 0; i <= monthsBetween; i++) {
            LocalDate currentDate = startDate.plusMonths(i);
            String displayKey = currentDate.format(outputFormatter) + "月预测量";
            String systemValue = currentDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
            resultMap.put(displayKey, systemValue);
        }

        return resultMap;
    }

    /**
     * 获取月份范围列表，支持三种模式：
     * 1. 传入起始和结束月份，生成对应范围
     * 2. 传入指定月份，生成该月份前后各6个月的范围（共12个月）
     * 3. 当forward12Months为true时，生成指定月份往前推12个月的范围（不含当月，共12个月）
     *
     * @param startYearMonthDimension 起始月份，格式：yyyy-MM（如：2025-07）
     * @param endYearMonthDimension   结束月份，格式：yyyy-MM（如：2026-01）
     * @param appointYearMonth        指定月份，格式：yyyy-MM（如：2025-07）
     * @param forward12Months         是否向前推12个月（不含当月）
     * @return 包含月份字符串的列表，格式为"yyyyMM"
     */
    public static List<String> getMonthRange(String startYearMonthDimension,
                                             String endYearMonthDimension,
                                             String appointYearMonth,
                                             boolean forward12Months) {
        List<String> result = new ArrayList<>();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMM");

        LocalDate startDate = null;
        LocalDate endDate = null;

        // 模式1：传入起始和结束月份
        if (startYearMonthDimension != null && !startYearMonthDimension.isEmpty() &&
                endYearMonthDimension != null && !endYearMonthDimension.isEmpty()) {
            YearMonth startYearMonth = YearMonth.parse(startYearMonthDimension, inputFormatter);
            YearMonth endYearMonth = YearMonth.parse(endYearMonthDimension, inputFormatter);
            startDate = startYearMonth.atDay(1);
            endDate = endYearMonth.atDay(1);
        }
        // 模式2：传入指定月份，生成该月份前后各6个月的范围
        else if (appointYearMonth != null && !appointYearMonth.isEmpty() && !forward12Months) {
            YearMonth appointMonth = YearMonth.parse(appointYearMonth, inputFormatter);
            // 前6个月（含当月）
            startDate = appointMonth.minusMonths(5).atDay(1);
            // 后6个月
            endDate = appointMonth.plusMonths(6).atDay(1);
        }
        // 模式3：向前推12个月（不含当月）
        else if (appointYearMonth != null && !appointYearMonth.isEmpty()) {
            YearMonth appointMonth = YearMonth.parse(appointYearMonth, inputFormatter);
            // 前12个月（不含当月）
            startDate = appointMonth.minusMonths(12).atDay(1);
            // 当月的前一个月
            endDate = appointMonth.minusMonths(1).atDay(1);
        } else {
            throw new IllegalArgumentException("参数配置不正确，请检查输入");
        }

        // 计算月份间隔
        long monthsBetween = ChronoUnit.MONTHS.between(startDate, endDate);

        // 生成月份列表
        for (int i = 0; i <= monthsBetween; i++) {
            LocalDate date = startDate.plusMonths(i);
            result.add(date.format(outputFormatter));
        }

        return result;
    }

    /**
     * 将指定年月向前推一年并转换格式
     * 输入格式：yyyy-MM（如：2025-07）
     * 输出格式：yyyyMM（如：202406）
     *
     * @param yearMonth 输入的年月字符串，格式为 yyyy-MM
     * @return 向前推一年后的年月字符串，格式为 yyyyMM
     * @throws IllegalArgumentException 如果输入格式不符合要求
     */
    public static String getPreviousYearMonth(String yearMonth) {
        if (yearMonth == null || !yearMonth.matches("\\d{4}-\\d{2}")) {
            throw new IllegalArgumentException("输入格式必须为 yyyy-MM");
        }

        try {
            // 解析输入的年月
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
            YearMonth ym = YearMonth.parse(yearMonth, inputFormatter);

            // 向前推一年（注意：这里是推12个月，而不是减1年，确保结果正确）
            YearMonth previousYearMonth = ym.minusMonths(12);

            // 转换为目标格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMM");
            return previousYearMonth.format(outputFormatter);
        } catch (DateTimeException e) {
            throw new IllegalArgumentException("无效的年月值", e);
        }
    }
}
