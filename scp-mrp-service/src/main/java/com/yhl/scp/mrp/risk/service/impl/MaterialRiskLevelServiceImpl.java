package com.yhl.scp.mrp.risk.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.risk.enums.RiskLevelEnum;
import com.yhl.scp.dfp.utils.DfpDateUtils;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mrp.enums.RiskLevelRuleEnum;
import com.yhl.scp.mrp.risk.convertor.MaterialRiskLevelConvertor;
import com.yhl.scp.mrp.risk.domain.entity.MaterialRiskLevelDO;
import com.yhl.scp.mrp.risk.domain.service.MaterialRiskLevelDomainService;
import com.yhl.scp.mrp.risk.dto.MaterialRiskLevelDTO;
import com.yhl.scp.mrp.risk.infrastructure.dao.MaterialRiskLevelDao;
import com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelPO;
import com.yhl.scp.mrp.risk.service.MaterialRiskLevelRuleService;
import com.yhl.scp.mrp.risk.service.MaterialRiskLevelService;
import com.yhl.scp.mrp.risk.vo.MaterialRiskLevelRuleVO;
import com.yhl.scp.mrp.risk.vo.MaterialRiskLevelVO;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialRiskLevelServiceImpl</code>
 * <p>
 * 材料风险等级应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23 14:39:10
 */
@Slf4j
@Service
public class MaterialRiskLevelServiceImpl extends AbstractService implements MaterialRiskLevelService {

    @Resource
    private MaterialRiskLevelDao materialRiskLevelDao;

    @Resource
    private MaterialRiskLevelDomainService materialRiskLevelDomainService;

    @Resource
    private MaterialRiskLevelRuleService materialRiskLevelRuleService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private NewMdsFeign mdsNewFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public BaseResponse<Void> doCreate(MaterialRiskLevelDTO materialRiskLevelDTO) {
        // 0.数据转换
        MaterialRiskLevelDO materialRiskLevelDO = MaterialRiskLevelConvertor.INSTANCE.dto2Do(materialRiskLevelDTO);
        MaterialRiskLevelPO materialRiskLevelPO = MaterialRiskLevelConvertor.INSTANCE.dto2Po(materialRiskLevelDTO);
        // 1.数据校验
        materialRiskLevelDomainService.validation(materialRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialRiskLevelPO);
        materialRiskLevelDao.insert(materialRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialRiskLevelDTO materialRiskLevelDTO) {
        // 0.数据转换
        MaterialRiskLevelDO materialRiskLevelDO = MaterialRiskLevelConvertor.INSTANCE.dto2Do(materialRiskLevelDTO);
        MaterialRiskLevelPO materialRiskLevelPO = MaterialRiskLevelConvertor.INSTANCE.dto2Po(materialRiskLevelDTO);
        // 1.数据校验
        materialRiskLevelDomainService.validation(materialRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialRiskLevelPO);
        materialRiskLevelDao.update(materialRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialRiskLevelDTO> list) {
        List<MaterialRiskLevelPO> newList = MaterialRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialRiskLevelDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialRiskLevelDTO> list) {
        List<MaterialRiskLevelPO> newList = MaterialRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialRiskLevelDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialRiskLevelDTO> list) {
        List<MaterialRiskLevelPO> newList = MaterialRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialRiskLevelDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialRiskLevelDao.deleteBatch(idList);
        }
        return materialRiskLevelDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialRiskLevelVO selectByPrimaryKey(String id) {
        MaterialRiskLevelPO po = materialRiskLevelDao.selectByPrimaryKey(id);
        return MaterialRiskLevelConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_material_risk_level")
    public List<MaterialRiskLevelVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_material_risk_level")
    public List<MaterialRiskLevelVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialRiskLevelVO> dataList = materialRiskLevelDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialRiskLevelServiceImpl target = SpringBeanUtils.getBean(MaterialRiskLevelServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialRiskLevelVO> selectByParams(Map<String, Object> params) {
        List<MaterialRiskLevelPO> list = materialRiskLevelDao.selectByParams(params);
        return MaterialRiskLevelConvertor.INSTANCE.po2Vos(list);
    }

    public List<MaterialRiskLevelVO> selectVOByParams(Map<String, Object> params) {
        return materialRiskLevelDao.selectVOByParams(params);
    }

    @Override
    public List<MaterialRiskLevelVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> updateRiskLevel(List<String> ids) {
        log.info("开始计算材料风险等级");

        List<MaterialRiskLevelVO> materialRiskLevelVOList = new ArrayList<>();
        // 如果传递ID参数为空，清空材料风险等级，进行全部更新
        if (CollectionUtils.isEmpty(ids)) {
            // 删除全部（清空表）
            materialRiskLevelDao.deleteAll();

            // 获取 原片（从物品表取RA.A）
            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder().dynamicColumnParam(Lists.newArrayList(
                            "product_code", "product_name", "product_classify", "stock_point_code", "product_color", "product_thickness"))
                    .queryParam(ImmutableMap.of("productClassify", "RA.A"))
                    .build();
            List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);

            // 物料采购数据赋值材料等级
            for (NewProductStockPointVO newProductStockPointVO : productStockPointVOList) {
                MaterialRiskLevelVO materialRiskLevelVO = new MaterialRiskLevelVO();
                materialRiskLevelVO.setMaterialCode(newProductStockPointVO.getProductCode());
                materialRiskLevelVO.setMaterialName(newProductStockPointVO.getProductName());
                materialRiskLevelVO.setStockPointCode(newProductStockPointVO.getStockPointCode());
                materialRiskLevelVO.setProductColor(newProductStockPointVO.getProductColor());
                materialRiskLevelVO.setProductThickness(newProductStockPointVO.getProductThickness());
                materialRiskLevelVO.setMaterialType(MaterialTypeEnum.ORIGINAL_FILM.getCode());
                materialRiskLevelVOList.add(materialRiskLevelVO);
            }

            // 获取 PVB 和 B类（从材料与供应商关系）
            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList = materialSupplierPurchaseService.selectVOByParams(new HashMap<>());
            for (MaterialSupplierPurchaseVO materialSupplierPurchaseVO : materialSupplierPurchaseVOList) {
                MaterialRiskLevelVO materialRiskLevelVO = new MaterialRiskLevelVO();
                BeanUtils.copyProperties(materialSupplierPurchaseVO, materialRiskLevelVO);
                materialRiskLevelVOList.add(materialRiskLevelVO);
            }
        } else {
            materialRiskLevelVOList = this.selectVOByParams(ImmutableMap.of("ids", ids));
        }

        // 分组区分材料类型（原片、PVB、B类）
        Map<String, List<MaterialRiskLevelVO>> materialTypeMap = materialRiskLevelVOList.stream()
                .filter(materialRiskLevelVO -> StringUtils.isNotEmpty(materialRiskLevelVO.getMaterialType()))
                .collect(Collectors.groupingBy(MaterialRiskLevelVO::getMaterialType));
        // 如果没有可用数据
        if (materialTypeMap.isEmpty()) {
            throw new BusinessException("没有找到满足材料风险等级规则的材料");
        }

        // 获取材料风险等级判定规则数据，并根据材料类型分组
        Map<String, List<MaterialRiskLevelRuleVO>> materialTypeRuleMap = materialRiskLevelRuleService.selectAll().stream()
                .filter(materialRiskLevelVO -> StringUtils.isNotEmpty(materialRiskLevelVO.getMaterialType()))
                .collect(Collectors.groupingBy(MaterialRiskLevelRuleVO::getMaterialType));

        Executor executor = Executors.newFixedThreadPool(3);
        // 创建异步任务
        CompletableFuture<Void> originalFilmTask = CompletableFuture.runAsync(() -> {
            log.info("开始处理原片风险等级");
            try {
                countOriginalFilm(materialTypeMap.get(MaterialTypeEnum.ORIGINAL_FILM.getCode()),
                        materialTypeRuleMap.get(MaterialTypeEnum.ORIGINAL_FILM.getCode()));
                log.info("结束处理原片风险等级");
            } catch (Exception e) {
                log.error("原片数据处理异常", e);
                throw new RuntimeException("原片数据处理异常", e);
            }
        }, executor);

        CompletableFuture<Void> pvbTask = CompletableFuture.runAsync(() -> {
            log.info("开始处理PVB风险等级");
            try {
                countPvb(materialTypeMap.get(MaterialTypeEnum.PVB.getCode()),
                        materialTypeRuleMap.get(MaterialTypeEnum.PVB.getCode()));
                log.info("结束处理PVB风险等级");
            } catch (Exception e) {
                log.error("PVB数据处理异常", e);
                throw new RuntimeException("PVB数据处理异常", e);
            }
        }, executor);

        CompletableFuture<Void> bTypeTask = CompletableFuture.runAsync(() -> {
            log.info("开始处理B类风险等级");
            try {
                countBType(materialTypeMap.get(MaterialTypeEnum.B_TYPE.getCode()),
                        materialTypeRuleMap.get(MaterialTypeEnum.B_TYPE.getCode()));
                log.info("结束处理B类风险等级");
            } catch (Exception e) {
                log.error("B类数据处理异常", e);
                throw new RuntimeException("B类数据处理异常", e);
            }
        }, executor);

        // 组合所有任务并等待完成
        try {
            CompletableFuture.allOf(originalFilmTask, pvbTask, bTypeTask).exceptionally(ex -> {
                Throwable cause = ex.getCause() != null ? ex.getCause() : ex;
                throw new BusinessException("材料风险等级计算过程中发生异常", cause);
            }).join();
        } catch (Exception e) {
            // 处理由exceptionally抛出的BusinessException
            throw (BusinessException) e.getCause();
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 处理计算原片数据的风险等级（根据厚度和颜色去物料基础数据里找，找到匹配的物料后修改风险等级并落库）
     *
     * @param materialRiskLevelVOS   材料风险等级
     * @param riskLevelRuleParamsVOS 材料风险等级规则
     */
    private void countOriginalFilm(List<MaterialRiskLevelVO> materialRiskLevelVOS,
                                   List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        if (CollectionUtils.isEmpty(materialRiskLevelVOS) || CollectionUtils.isEmpty(riskLevelRuleParamsVOS)) {
            return;
        }
        // 获取当前原片风险等级数据并根据库存点编码和材料编码分组
        List<MaterialRiskLevelVO> materialRiskLevelVOList =
                materialRiskLevelDao.selectByMaterialTypeList(MaterialTypeEnum.ORIGINAL_FILM.getCode());
        Map<String, MaterialRiskLevelVO> materialCodeMap = materialRiskLevelVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointCode()) &&
                        StringUtils.isNotEmpty(data.getMaterialCode()))
                .collect(Collectors.toMap(data -> String.join("&", data.getStockPointCode(), data.getMaterialCode()),
                        Function.identity(), (v1, v2) -> v1));

        // 根据厚度和颜色分组
        Map<String, MaterialRiskLevelRuleVO> thicknessColorMap = riskLevelRuleParamsVOS.stream()
                .filter(data -> null != data.getThickness() && StringUtils.isNotEmpty(data.getColor()))
                .collect(Collectors.toMap(data -> String.join("&", data.getThickness().toString(), data.getColor()),
                        Function.identity(), (v1, v2) -> v1));

        List<MaterialRiskLevelDTO> addList = new ArrayList<>();
        List<MaterialRiskLevelDTO> updateList = new ArrayList<>();

        // 处理计算逻辑
        for (MaterialRiskLevelVO vo : materialRiskLevelVOS) {
            // 如果存在通过物品厚度和颜色去关联规则
            String thicknessColorKey = String.join("&",
                    Optional.ofNullable(vo.getProductThickness())
                            .map(Object::toString)
                            .orElse(""),
                    Optional.ofNullable(vo.getProductColor())
                            .orElse(""));
            // 获取相应规则
            MaterialRiskLevelRuleVO materialRiskLevelRuleVO = thicknessColorMap.get(thicknessColorKey);

            if (Objects.nonNull(materialRiskLevelRuleVO)) {
                MaterialRiskLevelDTO dto = new MaterialRiskLevelDTO();
                BeanUtils.copyProperties(vo, dto);
                dto.setMaterialRiskLevel(materialRiskLevelRuleVO.getRiskLevel());
                dto.setMaterialRiskLevelRuleId(materialRiskLevelRuleVO.getId());

                // 根据库存编码和材料编码区分走添加还是修改
                MaterialRiskLevelVO materialRiskLevelVO =
                        materialCodeMap.get(String.join("&", dto.getStockPointCode(), dto.getMaterialCode()));
                if (Objects.nonNull(materialRiskLevelVO)) {
                    dto.setId(materialRiskLevelVO.getId());
                    updateList.add(dto);
                } else {
                    addList.add(dto);
                }
            }
        }

        // 落库添加材料风险等级
        if (CollectionUtils.isNotEmpty(addList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(addList, 100);
            partition.forEach(this::doCreateBatch);
            log.info("添加原片风险等级{}条", addList.size());
        }

        // 落库修改材料风险等级
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(updateList, 100);
            partition.forEach(this::doUpdateBatchSelective);
            log.info("修改原片风险等级{}条", updateList.size());
        }
    }

    /**
     * 处理计算PVB数据的风险等级（根据材料风险等级，材料编码截取材料风险等级规则，参数的前缀和后缀，满足前后缀匹配修改风险等级）
     *
     * @param materialRiskLevelVOS   材料风险等级
     * @param riskLevelRuleParamsVOS 材料风险等级规则
     */
    private void countPvb(List<MaterialRiskLevelVO> materialRiskLevelVOS,
                          List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        if (CollectionUtils.isEmpty(materialRiskLevelVOS) || CollectionUtils.isEmpty(riskLevelRuleParamsVOS)) {
            return;
        }
        // 获取当前PVB风险等级数据并根据库存点编码和材料编码分组
        List<MaterialRiskLevelVO> materialRiskLevelVOList =
                materialRiskLevelDao.selectByMaterialTypeList(MaterialTypeEnum.PVB.getCode());
        Map<String, MaterialRiskLevelVO> materialCodeMap = materialRiskLevelVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointCode()) &&
                        StringUtils.isNotEmpty(data.getMaterialCode()))
                .collect(Collectors.toMap(data -> String.join("&", data.getStockPointCode(), data.getMaterialCode()),
                        Function.identity(), (v1, v2) -> v1));

        List<MaterialRiskLevelDTO> addList = new ArrayList<>();
        List<MaterialRiskLevelDTO> updateList = new ArrayList<>();

        // 处理计算逻辑
        for (MaterialRiskLevelVO vo : materialRiskLevelVOS) {
            String materialCode = vo.getMaterialCode();

            MaterialRiskLevelRuleVO materialRiskLevelRuleVO = riskLevelRuleParamsVOS.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getRuleParams()))
                    .filter(data -> {
                        String[] split = data.getRuleParams().split("-");
                        return materialCode.contains(split[0]) && materialCode.contains(split[1]);
                    })
                    .findFirst()
                    .orElse(null);

            if (Objects.nonNull(materialRiskLevelRuleVO)) {
                MaterialRiskLevelDTO dto = new MaterialRiskLevelDTO();
                BeanUtils.copyProperties(vo, dto);
                dto.setMaterialRiskLevel(materialRiskLevelRuleVO.getRiskLevel());
                dto.setMaterialRiskLevelRuleId(materialRiskLevelRuleVO.getId());

                // 根据库存编码和材料编码区分走添加还是修改
                MaterialRiskLevelVO materialRiskLevelVO =
                        materialCodeMap.get(String.join("&", dto.getStockPointCode(), dto.getMaterialCode()));
                if (Objects.nonNull(materialRiskLevelVO)) {
                    dto.setId(materialRiskLevelVO.getId());
                    updateList.add(dto);
                } else {
                    addList.add(dto);
                }
            }
        }

        // 落库添加材料风险等级
        if (CollectionUtils.isNotEmpty(addList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(addList, 1000);
            partition.forEach(this::doCreateBatch);
            log.info("添加PVB风险等级{}条", addList.size());
        }

        // 落库修改材料风险等级
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(updateList, 1000);
            partition.forEach(this::doUpdateBatchSelective);
            log.info("修改PVB风险等级{}条", updateList.size());
        }
    }


    /**
     * 处理计算B类数据的风险等级（一共五种分别是：是否专用、是否超比例、是否高价值、是否超过保质期、是满足预测需求）
     *
     * @param materialRiskLevelVOS   材料风险等级
     * @param riskLevelRuleParamsVOS 材料风险等级规则
     */
    private void countBType(List<MaterialRiskLevelVO> materialRiskLevelVOS,
                            List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        if (CollectionUtils.isEmpty(materialRiskLevelVOS) || CollectionUtils.isEmpty(riskLevelRuleParamsVOS)) {
            return;
        }

        // 获取mds场景
//        String mdsScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
//                TenantCodeEnum.FYQB.getCode()).getData();
//
//        // 获取mps场景
//        String mpsScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(),
//                TenantCodeEnum.FYQB.getCode()).getData();
//
//        // 获取dfp场景
//        String dfpScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(),
//                TenantCodeEnum.FYQB.getCode()).getData();
        String scenario = SystemHolder.getScenario();

        List<String> productCodes = materialRiskLevelVOS.stream()
                .map(MaterialRiskLevelVO::getMaterialCode)
                .distinct()
                .collect(Collectors.toList());

        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(
                scenario,
                ImmutableMap.of("ioProductCodes", productCodes));

        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMap = productBomVOList.stream()
                .collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));

        // 获取对应的BOM版本数据
        Map<String, ProductBomVersionVO> productBomVersionMap = newMdsFeign.selectProductBomVersionVOByParams(
                        scenario,
                        ImmutableMap.of("ids", productBomVOMap.keySet())
                ).stream()
                .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));

        // 物料对应成品map
        Map<String, List<NewProductStockPointVO>> finishedProductMap = new HashMap<>();
        productBomVOMap.forEach((key, productBomList) -> {
            // 获取产品BOM版本
            ProductBomVersionVO productBomVersion = productBomVersionMap.get(key);
            if (productBomVersion == null) {
                return;
            }

            // 跳过非成品类型
            if (!ProductTypeEnum.FG.getCode().equals(productBomVersion.getProductType())) {
                return;
            }

            // 将成品信息关联到所有相关物料
            for (ProductBomVO bomItem : productBomList) {
                finishedProductMap.computeIfAbsent(
                        bomItem.getProductCode(),
                        k -> new ArrayList<>()
                ).add(NewProductStockPointVO.builder().productCode(productBomVersion.getProductCode()).build());
            }
        });

        // 收集成品BOM
        List<String> bomProductList = finishedProductMap.values().stream()
                .flatMap(Collection::stream).map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());

        // 获取当前风险等级数据并根据库存点编码和材料编码分组
        List<MaterialRiskLevelVO> materialRiskLevelVOList = this.selectByParams(ImmutableMap.of("materialCodes", productCodes));
        Map<String, MaterialRiskLevelVO> materialCodeMap = materialRiskLevelVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointCode()) &&
                        StringUtils.isNotEmpty(data.getMaterialCode()))
                .collect(Collectors.toMap(data -> String.join("&", data.getStockPointCode(), data.getMaterialCode()),
                        Function.identity(), (v1, v2) -> v1));

        // 获取高价值物料并根据库存点代码+物料编码分组
        Map<String, MpsHighValueMaterialsVO> highValueMap = mpsFeign.queryByParams(scenario, new HashMap<>()).stream()
                .filter(data -> StringUtils.isNotEmpty(data.getStockPointCode()) &&
                        StringUtils.isNotEmpty(data.getProductCode()))
                .filter(data -> StringUtils.isNotEmpty(data.getHighValue()) &&
                        data.getHighValue().equals("Y"))
                .collect(Collectors.toMap(data -> String.join("&", data.getStockPointCode(), data.getProductCode()),
                        Function.identity(), (v1, v2) -> v1));

        // 获取成品所需零件风险等级
        List<PartRiskLevelVO> partRiskLevelVOList =
                dfpFeign.selectMaterialRiskLeveByProductCodeList(scenario, bomProductList);
        // 收集主机厂编码
        List<String> oemCodes = partRiskLevelVOList.stream().map(PartRiskLevelVO::getOemCode).distinct().collect(Collectors.toList());

        // 获取主机厂风险等级
        List<OemRiskLevelVO> oemCodeList = dfpFeign.selectOemRiskLevelByParams(scenario, ImmutableMap.of("oemCodeList", oemCodes));
        // 根据主机厂编码分组（取评估时间最大的）
        Map<String, OemRiskLevelVO> oemRiskLevelVOMap = oemCodeList.stream()
                .collect(Collectors.toMap(
                        OemRiskLevelVO::getOemCode,
                        Function.identity(),
                        (v1, v2) -> v1.getEstimateTime().compareTo(v2.getEstimateTime()) > 0 ? v1 : v2
                ));

        // 获取当前月
        String date = DateUtils.dateToString(new Date(), "yyyy-MM");
        // 获取后三个月
        List<String> afterMonthsList = getMonths(date, 3, true);

        // 获取一致性需求预测预测销量（3个月超比例用）
        List<ConsistenceDemandForecastDataDetailVO> ratioForecastList =
                dfpFeign.selectDemandForecastByDataDetailForecastQuantitySumByOemCodesAndMonths(scenario,
                        ImmutableMap.of("oemCodes", oemCodes, "months", afterMonthsList));
        Map<String, BigDecimal> ratioForecastMap = ratioForecastList.stream()
                .collect(Collectors.toMap(ConsistenceDemandForecastDataDetailVO::getOemCode,
                        ConsistenceDemandForecastDataDetailVO::getForecastQuantity));

        //获取对应材料规则
        MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                        data.getRuleDescription().contains(RiskLevelRuleEnum.FORECAST_VALUE.getCode()))
                .findFirst()
                .orElse(null);
        //天数转换月数（是否满足预测需求用）
        int monthNumber = convertDaysToMonths(riskLevelRuleParamsVO.getRuleParams());
        List<String> afterMonthsList02 = getMonths(date, monthNumber, true);
        List<ConsistenceDemandForecastDataDetailVO> forecastList =
                dfpFeign.selectDemandForecastByDataDetailForecastQuantitySumByOemCodesAndMonths(scenario,
                        ImmutableMap.of("oemCodes", oemCodes, "months", afterMonthsList02));
        Map<String, BigDecimal> forecastMap = forecastList.stream()
                .collect(Collectors.toMap(ConsistenceDemandForecastDataDetailVO::getOemCode,
                        ConsistenceDemandForecastDataDetailVO::getForecastQuantity));

        // 查询仓库收发仓数据- 发货数量,前3个月的
        Date nowDate = new Date();
        Date startDate = DateUtils.moveMonth(nowDate, -3);
        Date endDate = DateUtils.moveMonth(nowDate, 0);
        startDate = DfpDateUtils.getFirstDayOfMonth(startDate);
        endDate = getLastMomentOfMonth(endDate);

        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList =
                dfpFeign.selectWarehouseReleaseRecordSumQtyByDate(scenario, bomProductList, DateUtils.dateToString(startDate), DateUtils.dateToString(endDate));
        log.info("仓库收发仓数量{}", warehouseReleaseRecordVOList.size());

        List<MaterialRiskLevelDTO> addList = new ArrayList<>();
        List<MaterialRiskLevelDTO> updateList = new ArrayList<>();

        log.info("结束整合B类风险等级计算所需参数");

        // 计算逻辑处理
        for (MaterialRiskLevelVO vo : materialRiskLevelVOS) {
            String riskLevel = null;
            String keyCode = String.join("&", vo.getStockPointCode(), vo.getMaterialCode());

            // 判断是否专用
            MaterialRiskLevelRuleVO materialRiskLevelRuleVO = getSpecificRiskLevel(vo, riskLevelRuleParamsVOS);
            if (null != materialRiskLevelRuleVO) {
                riskLevel = materialRiskLevelRuleVO.getRiskLevel();
            }

            if (null == riskLevel) {
                // 判断是否超比例
                materialRiskLevelRuleVO = getRatioRiskLevel(vo.getMaterialCode(), finishedProductMap, riskLevelRuleParamsVOS,
                        partRiskLevelVOList, ratioForecastMap,
                        warehouseReleaseRecordVOList, oemRiskLevelVOMap);
                if (null != materialRiskLevelRuleVO) {
                    riskLevel = materialRiskLevelRuleVO.getRiskLevel();
                }
            }

            // 判断是否为高价值
            if (null == riskLevel) {
                materialRiskLevelRuleVO = getHighValueRiskLevel(highValueMap, keyCode, riskLevelRuleParamsVOS);
                if (null != materialRiskLevelRuleVO) {
                    riskLevel = materialRiskLevelRuleVO.getRiskLevel();
                }
            }

            // 判断是否超过保质期
            if (null == riskLevel) {
                materialRiskLevelRuleVO = getProneFailureRiskLevel(vo.getExpireDate(), riskLevelRuleParamsVOS);
                if (null != materialRiskLevelRuleVO) {
                    riskLevel = materialRiskLevelRuleVO.getRiskLevel();
                }
            }

            // 判断是否达到提前期
            if (null == riskLevel) {
                materialRiskLevelRuleVO = getLeadTimeRiskLevel(vo, riskLevelRuleParamsVOS);
                if (null != materialRiskLevelRuleVO) {
                    riskLevel = materialRiskLevelRuleVO.getRiskLevel();
                }
            }

            // 判断是否满足预测需求
            if (null == riskLevel) {
                materialRiskLevelRuleVO = getForecastValueRiskLevel(vo, finishedProductMap, riskLevelRuleParamsVOS,
                        partRiskLevelVOList, forecastMap, warehouseReleaseRecordVOList, oemRiskLevelVOMap);
                if (null != materialRiskLevelRuleVO) {
                    riskLevel = materialRiskLevelRuleVO.getRiskLevel();
                }
            }

            if (null == riskLevel) {
                riskLevel = RiskLevelEnum.LOW.getCode();
            }
            MaterialRiskLevelDTO dto = new MaterialRiskLevelDTO();
            BeanUtils.copyProperties(vo, dto);
            dto.setMaterialRiskLevel(riskLevel);
            dto.setMaterialRiskLevelRuleId(null != materialRiskLevelRuleVO ? materialRiskLevelRuleVO.getId() : null);

            // 根据库存编码和材料编码区分走添加还是修改
            MaterialRiskLevelVO materialRiskLevelVO =
                    materialCodeMap.get(String.join("&", dto.getStockPointCode(), dto.getMaterialCode()));

            if (Objects.nonNull(materialRiskLevelVO)) {
                dto.setId(materialRiskLevelVO.getId());
                updateList.add(dto);
            } else {
                addList.add(dto);
            }
        }

        // 落库添加材料风险等级
        if (CollectionUtils.isNotEmpty(addList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(addList, 1000);
            partition.forEach(this::doCreateBatch);
            log.info("添加B类风险等级{}条", addList.size());
        }

        // 落库修改材料风险等级
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<MaterialRiskLevelDTO>> partition = Lists.partition(updateList, 1000);
            partition.forEach(this::doUpdateBatchSelective);
            log.info("修改B类风险等级{}条", updateList.size());
        }
    }

    /**
     * 获取材料（专用）风险等级（判断相应材料与供应商关系的是否专用字段，如果是就为专用）
     *
     * @param materialRiskLevelVO    风险等级
     * @param riskLevelRuleParamsVOS 材料风险等级规则
     * @return 材料风险等级规则
     */
    private MaterialRiskLevelRuleVO getSpecificRiskLevel(MaterialRiskLevelVO materialRiskLevelVO,
                                                         List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        if (Objects.nonNull(materialRiskLevelVO)
                && StringUtils.isNotEmpty(materialRiskLevelVO.getSpecific())
                && materialRiskLevelVO.getSpecific().equals(YesOrNoEnum.YES.getCode())) {
            return riskLevelRuleParamsVOS.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                            data.getRuleDescription().contains(RiskLevelRuleEnum.IS_SPECIFIC.getCode()))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    /**
     * 获取材料（比例）风险等级（预测耗量 / 总预测耗量大于规则参数，则获取相应风险等级）
     *
     * @param materialCode                 物料编码
     * @param finishedProductMap           物料对应成品
     * @param riskLevelRuleParamsVOS       材料风险等级规则
     * @param partRiskLevelVOList          零件风险等级
     * @param forecastMap                  一致性需求预测预测销量
     * @param warehouseReleaseRecordVOList 仓库发货
     * @param oemRiskLevelVOMap            主机厂风险等级
     * @return 材料（比例）风险等级
     */
    private MaterialRiskLevelRuleVO getRatioRiskLevel(
            String materialCode,
            Map<String, List<NewProductStockPointVO>> finishedProductMap,
            List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS,
            List<PartRiskLevelVO> partRiskLevelVOList,
            Map<String, BigDecimal> forecastMap,
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList,
            Map<String, OemRiskLevelVO> oemRiskLevelVOMap) {

        MaterialRiskLevelRuleVO materialRiskLevelRuleVO = null;

        // 获取相应规则参数
        MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                .filter(data -> data.getRuleDescription().contains(RiskLevelRuleEnum.RATIO.getCode()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(riskLevelRuleParamsVO)) {
            // 根据所属工艺路径步骤输入物品数据计算出消耗量比例
            Map<String, BigDecimal> numberMap = selectForecastNumber(materialCode, finishedProductMap,
                    partRiskLevelVOList, forecastMap, warehouseReleaseRecordVOList, oemRiskLevelVOMap);

            if (numberMap.isEmpty()) return null;

            // 获取高风险材料预测数量
            BigDecimal highNumber = numberMap.get("highNumber");
            // 获取总预测销量
            BigDecimal sumNumber = numberMap.get("sumNumber");
            String ruleParams = riskLevelRuleParamsVO.getRuleParams();
            BigDecimal ruleParamsValue = new BigDecimal(ruleParams);

            // 除数或被除数为0直接返回（防止数据问题产生的报错）
            if (highNumber.equals(BigDecimal.ZERO) || sumNumber.equals(BigDecimal.ZERO)) {
                return null;
            }

            // 进行比较
            if (sumNumber.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = highNumber.divide(sumNumber, RoundingMode.HALF_UP);
                if (ratio.compareTo(ruleParamsValue) > 0) {
                    materialRiskLevelRuleVO = riskLevelRuleParamsVO;
                }
            }
        }
        return materialRiskLevelRuleVO;
    }

    /**
     * 获取材料（高价值）风险等级 （通过库存点编码+物料编码去高价值基础表获取相应数据，找到则获取相应风险等级）
     *
     * @param highValueMap           高价值物料
     * @param keyCode                库存点编码+物料编码
     * @param riskLevelRuleParamsVOS 材料风险等级规则
     * @return 材料（高价值）风险等级
     */
    private MaterialRiskLevelRuleVO getHighValueRiskLevel(Map<String, MpsHighValueMaterialsVO> highValueMap,
                                                          String keyCode,
                                                          List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        MaterialRiskLevelRuleVO materialRiskLevelRuleVO = null;
        if (Objects.nonNull(highValueMap.get(keyCode))) {
            MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                            data.getRuleDescription().contains(RiskLevelRuleEnum.HIGH_VALUE.getCode()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(riskLevelRuleParamsVO)) {
                materialRiskLevelRuleVO = riskLevelRuleParamsVO;
            }
        }
        return materialRiskLevelRuleVO;
    }

    /**
     * 获取材料（保质期）风险等级（根据对应的物料基础数据获取相应保质期，没过期则获取相应风险等级）
     *
     * @param expireDate             保质期
     * @param riskLevelRuleParamsVOS 材料风险等级
     * @return 材料（保质期）风险等级
     */
    private MaterialRiskLevelRuleVO getProneFailureRiskLevel(Integer expireDate,
                                                             List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        MaterialRiskLevelRuleVO materialRiskLevelRuleVO = null;
        if (null != expireDate) {
            //获取保质期
            MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                            data.getRuleDescription().contains(RiskLevelRuleEnum.PRONE_FAILURE.getCode()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(riskLevelRuleParamsVO) &&
                    expireDate <= Integer.parseInt(riskLevelRuleParamsVO.getRuleParams())) {
                materialRiskLevelRuleVO = riskLevelRuleParamsVO;
            }
        }
        return materialRiskLevelRuleVO;
    }

    /**
     * 获取材料（提前期）风险等级（判断相应材料采购数据的提前期字段，大于等于判定规则相应规则参数则获取相应风险等级）
     *
     * @param materialRiskLevelVO    风险等级
     * @param riskLevelRuleParamsVOS 材料风险等级
     * @return 材料（保质期）风险等级
     */
    private MaterialRiskLevelRuleVO getLeadTimeRiskLevel(MaterialRiskLevelVO materialRiskLevelVO,
                                                         List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS) {
        MaterialRiskLevelRuleVO materialRiskLevelRuleVO = null;
        if (Objects.nonNull(materialRiskLevelVO) && null != materialRiskLevelVO.getOrderPlacementLeadTimeDay()) {
            MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                            data.getRuleDescription().contains(RiskLevelRuleEnum.LEAD_TIME.getCode()) &&
                            StringUtils.isNotEmpty(data.getRuleParams()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(riskLevelRuleParamsVO) && materialRiskLevelVO.getOrderPlacementLeadTimeDay().compareTo(new BigDecimal(riskLevelRuleParamsVO.getRuleParams())) >= 0) {
                materialRiskLevelRuleVO = riskLevelRuleParamsVO;
            }
        }
        return materialRiskLevelRuleVO;
    }

    /**
     * 获取材料（预测量）风险等级 （起订量或最小包装量/6个月预测量>1，则获取相应风险等级）
     *
     * @param materialRiskLevelVO          风险等级
     * @param finishedProductMap           物料对应成品
     * @param riskLevelRuleParamsVOS       材料风险等级
     * @param partRiskLevelVOList          零件风险等级
     * @param forecastMap                  一致性需求预测预测销量
     * @param warehouseReleaseRecordVOList 仓库发货记录
     * @param oemRiskLevelVOMap            主机厂风险等级
     * @return 材料（预测量）风险等级
     */
    private MaterialRiskLevelRuleVO getForecastValueRiskLevel(
            MaterialRiskLevelVO materialRiskLevelVO,
            Map<String, List<NewProductStockPointVO>> finishedProductMap,
            List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS,
            List<PartRiskLevelVO> partRiskLevelVOList,
            Map<String, BigDecimal> forecastMap,
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList,
            Map<String, OemRiskLevelVO> oemRiskLevelVOMap) {

        MaterialRiskLevelRuleVO materialRiskLevelRuleVO = null;

        //获取对应材料规则
        MaterialRiskLevelRuleVO riskLevelRuleParamsVO = riskLevelRuleParamsVOS.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getRuleDescription()) &&
                        data.getRuleDescription().contains(RiskLevelRuleEnum.FORECAST_VALUE.getCode()))
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(riskLevelRuleParamsVO) && StringUtils.isNotEmpty(riskLevelRuleParamsVO.getRuleParams())) {

            Map<String, BigDecimal> numberMap = selectForecastNumber(materialRiskLevelVO.getMaterialCode(), finishedProductMap,
                    partRiskLevelVOList,
                    forecastMap,
                    warehouseReleaseRecordVOList, oemRiskLevelVOMap);

            if (numberMap.isEmpty()) return null;

            BigDecimal highNumber = numberMap.get("highNumber");

            if (highNumber != null && highNumber.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal minOrderQty = materialRiskLevelVO.getMinOrderQty();
                BigDecimal purchaseLot = materialRiskLevelVO.getPurchaseLot();

                if (minOrderQty == null || purchaseLot == null) {
                    return null;
                }

                BigDecimal minOrderQtyDivHighNumber = minOrderQty.divide(highNumber, RoundingMode.HALF_UP);
                BigDecimal purchaseLotDivHighNumber = purchaseLot.divide(highNumber, RoundingMode.HALF_UP);

                //优化条件判断，避免嵌套
                if (minOrderQtyDivHighNumber.compareTo(BigDecimal.ONE) > 0 ||
                        purchaseLotDivHighNumber.compareTo(BigDecimal.ONE) > 0) {
                    materialRiskLevelRuleVO = riskLevelRuleParamsVO;
                }
            }
        }

        return materialRiskLevelRuleVO;
    }


    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_RISK_LEVEL.getCode();
    }

    @Override
    public List<MaterialRiskLevelVO> invocation(List<MaterialRiskLevelVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    /**
     * 计算材料预测耗量
     * 根据物料编码去物品BOM，找出对应的成品
     * 根据成品编码，找出对应零件风险等级
     * 根据零件风险等级的主机厂编码，找出主机厂风险等级（用于区分高风险预测销量）
     * 根据零件风险等级的主机厂编码，找出当前月后三个月的一致性预测
     * 根据成品编码，找出当前月后三个月的仓库收发货记录
     * 预测销量 = 取一致性预测的预测销量
     * 历史销量 = 仓库收发货的历史销量
     * 根据成品编码分组汇总（最后汇总预测销量（高风险）  和 历史销量）
     *
     * @param materialCode                 物料编码
     * @param finishedProductMap           物料对应成品
     * @param partRiskLevelVOList          零件风险等级
     * @param forecastMap                  一致性需求预测预测销量
     * @param warehouseReleaseRecordVOList 仓库发货记录
     * @param oemRiskLevelVOMap            主机厂风险等级
     * @return 预测耗量
     */
    private Map<String, BigDecimal> selectForecastNumber(String materialCode,
                                                         Map<String, List<NewProductStockPointVO>> finishedProductMap,
                                                         List<PartRiskLevelVO> partRiskLevelVOList,
                                                         Map<String, BigDecimal> forecastMap,
                                                         List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOList,
                                                         Map<String, OemRiskLevelVO> oemRiskLevelVOMap) {

        List<NewProductStockPointVO> finishProductCodeList = finishedProductMap.get(materialCode);

        if (CollectionUtils.isEmpty(finishProductCodeList)) return new HashMap<>();

        // 仓库收发货数据根据本厂编码（物料编码）分组
        Map<String, WarehouseReleaseRecordVO> itemCodeMap = warehouseReleaseRecordVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getItemCode()))
                .collect(Collectors.toMap(WarehouseReleaseRecordVO::getItemCode,
                        Function.identity(), (v1, v2) -> {
                            v1.setSumQty(v1.getSumQty().add(v2.getSumQty()));
                            return v1;
                        }));

        // 零件风险等级根据物料编码分组
        Map<String, List<PartRiskLevelVO>> partRiskProductCodeMap = partRiskLevelVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getProductCode()))
                .collect(Collectors.groupingBy(PartRiskLevelVO::getProductCode));

        // 根据物料车间编码去一致性需求预测获取预测销量
        for (NewProductStockPointVO newProductStockPointVO : finishProductCodeList) {
            // 获取相应零件等级数据
            List<PartRiskLevelVO> partRiskLevelVOs =
                    partRiskProductCodeMap.get(newProductStockPointVO.getProductCode());
            if (CollectionUtils.isNotEmpty(partRiskLevelVOs)) {
                String oemCode = partRiskLevelVOs.stream()
                        .map(PartRiskLevelVO::getOemCode)
                        .filter(forecastMap::containsKey).findFirst().orElse(null);
                if (StringUtils.isNotEmpty(oemCode)){
                    newProductStockPointVO.setForecastNumber(forecastMap.get(oemCode));
                }

                // 获取仓库收发货数据赋值物品历史数量
                WarehouseReleaseRecordVO warehouseReleaseRecordVO =
                        itemCodeMap.get(newProductStockPointVO.getProductCode());
                if (Objects.nonNull(warehouseReleaseRecordVO) && null != warehouseReleaseRecordVO.getSumQty()) {
                    newProductStockPointVO.setHistoryNumber(warehouseReleaseRecordVO.getSumQty());
                }
            }
        }

        // 预测耗量
        BigDecimal highNumber = BigDecimal.ZERO;
        // 总预测耗量
        BigDecimal sumNumber = BigDecimal.ZERO;
        // 收集完成品物料后，根据零件风险等级，区分哪些是高价值物料
        for (NewProductStockPointVO vo : finishProductCodeList) {
            List<PartRiskLevelVO> partRiskLevelVOs =
                    partRiskProductCodeMap.get(vo.getProductCode());

            if (CollectionUtils.isNotEmpty(partRiskLevelVOs)) {
                // 获取预测数量
                BigDecimal forecastNumber = (vo.getForecastNumber() != null)
                        ? vo.getForecastNumber()
                        : BigDecimal.ZERO;
                // 获取历史数量
                BigDecimal historyNumber = (vo.getHistoryNumber() != null)
                        ? vo.getHistoryNumber()
                        : BigDecimal.ZERO;

                // 获取主机厂风险等级
                String oemCode = partRiskLevelVOs.stream()
                        .map(PartRiskLevelVO::getOemCode)
                        .filter(forecastMap::containsKey).findFirst().orElse(null);
                if (StringUtils.isNotEmpty(oemCode)){
                    OemRiskLevelVO oemRiskLevelVO = oemRiskLevelVOMap.get(oemCode);
                    if (Objects.nonNull(oemRiskLevelVO) && oemRiskLevelVO.getRiskLevel().equals(RiskLevelEnum.HIGH.getCode())) {
                        highNumber = historyNumber.add(forecastNumber);
                    }
                }
                sumNumber = sumNumber.add(forecastNumber.add(historyNumber));
            }
        }

        HashMap<String, BigDecimal> dataMap = new HashMap<>();
        dataMap.put("highNumber", highNumber);
        dataMap.put("sumNumber", sumNumber);
        return dataMap;
    }


    /**
     * 根据年月获取前或后年月值（类型均为yyyy-MM）false前 true后
     *
     * @param date     年月（yyyy-MM）
     * @param months   几个月
     * @param isFuture 前/后
     * @return 年月值（yyyy-MM）
     */
    public static List<String> getMonths(String date, int months, boolean isFuture) {
        List<String> monthList = new ArrayList<>();
        try {
            // 设置日期格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date inputDate = sdf.parse(date);

            // 使用 Calendar 来操作日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(inputDate);

            // 根据 isFuture 参数决定是加还是减
            for (int i = 0; i < Math.abs(months); i++) {
                if (isFuture) {
                    // 后三个月
                    calendar.add(Calendar.MONTH, 1);
                } else {
                    // 前三个月
                    calendar.add(Calendar.MONTH, -1);
                }
                // 添加格式化后的日期到列表
                monthList.add(sdf.format(calendar.getTime()));
            }
        } catch (Exception e) {
            log.error("获取年月日失败：" + e);
        }
        return monthList;
    }

    /**
     * 根据天数换算成月数（超过一个天的部分按两个月算）
     *
     * @param daysStr 天数
     * @return 月数
     */
    public static int convertDaysToMonths(String daysStr) {
        try {
            int days = Integer.parseInt(daysStr);
            if (days <= 0) {
                return 0;
            } else if (days == 1) {
                return 1;
            } else {
                return (days / 30) * 2 + (days % 30 > 0 ? 2 : 0);
            }
        } catch (NumberFormatException e) {
            // 处理字符串无法转换为整数的情况
            log.error("Invalid input: " + daysStr);
            return 0;
        }
    }

    /**
     * 递归找当前工艺路径为成品的物品id
     *
     * @param routingStepInputVO           工艺路径输入物品
     * @param routingIdMap                 工艺路径Map
     * @param routingStepInputProductIdMap 工艺路径输入物品所属物品Map
     * @return 工艺路径为产品的物品id
     */
    public String findFinishProductStockPointId(RoutingStepInputVO routingStepInputVO,
                                                Map<String, RoutingVO> routingIdMap,
                                                Map<String, List<RoutingStepInputVO>> routingStepInputProductIdMap) {
        //获取相应工艺路径
        RoutingVO routingVO = routingIdMap.get(routingStepInputVO.getRoutingId());

        //根据工艺路径的物品id，查看工艺路径输入物品里是否还有整个物品
        List<RoutingStepInputVO> routingStepInputVOS =
                routingStepInputProductIdMap.get(routingVO.getProductId());

        //如果工艺输入物品物品里没有值说明已经找到最上层产品，返回当前的物品id
        if (CollectionUtils.isEmpty(routingStepInputVOS)) {
            return routingVO.getProductId();
        }

        //随便获取一个，因为他们的RoutingId都是一样的
        RoutingStepInputVO nextRoutingStepInputVO = routingStepInputVOS.get(0);

        return findFinishProductStockPointId(nextRoutingStepInputVO, routingIdMap, routingStepInputProductIdMap);
    }

    /**
     * 获取date月的最后一天
     *
     * @param date
     * @return
     */
    private Date getLastMomentOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置为本月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        // 将时间部分设置为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date lastMomentOfMonth = calendar.getTime();
        return lastMomentOfMonth;
    }

}
