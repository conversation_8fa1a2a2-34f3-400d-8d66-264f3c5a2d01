package com.yhl.scp.mrp.substitutionRelationship.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>GlassSubstitutionRelationshipPO</code>
 * <p>
 * 原片替代关系表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:39:54
 */
public class GlassSubstitutionRelationshipPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -24366096233397519L;

    /**
     * 工厂
     */
    private String stockPointCode;
    /**
     * 原物料编码
     */
    private String rawProductCode;
    /**
     * 原物料名称
     */
    private String rawProductName;
    /**
     * 生产BOM
     */
    private String productionSubstituteProductCode;
    /**
     * 生产BOM名称
     */
    private String productionSubstituteProductName;
    /**
     * 生产单耗
     */
    private BigDecimal productionInputFactor;
    /**
     * 替代料编码
     */
    private String substituteProductCode;
    /**
     * 替代料名称
     */
    private String substituteProductName;
    /**
     * 替代单耗
     */
    private BigDecimal substituteInputFactor;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 本厂名称
     */
    private String productName;
    /**
     * 颜色厚度
     */
    private String colorThickness;
    /**
     * 毛坯规格
     */
    private String blankSpec;
    /**
     * 需求计算规则
     */
    private String rule;
    /**
     * 产品尺寸
     */
    private String productSize;
    /**
     * 淋子方向
     */
    private String linziDirection;
    /**
     * 毛坯单耗
     */
    private BigDecimal blankInputFactor;
    /**
     * 原片对毛坯单耗
     */
    private BigDecimal glassInputFactor;
    /**
     * 切裁率
     */
    private BigDecimal cuttingRate;
    /**
     * 生产切裁率
     */
    private BigDecimal productionCuttingRate;
    /**
     * ERP-BOM切裁率
     */
    private BigDecimal rawProductCuttingRate;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 推荐替代
     */
    private String recommendSubstitute;

    /**
     * ERP-BOM库存禁用
     */
    private String rawProductInventoryDisabled;

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getRawProductCode() {
        return rawProductCode;
    }

    public void setRawProductCode(String rawProductCode) {
        this.rawProductCode = rawProductCode;
    }

    public String getRawProductName() {
        return rawProductName;
    }

    public void setRawProductName(String rawProductName) {
        this.rawProductName = rawProductName;
    }

    public String getSubstituteProductCode() {
        return substituteProductCode;
    }

    public void setSubstituteProductCode(String substituteProductCode) {
        this.substituteProductCode = substituteProductCode;
    }

    public String getSubstituteProductName() {
        return substituteProductName;
    }

    public void setSubstituteProductName(String substituteProductName) {
        this.substituteProductName = substituteProductName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getColorThickness() {
        return colorThickness;
    }

    public void setColorThickness(String colorThickness) {
        this.colorThickness = colorThickness;
    }

    public String getBlankSpec() {
        return blankSpec;
    }

    public void setBlankSpec(String blankSpec) {
        this.blankSpec = blankSpec;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize;
    }

    public String getLinziDirection() {
        return linziDirection;
    }

    public void setLinziDirection(String linziDirection) {
        this.linziDirection = linziDirection;
    }

    public BigDecimal getBlankInputFactor() {
        return blankInputFactor;
    }

    public void setBlankInputFactor(BigDecimal blankInputFactor) {
        this.blankInputFactor = blankInputFactor;
    }

    public BigDecimal getGlassInputFactor() {
        return glassInputFactor;
    }

    public void setGlassInputFactor(BigDecimal glassInputFactor) {
        this.glassInputFactor = glassInputFactor;
    }

    public BigDecimal getCuttingRate() {
        return cuttingRate;
    }

    public void setCuttingRate(BigDecimal cuttingRate) {
        this.cuttingRate = cuttingRate;
    }

    public BigDecimal getProductionCuttingRate() {
        return productionCuttingRate;
    }

    public void setProductionCuttingRate(BigDecimal productionCuttingRate) {
        this.productionCuttingRate = productionCuttingRate;
    }

    public BigDecimal getRawProductCuttingRate() {
        return rawProductCuttingRate;
    }

    public void setRawProductCuttingRate(BigDecimal rawProductCuttingRate) {
        this.rawProductCuttingRate = rawProductCuttingRate;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getRecommendSubstitute() {
        return recommendSubstitute;
    }

    public void setRecommendSubstitute(String recommendSubstitute) {
        this.recommendSubstitute = recommendSubstitute;
    }

    public String getProductionSubstituteProductCode() {
        return productionSubstituteProductCode;
    }

    public void setProductionSubstituteProductCode(String productionSubstituteProductCode) {
        this.productionSubstituteProductCode = productionSubstituteProductCode;
    }

    public String getProductionSubstituteProductName() {
        return productionSubstituteProductName;
    }

    public void setProductionSubstituteProductName(String productionSubstituteProductName) {
        this.productionSubstituteProductName = productionSubstituteProductName;
    }

    public BigDecimal getProductionInputFactor() {
        return productionInputFactor;
    }

    public void setProductionInputFactor(BigDecimal productionInputFactor) {
        this.productionInputFactor = productionInputFactor;
    }

    public BigDecimal getSubstituteInputFactor() {
        return substituteInputFactor;
    }

    public void setSubstituteInputFactor(BigDecimal substituteInputFactor) {
        this.substituteInputFactor = substituteInputFactor;
    }

    public String getRawProductInventoryDisabled() {
        return rawProductInventoryDisabled;
    }

    public void setRawProductInventoryDisabled(String rawProductInventoryDisabled) {
        this.rawProductInventoryDisabled = rawProductInventoryDisabled;
    }
}
