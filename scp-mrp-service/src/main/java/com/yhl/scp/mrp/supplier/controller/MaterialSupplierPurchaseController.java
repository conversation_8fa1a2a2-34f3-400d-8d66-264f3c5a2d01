package com.yhl.scp.mrp.supplier.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.supplier.dto.MaterialSupplierPurchaseDTO;
import com.yhl.scp.mrp.supplier.dto.MaterialSupplierPurchaseParamDTO;
import com.yhl.scp.mrp.supplier.dto.SupplierPurchaseRatioEditDTO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>MrpMaterialSupplierPurchaseController</code>
 * <p>
 * 材料采购控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 14:40:38
 */
@Slf4j
@Api(tags = "材料采购控制器")
@RestController
@RequestMapping("materialSupplierPurchase")
public class MaterialSupplierPurchaseController extends BaseController {

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialSupplierPurchaseVO>> page() {
        List<MaterialSupplierPurchaseVO> mrpMaterialSupplierPurchaseList = materialSupplierPurchaseService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialSupplierPurchaseVO> pageInfo = new PageInfo<>(mrpMaterialSupplierPurchaseList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }
    @ApiOperation(value = "分页查询（新）")
    @GetMapping(value = "pageNew")
    public BaseResponse<PageInfo<MaterialSupplierPurchaseVO>> pageNew() {
        List<MaterialSupplierPurchaseVO> mrpMaterialSupplierPurchaseList = materialSupplierPurchaseService.selectByPageNew(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialSupplierPurchaseVO> pageInfo = new PageInfo<>(mrpMaterialSupplierPurchaseList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页-自定义查询")
    @PostMapping(value = "pageCustom")
    public BaseResponse<PageInfo<MaterialSupplierPurchaseVO>> pageCustom(@RequestBody MaterialSupplierPurchaseParamDTO paramDTO) {
        List<MaterialSupplierPurchaseVO> mrpMaterialSupplierPurchaseList = materialSupplierPurchaseService.pageCustom(paramDTO);
        PageInfo<MaterialSupplierPurchaseVO> pageInfo = new PageInfo<>(mrpMaterialSupplierPurchaseList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }


    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO) {
        return materialSupplierPurchaseService.doCreate(materialSupplierPurchaseDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO) {
        return materialSupplierPurchaseService.doUpdate(materialSupplierPurchaseDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialSupplierPurchaseService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialSupplierPurchaseVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialSupplierPurchaseService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        materialSupplierPurchaseService.export(response);
    }

    @ApiOperation(value = "根据供应商修改")
    @PostMapping(value = "updateBySupplier")
    public BaseResponse<Void> updateBySupplier(@RequestBody SupplierPurchaseRatioEditDTO dto) {
        materialSupplierPurchaseService.doUpdateBySupplier(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }


    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncSupplier() {
        return materialSupplierPurchaseService.syncSupplierPurchase(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "upload")
    public BaseResponse<String> upload(@RequestPart MultipartFile file) {
           return materialSupplierPurchaseService.doUpload(file);
    }

    @ApiOperation(value = "模板导出")
    @GetMapping(value = "exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        materialSupplierPurchaseService.exportTemplate(response);
    }
}
