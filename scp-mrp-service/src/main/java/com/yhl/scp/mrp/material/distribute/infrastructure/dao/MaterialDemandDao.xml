<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.distribute.infrastructure.dao.MaterialDemandDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialDemandPO">
        <!--@Table mrp_material_demand-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_version_id" jdbcType="VARCHAR" property="planVersionId"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="material_product_id" jdbcType="VARCHAR" property="materialProductId"/>
        <result column="material_product_code" jdbcType="VARCHAR" property="materialProductCode"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="unfulfilled_quantity" jdbcType="VARCHAR" property="unfulfilledQuantity"/>
        <result column="demand_order_id" jdbcType="VARCHAR" property="demandOrderId"/>
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
        <result column="demand_type" jdbcType="VARCHAR" property="demandType"/>
        <result column="supply_source" jdbcType="VARCHAR" property="supplySource"/>
        <result column="replace_type" jdbcType="VARCHAR" property="replaceType"/>
        <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.distribute.vo.MaterialDemandVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,plan_version_id,demand_time,product_id,product_code,stock_point_id,material_product_id,material_product_code,material_type,quantity,unfulfilled_quantity,demand_order_id,operation_id,demand_type,supply_source,replace_type,fulfillment_status,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.planVersionId != null and params.planVersionId != ''">
                and plan_version_id = #{params.planVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTime != null">
                and demand_time = #{params.demandTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialProductId != null and params.materialProductId != ''">
                and material_product_id = #{params.materialProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialProductCode != null and params.materialProductCode != ''">
                and material_product_code = #{params.materialProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialType != null and params.materialType != ''">
                and material_type = #{params.materialType,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.unfulfilledQuantity != null">
                and unfulfilled_quantity = #{params.unfulfilledQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.demandOrderId != null and params.demandOrderId != ''">
                and demand_order_id = #{params.demandOrderId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationId != null and params.operationId != ''">
                and operation_id = #{params.operationId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandType != null and params.demandType != ''">
                and demand_type = #{params.demandType,jdbcType=VARCHAR}
            </if>
            <if test="params.supplySource != null and params.supplySource != ''">
                and supply_source = #{params.supplySource,jdbcType=VARCHAR}
            </if>
            <if test="params.replaceType != null and params.replaceType != ''">
                and replace_type = #{params.replaceType,jdbcType=VARCHAR}
            </if>
            <if test="params.fulfillmentStatus != null and params.fulfillmentStatus != ''">
                and fulfillment_status = #{params.fulfillmentStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_demand
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_demand
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_demand
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_demand
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_demand
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增（带主键） -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialDemandPO">
        insert into mrp_material_demand(id,
                                        plan_version_id,
                                        demand_time,
                                        product_id,
                                        product_code,
                                        stock_point_id,
                                        material_product_id,
                                        material_product_code,
                                        material_type,
                                        quantity,
                                        unfulfilled_quantity,
                                        demand_order_id,
                                        operation_id,
                                        demand_type,
                                        supply_source,
                                        replace_type,
                                        fulfillment_status,
                                        remark,
                                        enabled,
                                        creator,
                                        create_time,
                                        modifier,
                                        modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{planVersionId,jdbcType=VARCHAR},
                #{demandTime,jdbcType=TIMESTAMP},
                #{productId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{stockPointId,jdbcType=VARCHAR},
                #{materialProductId,jdbcType=VARCHAR},
                #{materialProductCode,jdbcType=VARCHAR},
                #{materialType,jdbcType=VARCHAR},
                #{quantity,jdbcType=VARCHAR},
                #{unfulfilledQuantity,jdbcType=VARCHAR},
                #{demandOrderId,jdbcType=VARCHAR},
                #{operationId,jdbcType=VARCHAR},
                #{demandType,jdbcType=VARCHAR},
                #{supplySource,jdbcType=VARCHAR},
                #{replaceType,jdbcType=VARCHAR},
                #{fulfillmentStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 批量新增（带主键） -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_demand(
        id,
        plan_version_id,
        demand_time,
        product_id,
        product_code,
        stock_point_id,
        material_product_id,
        material_product_code,
        material_type,
        quantity,
        unfulfilled_quantity,
        demand_order_id,
        operation_id,
        demand_type,
        supply_source,
        replace_type,
        fulfillment_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.planVersionId,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.stockPointId,jdbcType=VARCHAR},
            #{entity.materialProductId,jdbcType=VARCHAR},
            #{entity.materialProductCode,jdbcType=VARCHAR},
            #{entity.materialType,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.unfulfilledQuantity,jdbcType=VARCHAR},
            #{entity.demandOrderId,jdbcType=VARCHAR},
            #{entity.operationId,jdbcType=VARCHAR},
            #{entity.demandType,jdbcType=VARCHAR},
            #{entity.supplySource,jdbcType=VARCHAR},
            #{entity.replaceType,jdbcType=VARCHAR},
            #{entity.fulfillmentStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialDemandPO">
        update mrp_material_demand
        set plan_version_id       = #{planVersionId,jdbcType=VARCHAR},
            demand_time           = #{demandTime,jdbcType=TIMESTAMP},
            product_id            = #{productId,jdbcType=VARCHAR},
            product_code          = #{productCode,jdbcType=VARCHAR},
            stock_point_id        = #{stockPointId,jdbcType=VARCHAR},
            material_product_id   = #{materialProductId,jdbcType=VARCHAR},
            material_product_code = #{materialProductCode,jdbcType=VARCHAR},
            material_type         = #{materialType,jdbcType=VARCHAR},
            quantity              = #{quantity,jdbcType=VARCHAR},
            unfulfilled_quantity  = #{unfulfilledQuantity,jdbcType=VARCHAR},
            demand_order_id       = #{demandOrderId,jdbcType=VARCHAR},
            operation_id          = #{operationId,jdbcType=VARCHAR},
            demand_type           = #{demandType,jdbcType=VARCHAR},
            supply_source         = #{supplySource,jdbcType=VARCHAR},
            replace_type          = #{replaceType,jdbcType=VARCHAR},
            fulfillment_status    = #{fulfillmentStatus,jdbcType=VARCHAR},
            remark                = #{remark,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.distribute.infrastructure.po.MaterialDemandPO">
        update mrp_material_demand
        <set>
            <if test="item.planVersionId != null and item.planVersionId != ''">
                plan_version_id = #{item.planVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProductId != null and item.materialProductId != ''">
                material_product_id = #{item.materialProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProductCode != null and item.materialProductCode != ''">
                material_product_code = #{item.materialProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.unfulfilledQuantity != null">
                unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.demandOrderId != null and item.demandOrderId != ''">
                demand_order_id = #{item.demandOrderId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandType != null and item.demandType != ''">
                demand_type = #{item.demandType,jdbcType=VARCHAR},
            </if>
            <if test="item.supplySource != null and item.supplySource != ''">
                supply_source = #{item.supplySource,jdbcType=VARCHAR},
            </if>
            <if test="item.replaceType != null and item.replaceType != ''">
                replace_type = #{item.replaceType,jdbcType=VARCHAR},
            </if>
            <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_demand
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unfulfilled_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unfulfilledQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandOrderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplySource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="replace_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.replaceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fulfillment_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fulfillmentStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_demand
            <set>
                <if test="item.planVersionId != null and item.planVersionId != ''">
                    plan_version_id = #{item.planVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandTime != null">
                    demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointId != null and item.stockPointId != ''">
                    stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialProductId != null and item.materialProductId != ''">
                    material_product_id = #{item.materialProductId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialProductCode != null and item.materialProductCode != ''">
                    material_product_code = #{item.materialProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialType != null and item.materialType != ''">
                    material_type = #{item.materialType,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.unfulfilledQuantity != null">
                    unfulfilled_quantity = #{item.unfulfilledQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.demandOrderId != null and item.demandOrderId != ''">
                    demand_order_id = #{item.demandOrderId,jdbcType=VARCHAR},
                </if>
                <if test="item.operationId != null and item.operationId != ''">
                    operation_id = #{item.operationId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandType != null and item.demandType != ''">
                    demand_type = #{item.demandType,jdbcType=VARCHAR},
                </if>
                <if test="item.supplySource != null and item.supplySource != ''">
                    supply_source = #{item.supplySource,jdbcType=VARCHAR},
                </if>
                <if test="item.replaceType != null and item.replaceType != ''">
                    replace_type = #{item.replaceType,jdbcType=VARCHAR},
                </if>
                <if test="item.fulfillmentStatus != null and item.fulfillmentStatus != ''">
                    fulfillment_status = #{item.fulfillmentStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_demand
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_demand where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
