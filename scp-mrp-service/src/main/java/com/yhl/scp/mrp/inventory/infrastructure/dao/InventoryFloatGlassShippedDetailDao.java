package com.yhl.scp.mrp.inventory.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassShippedDetailPO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailValidateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryFloatGlassShippedDetailDao</code>
 * <p>
 * 原片浮法已发运库存批次明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:15
 */
public interface InventoryFloatGlassShippedDetailDao extends BaseDao<InventoryFloatGlassShippedDetailPO, InventoryFloatGlassShippedDetailVO> {

    List<InventoryFloatGlassShippedDetailPO> selectByPlanNumbersAndlineIds(Map<String, Object> params);
    /**
     * 组合查询
     *
     */
    List<InventoryFloatGlassShippedDetailVO> selectVOByParams(@Param("params") Map<String, Object> params);
    List<InventoryFloatGlassShippedDetailValidateVO> selectErrorStockPoint();
}
