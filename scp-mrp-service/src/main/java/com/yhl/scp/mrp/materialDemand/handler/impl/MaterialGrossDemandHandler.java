package com.yhl.scp.mrp.materialDemand.handler.impl;

import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.common.enums.TemplateDayPatternEnum;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandDTO;
import com.yhl.scp.mrp.materialDemand.handler.MaterialGrossDemandAbstractHandler;
import com.yhl.scp.mrp.materialDemand.handler.MaterialGrossDemandDataHolder;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <code>LoadingHorizonYearMonthDayHandler</code>
 * <p>
 * LoadingHorizonYearMonthDayHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 13:59:42
 */
@Component
@Slf4j
public class MaterialGrossDemandHandler extends MaterialGrossDemandAbstractHandler {

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private MaterialGrossDemandVersionService materialGrossDemandVersionService;

    @Override
    protected String handleFileImport(MaterialGrossDemandDataHolder materialGrossDemandDataHolder, Map<Integer, String> headers, List<Map<Integer, String>> data, Map<String, String> extMap) {
        StringBuilder errMsg = new StringBuilder();
        // 行数据循环
        AtomicInteger rowNo = new AtomicInteger(1);
        List<MaterialGrossDemandDTO> materialGrossDemandDTOList = Lists.newArrayList();
        int successCount = 0, failCount = 0;
        List<String> productCodeList = new ArrayList<>();
        // 查询最新版本
        MaterialGrossDemandVersionVO materialGrossDemandVersionVO = materialGrossDemandVersionService.selectLastVersion();
        for (Map<Integer, String> rowMap : data) {
            rowNo.incrementAndGet();
            String productCode = "";
            String productName = "";
            String productCategory = "";
            String productClassify = "";
            String productFactoryCode = "";
            String vehicleModeCode = "";
            Map<String, Double> quantityMap = new HashMap<>();
            StringBuilder rowErrMsg = new StringBuilder();
            // 列数据循环
            for (int i = 1; i < headers.size() + 1; i++) {
                String columnValue = rowMap.get(i);
                if (1 == i) {
                    productCode = columnValue;
                    continue;
                } else if (2 == i) {
                    productName = columnValue;
                    continue;
                } else if (3 == i) {
                    productCategory = columnValue;
                    continue;
                }else if (4 == i){
                    productClassify = columnValue;
                    continue;
                }else if (5 == i){
                    productFactoryCode = columnValue;
                    continue;
                }else if (6 == i){
                    vehicleModeCode = columnValue;
                    continue;
                }
                // 校验数据
                if (!validData(materialGrossDemandDataHolder, productCode, productCategory, productClassify, rowNo.get(), rowErrMsg)) {
                    break;
                }

                String header = headers.get(i);
                Double quantity = null;
                try {
                    if (StringUtils.isNotBlank(columnValue)) {
                        quantity = Double.valueOf(columnValue);
                    }
                } catch (Exception ex) {
                    rowErrMsg.append(String.format("行数：%s", rowNo.get()));
                    rowErrMsg.append(String.format("，列数：%s", i + 1));
                    rowErrMsg.append("，内容非数值\n");
                    break;
                }
                if (Objects.nonNull(quantity)) {
                    Double mapQuantity = quantityMap.get(header);
                    if (Objects.nonNull(mapQuantity)) {
                        quantityMap.put(header, quantity + mapQuantity);
                    } else {
                        quantityMap.put(header, quantity);
                    }
                }
            }
            if (StringUtils.isNotBlank(rowErrMsg)) {
                errMsg.append(rowErrMsg);
                errMsg.append("\n");
                failCount++;
                continue;
            }

            // 处理数据
            if (MapUtils.isEmpty(quantityMap)) {
                continue;
            }
            productCodeList.add(productCode);
            //插入数据
            for (Map.Entry<String, Double> entry : quantityMap.entrySet()) {
                MaterialGrossDemandDTO materialGrossDemandDTO = new MaterialGrossDemandDTO();
                materialGrossDemandDTO.setId(UUID.randomUUID().toString());
                materialGrossDemandDTO.setMaterialGrossDemandVersionId(materialGrossDemandVersionVO.getId());
                materialGrossDemandDTO.setProductCode(productCode);
                materialGrossDemandDTO.setProductName(productName);
                materialGrossDemandDTO.setProductCategory(productCategory);
                materialGrossDemandDTO.setProductClassify(productClassify);
                materialGrossDemandDTO.setProductFactoryCode(productFactoryCode);
                materialGrossDemandDTO.setVehicleModeCode(vehicleModeCode);
                materialGrossDemandDTO.setDemandTime(DateUtils.stringToDate(entry.getKey()));
                materialGrossDemandDTO.setDemandSource(MrpDemandSourceEnum.MANUAL_ADDITION_DEMAND.getCode());
                if (Objects.nonNull(entry.getValue())) {
                    materialGrossDemandDTO.setDemandQuantity(BigDecimal.valueOf(entry.getValue()));
                } else {
                    materialGrossDemandDTO.setDemandQuantity(null);
                }
                materialGrossDemandDTOList.add(materialGrossDemandDTO);
            }
            successCount++;
        }

        if (CollectionUtils.isNotEmpty(productCodeList)){
            // 删除手工新增需求数据
            Lists.partition(productCodeList, 500).forEach(materialGrossDemandService::doDeleteByProductCodeList);
        }
        if (CollectionUtils.isNotEmpty(materialGrossDemandDTOList)) {
            Lists.partition(materialGrossDemandDTOList, 500).forEach(materialGrossDemandService::doCreateBatch);

        }
        extMap.put("successInfo", String.format("导入数量共%s条,成功导入%s条", data.size(), successCount));
        if (failCount > 0){
            extMap.put("errorInfo", String.format("失败%s条,失败信息:%s", failCount, errMsg));
        }

        return String.format("导入数量共%s条,成功导入%s条",data.size(),successCount)
                + (failCount>0?String.format(",失败%s条,失败信息:%s",failCount,errMsg):"");
    }

    @Override
    protected Boolean validData(MaterialGrossDemandDataHolder materialGrossDemandDataHolder, String productCode, String productCategory, String productClassify, Integer rowIndex, StringBuilder errMsg) {
        if (StringUtils.isBlank(productCode)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，材料编码为空\n");
            return false;
        }
        if (StringUtils.isBlank(productCategory)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，物料类别为空\n");
            return false;
        }
        if (StringUtils.isBlank(productClassify)) {
            errMsg.append(String.format("行数：%s", rowIndex));
            errMsg.append("，通用类别为空\n");
            return false;
        }
        return true;
    }

    @Override
    protected String getCommand() {
        return TemplateDayPatternEnum.YYYY_MM_DD.getCode();
    }

    public static boolean hasLaterDate(Set<String> dates, String date) {
        LocalDate targetDate = LocalDate.parse(date);

        for (String d : dates) {
            LocalDate currentDate = LocalDate.parse(d);
            if (targetDate.getMonth() != currentDate.getMonth() || targetDate.getYear()!=currentDate.getYear()){
                continue;
            }
            if (currentDate.isAfter(targetDate)) {
                return true;
            }
        }
        return false;
    }
}