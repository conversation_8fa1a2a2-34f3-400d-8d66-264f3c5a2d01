package com.yhl.scp.mrp.materialDemand.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.materialDemand.convertor.MaterialGrossDemandHistoryConvertor;
import com.yhl.scp.mrp.materialDemand.domain.entity.MaterialGrossDemandHistoryDO;
import com.yhl.scp.mrp.materialDemand.domain.service.MaterialGrossDemandHistoryDomainService;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandHistoryDTO;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandParam;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandHistoryDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandHistoryPO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandHistoryService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionDetailService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandHistoryVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <code>MaterialGrossDemandHistoryServiceImpl</code>
 * <p>
 * 材料计划毛需求（历史）应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11 09:51:48
 */
@Slf4j
@Service
public class MaterialGrossDemandHistoryServiceImpl extends AbstractService implements MaterialGrossDemandHistoryService {

    @Resource
    private MaterialGrossDemandHistoryDao materialGrossDemandHistoryDao;

    @Resource
    private MaterialGrossDemandHistoryDomainService materialGrossDemandDetailDomainService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialGrossDemandHistoryDTO materialGrossDemandDetailDTO) {
        // 0.数据转换
        MaterialGrossDemandHistoryDO materialGrossDemandDetailDO = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Do(materialGrossDemandDetailDTO);
        MaterialGrossDemandHistoryPO materialGrossDemandDetailPO = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Po(materialGrossDemandDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDetailDomainService.validation(materialGrossDemandDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialGrossDemandDetailPO);
        materialGrossDemandHistoryDao.insert(materialGrossDemandDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialGrossDemandHistoryDTO materialGrossDemandDetailDTO) {
        // 0.数据转换
        MaterialGrossDemandHistoryDO materialGrossDemandDetailDO = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Do(materialGrossDemandDetailDTO);
        MaterialGrossDemandHistoryPO materialGrossDemandDetailPO = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Po(materialGrossDemandDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialGrossDemandDetailDomainService.validation(materialGrossDemandDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialGrossDemandDetailPO);
        materialGrossDemandHistoryDao.update(materialGrossDemandDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialGrossDemandHistoryDTO> list) {
        List<MaterialGrossDemandHistoryPO> newList = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialGrossDemandHistoryDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialGrossDemandHistoryDTO> list) {
        List<MaterialGrossDemandHistoryPO> newList = MaterialGrossDemandHistoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialGrossDemandHistoryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialGrossDemandHistoryDao.deleteBatch(idList);
        }
        return materialGrossDemandHistoryDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialGrossDemandHistoryVO selectByPrimaryKey(String id) {
        MaterialGrossDemandHistoryPO po = materialGrossDemandHistoryDao.selectByPrimaryKey(id);
        return MaterialGrossDemandHistoryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND_DETAIL")
    public List<MaterialGrossDemandHistoryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    public PageInfo<MaterialGrossDemandHistoryVO> selectByPage2(MaterialGrossDemandParam materialGrossDemandParam) {
        Map<String, Object> params = materialGrossDemandService.assemblyParams(materialGrossDemandParam);
        // 分组查询材料的信息
        PageHelper.startPage(materialGrossDemandParam.getPageNum(), materialGrossDemandParam.getPageSize());
        List<MaterialGrossDemandHistoryVO> materialGrossDemandHistoryVOList = materialGrossDemandHistoryDao.selectGroupByParams(params);
        PageInfo<MaterialGrossDemandHistoryVO> pageInfo = new PageInfo<>(materialGrossDemandHistoryVOList);

        // 查询详细数据
        List<String> productCodeList = materialGrossDemandHistoryVOList.stream()
                .map(MaterialGrossDemandHistoryVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());
        // 查询详细数据
        List<MaterialGrossDemandHistoryVO> list =  getMaterialGrossDemandHistory(productCodeList,materialGrossDemandParam);

        // 按照本厂编码+物料编码分组
        Map<String, List<MaterialGrossDemandHistoryVO>> grossDemandDetailGroup = list.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductFactoryCode(), item.getProductCode())));

        // 本厂编码 本厂名称
        Map<String, String> productFactoryCodeMap = new HashMap<>();
        List<String> productFactoryCodeLit = list.stream().map(MaterialGrossDemandHistoryVO::getProductFactoryCode).distinct().collect(Collectors.toList());
        // 获取物料数据
        if (!grossDemandDetailGroup.isEmpty()) {
            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("product_code", "product_name"))
                    .queryParam(ImmutableMap.of("productCodeList", productFactoryCodeLit))
                    .build();
            List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
            productFactoryCodeMap = productStockPointVOList.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getProductName, (v1, v2) -> v1));
        }

        List<String> dateStrList = list.stream().map(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)).distinct().sorted() // 添加排序步骤
                .collect(Collectors.toList());

        for (MaterialGrossDemandHistoryVO materialGrossDemandHistoryVO : materialGrossDemandHistoryVOList) {
            List<MaterialGrossDemandHistoryVO> detailList = grossDemandDetailGroup.get(String.join("#", materialGrossDemandHistoryVO.getProductFactoryCode(), materialGrossDemandHistoryVO.getProductCode()));
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            // 获取详细数据根据日期来汇总
            Map<String, List<MaterialGrossDemandHistoryVO>> detailsGroup = detailList.stream().collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(), DateUtils.COMMON_DATE_STR3)));

            List<MaterialGrossDemandSummaryVO> detailVOList = new ArrayList<>();
            for (Map.Entry<String, List<MaterialGrossDemandHistoryVO>> entry : detailsGroup.entrySet()) {
                List<MaterialGrossDemandHistoryVO> materialGrossDemandHistoryVOS = entry.getValue();
                // 组装每天汇总数据
                MaterialGrossDemandSummaryVO materialGrossDemandSummaryVO = new MaterialGrossDemandSummaryVO();
                BigDecimal dayDemandQuantitySum = materialGrossDemandHistoryVOS.stream().map(MaterialGrossDemandHistoryVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                materialGrossDemandSummaryVO.setDemandQuantity(dayDemandQuantitySum);
                materialGrossDemandSummaryVO.setDemandTime(entry.getKey());

                List<MaterialGrossDemandHistoryVO> childrenList = new ArrayList<>();
                for (MaterialGrossDemandHistoryVO grossDemandVO : materialGrossDemandHistoryVOS) {
                    MaterialGrossDemandHistoryVO materialGrossDemandDetailVO = new MaterialGrossDemandHistoryVO();
                    materialGrossDemandDetailVO.setId(grossDemandVO.getId());
                    materialGrossDemandDetailVO.setDemandTime(grossDemandVO.getDemandTime());
                    materialGrossDemandDetailVO.setDemandQuantity(grossDemandVO.getDemandQuantity());
                    materialGrossDemandDetailVO.setDemandSource(grossDemandVO.getDemandSource());
                    childrenList.add(materialGrossDemandDetailVO);
                }
                materialGrossDemandSummaryVO.setChildrenList(childrenList);
                detailVOList.add(materialGrossDemandSummaryVO);
            }
            materialGrossDemandHistoryVO.setProductId(detailList.get(0).getProductId());
            materialGrossDemandHistoryVO.setProductName(detailList.get(0).getProductName());
            materialGrossDemandHistoryVO.setProductCategory(detailList.get(0).getProductCategory());
            materialGrossDemandHistoryVO.setProductClassify(detailList.get(0).getProductClassify());
            materialGrossDemandHistoryVO.setProductFactoryCode(detailList.get(0).getProductFactoryCode());
            if (StringUtils.isNotEmpty(materialGrossDemandHistoryVO.getProductFactoryCode())){
                materialGrossDemandHistoryVO.setProductFactoryName(productFactoryCodeMap.get(materialGrossDemandHistoryVO.getProductFactoryCode()));
            }
            materialGrossDemandHistoryVO.setVehicleModeCode(detailList.get(0).getVehicleModeCode());
            materialGrossDemandHistoryVO.setDataList(dateStrList);
            materialGrossDemandHistoryVO.setDetailList(detailVOList);
        }

        PageInfo<MaterialGrossDemandHistoryVO> pageInfoResult = new PageInfo<>(materialGrossDemandHistoryVOList);
        pageInfoResult.setTotal(pageInfo.getTotal());
        pageInfoResult.setPages(pageInfo.getPages());
        return pageInfoResult;
    }

    private List<MaterialGrossDemandHistoryVO> getMaterialGrossDemandHistory(List<String> productCodeList,
                                                                             MaterialGrossDemandParam materialGrossDemandParam) {
        // 创建固定大小的线程池（根据实际情况调整线程数）
        Executor executor = Executors.newFixedThreadPool(Math.min(10, productCodeList.size()));
        // 按批次拆分查询任务
        int batchSize = 10;
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < productCodeList.size(); i += batchSize) {
            batches.add(productCodeList.subList(i, Math.min(i + batchSize, productCodeList.size())));
        }

        // 使用CompletionService处理异步结果
        CompletionService<List<MaterialGrossDemandHistoryVO>> completionService =
                new ExecutorCompletionService<>(executor);

        // 提交所有批次任务
        for (List<String> batch : batches) {
            completionService.submit(() ->
                    materialGrossDemandHistoryDao.selectVOByParams(
                            ImmutableMap.of("productCodeList", batch,
                                    "materialGrossDemandVersionId", materialGrossDemandParam.getMaterialGrossDemandVersionId())
                    )
            );
        }

        // 流式处理结果，避免一次性加载所有数据
        List<MaterialGrossDemandHistoryVO> result = new ArrayList<>();
        for (int i = 0; i < batches.size(); i++) {
            try {
                // 阻塞获取完成的任务结果
                List<MaterialGrossDemandHistoryVO> batchResult = completionService.take().get();
                result.addAll(batchResult);
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Error fetching batch results", e);
            }
        }

        return result;
    }

    @Override
    @Expression(value = "MATERIAL_GROSS_DEMAND_DETAIL")
    public List<MaterialGrossDemandHistoryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialGrossDemandHistoryVO> dataList = materialGrossDemandHistoryDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialGrossDemandHistoryServiceImpl target = SpringBeanUtils.getBean(MaterialGrossDemandHistoryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialGrossDemandHistoryVO> selectByParams(Map<String, Object> params) {
        List<MaterialGrossDemandHistoryPO> list = materialGrossDemandHistoryDao.selectByParams(params);
        return MaterialGrossDemandHistoryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialGrossDemandHistoryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialGrossDemandHistoryVO> invocation(List<MaterialGrossDemandHistoryVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }



}
