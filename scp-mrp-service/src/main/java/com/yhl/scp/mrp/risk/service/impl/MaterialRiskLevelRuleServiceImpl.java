package com.yhl.scp.mrp.risk.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.risk.convertor.MaterialRiskLevelRuleConvertor;
import com.yhl.scp.mrp.risk.domain.entity.MaterialRiskLevelRuleDO;
import com.yhl.scp.mrp.risk.domain.service.MaterialRiskLevelRuleDomainService;
import com.yhl.scp.mrp.risk.dto.MaterialRiskLevelRuleExportDTO;
import com.yhl.scp.mrp.risk.dto.MaterialRiskLevelRuleDTO;
import com.yhl.scp.mrp.risk.infrastructure.dao.MaterialRiskLevelRuleDao;
import com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO;
import com.yhl.scp.mrp.risk.service.MaterialRiskLevelRuleService;
import com.yhl.scp.mrp.risk.vo.MaterialRiskLevelRuleVO;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialRiskLevelRuleServiceImpl</code>
 * <p>
 * 材料风险等级判定规则应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23 14:39:10
 */
@Slf4j
@Service
public class MaterialRiskLevelRuleServiceImpl extends AbstractService implements MaterialRiskLevelRuleService {

    @Resource
    private MaterialRiskLevelRuleDao materialRiskLevelRuleDao;

    @Resource
    private MaterialRiskLevelRuleDomainService materialRiskLevelRuleDomainService;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialRiskLevelRuleDTO materialRiskLevelRuleDTO) {
        // 0.数据转换
        MaterialRiskLevelRuleDO materialRiskLevelRuleDO = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Do(materialRiskLevelRuleDTO);
        MaterialRiskLevelRulePO materialRiskLevelRulePO = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Po(materialRiskLevelRuleDTO);
        // 1.数据校验
        materialRiskLevelRuleDomainService.validation(materialRiskLevelRuleDO);
        if (MaterialTypeEnum.B_TYPE.getCode().equals(materialRiskLevelRuleDO.getMaterialType())){
            throw new BusinessException("B类不能新增！");
        }
        //公司不填默认为（FYSH）定死
        String rangeData = getRangeData();
        if (StringUtils.isEmpty(materialRiskLevelRulePO.getCompany())){
            materialRiskLevelRulePO.setCompany(rangeData);
        }
        // 2.数据持久化
        BasePOUtils.insertFiller(materialRiskLevelRulePO);
        materialRiskLevelRuleDao.insert(materialRiskLevelRulePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialRiskLevelRuleDTO riskLevelRuleParamsDTO) {
        // 0.数据转换
        MaterialRiskLevelRuleDO materialRiskLevelRuleDO = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Do(riskLevelRuleParamsDTO);
        MaterialRiskLevelRulePO materialRiskLevelRulePO = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Po(riskLevelRuleParamsDTO);
        // 1.数据校验
        materialRiskLevelRuleDomainService.validation(materialRiskLevelRuleDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialRiskLevelRulePO);
        materialRiskLevelRuleDao.update(materialRiskLevelRulePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialRiskLevelRuleDTO> list) {
        String rangeData = getRangeData();
        List<MaterialRiskLevelRulePO> newList = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Pos(list);
        for (MaterialRiskLevelRulePO materialRiskLevelRulePO : newList) {
            //公司不填默认为（FYSH）定死
            if (StringUtils.isEmpty(materialRiskLevelRulePO.getCompany())){
                materialRiskLevelRulePO.setCompany(rangeData);
            }
        }
        BasePOUtils.insertBatchFiller(newList);
        materialRiskLevelRuleDao.insertBatch(newList);
    }

    private  String getRangeData() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "OP_BUSINESS_AREA", "EXTERNAL_REQ", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        return rangeData;
    }

    @Override
    public void doUpdateBatch(List<MaterialRiskLevelRuleDTO> list) {
        List<MaterialRiskLevelRulePO> newList = MaterialRiskLevelRuleConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialRiskLevelRuleDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        //判断此次删除的规则类型是否包含B类，B类不可删除
        if (this.selectByParams(ImmutableMap.of("ids", idList)).stream()
                .anyMatch(data -> MaterialTypeEnum.B_TYPE.getCode().equals(data.getMaterialType()))) {
            throw new BusinessException("材料类型为B类的规则不可删除");
        }
        if (idList.size() > 1) {
            return materialRiskLevelRuleDao.deleteBatch(idList);
        }
        return materialRiskLevelRuleDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialRiskLevelRuleVO selectByPrimaryKey(String id) {
        MaterialRiskLevelRulePO po = materialRiskLevelRuleDao.selectByPrimaryKey(id);
        return MaterialRiskLevelRuleConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "mrp_material_risk_level_rule")
    public List<MaterialRiskLevelRuleVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "mrp_material_risk_level_rule")
    public List<MaterialRiskLevelRuleVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialRiskLevelRuleVO> dataList = materialRiskLevelRuleDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialRiskLevelRuleServiceImpl target = SpringBeanUtils.getBean(MaterialRiskLevelRuleServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialRiskLevelRuleVO> selectByParams(Map<String, Object> params) {
        List<MaterialRiskLevelRulePO> list = materialRiskLevelRuleDao.selectByParams(params);
        return MaterialRiskLevelRuleConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialRiskLevelRuleVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }


    @Override
    public void export(HttpServletResponse response) {
        List<MaterialRiskLevelRulePO> materialRiskLevelRulePOS = materialRiskLevelRuleDao.selectByParams(new HashMap<>(2));
        List<MaterialRiskLevelRuleExportDTO> riskLevelRuleParamsExportDTOS = MaterialRiskLevelRuleConvertor.INSTANCE.po2ExportDtos(materialRiskLevelRulePOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("材料风险等级判定规则.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), MaterialRiskLevelRuleExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("材料风险等级判定规则")
                    .doWrite(riskLevelRuleParamsExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void loseEffectivenessBatch(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        List<MaterialRiskLevelRulePO> materialRiskLevelRulePOS = materialRiskLevelRuleDao.selectByPrimaryKeys(ids);
        if (CollectionUtils.isNotEmpty(materialRiskLevelRulePOS)){
            materialRiskLevelRulePOS.forEach(t->t.setEnabled(YesOrNoEnum.NO.getCode()));
            BasePOUtils.updateBatchFiller(materialRiskLevelRulePOS);
            materialRiskLevelRuleDao.updateBatch(materialRiskLevelRulePOS);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.RISK_LEVEL_RULE_PARAMS.getCode();
    }

    @Override
    public List<MaterialRiskLevelRuleVO> invocation(List<MaterialRiskLevelRuleVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

}
