package com.yhl.scp.mrp.substitutionRelationship.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.substitutionRelationship.convertor.GlassSubstitutionRelationshipConvertor;
import com.yhl.scp.mrp.substitutionRelationship.domain.entity.GlassSubstitutionRelationshipDO;
import com.yhl.scp.mrp.substitutionRelationship.domain.service.GlassSubstitutionRelationshipDomainService;
import com.yhl.scp.mrp.substitutionRelationship.dto.GlassSubstitutionRelationshipDTO;
import com.yhl.scp.mrp.substitutionRelationship.infrastructure.dao.GlassSubstitutionRelationshipDao;
import com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>GlassSubstitutionRelationshipServiceImpl</code>
 * <p>
 * 原片替代关系表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:39:49
 */
@Slf4j
@Service
public class GlassSubstitutionRelationshipServiceImpl extends AbstractService implements GlassSubstitutionRelationshipService {

    @Resource
    private GlassSubstitutionRelationshipDao glassSubstitutionRelationshipDao;

    @Resource
    private GlassSubstitutionRelationshipDomainService glassSubstitutionRelationshipDomainService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;

    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @Resource
    private MrpNewProductStockPointDao mrpNewProductStockPointDao;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(GlassSubstitutionRelationshipDTO glassSubstitutionRelationshipDTO) {
        // 计算切裁率
        List<ProductBomVersionVO> productBomVersionList = inventoryAlternativeRelationshipService
                .getProductBomVersion(Lists.newArrayList(glassSubstitutionRelationshipDTO.getRawProductCode()));
        List<ProductBomVO> productBomList = inventoryAlternativeRelationshipService
                .getProductBom(Lists.newArrayList(glassSubstitutionRelationshipDTO.getRawProductCode()));
        assembleCuttingRate(Lists.newArrayList(glassSubstitutionRelationshipDTO), productBomList, productBomVersionList);

        // 0.数据转换
        GlassSubstitutionRelationshipDO glassSubstitutionRelationshipDO = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Do(glassSubstitutionRelationshipDTO);
        GlassSubstitutionRelationshipPO glassSubstitutionRelationshipPO = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Po(glassSubstitutionRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassSubstitutionRelationshipDomainService.validation(glassSubstitutionRelationshipDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassSubstitutionRelationshipPO);
        glassSubstitutionRelationshipDao.insertWithPrimaryKey(glassSubstitutionRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(GlassSubstitutionRelationshipDTO glassSubstitutionRelationshipDTO) {
        // 计算切裁率
        List<ProductBomVersionVO> productBomVersionList = inventoryAlternativeRelationshipService
                .getProductBomVersion(Lists.newArrayList(glassSubstitutionRelationshipDTO.getRawProductCode()));
        List<ProductBomVO> productBomList = inventoryAlternativeRelationshipService
                .getProductBom(Lists.newArrayList(glassSubstitutionRelationshipDTO.getRawProductCode()));
        assembleCuttingRate(Lists.newArrayList(glassSubstitutionRelationshipDTO), productBomList, productBomVersionList);
        // 0.数据转换
        GlassSubstitutionRelationshipDO glassSubstitutionRelationshipDO = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Do(glassSubstitutionRelationshipDTO);
        GlassSubstitutionRelationshipPO glassSubstitutionRelationshipPO = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Po(glassSubstitutionRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassSubstitutionRelationshipDomainService.validation(glassSubstitutionRelationshipDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassSubstitutionRelationshipPO);
        glassSubstitutionRelationshipDao.update(glassSubstitutionRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassSubstitutionRelationshipDTO> list) {
        List<GlassSubstitutionRelationshipPO> newList = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassSubstitutionRelationshipDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassSubstitutionRelationshipDTO> list) {
        // 校验生产BOM是否不一致
        glassSubstitutionRelationshipDomainService.checkProductionSubstituteProductCode(list);
        // 计算切裁率
        List<String> rawProductCodeList = list.stream().map(GlassSubstitutionRelationshipDTO::getRawProductCode).distinct().collect(Collectors.toList());
        List<ProductBomVersionVO> productBomVersionList = inventoryAlternativeRelationshipService
                .getProductBomVersion(rawProductCodeList);
        List<ProductBomVO> productBomList = inventoryAlternativeRelationshipService
                .getProductBom(rawProductCodeList);

        assembleCuttingRate(list, productBomList, productBomVersionList);
        List<GlassSubstitutionRelationshipPO> newList = GlassSubstitutionRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassSubstitutionRelationshipDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassSubstitutionRelationshipDao.deleteBatch(idList);
        }
        return glassSubstitutionRelationshipDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassSubstitutionRelationshipVO selectByPrimaryKey(String id) {
        GlassSubstitutionRelationshipPO po = glassSubstitutionRelationshipDao.selectByPrimaryKey(id);
        return GlassSubstitutionRelationshipConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "GLASS_SUBSTITUTION_RELATIONSHIP")
    public List<GlassSubstitutionRelationshipVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "GLASS_SUBSTITUTION_RELATIONSHIP")
    public List<GlassSubstitutionRelationshipVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassSubstitutionRelationshipVO> dataList = glassSubstitutionRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassSubstitutionRelationshipServiceImpl target = SpringBeanUtils.getBean(GlassSubstitutionRelationshipServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassSubstitutionRelationshipVO> selectByParams(Map<String, Object> params) {
        List<GlassSubstitutionRelationshipPO> list = glassSubstitutionRelationshipDao.selectByParams(params);
        return GlassSubstitutionRelationshipConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassSubstitutionRelationshipVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<GlassSubstitutionRelationshipVO> invocation(List<GlassSubstitutionRelationshipVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doUpload(MultipartFile file) {
        List<GlassSubstitutionRelationshipDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(GlassSubstitutionRelationshipDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 校验文件、数据
        if (CollectionUtils.isEmpty(fileList)) throw new BusinessException("文件数据为空");
        for (GlassSubstitutionRelationshipDTO excelDTO : fileList) {
            if (StringUtils.isEmpty(excelDTO.getProductCode())) throw new BusinessException("本厂编码*不能为空");
            if (StringUtils.isEmpty(excelDTO.getRawProductCode())) throw new BusinessException("ERP-BOM*不能为空");
            if (StringUtils.isEmpty(excelDTO.getProductionSubstituteProductCode())) throw new BusinessException("生产BOM*不能为空");
            if (StringUtils.isEmpty(excelDTO.getSubstituteProductCode())) throw new BusinessException("替代BOM*不能为空");
        }

        List<GlassSubstitutionRelationshipDTO> totalExportDtoList = fileList;

        // 获取bomVersion
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), new HashMap<>());
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), new HashMap<>());

        // 根据bom获取关联的本厂编码，如果原片有多个本厂编码，则生成多条数据
        getGlassSubstitutionRelationshipByGrossDemand(fileList, totalExportDtoList);

        List<String> rawProductCodeList = fileList.stream()
                .map(GlassSubstitutionRelationshipDTO::getRawProductCode)
                .distinct().collect(Collectors.toList());

        Map<String, GlassSubstitutionRelationshipPO> substitutionRelationshipPOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rawProductCodeList)) {
            // 根据标准原片删除自动生成数据
            glassSubstitutionRelationshipDao.deleteByParams(
                    ImmutableMap.of("rawProductCodeList", rawProductCodeList,
                    "recommendSubstitute", YesOrNoEnum.YES.getCode()));

            // 查询原片替代映射
            List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao
                    .selectByParams(ImmutableMap.of("rawProductCodeList", rawProductCodeList));
            substitutionRelationshipPOMap = glassSubstitutionRelationshipPOS.stream()
                    .collect(Collectors.toMap(item -> String.join("#", item.getRawProductCode(), item.getSubstituteProductCode(), item.getProductCode()),
                            Function.identity(), (k1, k2) -> k2));

        }

        List<GlassSubstitutionRelationshipDTO> insertList = new ArrayList<>();
        List<GlassSubstitutionRelationshipDTO> updateList = new ArrayList<>();
        for (GlassSubstitutionRelationshipDTO dto : totalExportDtoList) {
            String joinKey = String.join("#", dto.getRawProductCode(), dto.getSubstituteProductCode(), dto.getProductCode());
            if (substitutionRelationshipPOMap.containsKey(joinKey)) {
                GlassSubstitutionRelationshipPO po = substitutionRelationshipPOMap.get(joinKey);
                dto.setId(po.getId());
                updateList.add(dto);
            } else {
                insertList.add(dto);
            }
        }

        // 持久化数据
        if (CollectionUtils.isNotEmpty(insertList)) {
            // 校验文件里的数据是否有生产BOM不一致的问题
            checkProductionSubstituteProductCodeByFileData(insertList);
            assembleCuttingRate(insertList, productBomVOList, productBomVersionVOList);
            this.doCreateBatch(insertList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            assembleCuttingRate(updateList, productBomVOList, productBomVersionVOList);
            this.doUpdateBatch(updateList);
        }
    }

    private void checkProductionSubstituteProductCodeByFileData(List<GlassSubstitutionRelationshipDTO> insertList) {
        Map<String, List<GlassSubstitutionRelationshipDTO>> dataGroup = insertList.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));
        for (Map.Entry<String, List<GlassSubstitutionRelationshipDTO>> entry : dataGroup.entrySet()) {
            List<GlassSubstitutionRelationshipDTO> dtoList = entry.getValue();
            List<String> productSubstitutionProductCodeList = dtoList.stream().map(GlassSubstitutionRelationshipDTO::getProductionSubstituteProductCode)
                    .distinct().collect(Collectors.toList());
            if (productSubstitutionProductCodeList.size() > 1){
                throw new BusinessException(entry.getKey() + "生产BOM不一致");
            }
        }
    }


    @Override
    public void doGenerateData() {
        List<GlassSubstitutionRelationshipDTO> totalList = new ArrayList<>();
        List<GlassSubstitutionRelationshipDTO> list = new ArrayList<>();

        // 根据毛需求生成替代映射数据
        getGlassSubstitutionRelationshipByGrossDemand(null, list);

        // 没有替代料的数据
        List<GlassSubstitutionRelationshipDTO> noSubstituteList = list.stream()
                .filter(item -> StringUtils.isBlank(item.getSubstituteProductCode())).collect(Collectors.toList());

        // 获取本厂编码
        List<String> productCodeList = noSubstituteList.stream()
                .map(GlassSubstitutionRelationshipDTO::getProductCode).distinct().collect(Collectors.toList());

        // 查询本厂编码
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("stock_point_code", "product_code", "product_name", "product_length", "product_width", "product_area"))
                .queryParam(ImmutableMap.of("productCodeList", productCodeList))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .filter(item -> null != item.getProductArea())
                .collect(Collectors.toList())
                .stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        // 获取产品的工艺基础数据
        List<MdsProductStockPointBaseVO> productStockPointBaseVOS = newMdsFeign
                .selectProductStockPointBasicVOByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodeList", productCodeList));
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseVOMap = productStockPointBaseVOS.stream()
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        // 自动生成推荐替代
        getGlassSubstitutionRelationshipByInventoryAlternativeRelationship(noSubstituteList, totalList, productStockPointVOMap, productStockPointBaseVOMap);

        // 维护其他属性
//        assembleOtherProperties(totalList);
        // 查询替代数据
        List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao.selectByParams(new HashMap<>());
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipPOS)){
            // 持久化数据
            if (CollectionUtils.isNotEmpty(totalList)) {
                this.doCreateBatch(totalList);
            }
        }else {
            List<GlassSubstitutionRelationshipDTO> insertList = new ArrayList<>();
            List<String> deleteIdList = new ArrayList<>();
            // 分组数据库的数据
            Map<String, List<GlassSubstitutionRelationshipPO>> oldDataGroup = glassSubstitutionRelationshipPOS.stream()
                    .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));
            // 分组新增的数据
            Map<String, List<GlassSubstitutionRelationshipDTO>> newDataGroup = totalList.stream()
                    .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));
            for (Map.Entry<String, List<GlassSubstitutionRelationshipDTO>> entry : newDataGroup.entrySet()) {
                List<GlassSubstitutionRelationshipDTO> dtoList = entry.getValue();
                List<GlassSubstitutionRelationshipPO> poList = oldDataGroup.get(entry.getKey());
                if (CollectionUtils.isEmpty(poList)){
                    insertList.addAll(entry.getValue());
                }else {
                    // 删除未启用的数据
                    List<String> ids = poList.stream()
                            .filter(item -> StringUtils.equals(YesOrNoEnum.NO.getCode(), item.getEnabled()))
                            .map(GlassSubstitutionRelationshipPO::getId)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(ids)){
                        deleteIdList.addAll(ids);
                    }
                    // 过滤出启用数据条数
                    int remainingCount  = poList.size() - ids.size();
                    int toAdd = Math.max(0, 10 - remainingCount);

                    // 从文件数据中取需要新增的部分（不超过可用数据量）
                    if (toAdd > 0) {
                        int actualAdd = Math.min(toAdd, dtoList.size());
                        insertList.addAll(dtoList.subList(0, actualAdd));
                    }
                }
            }
            // 删除未启用数据
            if (CollectionUtils.isNotEmpty(deleteIdList)){
                glassSubstitutionRelationshipDao.deleteBatch(deleteIdList);
            }
            // 持久化数据
            if (CollectionUtils.isNotEmpty(insertList)) {
                this.doCreateBatch(insertList);
            }
        }
    }

    private void assembleCuttingRate(List<GlassSubstitutionRelationshipDTO> list,
                     List<ProductBomVO> productBomVOList,
                     List<ProductBomVersionVO> productBomVersionVOList) {

        Map<String, List<ProductBomVO>> productBomGroup = productBomVOList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getIoProductCode()) &&
                        (item.getProductArea() != null ||
                                (item.getProductLength() != null &&
                                        item.getProductWidth() != null)))
                .collect(Collectors.groupingBy(ProductBomVO::getIoProductCode));

        Map<String, ProductBomVersionVO> productBomVersionVOMap = productBomVersionVOList.stream()
                .filter(item -> item.getProductArea() != null ||
                        (item.getProductLength() != null && item.getProductWidth() != null))
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));

        // 获取本厂编码
        List<String> productCodeList = list.stream()
                .map(GlassSubstitutionRelationshipDTO::getProductCode)
                .distinct().collect(Collectors.toList());

        // 查询本厂编码
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("stock_point_code", "product_code", "product_name", "product_length", "product_width", "product_area"))
                .queryParam(ImmutableMap.of("productCodeList", productCodeList))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        newProductStockPointVOS = newProductStockPointVOS.stream()
                .filter(item -> null != item.getProductArea())
                .collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productStockPointVOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)){
            productStockPointVOMap = newProductStockPointVOS.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        }
        // 获取产品的工艺基础数据
        List<MdsProductStockPointBaseVO> productStockPointBaseVOS = newMdsFeign
                .selectProductStockPointBasicVOByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodeList", productCodeList));
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseVOMap = productStockPointBaseVOS.stream()
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        // ERP-BOM和替代料的切裁率
        Map<String, List<String>> rawProductCodeToSubstituteProductCodes = list.stream()
                .collect(Collectors.groupingBy(
                        GlassSubstitutionRelationshipDTO::getRawProductCode,
                        Collectors.mapping(
                                GlassSubstitutionRelationshipDTO::getSubstituteProductCode,
                                Collectors.toList())));

        // ERP-BOM和生产BOM的切裁率
        Map<String, List<String>> rawProductCodeToProductionProductCodes = list.stream()
                .collect(Collectors.groupingBy(
                        GlassSubstitutionRelationshipDTO::getRawProductCode,
                        Collectors.mapping(
                                GlassSubstitutionRelationshipDTO::getProductionSubstituteProductCode,
                                Collectors.toList())));

        List<InventoryAlternativeRelationshipVO> substituteProductCuttingRateList = inventoryAlternativeRelationshipService.
                getInventoryAlternativeRelationshipVO(rawProductCodeToSubstituteProductCodes, productBomVersionVOMap, productBomGroup);

        List<InventoryAlternativeRelationshipVO> productionProductCuttingRateList = inventoryAlternativeRelationshipService.
                getInventoryAlternativeRelationshipVO(rawProductCodeToProductionProductCodes, productBomVersionVOMap, productBomGroup);

        Map<String, InventoryAlternativeRelationshipVO> subtituteRelationshipMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(substituteProductCuttingRateList)){
            subtituteRelationshipMap = substituteProductCuttingRateList.stream()
                    .collect(Collectors.toMap(item -> String.join("#", item.getDemandProductCode(), item.getReplacedProductCode()),
                            Function.identity(), (k1, k2) -> k2));
        }
        Map<String, InventoryAlternativeRelationshipVO> productionelationshipMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productionProductCuttingRateList)){
            productionelationshipMap = productionProductCuttingRateList.stream()
                    .collect(Collectors.toMap(item -> String.join("#", item.getDemandProductCode(), item.getReplacedProductCode()),
                            Function.identity(), (k1, k2) -> k2));
        }

        for (GlassSubstitutionRelationshipDTO dto : list) {
            // 维护淋子方向
            MdsProductStockPointBaseVO productStockPointBaseVO = productStockPointBaseVOMap.get(dto.getProductCode());
            assembleLinziDirection(dto, productStockPointBaseVO);
            if (productStockPointVOMap.containsKey(dto.getProductCode())) {
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(dto.getProductCode());
                dto.setProductName(newProductStockPointVO.getProductName());
                dto.setProductSize(null != newProductStockPointVO.getProductArea() ? newProductStockPointVO.getProductArea().toString() : null);
                dto.setProductArea(newProductStockPointVO.getProductArea());
            }
            if (StringUtils.isBlank(dto.getColorThickness())) {
                NewProductStockPointVO productAttribute = getProductAttribute(dto.getSubstituteProductCode());
                dto.setColorThickness(null != productAttribute ?
                        productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) + productAttribute.getProductColor() : null);
            }

            String joinKey = String.join("#", dto.getRawProductCode(), dto.getSubstituteProductCode());
            String joinKey2 = String.join("#", dto.getRawProductCode(), dto.getProductionSubstituteProductCode());
            BigDecimal rawProductArea = BigDecimal.ZERO;
            if (subtituteRelationshipMap.containsKey(joinKey)) {
                InventoryAlternativeRelationshipVO substituteRelationshipVO = subtituteRelationshipMap.get(joinKey);
                dto.setColorThickness(null != substituteRelationshipVO.getReplacedProductColor() && null != substituteRelationshipVO.getReplacedProductThickness() ?
                        substituteRelationshipVO.getReplacedProductThickness() + substituteRelationshipVO.getReplacedProductColor() : null);
                dto.setCuttingRate(substituteRelationshipVO.getCuttingRate());
                dto.setBlankSpec(null != substituteRelationshipVO.getBlankProductLength() && null != substituteRelationshipVO.getBlankProductWidth() ?
                        StringUtils.join(substituteRelationshipVO.getBlankProductLength(), "*", substituteRelationshipVO.getBlankProductWidth()) : null);

                dto.setBlankInputFactor(substituteRelationshipVO.getBlankInputFactor());

                // ERP-BOM(一切几毛坯)
                if (null == dto.getGlassInputFactor()){
                    NewProductStockPointVO productAttribute = getProductAttribute(dto.getRawProductCode());
                    if (null != productAttribute) {
                        rawProductArea = productAttribute.getProductLength()
                                .multiply(productAttribute.getProductWidth()).divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP);
                        BigDecimal glassInputFactor = getInputFactor(substituteRelationshipVO, productAttribute.getProductLength(), productAttribute.getProductWidth());
                        dto.setGlassInputFactor(glassInputFactor);
                    }
                }


                // 替代BOM(一切几毛坯)
                // 获取替代料的毛坯
                if (null == dto.getSubstituteInputFactor()){
                    NewProductStockPointVO productAttribute = getProductAttribute(dto.getSubstituteProductCode());
                    if (null != productAttribute){
                        BigDecimal substituteInputFactor = getInputFactor(substituteRelationshipVO, productAttribute.getProductLength(), productAttribute.getProductWidth());
                        dto.setSubstituteInputFactor(substituteInputFactor);
                    }
                }


                // 生产BOM(一切几毛坯)
                if (null == dto.getProductionInputFactor()){
                    NewProductStockPointVO productAttribute = getProductAttribute(dto.getProductionSubstituteProductCode());
                    if (null != productAttribute){
                        BigDecimal productionInputFactor = getInputFactor(substituteRelationshipVO, productAttribute.getProductLength(), productAttribute.getProductWidth());
                        dto.setProductionInputFactor(productionInputFactor);
                    }
                }
            }
            // 计算切裁率
            // 产品面积÷ERP-BOM原片面积÷ERP-BOM一切几×毛坯单耗
            if (null != dto.getProductArea()
                    && rawProductArea.compareTo(BigDecimal.ZERO) != 0 &&
                    null != dto.getGlassInputFactor() &&
                    dto.getGlassInputFactor().compareTo(BigDecimal.ZERO) != 0 &&
                    null != dto.getBlankInputFactor()){
                BigDecimal value = rawProductArea.divide(dto.getGlassInputFactor(), RoundingMode.HALF_UP)
                        .multiply(dto.getBlankInputFactor());

                BigDecimal rawProductCuttingRate = dto.getProductArea().divide(value, RoundingMode.HALF_UP);
                dto.setRawProductCuttingRate(rawProductCuttingRate);
            }

            if (productionelationshipMap.containsKey(joinKey2)){
                InventoryAlternativeRelationshipVO productionRelationshipVO = productionelationshipMap.get(joinKey2);
                dto.setProductionCuttingRate(productionRelationshipVO.getCuttingRate());
            }
        }
    }

    private BigDecimal getInputFactor(InventoryAlternativeRelationshipVO alternativeRelationshipVO, BigDecimal productLength, BigDecimal productWidth) {
        BigDecimal length = BigDecimal.ZERO;
        BigDecimal width = BigDecimal.ZERO;
        if (null != alternativeRelationshipVO.getBlankProductLength() && null != productLength && productLength.compareTo(BigDecimal.ZERO) != 0){
            // 长 / 长
//            length = alternativeRelationshipVO.getBlankProductLength().divide(productLength, 0, RoundingMode.DOWN);
            length = productLength.divide(alternativeRelationshipVO.getBlankProductLength(), 0, RoundingMode.DOWN);
        }
        if (null != alternativeRelationshipVO.getBlankProductWidth() && null != productWidth && productWidth.compareTo(BigDecimal.ZERO) != 0){
            // 宽 / 宽
            width = productWidth.divide(alternativeRelationshipVO.getBlankProductWidth(), 0, RoundingMode.DOWN);
//            width = alternativeRelationshipVO.getBlankProductWidth().divide(productWidth, 0, RoundingMode.DOWN);
        }
        return length.multiply(width);
    }

    private void getGlassSubstitutionRelationshipByGrossDemand(List<GlassSubstitutionRelationshipDTO> originalList,
                                                               List<GlassSubstitutionRelationshipDTO> totalExportDtoList) {
        // 查询最新的毛需求版本
//        MaterialGrossDemandVersionVO materialGrossDemandVersionVO = materialGrossDemandVersionService.selectLastVersion();
        Map<String, Object> params = new HashMap<>();
//        params.put("materialGrossDemandVersionId", materialGrossDemandVersionVO.getId());
        params.put("productCategory", "RA.A");
        params.put("demandSourceList", Lists.newArrayList(MrpDemandSourceEnum.MPS.getCode(), MrpDemandSourceEnum.ZZK.getCode(),
                MrpDemandSourceEnum.MCB.getCode(), MrpDemandSourceEnum.MANUAL_ADDITION_DEMAND.getCode(), MrpDemandSourceEnum.OUTSOURCE_TRANSFER.getCode()));
        // 查询毛需求
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandService.selectByParams(params);
        Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfProductCode = materialGrossDemandVOList.stream()
                .collect(Collectors.groupingBy(MaterialGrossDemandVO::getProductCode));
        // 适用于自动生成
        if (CollectionUtils.isEmpty(originalList)) {
            for (Map.Entry<String, List<MaterialGrossDemandVO>> entry : grossDemandGroupOfProductCode.entrySet()) {
                List<String> productFactoryCodeList = entry.getValue().stream()
                        .map(MaterialGrossDemandVO::getProductFactoryCode).distinct().collect(Collectors.toList());
                for (String productFactoryCode : productFactoryCodeList) {
                    GlassSubstitutionRelationshipDTO relationshipDTO = GlassSubstitutionRelationshipDTO.builder()
                            .id(UUID.randomUUID().toString())
                            .rawProductCode(entry.getKey())
                            .rawProductName(entry.getValue().get(0).getProductName())
                            .productCode(productFactoryCode)
                            .build();
                    totalExportDtoList.add(relationshipDTO);
                }
            }
            return;
        }

        // 适用于导入
        Map<String, List<GlassSubstitutionRelationshipDTO>> originDateGroup = originalList.stream()
                .collect(Collectors.groupingBy(GlassSubstitutionRelationshipDTO::getRawProductCode));
        for (Map.Entry<String, List<GlassSubstitutionRelationshipDTO>> entry : originDateGroup.entrySet()) {
            List<GlassSubstitutionRelationshipDTO> dtoList = entry.getValue();
            // 获取导入数据的本厂编码
            List<String> importFactoryCodeList = dtoList.stream()
                    .map(GlassSubstitutionRelationshipDTO::getProductCode)
                    .distinct().collect(Collectors.toList());

            List<MaterialGrossDemandVO> grossDemandVOS = grossDemandGroupOfProductCode.get(entry.getKey());
            if (CollectionUtils.isEmpty(grossDemandVOS)){
                continue;
            }
            // 获取本厂编码
            // 计算差值（factory中有但import中没有的元素）
            List<String> differenceList = grossDemandVOS.stream()
                    .map(MaterialGrossDemandVO::getProductFactoryCode)
                    .distinct().collect(Collectors.toList());
            differenceList.removeAll(importFactoryCodeList);
            if (CollectionUtils.isEmpty(differenceList)){
                continue;
            }
            for (String factoryCode : differenceList) {
                for (GlassSubstitutionRelationshipDTO dto : dtoList) {
                    if (StringUtils.equals(factoryCode, dto.getProductCode())){
                       continue;
                    }
                    GlassSubstitutionRelationshipDTO relationshipDTO = GlassSubstitutionRelationshipDTO.builder()
                            .id(UUID.randomUUID().toString())
                            .stockPointCode(dto.getStockPointCode())
                            .rawProductCode(dto.getRawProductCode())
                            .rawProductName(dto.getRawProductName())
                            .substituteProductCode(dto.getSubstituteProductCode())
                            .substituteProductName(dto.getSubstituteProductName())
                            .productionSubstituteProductCode(dto.getProductionSubstituteProductCode())
                            .productionSubstituteProductName(dto.getProductionSubstituteProductName())
                            .productionInputFactor(dto.getProductionInputFactor())
                            .substituteInputFactor(dto.getSubstituteInputFactor())
                            .productCode(factoryCode)
                            .build();
                    totalExportDtoList.add(relationshipDTO);
                }
            }
        }
    }

    private void getGlassSubstitutionRelationshipByInventoryAlternativeRelationship(List<GlassSubstitutionRelationshipDTO> notSubstituteProductCodeList,
                                                                                    List<GlassSubstitutionRelationshipDTO> totalList,
                                                                                    Map<String, NewProductStockPointVO> productStockPointVOMap,
                                                                                    Map<String, MdsProductStockPointBaseVO> productStockPointBaseVOMap) {
        // 获取bomVersion
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), new HashMap<>());
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), new HashMap<>());

        Map<String, ProductBomVO> productBomVOMap = productBomVOList.stream()
                .collect(Collectors.toMap(ProductBomVO::getIoProductCode, Function.identity(), (k1, k2) -> k2));

        Map<String, List<ProductBomVO>> productBomGroup = productBomVOList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getIoProductCode()) &&
                        (item.getProductArea() != null ||
                                (item.getProductLength() != null &&
                                        item.getProductWidth() != null)))
                .collect(Collectors.groupingBy(ProductBomVO::getIoProductCode));

        Map<String, ProductBomVersionVO> productBomVersionVOMap = productBomVersionVOList.stream()
                .filter(item -> item.getProductArea() != null ||
                        (item.getProductLength() != null && item.getProductWidth() != null))
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));

        Map<String, List<InventoryAlternativeRelationshipVO>> relationshipMapOfRawProductCode = inventoryAlternativeRelationshipService.batchBacklogReplace
                (notSubstituteProductCodeList.stream().map(GlassSubstitutionRelationshipDTO::getRawProductCode)
                        .distinct().collect(Collectors.toList()), "0.8", productBomVersionVOMap, productBomGroup);

        for (GlassSubstitutionRelationshipDTO glassSubstitutionRelationshipDTO : notSubstituteProductCodeList) {
            if (!relationshipMapOfRawProductCode.containsKey(glassSubstitutionRelationshipDTO.getRawProductCode())) {
                totalList.add(glassSubstitutionRelationshipDTO);
                continue;
            }
            List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOS = relationshipMapOfRawProductCode.get(glassSubstitutionRelationshipDTO.getRawProductCode());
            for (InventoryAlternativeRelationshipVO relationshipVO : inventoryAlternativeRelationshipVOS) {
                GlassSubstitutionRelationshipDTO glassSubstitutionDto = GlassSubstitutionRelationshipDTO.builder()
                        .id(UUID.randomUUID().toString())
                        .rawProductCode(glassSubstitutionRelationshipDTO.getRawProductCode())
                        .rawProductName(glassSubstitutionRelationshipDTO.getRawProductName())
                        .substituteProductCode(relationshipVO.getReplacedProductCode())
                        .substituteProductName(relationshipVO.getReplacedProductName())
                        .productCode(glassSubstitutionRelationshipDTO.getProductCode())
                        .colorThickness(null != relationshipVO.getReplacedProductColor() && null != relationshipVO.getReplacedProductThickness() ?
                                relationshipVO.getReplacedProductThickness() + relationshipVO.getReplacedProductColor() : null)
                        .cuttingRate(relationshipVO.getCuttingRate())
                        .rule(glassSubstitutionRelationshipDTO.getRule())
                        .blankSpec(null != relationshipVO.getBlankProductLength() && null != relationshipVO.getBlankProductWidth() ?
                                StringUtils.join(relationshipVO.getBlankProductLength(), "*", relationshipVO.getBlankProductWidth()) : null)
                        .blankInputFactor(relationshipVO.getBlankInputFactor())
                        .glassInputFactor(relationshipVO.getGlassInputFactor())
                        .recommendSubstitute(YesOrNoEnum.YES.getCode())
                        .enabled(YesOrNoEnum.NO.getCode())
                        .build();
                MdsProductStockPointBaseVO productStockPointBaseVO = productStockPointBaseVOMap.get(glassSubstitutionRelationshipDTO.getProductCode());
                // 维护淋子方向
                assembleLinziDirection(glassSubstitutionDto, productStockPointBaseVO);
                BigDecimal productArea = BigDecimal.ZERO;
                if (productStockPointVOMap.containsKey(glassSubstitutionDto.getProductCode())) {
                    NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(glassSubstitutionDto.getProductCode());
                    glassSubstitutionDto.setProductName(newProductStockPointVO.getProductName());
                    glassSubstitutionDto.setProductSize(null != newProductStockPointVO.getProductArea() ? newProductStockPointVO.getProductArea().toString() : null);
                    productArea = newProductStockPointVO.getProductArea();
                }
                if (StringUtils.isBlank(glassSubstitutionDto.getColorThickness())) {
                    NewProductStockPointVO productAttribute = getProductAttribute(glassSubstitutionDto.getSubstituteProductCode());
                    glassSubstitutionDto.setColorThickness(null != productAttribute ?
                            productAttribute.getProductThickness().setScale(1, RoundingMode.DOWN) + productAttribute.getProductColor() : null);
                }


                // ERP-BOM面积
                BigDecimal rawProductArea = BigDecimal.ZERO;
                NewProductStockPointVO rawProductAttribute = getProductAttribute(glassSubstitutionDto.getRawProductCode());
                if (null != rawProductAttribute) {
                    rawProductArea = rawProductAttribute.getProductLength()
                            .multiply(rawProductAttribute.getProductWidth()).divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP);
                    BigDecimal glassInputFactor = getInputFactor(relationshipVO, rawProductAttribute.getProductLength(), rawProductAttribute.getProductWidth());
                    // ERP-BOM(一切几毛坯)
                    glassSubstitutionDto.setGlassInputFactor(glassInputFactor);
                }

                // 替代BOM(一切几毛坯)
                // 获取替代料的毛坯
                NewProductStockPointVO substitutionProductAttribute = getProductAttribute(glassSubstitutionDto.getSubstituteProductCode());
                if (StringUtils.isNotBlank(substitutionProductAttribute.getProductCode())){
                    BigDecimal substituteInputFactor = getInputFactor(relationshipVO, substitutionProductAttribute.getProductLength(), substitutionProductAttribute.getProductWidth());
                    glassSubstitutionDto.setSubstituteInputFactor(substituteInputFactor);
                }

                if (productBomVOMap.containsKey(glassSubstitutionDto.getRawProductCode())) {
                    glassSubstitutionDto.setStockPointCode(productBomVOMap.get(glassSubstitutionDto.getRawProductCode()).getIoStockPointCode());
                }

                //ERP切裁率 = 产品面积÷（ERP-BOM原片面积÷ERP-BOM一切几×毛坯单耗）
                if (null != productArea
                        && rawProductArea.compareTo(BigDecimal.ZERO) != 0 &&
                        null != glassSubstitutionDto.getGlassInputFactor() &&
                        glassSubstitutionDto.getGlassInputFactor().compareTo(BigDecimal.ZERO) != 0 &&
                        null != glassSubstitutionDto.getBlankInputFactor()){

                    BigDecimal value = rawProductArea.divide(glassSubstitutionDto.getGlassInputFactor(), RoundingMode.HALF_UP)
                            .multiply(glassSubstitutionDto.getBlankInputFactor());
                    BigDecimal rawProductCuttingRate = productArea.divide(value, RoundingMode.HALF_UP);
                    glassSubstitutionDto.setRawProductCuttingRate(rawProductCuttingRate);
                }
                totalList.add(glassSubstitutionDto);
            }
        }
    }

    private void assembleLinziDirection(GlassSubstitutionRelationshipDTO glassSubstitutionDto,
                                        MdsProductStockPointBaseVO productStockPointBaseVO){
        glassSubstitutionDto.setLinziDirection("无");
        if (null != productStockPointBaseVO){
            if (StringUtils.equals("1", productStockPointBaseVO.getItemType())){
                glassSubstitutionDto.setLinziDirection(productStockPointBaseVO.getSprinkleDirection());
            }
            if (StringUtils.equals("2", productStockPointBaseVO.getItemType())) {
                if (StringUtils.isNotBlank(productStockPointBaseVO.getOneSprinkleDirection())){
                    glassSubstitutionDto.setLinziDirection(productStockPointBaseVO.getOneSprinkleDirection());
                }else if (StringUtils.isNotBlank(productStockPointBaseVO.getTwoSprinkleDirection())){
                    glassSubstitutionDto.setLinziDirection(productStockPointBaseVO.getTwoSprinkleDirection());
                }else if (StringUtils.isNotBlank(productStockPointBaseVO.getThreeSprinkleDirection())){
                    glassSubstitutionDto.setLinziDirection(productStockPointBaseVO.getThreeSprinkleDirection());
                }else if (StringUtils.isNotBlank(productStockPointBaseVO.getFourSprinkleDirection())){
                    glassSubstitutionDto.setLinziDirection(productStockPointBaseVO.getFourSprinkleDirection());
                }
            }
        }
    }

    /**
     * 根据物料（原片）编码 截取获取物料属性
     *
     * @param productCode
     * @return
     */
    public static NewProductStockPointVO getProductAttribute(String productCode) {
        if (productCode == null || productCode.length() < 12) {
            return null;
        }
        String trimmed = productCode.substring(3);
        BigDecimal productLength = new BigDecimal(trimmed.substring(0, 4));
        BigDecimal productWidth = new BigDecimal(trimmed.substring(4, 8));
        BigDecimal productThickness = new BigDecimal(trimmed.substring(9, 10) + "." + trimmed.substring(10, 11));
        String productColor = trimmed.substring(12);

        return NewProductStockPointVO.builder()
                .productCode(productCode)
                .productLength(productLength)
                .productWidth(productWidth)
                .productThickness(productThickness)
                .productColor(productColor).build();
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "原片替代映射导入模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("工厂"));
        headers.add(Collections.singletonList("本厂编码*"));
        headers.add(Collections.singletonList("ERP-BOM*"));
        headers.add(Collections.singletonList("ERP-BOM(一切几毛坯)"));
        headers.add(Collections.singletonList("生产BOM*"));
        headers.add(Collections.singletonList("生产BOM(一切几毛坯)"));
        headers.add(Collections.singletonList("替代BOM*"));
        headers.add(Collections.singletonList("替代BOM(一切几毛胚)"));

        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public void syncGlassSubstitutionRelationshipByBom(String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<GlassSubstitutionRelationshipDTO> insertList = new ArrayList<>();
        // 查询原片映射数据
        List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao.selectByParams(new HashMap<>());

        // 查询原片对应的成品
        List<String> rawProductCodeList = glassSubstitutionRelationshipPOS.stream()
                .map(GlassSubstitutionRelationshipPO::getRawProductCode).distinct()
                .collect(Collectors.toList());

        Map<String, List<String>> factoryProductCodeListToRawProductCode = getProductFactoryCodeToRawProductCode(scenario, rawProductCodeList);
        if (MapUtils.isEmpty(factoryProductCodeListToRawProductCode)) {
            return;
        }
        List<String> allValues = factoryProductCodeListToRawProductCode.values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "product_code", "product_name"))
                .queryParam(ImmutableMap.of("productCodeList", allValues))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam);
        Map<String, String> productNameToCode = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getProductName, (k1, k2) -> k2));


        // 原片替代映射按照rawProductCode分组
        Map<String, List<GlassSubstitutionRelationshipPO>> glassSubstitutionRelationshipGroupOfRwaProductCode = glassSubstitutionRelationshipPOS.stream()
                .collect(Collectors.groupingBy(GlassSubstitutionRelationshipPO::getRawProductCode));
        for (Map.Entry<String, List<GlassSubstitutionRelationshipPO>> entry : glassSubstitutionRelationshipGroupOfRwaProductCode.entrySet()) {
            // 获取该原片的成品数据
            List<String> factoryProductCodeList = factoryProductCodeListToRawProductCode.get(entry.getKey());
            if (CollectionUtils.isEmpty(factoryProductCodeList)) {
                continue;
            }

            List<GlassSubstitutionRelationshipPO> value = entry.getValue();
            // 按照成品编码分组
            Map<String, List<GlassSubstitutionRelationshipPO>> glassSubstitutionRelationshipGroupOfProductCode = value.stream()
                    .collect(Collectors.groupingBy(GlassSubstitutionRelationshipPO::getProductCode));
            Set<String> productCodeSet = glassSubstitutionRelationshipGroupOfProductCode.keySet();
            // 比较factoryProductCodeList和productCodeSet的差值
            List<String> differenceList = factoryProductCodeList.stream()
                    .filter(element -> !productCodeSet.contains(element))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(differenceList)) {
                continue;
            }
            // 生成数据
            for (String factoryProductCode : differenceList) {
                List<GlassSubstitutionRelationshipPO> relationshipPOS = glassSubstitutionRelationshipGroupOfProductCode.get(productCodeSet.iterator().next());
                for (GlassSubstitutionRelationshipPO relationshipPO : relationshipPOS) {
                    GlassSubstitutionRelationshipDTO relationshipDTO = GlassSubstitutionRelationshipDTO.builder()
                            .id(UUID.randomUUID().toString())
                            .stockPointCode(relationshipPO.getProductCode())
                            .rawProductCode(relationshipPO.getRawProductCode())
                            .substituteProductCode(relationshipPO.getSubstituteProductCode())
                            .substituteProductName(relationshipPO.getSubstituteProductName())
                            .productCode(factoryProductCode)
                            .productName(productNameToCode.get(factoryProductCode))
                            .colorThickness(relationshipPO.getColorThickness())
                            .blankSpec(relationshipPO.getBlankSpec())
                            .rule(relationshipPO.getRule())
                            .productSize(relationshipPO.getProductSize())
                            .linziDirection(relationshipPO.getLinziDirection())
                            .blankInputFactor(relationshipPO.getBlankInputFactor())
                            .glassInputFactor(relationshipPO.getGlassInputFactor())
                            .cuttingRate(relationshipPO.getCuttingRate())
                            .recommendSubstitute(relationshipPO.getRecommendSubstitute())
                            .build();
                    insertList.add(relationshipDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.doCreateBatch(insertList);
        }
    }

    private Map<String, List<String>> getProductFactoryCodeToRawProductCode(String scenario, List<String> rawProductCodeList) {
        Map<String, List<String>> result = new HashMap<>();

        // 查询产品信息，获取 productId 与 rawProductCode 的映射
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("id", "product_code"))
                .queryParam(ImmutableMap.of("productCodeList", rawProductCodeList))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam);
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            return null;
        }
        Map<String, String> productIdToRawCode = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, NewProductStockPointVO::getProductCode));

        // 查询产品 BOM 信息
        List<String> productIdList = newProductStockPointVOS.stream()
                .map(NewProductStockPointVO::getId)
                .collect(Collectors.toList());
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(scenario, ImmutableMap.of("ioProductIdList", productIdList));
        if (CollectionUtils.isEmpty(productBomVOList)) {
            return null;
        }

        // 获取 BOM 版本信息，并建立映射
        List<String> bomVersionIdList = productBomVOList.stream()
                .map(ProductBomVO::getBomVersionId)
                .distinct()
                .collect(Collectors.toList());
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(scenario, ImmutableMap.of("ids", bomVersionIdList));
        if (CollectionUtils.isEmpty(productBomVersionVOList)) {
            return null;
        }
        Map<String, ProductBomVersionVO> bomVersionMap = productBomVersionVOList.stream()
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));

        for (ProductBomVO bom : productBomVOList) {
            String ioProductId = bom.getIoProductId();
            String rawProductCode = productIdToRawCode.get(ioProductId);
            if (rawProductCode == null) {
                continue;
            }

            ProductBomVersionVO version = bomVersionMap.get(bom.getBomVersionId());
            if (version == null) {
                continue;
            }

            String factoryCode = version.getProductCode();
            result.computeIfAbsent(factoryCode, k -> new ArrayList<>()).add(rawProductCode);
        }
        return result;
    }

    @Override
    public List<LabelValue<String>> dropDownSubstituteProduct(List<String> rawProductCodeList) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<String> list = mrpNewProductStockPointDao.selectGlassProductCodes();
        if (CollectionUtils.isNotEmpty(list)){
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x);
                labelValue.setValue(x);
                result.add(labelValue);
            });
        }

//        List<String> substituteProductCodeList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(rawProductCodeList)){
//            // 查询原片映射表
//            List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao.selectByParams(
//                    ImmutableMap.of("rawProductCodeList", rawProductCodeList));
//            if (CollectionUtils.isNotEmpty(glassSubstitutionRelationshipPOS)){
//                substituteProductCodeList = glassSubstitutionRelationshipPOS.stream()
//                        .map(GlassSubstitutionRelationshipPO::getSubstituteProductCode)
//                        .distinct().collect(Collectors.toList());
//            }
//        }else {
//            // 查询库存
//            substituteProductCodeList = getSubstituteProductListByInventory();
//        }
//        if (CollectionUtils.isNotEmpty(substituteProductCodeList)) {
//            substituteProductCodeList.forEach(x -> {
//                LabelValue<String> labelValue = new LabelValue<>();
//                labelValue.setLabel(x);
//                labelValue.setValue(x);
//                result.add(labelValue);
//            });
//        }
        return result;
    }

    @Override
    public List<String> getSubstituteProductListByInventory(){
        String scenario = SystemHolder.getScenario();
        List<String> result = new ArrayList<>();

        //1.查询原片本厂库存(实时库存)
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectAllGlassInventoryBatch(scenario);

        //2.获取码头库存
        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));

        //3.浮法已发运库存
        List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOS = inventoryFloatGlassShippedDetailService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));

        //4.浮法库存
        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOS = inventoryFloatGlassDetailService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)){
            List<String> productCodeList = inventoryBatchDetailVOList.stream()
                    .map(InventoryBatchDetailVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            result.addAll(productCodeList);
        }
        if (CollectionUtils.isNotEmpty(inventoryQuayDetailVOS)){
            List<String> productCodeList = inventoryQuayDetailVOS.stream()
                    .map(InventoryQuayDetailVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            result.addAll(productCodeList);
        }
        if (CollectionUtils.isNotEmpty(floatGlassShippedDetailVOS)){
            List<String> productCodeList = floatGlassShippedDetailVOS.stream()
                    .map(InventoryFloatGlassShippedDetailVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            result.addAll(productCodeList);
        }
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassDetailVOS)){
            List<String> productCodeList = inventoryFloatGlassDetailVOS.stream()
                    .map(InventoryFloatGlassDetailVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            result.addAll(productCodeList);
        }
        return CollectionUtils.isNotEmpty(result) ? result.stream().distinct().collect(Collectors.toList()) : result;
    }

}
