package com.yhl.scp.mrp.inventory.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.inventory.convertor.SafetyInventoryConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.SafetyInventoryDO;
import com.yhl.scp.mrp.inventory.domain.service.SafetyInventoryDomainService;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryDTO;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryExportDTO;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryRiskDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.SafetyInventoryDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.SafetyInventoryPO;
import com.yhl.scp.mrp.inventory.service.SafetyInventoryService;
import com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO;
import com.yhl.scp.mrp.risk.service.MaterialRiskLevelRuleService;
import com.yhl.scp.mrp.risk.vo.MaterialRiskLevelRuleVO;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MrpSafetyInventoryServiceImpl</code>
 * <p>
 * 材料安全库存应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 10:25:16
 */
@Slf4j
@Service
public class SafetyInventoryServiceImpl extends AbstractService implements SafetyInventoryService {

    @Resource
    private SafetyInventoryDao safetyInventoryDao;

    @Resource
    private SafetyInventoryDomainService safetyInventoryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MaterialRiskLevelRuleService riskLevelRuleParamsService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsFeign mpsFeign;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SafetyInventoryDTO safetyInventoryDTO) {
        // 0.数据转换
        SafetyInventoryDO safetyInventoryDO = SafetyInventoryConvertor.INSTANCE.dto2Do(safetyInventoryDTO);
        SafetyInventoryPO safetyInventoryPO = SafetyInventoryConvertor.INSTANCE.dto2Po(safetyInventoryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        safetyInventoryDomainService.validation(safetyInventoryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(safetyInventoryPO);
        safetyInventoryDao.insert(safetyInventoryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SafetyInventoryDTO safetyInventoryDTO) {
        // 0.数据转换
        SafetyInventoryDO safetyInventoryDO = SafetyInventoryConvertor.INSTANCE.dto2Do(safetyInventoryDTO);
        SafetyInventoryPO safetyInventoryPO = SafetyInventoryConvertor.INSTANCE.dto2Po(safetyInventoryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        safetyInventoryDomainService.validation(safetyInventoryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(safetyInventoryPO);
        safetyInventoryDao.update(safetyInventoryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SafetyInventoryDTO> list) {
        List<SafetyInventoryPO> newList = SafetyInventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        safetyInventoryDao.insertBatch(newList);
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<SafetyInventoryDTO> list) {
        List<SafetyInventoryPO> newList = SafetyInventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        safetyInventoryDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<SafetyInventoryDTO> list) {
        List<SafetyInventoryPO> newList = SafetyInventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        safetyInventoryDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<SafetyInventoryDTO> list) {
        List<SafetyInventoryPO> newList = SafetyInventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        safetyInventoryDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return safetyInventoryDao.deleteBatch(idList);
        }
        return safetyInventoryDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SafetyInventoryVO selectByPrimaryKey(String id) {
        SafetyInventoryPO po = safetyInventoryDao.selectByPrimaryKey(id);
        return SafetyInventoryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MRP_SAFETY_INVENTORY")
    public List<SafetyInventoryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MRP_SAFETY_INVENTORY")
    public List<SafetyInventoryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SafetyInventoryVO> dataList = safetyInventoryDao.selectByCondition(sortParam, queryCriteriaParam);
        SafetyInventoryServiceImpl target = springBeanUtils.getBean(SafetyInventoryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SafetyInventoryVO> selectByParams(Map<String, Object> params) {
        List<SafetyInventoryPO> list = safetyInventoryDao.selectByParams(params);
        return SafetyInventoryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SafetyInventoryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void export(HttpServletResponse response) {
        List<SafetyInventoryPO> safetyInventoryPOS = safetyInventoryDao.selectByParams(new HashMap<>(2));
        List<SafetyInventoryExportDTO> safetyInventoryExportDTOS = SafetyInventoryConvertor.INSTANCE.po2ExportDtos(safetyInventoryPOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("材料安全库存.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), SafetyInventoryExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("材料安全库存")
                    .doWrite(safetyInventoryExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 模板导出
     */
    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {

        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "材料安全库存模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("库存点代码"));
        headers.add(Collections.singletonList("材料代码"));
        headers.add(Collections.singletonList("最小库存天数"));
        headers.add(Collections.singletonList("最大库存天数"));
        headers.add(Collections.singletonList("最小库存数量"));
        headers.add(Collections.singletonList("最大库存数量"));
        headers.add(Collections.singletonList("目标安全库存天数(中间线)"));
        headers.add(Collections.singletonList("目标安全库存数量"));
        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public void checkMinMax(SafetyInventoryDTO safetyInventoryDTO) {
        if (safetyInventoryDTO == null) {
            throw new BusinessException("请传入数据后再试~");
        }
        if (safetyInventoryDTO.getMaxInventoryDays() != null) {
            if (safetyInventoryDTO.getMinInventoryDays().compareTo(safetyInventoryDTO.getMaxInventoryDays()) > 0) {
                throw new BusinessException("最小库存天数不能大于最大库存天数");
            }
        }
        if (safetyInventoryDTO.getMaxInventoryQty() != null) {
            if (safetyInventoryDTO.getMinInventoryQty().compareTo(safetyInventoryDTO.getMaxInventoryQty()) > 0) {
                throw new BusinessException("最小库存数量不能大于最大库存数量");
            }
        }
    }

    /**
     * 查询库存点代码
     *
     * @return
     */
    @Override
    public List<LabelValue<String>> selectStockPointCode() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES);
        String scenario = SystemHolder.getScenario();
//        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario, params);
        if (newStockPointVOS == null) {
            return Collections.emptyList();
        }
        return newStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getStockPointName(), x.getStockPointCode()))
                .collect(Collectors.toList());
    }

    /**
     * 查询物料代码
     *
     * @param stockPointCode
     * @return
     */
    @Override
    public List<LabelValue<String>> selectMaterialCode(String stockPointCode, String materialCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("stockPointCode", stockPointCode);
        if (StringUtils.isNotEmpty(materialCode)) {
            params.put("materialCodeLike", StringConvertUtils.convertToLike(materialCode));
        }
        String scenario = SystemHolder.getScenario();
//        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "product_name"))
                .queryParam(params)
                .build();

        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam);

//        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario.getData(), params);
        if (newProductStockPointVOS == null) {
            return Collections.emptyList();
        }
        return newProductStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getProductCode() + "(" + x.getProductName() + ")", x.getProductCode()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> pointDown(String stockPointCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("stockPointCode", stockPointCode);
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> newStockPointVOS =
                newMdsFeign.selectStockPointByParams(scenario.getData(), params);
        if (newStockPointVOS == null) {
            return Collections.emptyList();
        }
        return newStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getStockPointName() + "（" + x.getStockPointCode() + "）", x.getId()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void riskCalculate(List<String> ids) {
        //有勾选数据，就更新勾选数据的，没有查询全部的物料信息
        List<SafetyInventoryPO> safetyInventoryPOS = new ArrayList<>();
        List<String> productCodeList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            safetyInventoryPOS = safetyInventoryDao.selectByPrimaryKeys(ids);
            productCodeList = safetyInventoryPOS.stream().map(SafetyInventoryPO::getMaterialCode).collect(Collectors.toList());
        }
        String scenario = SystemHolder.getScenario();
//        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), "shanghai");
//        String defaultScenario = defaultScenario1.getData();
//        BaseResponse<String> defaultScenario2 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
//        String scenario2 = defaultScenario2.getData();
//        BaseResponse<String> defaultScenario3 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
//        String scenario3 = defaultScenario3.getData();

        // 查询所有可用规则
        params.put("enabled", YesOrNoEnum.YES);
        List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS = riskLevelRuleParamsService.selectByParams(params);
        if (CollectionUtils.isEmpty(riskLevelRuleParamsVOS)) {
            throw new BusinessException("没有规则，无法计算风险等级");
        }
        Map<String, List<MaterialRiskLevelRuleVO>> materialTypeMap = riskLevelRuleParamsVOS.stream().collect(Collectors.groupingBy(MaterialRiskLevelRuleVO::getMaterialType));
        // 获取物料主数据
        params = new HashMap<>();
        params.put("productType", "材料");// 这里等数据字典定义后修改
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            params.put("productCodeList", productCodeList);
        }
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario, params);
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("没有物料类型是材料的数据，无法计算风险等级");
        }
        productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        // 获取对应的零件风险等级
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(scenario, productCodeList);
        if (CollectionUtils.isEmpty(partRiskLevelVOS)) {
            throw new BusinessException("没有零件风险等级，无法计算风险等级");
        }
        Map<String, PartRiskLevelVO> materialRiskLevelVOMap = partRiskLevelVOS.stream().collect(Collectors.toMap(PartRiskLevelVO::getProductCode, Function.identity()));
        // 查询最新一致性业务预测6个月的数据
        Date now = new Date();
        String planPeriod = DateUtils.dateToString(now, "yyyyMM");
        Date demandTime = DateUtils.moveMonth(now, 7);
        String endTime = DateUtils.dateToString(demandTime, "yyyy-MM-dd");
        List<DeliveryPlanVO2> deliveryPlanVO2List = dfpFeign.selectConsistenceDemandForecastDataByPlanPeriod(scenario, planPeriod, null, endTime);
        Map<String, Integer> demandMap = deliveryPlanVO2List.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode, Collectors.reducing(0, DeliveryPlanVO2::getDemandQuantity, Integer::sum)));
        // 查询材料采购基础数据
        params = new HashMap<>();
        params.put("materialCodeList", productCodeList);
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList = materialSupplierPurchaseService.selectByParams(params);
        if (CollectionUtils.isEmpty(materialSupplierPurchaseVOList)) {
            throw new BusinessException("没有材料采购基础数据，无法计算风险等级");
        }
        Map<String, MaterialSupplierPurchaseVO> materialSupplierMap = materialSupplierPurchaseVOList.stream().collect(Collectors.toMap(MaterialSupplierPurchaseVO::getMaterialCode, Function.identity()));

        // safetyInventoryPOS = safetyInventoryDao.selectByParams(params);
        // 查询高价值物料数据
        params = new HashMap<>();
        params.put("productCodeList", productCodeList);
        List<MpsHighValueMaterialsVO> mpsHighValueMaterialsVOS = mpsFeign.queryByParams(scenario, params);
        Map<String, MpsHighValueMaterialsVO> highValueMaterialsVOMap = mpsHighValueMaterialsVOS.stream().collect(Collectors.toMap(MpsHighValueMaterialsVO::getProductCode, Function.identity()));
        List<SafetyInventoryPO> insertList = new ArrayList<>();
        for (NewProductStockPointVO newProductStockPointVO : newProductStockPointVOS) {
            // 判断该材料的类型，是原片，pvb，还是b类
            if (!materialSupplierMap.containsKey(newProductStockPointVO.getProductCode())) {
                continue;
            }
            // 零件风险
            PartRiskLevelVO partRiskLevelVO = materialRiskLevelVOMap.get(newProductStockPointVO.getProductCode());
            MpsHighValueMaterialsVO mpsHighValueMaterialsVO = highValueMaterialsVOMap.get(newProductStockPointVO.getProductCode());
            SafetyInventoryPO po = new SafetyInventoryPO();
            po.setStockPointCode(newProductStockPointVO.getStockPointCode());
            // todo getStockPointName()为null
            po.setStockPointName(newProductStockPointVO.getStockPointName());
            po.setMaterialCode(newProductStockPointVO.getProductCode());
            po.setMaterialName(newProductStockPointVO.getProductName());
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = materialSupplierMap.get(newProductStockPointVO.getProductCode());
            String materialType = materialSupplierPurchaseVO.getMaterialType();
            List<MaterialRiskLevelRuleVO> riskLevelRuleParamsVOS1 = materialTypeMap.get(materialType);
            // b类的材料判定规则只有5条，不能新增，这里类型判断现在用的枚举，后面考虑改成数据字典 todo
            boolean flag = false;
            if (MaterialTypeEnum.B_TYPE.getCode().equals(materialType)) {
                Integer demandQty = demandMap.get(newProductStockPointVO.getProductCode());
                for (MaterialRiskLevelRuleVO riskLevelRuleParamsVO : riskLevelRuleParamsVOS1) {
                    String ruleName = riskLevelRuleParamsVO.getRuleName();
                    switch (ruleName) {
                        case "唯一专用":
                            flag = exclusiveUse(po, materialSupplierPurchaseVO, partRiskLevelVO);
                            break;
                        case "占总物料比例":
                            flag = proportionOfTotalMaterial(po, materialSupplierPurchaseVO, partRiskLevelVO);
                            break;
                        case "高价值材料":
                            flag = highValueMaterials(po, riskLevelRuleParamsVO, mpsHighValueMaterialsVO);
                            break;
                        case "易失效物料":
                            flag = vulnerableMaterial(po, newProductStockPointVO, riskLevelRuleParamsVO);
                            break;
                        case "最小起订量":
                            flag = minOrderQuantity(po, newProductStockPointVO, riskLevelRuleParamsVO, demandQty);
                            break;
                        default:
                            break;
                    }
                    if (flag) {
                        break;
                    }
                }
            } else if (MaterialTypeEnum.ORIGINAL_FILM.getCode().equals(materialType)) { // 原片类型,看编码后缀是否包含1.6YP/1.8YP/2.8YP/2.6YP/所有LY

                for (MaterialRiskLevelRuleVO riskLevelRuleParamsVO : riskLevelRuleParamsVOS1) {
                    String ruleParams = riskLevelRuleParamsVO.getRuleParams();
                    if (StringUtils.isNotEmpty(ruleParams)) {
                        if (ruleParams.contains("所有")) { // 1.6YP/1.8YP/2.8YP/2.6YP/所有LY
                            ruleParams = ruleParams.replace("所有", "");
                        }
                        // 厚度可以不维护
                        boolean thicknessFlag = true;
                        if (riskLevelRuleParamsVO.getThickness() != null) {
                            thicknessFlag = riskLevelRuleParamsVO.getThickness().compareTo(newProductStockPointVO.getProductThickness()) == 0;
                        }
                        if (newProductStockPointVO.getProductCode().contains(ruleParams)
                                && riskLevelRuleParamsVO.getColor().equals(newProductStockPointVO.getProductColor())
                                && thicknessFlag) {
                            po.setRiskLevel(riskLevelRuleParamsVO.getRiskLevel());
                            flag = true;
                            break;
                        }
                    }
                }
            } else if (MaterialTypeEnum.PVB.getCode().equals(materialType)) { // PVB类型，看编码前缀后缀是否包含参数值

                for (MaterialRiskLevelRuleVO riskLevelRuleParamsVO : riskLevelRuleParamsVOS1) {
                    String ruleParams = riskLevelRuleParamsVO.getRuleParams();

                    if (StringUtils.isNotEmpty(ruleParams)) {
                        String[] split = ruleParams.split("-");
                        if (newProductStockPointVO.getProductCode().contains(split[0])
                                && newProductStockPointVO.getProductCode().contains(split[1])) {
                            po.setRiskLevel(riskLevelRuleParamsVO.getRiskLevel());
                            flag = true;
                            break;
                        }
                    }
                }
            }
            if (flag) {
                insertList.add(po);
                break;
            }
        }
        // 数据处理
        if (CollectionUtils.isNotEmpty(insertList)) {
            // 走更新
            if (CollectionUtils.isNotEmpty(safetyInventoryPOS)) {
                Map<String, SafetyInventoryPO> inventoryPOMap = insertList.stream().collect(Collectors.toMap(SafetyInventoryPO::getMaterialCode, Function.identity(), (v1, v2) -> v1));
                for (SafetyInventoryPO safetyInventoryPO : safetyInventoryPOS) {
                    if (inventoryPOMap.containsKey(safetyInventoryPO.getMaterialCode())) {
                        safetyInventoryPO.setRiskLevel(inventoryPOMap.get(safetyInventoryPO.getMaterialCode()).getRiskLevel());
                    }
                }
                BasePOUtils.updateBatchFiller(safetyInventoryPOS);
                safetyInventoryDao.updateBatch(safetyInventoryPOS);
            } else { // 直接走新增
                BasePOUtils.insertBatchFiller(insertList);
                safetyInventoryDao.insertBatchWithPrimaryKey(insertList);
            }
        }
    }

    @Override
    public void updateRiskLevelBatch(SafetyInventoryRiskDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数不能为空！");
        }
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("勾选数据不能为空！");
        }
        if (StringUtils.isEmpty(dto.getRiskLevel())) {
            throw new BusinessException("风险等级不能为空！");
        }
        List<SafetyInventoryPO> safetyInventoryPOList = safetyInventoryDao.selectByPrimaryKeys(dto.getIds());
        safetyInventoryPOList.forEach(t -> t.setRiskLevel(dto.getRiskLevel()));
        BasePOUtils.updateBatchFiller(safetyInventoryPOList);
        safetyInventoryDao.updateBatch(safetyInventoryPOList);
    }

    private boolean exclusiveUse(SafetyInventoryPO po, MaterialSupplierPurchaseVO purchaseVO, PartRiskLevelVO partRiskLevelVO) {
        if (YesOrNoEnum.YES.getCode().equals(purchaseVO.getSpecific())) {
            po.setRiskLevel(partRiskLevelVO.getMaterialRiskLevel());
            return true;
        }
        return false;
    }

    private boolean proportionOfTotalMaterial(SafetyInventoryPO po, MaterialSupplierPurchaseVO purchaseVO, PartRiskLevelVO partRiskLevelVO) {
        // todo bom那块有点问题，待补充
        return false;
    }

    private boolean highValueMaterials(SafetyInventoryPO po, MaterialRiskLevelRuleVO riskLevelRuleParamsVO, MpsHighValueMaterialsVO mpsHighValueMaterialsVO) {
        if (mpsHighValueMaterialsVO != null && YesOrNoEnum.YES.getCode().equals(mpsHighValueMaterialsVO.getHighValue())) {
            po.setRiskLevel(riskLevelRuleParamsVO.getRiskLevel());
            return true;
        }
        return false;
    }

    private boolean vulnerableMaterial(SafetyInventoryPO po, NewProductStockPointVO newProductStockPointVO, MaterialRiskLevelRuleVO riskLevelRuleParamsVO) {
        // 该材料的保质期小于等于该规则设定天数
        int day = Integer.parseInt(riskLevelRuleParamsVO.getRuleParams());
        if (newProductStockPointVO.getExpireDate().compareTo(day) <= 0) {
            po.setRiskLevel(riskLevelRuleParamsVO.getRiskLevel());
            return true;
        }
        return false;
    }

    private boolean minOrderQuantity(SafetyInventoryPO po, NewProductStockPointVO newProductStockPointVO, MaterialRiskLevelRuleVO riskLevelRuleParamsVO, Integer demandQty) {
        if (demandQty != null && newProductStockPointVO.getMinOrderQuantity() != null && BigDecimal.valueOf(demandQty).compareTo(newProductStockPointVO.getMinOrderQuantity()) >= 0) {
            po.setRiskLevel(riskLevelRuleParamsVO.getRiskLevel());
            return true;
        }
        return false;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SAFETY_INVENTORY.getCode();
    }

    @Override
    public List<SafetyInventoryVO> invocation(List<SafetyInventoryVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
