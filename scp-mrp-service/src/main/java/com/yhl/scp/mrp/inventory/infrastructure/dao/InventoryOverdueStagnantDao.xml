<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOverdueStagnantDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO">
        <!--@Table mrp_inventory_overdue_stagnant-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="fdp_inventory_batch_detail_id" jdbcType="VARCHAR" property="fdpInventoryBatchDetailId"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="over_deadline_day" jdbcType="VARCHAR" property="overDeadlineDay"/>
        <result column="overdue_days" jdbcType="VARCHAR" property="overdueDays"/>
        <result column="stock_age_day" jdbcType="VARCHAR" property="stockAgeDay"/>
        <result column="scope_demand_quantity" jdbcType="VARCHAR" property="scopeDemandQuantity"/>
        <result column="inventory_depletion_date" jdbcType="TIMESTAMP" property="inventoryDepletionDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryOverdueStagnantVO">
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="expire_date" jdbcType="INTEGER" property="expireDate"/>
        <result column="sub_inventory" jdbcType="VARCHAR" property="subInventory"/>
        <result column="sub_inventory_description" jdbcType="VARCHAR" property="subInventoryDescription"/>
        <result column="freight_space" jdbcType="VARCHAR" property="freightSpace"/>
        <result column="freight_space_description" jdbcType="VARCHAR" property="freightSpaceDescription"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="current_quantity" jdbcType="VARCHAR" property="currentQuantity"/>
        <result column="assigned_time" jdbcType="VARCHAR" property="assignedTime"/>
        <result column="last_update_date" jdbcType="VARCHAR" property="lastUpdateDate"/>
        <result column="expire_date" jdbcType="VARCHAR" property="expireDate"/>
        <result column="distance_enable_date" jdbcType="VARCHAR" property="distanceEnableDate"/>
        <result column="materials_main_classification" jdbcType="VARCHAR" property="materialsMainClassification"/>
        <result column="materials_second_classification" jdbcType="VARCHAR" property="materialsSecondClassification"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,version_id,fdp_inventory_batch_detail_id,item_type,over_deadline_day,overdue_days,stock_age_day,scope_demand_quantity,inventory_depletion_date,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>
        ,stock_point_code,product_code,product_name,product_classify,product_type,product_color
        ,sub_inventory,sub_inventory_description,freight_space,freight_space_description,batch,current_quantity,assigned_time
        ,last_update_date,expire_date,distance_enable_date,materials_main_classification,materials_second_classification
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.fdpInventoryBatchDetailId != null and params.fdpInventoryBatchDetailId != ''">
                and fdp_inventory_batch_detail_id = #{params.fdpInventoryBatchDetailId,jdbcType=VARCHAR}
            </if>
            <if test="params.itemType != null and params.itemType != ''">
                and item_type = #{params.itemType,jdbcType=VARCHAR}
            </if>
            <if test="params.overDeadlineDay != null">
                and over_deadline_day = #{params.overDeadlineDay,jdbcType=VARCHAR}
            </if>
            <if test="params.overdueDays != null">
                and overdue_days = #{params.overdueDays,jdbcType=VARCHAR}
            </if>
            <if test="params.stockAgeDay != null">
                and stock_age_day = #{params.stockAgeDay,jdbcType=VARCHAR}
            </if>
            <if test="params.scopeDemandQuantity != null">
                and scope_demand_quantity = #{params.scopeDemandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDepletionDate != null">
                and inventory_depletion_date = #{params.inventoryDepletionDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_inventory_overdue_stagnant
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_inventory_overdue_stagnant
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_inventory_overdue_stagnant
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_inventory_overdue_stagnant
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_inventory_overdue_stagnant(
        id,
        version_id,
        fdp_inventory_batch_detail_id,
        item_type,
        over_deadline_day,
        overdue_days,
        stock_age_day,
        scope_demand_quantity,
        inventory_depletion_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{fdpInventoryBatchDetailId,jdbcType=VARCHAR},
        #{itemType,jdbcType=VARCHAR},
        #{overDeadlineDay,jdbcType=VARCHAR},
        #{overdueDays,jdbcType=VARCHAR},
        #{stockAgeDay,jdbcType=VARCHAR},
        #{scopeDemandQuantity,jdbcType=VARCHAR},
        #{inventoryDepletionDate,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO">
        insert into mrp_inventory_overdue_stagnant(id,
                                                   version_id,
                                                   fdp_inventory_batch_detail_id,
                                                   item_type,
                                                   over_deadline_day,
                                                   overdue_days,
                                                   stock_age_day,
                                                   scope_demand_quantity,
                                                   inventory_depletion_date,
                                                   remark,
                                                   enabled,
                                                   creator,
                                                   create_time,
                                                   modifier,
                                                   modify_time,
                                                   version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{fdpInventoryBatchDetailId,jdbcType=VARCHAR},
                #{itemType,jdbcType=VARCHAR},
                #{overDeadlineDay,jdbcType=VARCHAR},
                #{overdueDays,jdbcType=VARCHAR},
                #{stockAgeDay,jdbcType=VARCHAR},
                #{scopeDemandQuantity,jdbcType=VARCHAR},
                #{inventoryDepletionDate,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_inventory_overdue_stagnant(
        id,
        version_id,
        fdp_inventory_batch_detail_id,
        item_type,
        over_deadline_day,
        overdue_days,
        stock_age_day,
        scope_demand_quantity,
        inventory_depletion_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.fdpInventoryBatchDetailId,jdbcType=VARCHAR},
            #{entity.itemType,jdbcType=VARCHAR},
            #{entity.overDeadlineDay,jdbcType=VARCHAR},
            #{entity.overdueDays,jdbcType=VARCHAR},
            #{entity.stockAgeDay,jdbcType=VARCHAR},
            #{entity.scopeDemandQuantity,jdbcType=VARCHAR},
            #{entity.inventoryDepletionDate,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_inventory_overdue_stagnant(
        id,
        version_id,
        fdp_inventory_batch_detail_id,
        item_type,
        over_deadline_day,
        overdue_days,
        stock_age_day,
        scope_demand_quantity,
        inventory_depletion_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.fdpInventoryBatchDetailId,jdbcType=VARCHAR},
            #{entity.itemType,jdbcType=VARCHAR},
            #{entity.overDeadlineDay,jdbcType=VARCHAR},
            #{entity.overdueDays,jdbcType=VARCHAR},
            #{entity.stockAgeDay,jdbcType=VARCHAR},
            #{entity.scopeDemandQuantity,jdbcType=VARCHAR},
            #{entity.inventoryDepletionDate,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO">
        update mrp_inventory_overdue_stagnant
        set version_id                    = #{versionId,jdbcType=VARCHAR},
            fdp_inventory_batch_detail_id = #{fdpInventoryBatchDetailId,jdbcType=VARCHAR},
            item_type                     = #{itemType,jdbcType=VARCHAR},
            over_deadline_day             = #{overDeadlineDay,jdbcType=VARCHAR},
            overdue_days                  = #{overdueDays,jdbcType=VARCHAR},
            stock_age_day                 = #{stockAgeDay,jdbcType=VARCHAR},
            scope_demand_quantity         = #{scopeDemandQuantity,jdbcType=VARCHAR},
            inventory_depletion_date      = #{inventoryDepletionDate,jdbcType=TIMESTAMP},
            remark                        = #{remark,jdbcType=VARCHAR},
            enabled                       = #{enabled,jdbcType=VARCHAR},
            modifier                      = #{modifier,jdbcType=VARCHAR},
            modify_time                   = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                 = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO">
        update mrp_inventory_overdue_stagnant
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.fdpInventoryBatchDetailId != null and item.fdpInventoryBatchDetailId != ''">
                fdp_inventory_batch_detail_id = #{item.fdpInventoryBatchDetailId,jdbcType=VARCHAR},
            </if>
            <if test="item.itemType != null and item.itemType != ''">
                item_type = #{item.itemType,jdbcType=VARCHAR},
            </if>
            <if test="item.overDeadlineDay != null">
                over_deadline_day = #{item.overDeadlineDay,jdbcType=VARCHAR},
            </if>
            <if test="item.overdueDays != null">
                overdue_days = #{item.overdueDays,jdbcType=VARCHAR},
            </if>
            <if test="item.stockAgeDay != null">
                stock_age_day = #{item.stockAgeDay,jdbcType=VARCHAR},
            </if>
            <if test="item.scopeDemandQuantity != null">
                scope_demand_quantity = #{item.scopeDemandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDepletionDate != null">
                inventory_depletion_date = #{item.inventoryDepletionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_inventory_overdue_stagnant
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fdp_inventory_batch_detail_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fdpInventoryBatchDetailId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="days_to_expiration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.daysToExpiration,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="overdue_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overdueDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_age_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockAgeDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scope_demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scopeDemandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_depletion_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDepletionDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_inventory_overdue_stagnant
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.fdpInventoryBatchDetailId != null and item.fdpInventoryBatchDetailId != ''">
                    fdp_inventory_batch_detail_id = #{item.fdpInventoryBatchDetailId,jdbcType=VARCHAR},
                </if>
                <if test="item.itemType != null and item.itemType != ''">
                    item_type = #{item.itemType,jdbcType=VARCHAR},
                </if>
                <if test="item.overDeadlineDay != null">
                    over_deadline_day = #{item.overDeadlineDay,jdbcType=VARCHAR},
                </if>
                <if test="item.overdueDays != null">
                    overdue_days = #{item.overdueDays,jdbcType=VARCHAR},
                </if>
                <if test="item.stockAgeDay != null">
                    stock_age_day = #{item.stockAgeDay,jdbcType=VARCHAR},
                </if>
                <if test="item.scopeDemandQuantity != null">
                    scope_demand_quantity = #{item.scopeDemandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDepletionDate != null">
                    inventory_depletion_date = #{item.inventoryDepletionDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_inventory_overdue_stagnant
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_inventory_overdue_stagnant where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
