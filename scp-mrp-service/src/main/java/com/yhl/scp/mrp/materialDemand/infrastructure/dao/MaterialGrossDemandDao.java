package com.yhl.scp.mrp.materialDemand.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandPO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialGrossDemandDao</code>
 * <p>
 * 材料计划毛需求DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:51:45
 */
public interface MaterialGrossDemandDao extends BaseDao<MaterialGrossDemandPO, MaterialGrossDemandVO> {

    void deleteAll();

    List<MaterialGrossDemandVO> selectGroupByParams(@Param("params") Map<String, Object> params);

    void deleteByProductCodeList(@Param("list") List<String> productCodeList);

}
