<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao">
    <resultMap id="VOResultMap" type="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        <!--@Table mds_product_stock_point-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="measurement_unit" jdbcType="VARCHAR" property="measurementUnit"/>
        <result column="sale_type" jdbcType="VARCHAR" property="saleType"/>
        <result column="loading_position" jdbcType="VARCHAR" property="loadingPosition"/>
        <result column="loading_position_sub" jdbcType="VARCHAR" property="loadingPositionSub"/>
        <result column="vehicle_model_type" jdbcType="VARCHAR" property="vehicleModelType"/>
        <result column="business_special" jdbcType="VARCHAR" property="businessSpecial"/>
        <result column="core_process" jdbcType="VARCHAR" property="coreProcess"/>
        <result column="product_special" jdbcType="VARCHAR" property="productSpecial"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_area" jdbcType="VARCHAR" property="productArea"/>
        <result column="product_weight" jdbcType="VARCHAR" property="productWeight"/>
        <result column="weight_unit" jdbcType="VARCHAR" property="weightUnit"/>
        <result column="product_quantity" jdbcType="VARCHAR" property="productQuantity"/>
        <result column="expire_date" jdbcType="INTEGER" property="expireDate"/>
        <result column="special_desc" jdbcType="VARCHAR" property="specialDesc"/>
        <result column="min_order_quantity" jdbcType="VARCHAR" property="minOrderQuantity"/>
        <result column="transport_cycle" jdbcType="VARCHAR" property="transportCycle"/>
        <result column="purchase_process_pre" jdbcType="VARCHAR" property="purchaseProcessPre"/>
        <result column="purchase_process_ing" jdbcType="VARCHAR" property="purchaseProcessIng"/>
        <result column="purchase_process_after" jdbcType="VARCHAR" property="purchaseProcessAfter"/>
        <result column="purchase_lock_period" jdbcType="VARCHAR" property="purchaseLockPeriod"/>
        <result column="product_plan_user" jdbcType="VARCHAR" property="productPlanUser"/>
        <result column="product_user" jdbcType="VARCHAR" property="productUser"/>
        <result column="order_planner" jdbcType="VARCHAR" property="orderPlanner"/>
        <result column="production_planner" jdbcType="VARCHAR" property="productionPlanner"/>
        <result column="material_planner" jdbcType="VARCHAR" property="materialPlanner"/>
        <result column="purchase_planner" jdbcType="VARCHAR" property="purchasePlanner"/>
        <result column="product_sop" jdbcType="TIMESTAMP" property="productSop"/>
        <result column="product_eop" jdbcType="TIMESTAMP" property="productEop"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="planner_code" jdbcType="VARCHAR" property="plannerCode"/>
        <result column="po_category" jdbcType="VARCHAR" property="poCategory"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="inventory_item_id" jdbcType="VARCHAR" property="inventoryItemId"/>
        <result column="inventory_item_status_code" jdbcType="VARCHAR" property="inventoryItemStatusCode"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="material_demand_status" jdbcType="VARCHAR" property="materialDemandStatus"/>
        <result column="edi_flag" jdbcType="VARCHAR" property="ediFlag"/>
        <result column="item_flag" jdbcType="VARCHAR" property="itemFlag"/>
        <result column="isbj" jdbcType="VARCHAR" property="isbj"/>
        <result column="mold_quantity_limit" jdbcType="VARCHAR" property="moldQuantityLimit"/>
        <result column="pick_up_type" jdbcType="VARCHAR" property="pickUpType"/>
        <result column="tally_order_mode" jdbcType="VARCHAR" property="tallyOrderMode"/>
        <result column="full_box_flag" jdbcType="VARCHAR" property="fullBoxFlag"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
        <!--<result column="order_planner_name" jdbcType="VARCHAR" property="orderPlannerName"/>
        <result column="production_planner_name" jdbcType="VARCHAR" property="productionPlannerName"/>
        <result column="material_planner_name" jdbcType="VARCHAR" property="materialPlannerName"/>-->
    </resultMap>
    <sql id="Base_Column_List">
        id,stock_point_code,product_code,product_name,product_type,product_classify,classify_desc,vehicle_model_code,
        supply_type,measurement_unit,sale_type,loading_position,loading_position_sub,vehicle_model_type,business_special,
        core_process,product_special,product_length,product_width,product_thickness,product_color,product_area,
        product_weight,weight_unit,product_quantity,expire_date,special_desc,min_order_quantity,transport_cycle,
        purchase_process_pre,purchase_process_ing,purchase_process_after,purchase_lock_period,product_plan_user,
        product_user,order_planner,production_planner,material_planner,purchase_planner,product_sop,product_eop,
        remark,enabled,creator,create_time,modifier,modify_time,version_value,planner_code,po_category,
        organization_id,inventory_item_id,inventory_item_status_code,product_category,last_update_date,
        material_demand_status,edi_flag,item_flag,isbj,mold_quantity_limit,pick_up_type,tally_order_mode,full_box_flag
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,stock_point_name,part_number,part_name,risk_level/*,order_planner_name,production_planner_name,material_planner_name*/
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size > 0">
                <choose>
                    <when test="params.stockPointCodes.size == 1">
                        and stock_point_code = #{params.stockPointCodes[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and stock_point_code in
                        <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like #{params.productCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCodeLike != null and params.materialCodeLike != ''">
                and product_code like #{params.materialCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.productTypes != null and params.productTypes.size() > 0">
                and product_type in
                <foreach collection="params.productTypes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.classifyDesc != null and params.classifyDesc != ''">
                and classify_desc = #{params.classifyDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleCodeList != null and params.vehicleCodeList.size() > 0">
                and vehicle_model_code in
                <foreach collection="params.vehicleCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.measurementUnit != null and params.measurementUnit != ''">
                and measurement_unit = #{params.measurementUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.saleType != null and params.saleType != ''">
                and sale_type = #{params.saleType,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPosition != null and params.loadingPosition != ''">
                and loading_position = #{params.loadingPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPositionSub != null and params.loadingPositionSub != ''">
                and loading_position_sub = #{params.loadingPositionSub,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelType != null and params.vehicleModelType != ''">
                and vehicle_model_type = #{params.vehicleModelType,jdbcType=VARCHAR}
            </if>
            <if test="params.businessSpecial != null and params.businessSpecial != ''">
                and business_special = #{params.businessSpecial,jdbcType=VARCHAR}
            </if>
            <if test="params.coreProcess != null and params.coreProcess != ''">
                and core_process = #{params.coreProcess,jdbcType=VARCHAR}
            </if>
            <if test="params.productSpecial != null and params.productSpecial != ''">
                and product_special = #{params.productSpecial,jdbcType=VARCHAR}
            </if>
            <if test="params.productLength != null">
                and product_length = #{params.productLength,jdbcType=VARCHAR}
            </if>
            <if test="params.productWidth != null">
                and product_width = #{params.productWidth,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productArea != null">
                and product_area = #{params.productArea,jdbcType=VARCHAR}
            </if>
            <if test="params.productWeight != null">
                and product_weight = #{params.productWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.weightUnit != null and params.weightUnit != ''">
                and weight_unit = #{params.weightUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.productQuantity != null">
                and product_quantity = #{params.productQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.expireDate != null">
                and expire_date = #{params.expireDate,jdbcType=INTEGER}
            </if>
            <if test="params.specialDesc != null and params.specialDesc != ''">
                and special_desc = #{params.specialDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.minOrderQuantity != null">
                and min_order_quantity = #{params.minOrderQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.transportCycle != null">
                and transport_cycle = #{params.transportCycle,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessPre != null and params.purchaseProcessPre != ''">
                and purchase_process_pre = #{params.purchaseProcessPre,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessIng != null and params.purchaseProcessIng != ''">
                and purchase_process_ing = #{params.purchaseProcessIng,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessAfter != null and params.purchaseProcessAfter != ''">
                and purchase_process_after = #{params.purchaseProcessAfter,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLockPeriod != null">
                and purchase_lock_period = #{params.purchaseLockPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.productPlanUser != null and params.productPlanUser != ''">
                and product_plan_user = #{params.productPlanUser,jdbcType=VARCHAR}
            </if>
            <if test="params.productUser != null and params.productUser != ''">
                and product_user = #{params.productUser,jdbcType=VARCHAR}
            </if>
            <if test="params.orderPlanner != null and params.orderPlanner != ''">
                and order_planner = #{params.orderPlanner,jdbcType=VARCHAR}
            </if>
            <if test="params.productionPlanner != null and params.productionPlanner != ''">
                and find_in_set(#{params.productionPlanner,jdbcType=VARCHAR}, production_planner) > 0
            </if>
            <if test="params.materialPlanner != null and params.materialPlanner != ''">
                and find_in_set(#{params.materialPlanner,jdbcType=VARCHAR}, material_planner) > 0
            </if>
            <if test="params.purchasePlanner != null and params.purchasePlanner != ''">
                and find_in_set(#{params.purchasePlanner,jdbcType=VARCHAR}, purchase_planner) > 0
            </if>
            <if test="params.productSop != null">
                and product_sop = #{params.productSop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productEop != null">
                and product_eop = #{params.productEop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.plannerCode != null and params.plannerCode != ''">
                and planner_code = #{params.plannerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.poCategory != null and params.poCategory != ''">
                and po_category = #{params.poCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemId != null and params.inventoryItemId != ''">
                and inventory_item_id = #{params.inventoryItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemIds != null and params.inventoryItemIds.size() > 0">
                and inventory_item_id in
                <foreach collection="params.inventoryItemIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.inventoryItemStatusCode != null and params.inventoryItemStatusCode != ''">
                and inventory_item_status_code = #{params.inventoryItemStatusCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCategory != null and params.productCategory != ''">
                and product_category = #{params.productCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.materialDemandStatus != null">
                and material_demand_status = #{params.materialDemandStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.ediFlag != null and params.ediFlag != ''">
                and edi_flag = #{params.ediFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.productColorIsNotNull != null and params.productColorIsNotNull != '' and params.productColorIsNotNull == 'YES'">
                and product_color is not null
            </if>
            <if test="params.itemFlag != null and params.itemFlag != ''">
                and item_flag = #{params.itemFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.isbj != null and params.isbj != ''">
                and isbj = #{params.isbj,jdbcType=VARCHAR}
            </if>
            <if test="params.moldQuantityLimit != null and params.moldQuantityLimit != ''">
                and mold_quantity_limit = #{params.moldQuantityLimit,jdbcType=INTEGER}
            </if>
            <if test="params.pickUpType != null and params.pickUpType != ''">
                and pick_up_type = #{params.pickUpType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlannerNotNull != null and params.materialPlannerNotNull == 'YES'">
                and material_planner is not null
            </if>
            <if test="params.tallyOrderMode != null and params.tallyOrderMode != ''">
                and tally_order_mode = #{params.tallyOrderMode,jdbcType=VARCHAR}
            </if>
            <if test="params.fullBoxFlag != null and params.fullBoxFlag != ''">
                and full_box_flag = #{params.fullBoxFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.productColorList != null and params.productColorList.size() > 0">
                and product_color in
                <foreach collection="params.productColorList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productThicknessList != null and params.productThicknessList.size() > 0">
                and product_thickness in
                <foreach collection="params.productThicknessList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productClassifyLikeOr != null and params.productClassifyLikeOr != ''">
                or (product_classify like concat('%', 'BB', '%')
                or product_classify like concat('%', 'BG', '%')
                or product_classify like concat('%', 'BJ', '%'))
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_stock_point
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectYpProductCodes" resultType="java.lang.String">
        SELECT DISTINCT
            product_code
        FROM
            mds_product_stock_point
        WHERE
            product_classify = 'RA.A'
          AND product_code NOT LIKE '%-DEL'
          AND LENGTH( product_code ) &gt;= 16;
    </select>

    <select id="selectYpFactoryCode" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        SELECT
            t2.product_code productCode,
            t4.product_code productFactoryCode,
            t4.vehicle_model_code vehicleModelCode
        FROM
            mds_rou_product_bom t1
                LEFT JOIN mds_product_stock_point t2 ON t2.id = t1.io_product_id
                LEFT JOIN mds_rou_product_bom_version t3 ON t3.id = t1.bom_version_id
                LEFT JOIN mds_product_stock_point t4 ON t4.id = t3.product_id
        WHERE
            t1.enabled = 'YES'
          AND t2.product_classify = 'RA.A'
          AND LENGTH( t2.product_code ) >= 16
    </select>

    <select id="selectColumnVOByParams" resultMap="VOResultMap">
        select
        id,
        product_code,
        product_type,
        product_classify
        from v_mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectGlassProductCodes" resultType="java.lang.String">
        SELECT DISTINCT
            product_code
        FROM
            mds_product_stock_point
        WHERE
            product_classify = 'RA.A'
          AND product_code NOT LIKE '%-DEL'
    </select>

    <select id="selectYpFactoryCodeNew" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        WITH RECURSIVE product_hierarchy AS (
        SELECT t2.product_code AS material_code,
        t4.product_code AS product_code,
        t4.vehicle_model_code AS vehicle_model_code,
        t4.product_type AS product_type,
        1 AS hierarchy_level
        FROM mds_rou_product_bom t1
        LEFT JOIN mds_product_stock_point t2 ON t2.id = t1.io_product_id
        LEFT JOIN mds_rou_product_bom_version t3 ON t3.id = t1.bom_version_id
        LEFT JOIN mds_product_stock_point t4 ON t4.id = t3.product_id
        WHERE t1.enabled = 'YES'
        AND t4.product_code IS NOT NULL
        <if test="productCodes != null and productCodes.size() > 0">
            AND t2.product_code IN
            <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        UNION ALL
        SELECT ph.material_code,
        t4_upper.product_code AS product_code,
        t4_upper.vehicle_model_code AS vehicle_model_code,
        t4_upper.product_type AS product_type,
        ph.hierarchy_level + 1 AS hierarchy_level
        FROM product_hierarchy ph
        LEFT JOIN mds_product_stock_point t2_upper ON t2_upper.product_code = ph.product_code
        LEFT JOIN mds_rou_product_bom t1_upper ON t1_upper.io_product_id = t2_upper.id
        AND t1_upper.enabled = 'YES'
        LEFT JOIN mds_rou_product_bom_version t3_upper ON t3_upper.id = t1_upper.bom_version_id
        LEFT JOIN mds_product_stock_point t4_upper ON t4_upper.id = t3_upper.product_id
        WHERE t2_upper.id IS NOT NULL
        AND t4_upper.product_code IS NOT NULL
        AND ph.product_code
        != t4_upper.product_code

        )
        , material_max_level AS (
        SELECT material_code, MAX ( hierarchy_level ) AS max_level
        FROM product_hierarchy
        GROUP BY material_code )
        SELECT DISTINCT
        ph.material_code AS productCode,
        ph.product_code AS productFactoryCode,
        ph.vehicle_model_code AS vehiTcleModelCode
        FROM product_hierarchy ph
        INNER JOIN material_max_level mml ON ph.material_code = mml.material_code
        AND ph.hierarchy_level = mml.max_level
        WHERE ph.product_type = 'FG'
        ORDER BY ph.material_code,
        ph.product_code;
    </select>

</mapper>
