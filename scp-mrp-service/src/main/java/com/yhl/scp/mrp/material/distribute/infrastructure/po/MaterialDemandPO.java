package com.yhl.scp.mrp.material.distribute.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialDemandPO</code>
 * <p>
 * MaterialDemandPO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30 10:48:58
 */
public class MaterialDemandPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 726760061743842501L;

    private String planVersionId;
    /**
     * 需求时间
     */
    private Date demandTime;
    /**
     * 物品ID
     */
    private String productId;
    /**
     * 物品代码
     */
    private String productCode;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 材料物品id
     */
    private String materialProductId;
    /**
     * 材料code
     */
    private String materialProductCode;
    /**
     * 材料类型
     */
    private String materialType;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 未分配数量
     */
    private BigDecimal unfulfilledQuantity;
    /**
     * 产生需求的订单ID
     */
    private String demandOrderId;
    /**
     * 工序ID
     */
    private String operationId;
    /**
     * 需求类型
     */
    private String demandType;
    /**
     * 供应来源
     */
    private String supplySource;
    /**
     * 替代类型
     */
    private String replaceType;
    /**
     * 分配状态
     */
    private String fulfillmentStatus;

    public String getPlanVersionId() {
        return planVersionId;
    }

    public void setPlanVersionId(String planVersionId) {
        this.planVersionId = planVersionId;
    }

    public Date getDemandTime() {
        return demandTime;
    }

    public void setDemandTime(Date demandTime) {
        this.demandTime = demandTime;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStockPointId() {
        return stockPointId;
    }

    public void setStockPointId(String stockPointId) {
        this.stockPointId = stockPointId;
    }

    public String getMaterialProductId() {
        return materialProductId;
    }

    public void setMaterialProductId(String materialProductId) {
        this.materialProductId = materialProductId;
    }

    public String getMaterialProductCode() {
        return materialProductCode;
    }

    public void setMaterialProductCode(String materialProductCode) {
        this.materialProductCode = materialProductCode;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnfulfilledQuantity() {
        return unfulfilledQuantity;
    }

    public void setUnfulfilledQuantity(BigDecimal unfulfilledQuantity) {
        this.unfulfilledQuantity = unfulfilledQuantity;
    }

    public String getDemandOrderId() {
        return demandOrderId;
    }

    public void setDemandOrderId(String demandOrderId) {
        this.demandOrderId = demandOrderId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getDemandType() {
        return demandType;
    }

    public void setDemandType(String demandType) {
        this.demandType = demandType;
    }

    public String getSupplySource() {
        return supplySource;
    }

    public void setSupplySource(String supplySource) {
        this.supplySource = supplySource;
    }

    public String getReplaceType() {
        return replaceType;
    }

    public void setReplaceType(String replaceType) {
        this.replaceType = replaceType;
    }

    public String getFulfillmentStatus() {
        return fulfillmentStatus;
    }

    public void setFulfillmentStatus(String fulfillmentStatus) {
        this.fulfillmentStatus = fulfillmentStatus;
    }

}
