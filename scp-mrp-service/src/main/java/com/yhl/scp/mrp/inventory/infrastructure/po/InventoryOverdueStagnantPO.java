package com.yhl.scp.mrp.inventory.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryOverdueStagnantPO</code>
 * <p>
 * 超期呆滞库存（历史）PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:48:27
 */
public class InventoryOverdueStagnantPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -53560669154531461L;

    /**
     * 版本id
     */
    private String versionId;
    /**
     * 实时库存id
     */
    private String fdpInventoryBatchDetailId;
    /**
     * 工艺类型
     */
    private String itemType;
    /**
     * 库龄超期界定天数
     */
    private BigDecimal overDeadlineDay;
    /**
     * 库龄天数
     */
    private BigDecimal stockAgeDay;
    /**
     * 超期天数
     */
    private BigDecimal overdueDays;
    /**
     * 筛选范围内需求数量
     */
    private BigDecimal scopeDemandQuantity;
    /**
     * 库存耗尽日期
     */
    private Date inventoryDepletionDate;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getFdpInventoryBatchDetailId() {
        return fdpInventoryBatchDetailId;
    }

    public void setFdpInventoryBatchDetailId(String fdpInventoryBatchDetailId) {
        this.fdpInventoryBatchDetailId = fdpInventoryBatchDetailId;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public BigDecimal getOverDeadlineDay() {
        return overDeadlineDay;
    }

    public void setOverDeadlineDay(BigDecimal overDeadlineDay) {
        this.overDeadlineDay = overDeadlineDay;
    }

    public BigDecimal getStockAgeDay() {
        return stockAgeDay;
    }

    public void setStockAgeDay(BigDecimal stockAgeDay) {
        this.stockAgeDay = stockAgeDay;
    }

    public BigDecimal getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(BigDecimal overdueDays) {
        this.overdueDays = overdueDays;
    }

    public BigDecimal getScopeDemandQuantity() {
        return scopeDemandQuantity;
    }

    public void setScopeDemandQuantity(BigDecimal scopeDemandQuantity) {
        this.scopeDemandQuantity = scopeDemandQuantity;
    }

    public Date getInventoryDepletionDate() {
        return inventoryDepletionDate;
    }

    public void setInventoryDepletionDate(Date inventoryDepletionDate) {
        this.inventoryDepletionDate = inventoryDepletionDate;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
