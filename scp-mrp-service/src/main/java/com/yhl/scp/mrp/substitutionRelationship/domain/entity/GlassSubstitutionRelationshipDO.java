package com.yhl.scp.mrp.substitutionRelationship.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>GlassSubstitutionRelationshipDO</code>
 * <p>
 * 原片替代关系表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:39:49
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GlassSubstitutionRelationshipDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 838795587242363097L;

    /**
     * 主键
     */
    private String id;
    /**
     * 工厂
     */
    private String stockPointCode;
    /**
     * 原物料编码
     */
    private String rawProductCode;
    /**
     * 原物料名称
     */
    private String rawProductName;
    /**
     * 生产BOM
     */
    private String productionSubstituteProductCode;
    /**
     * 生产BOM名称
     */
    private String productionSubstituteProductName;
    /**
     * 生产单耗
     */
    private BigDecimal productionInputFactor;
    /**
     * 替代料编码
     */
    private String substituteProductCode;
    /**
     * 替代料名称
     */
    private String substituteProductName;
    /**
     * 替代单耗
     */
    private BigDecimal substituteInputFactor;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 本厂名称
     */
    private String productName;
    /**
     * 颜色厚度
     */
    private String colorThickness;
    /**
     * 毛坯规格
     */
    private String blankSpec;
    /**
     * 需求计算规则
     */
    private String rule;
    /**
     * 产品尺寸
     */
    private String productSize;
    /**
     * 淋子方向
     */
    private String linziDirection;
    /**
     * 毛坯单耗
     */
    private BigDecimal blankInputFactor;
    /**
     * 原片对毛坯单耗
     */
    private BigDecimal glassInputFactor;
    /**
     * 切裁率
     */
    private BigDecimal cuttingRate;
    /**
     * 生产切裁率
     */
    private BigDecimal productionCuttingRate;
    /**
     * ERP-BOM切裁率
     */
    private BigDecimal rawProductCuttingRate;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 推荐替代
     */
    private String recommendSubstitute;

    /**
     * ERP-BOM库存禁用
     */
    private String rawProductInventoryDisabled;

}
