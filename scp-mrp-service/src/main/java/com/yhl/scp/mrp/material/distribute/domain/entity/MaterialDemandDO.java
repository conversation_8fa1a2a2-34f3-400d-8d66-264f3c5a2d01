package com.yhl.scp.mrp.material.distribute.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialDemandDO</code>
 * <p>
 * MaterialDemandDO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30 10:48:58
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialDemandDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 451394041777245254L;

    /**
     * 主键ID
     */
    private String id;
    private String planVersionId;
    /**
     * 需求时间
     */
    private Date demandTime;
    /**
     * 物品ID
     */
    private String productId;
    /**
     * 物品代码
     */
    private String productCode;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 材料物品id
     */
    private String materialProductId;
    /**
     * 材料code
     */
    private String materialProductCode;
    /**
     * 材料类型
     */
    private String materialType;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 未分配数量
     */
    private BigDecimal unfulfilledQuantity;
    /**
     * 产生需求的订单ID
     */
    private String demandOrderId;
    /**
     * 工序ID
     */
    private String operationId;
    /**
     * 需求类型
     */
    private String demandType;
    /**
     * 供应来源
     */
    private String supplySource;
    /**
     * 替代类型
     */
    private String replaceType;
    /**
     * 分配状态
     */
    private String fulfillmentStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

}
