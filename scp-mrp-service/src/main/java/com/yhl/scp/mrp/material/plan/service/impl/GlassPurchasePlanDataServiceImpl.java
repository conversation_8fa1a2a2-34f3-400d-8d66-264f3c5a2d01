package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.material.plan.convertor.GlassPurchasePlanDataConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.GlassPurchasePlanDataDO;
import com.yhl.scp.mrp.material.plan.domain.service.GlassPurchasePlanDataDomainService;
import com.yhl.scp.mrp.material.plan.dto.GlassPurchasePlanDataDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassPurchasePlanDataPageParamsDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassPurchasePlanDetailDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassPurchasePlanVersionDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassPurchasePlanDataDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.GlassPurchasePlanDataPO;
import com.yhl.scp.mrp.material.plan.service.GlassPurchasePlanDataService;
import com.yhl.scp.mrp.material.plan.service.GlassPurchasePlanDetailService;
import com.yhl.scp.mrp.material.plan.service.GlassPurchasePlanVersionService;
import com.yhl.scp.mrp.material.plan.vo.GlassPurchasePlanDataVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultDetailService;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultSummaryService;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultVersionService;
import com.yhl.scp.mrp.originalFilm.vo.GlassPurchasePlanVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <code>GlassPurchasePlanDataServiceImpl</code>
 * <p>
 * 原片采购计划应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-05 10:42:57
 */
@Slf4j
@Service
public class GlassPurchasePlanDataServiceImpl extends AbstractService implements GlassPurchasePlanDataService {

    @Resource
    private GlassPurchasePlanDataDao glassPurchasePlanDataDao;

    @Resource
    private GlassPurchasePlanDataDomainService glassPurchasePlanDataDomainService;

    @Resource
    private GlassPurchasePlanVersionService glassPurchasePlanVersionService;

    @Resource
    private GlassPurchasePlanDetailService glassPurchasePlanDetailService;

    @Resource
    private OriginalFilmDemandConsultSummaryService originalFilmDemandConsultSummaryService;

    @Resource
    private OriginalFilmDemandConsultDetailService originalFilmDemandConsultDetailService;

    @Resource
    private OriginalFilmDemandConsultVersionService originalFilmDemandConsultVersionService;

    @Resource
    private InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(GlassPurchasePlanDataDTO glassPurchasePlanDataDTO) {
        // 0.数据转换
        GlassPurchasePlanDataDO glassPurchasePlanDataDO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Do(glassPurchasePlanDataDTO);
        GlassPurchasePlanDataPO glassPurchasePlanDataPO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Po(glassPurchasePlanDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassPurchasePlanDataDomainService.validation(glassPurchasePlanDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassPurchasePlanDataPO);
        glassPurchasePlanDataDao.insert(glassPurchasePlanDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(GlassPurchasePlanDataDTO glassPurchasePlanDataDTO) {
        // 0.数据转换
        GlassPurchasePlanDataDO glassPurchasePlanDataDO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Do(glassPurchasePlanDataDTO);
        GlassPurchasePlanDataPO glassPurchasePlanDataPO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Po(glassPurchasePlanDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassPurchasePlanDataDomainService.validation(glassPurchasePlanDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassPurchasePlanDataPO);
        glassPurchasePlanDataDao.update(glassPurchasePlanDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdateSelective(GlassPurchasePlanDataDTO glassPurchasePlanDataDTO) {
        // 0.数据转换
        GlassPurchasePlanDataDO glassPurchasePlanDataDO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Do(glassPurchasePlanDataDTO);
        GlassPurchasePlanDataPO glassPurchasePlanDataPO = GlassPurchasePlanDataConvertor.INSTANCE.dto2Po(glassPurchasePlanDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassPurchasePlanDataDomainService.validation(glassPurchasePlanDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassPurchasePlanDataPO);
        glassPurchasePlanDataDao.updateSelective(glassPurchasePlanDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassPurchasePlanDataDTO> list) {
        List<GlassPurchasePlanDataPO> newList = GlassPurchasePlanDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassPurchasePlanDataDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassPurchasePlanDataDTO> list) {
        List<GlassPurchasePlanDataPO> newList = GlassPurchasePlanDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassPurchasePlanDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassPurchasePlanDataDao.deleteBatch(idList);
        }
        return glassPurchasePlanDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassPurchasePlanDataVO selectByPrimaryKey(String id) {
        GlassPurchasePlanDataPO po = glassPurchasePlanDataDao.selectByPrimaryKey(id);
        return GlassPurchasePlanDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "GLASS_PURCHASE_PLAN_DATA")
    public List<GlassPurchasePlanDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "GLASS_PURCHASE_PLAN_DATA")
    public List<GlassPurchasePlanDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassPurchasePlanDataVO> dataList = glassPurchasePlanDataDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassPurchasePlanDataServiceImpl target = SpringBeanUtils.getBean(GlassPurchasePlanDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassPurchasePlanDataVO> selectByParams(Map<String, Object> params) {
        List<GlassPurchasePlanDataPO> list = glassPurchasePlanDataDao.selectByParams(params);
        return GlassPurchasePlanDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassPurchasePlanDataVO> selectVOByParams(Map<String, Object> params) {
        return glassPurchasePlanDataDao.selectVOByParams(params);
    }

    @Override
    public List<GlassPurchasePlanDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> calc(String versionId) {
        String scenario = SystemHolder.getScenario();

        List<GlassPurchasePlanDataDTO> addList = new ArrayList<>();

        if (StringUtils.isEmpty(versionId)) {
            versionId = originalFilmDemandConsultVersionService.selectFinallyVersion();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("originalFilmDemandConsultVersionId", versionId);

        List<OriginalFilmDemandConsultSummaryVO> originallySummaryVoList = originalFilmDemandConsultSummaryService.selectByParams(params);
        List<String> consultSummaryIds = originallySummaryVoList.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());

        List<OriginalFilmDemandConsultDetailVO> detailList = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
        Map<String, List<OriginalFilmDemandConsultDetailVO>> originalFilmDemandConsultDetailMap = detailList.stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));

        List<String> demandedMonthList = detailList.stream()
                .sorted(Comparator.comparing(OriginalFilmDemandConsultDetailVO::getDemandedDate))
                .map(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());
        List<String> materialCodeList = detailList.stream()
                .map(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode).distinct().collect(Collectors.toList());

        List<String> productFactoryCodeList = detailList.stream()
                .filter(data -> null != data.getProductCode())
                .map(data -> Arrays.stream(data.getProductCode().split(",")))
                .distinct().flatMap(Stream::sorted)
                .collect(Collectors.toList());


        List<String> allProductCode = new ArrayList<>();
        allProductCode.addAll(materialCodeList);
        allProductCode.addAll(productFactoryCodeList);
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "loading_position", "vehicle_model_code"))
                .queryParam(ImmutableMap.of("productCodeList", allProductCode))
                .build();
        Map<String, NewProductStockPointVO> productStockPointMap = newMdsFeign.selectProductListByParamOnDynamicColumns(
                        scenario, feignDynamicParam).stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));

        for (OriginalFilmDemandConsultSummaryVO summaryVO : originallySummaryVoList) {
            List<OriginalFilmDemandConsultDetailVO> demandConsultDetailVOS = originalFilmDemandConsultDetailMap.get(summaryVO.getId());
            Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = demandConsultDetailVOS.stream()
                    .filter(data -> null != data.getOriginalFilmProductCode())
                    .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode));

            if (CollectionUtils.isEmpty(demandConsultDetailVOS)) {
                GlassPurchasePlanDataDTO glassPurchasePlanDataDTO = new GlassPurchasePlanDataDTO();
                glassPurchasePlanDataDTO.setStockPointCode("SJG");
                glassPurchasePlanDataDTO.setOriginalFilmDemandConsultSummaryId(summaryVO.getId());
                addList.add(glassPurchasePlanDataDTO);
            }

            for (Map.Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry : detailMap.entrySet()) {
                String key = entry.getKey();
                List<OriginalFilmDemandConsultDetailVO> value = entry.getValue();
                OriginalFilmDemandConsultDetailVO originalFilmDemandConsultDetailVO = value.get(0);

                Map<String, List<OriginalFilmDemandConsultDetailVO>> demandDetailMap = value.stream()
                        .collect(Collectors.groupingBy(data -> DateUtils.dateToString(data.getDemandedDate(), DateUtils.YEAR_MONTH)));
                List<String> productFactoryList = value.stream()
                        .filter(data -> null != data.getProductCode())
                        .map(data -> Arrays.stream(data.getProductCode().split(",")))
                        .distinct()
                        .flatMap(Stream::sorted)
                        .collect(Collectors.toList());
                // 获取物料的毛胚

//                List<String> targetProducts = getTargetProducts(key, grossDemandMap, productFactoryList, 3);

                GlassPurchasePlanDataDTO glassPurchasePlanDataDTO = new GlassPurchasePlanDataDTO();
                glassPurchasePlanDataDTO.setStockPointCode("SJG");
                glassPurchasePlanDataDTO.setProductCode(key);

                glassPurchasePlanDataDTO.setOriginalFilmDemandConsultSummaryId(summaryVO.getId());

                glassPurchasePlanDataDTO.setMpblSpec(originalFilmDemandConsultDetailVO.getMpblSpec());

                glassPurchasePlanDataDTO.setVehicleModelCode(productFactoryList.stream().map(data -> {
                    if (productStockPointMap.containsKey(data)) {
                        NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(data);
                        return newProductStockPointVO.getVehicleModelCode();
                    }
                    return null;
                }).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));

                glassPurchasePlanDataDTO.setLoadingPosition(productFactoryList.stream().map(data -> {
                    if (productStockPointMap.containsKey(data)) {
                        NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(data);
                        return newProductStockPointVO.getLoadingPosition();
                    }
                    return null;
                }).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));

                if (key.contains("ABB")) {
                    glassPurchasePlanDataDTO.setQualityGrade("汽车优级");
                } else if (key.contains("ABT")) {
                    glassPurchasePlanDataDTO.setQualityGrade("汽车特级");
                } else if (key.contains("*")) {
                    glassPurchasePlanDataDTO.setQualityGrade("汽车优级");
                }

                glassPurchasePlanDataDTO.setSpecificationDirection("平行于" + key.substring(3, 7));

                glassPurchasePlanDataDTO.setProductThicknessDeviation("内控");

                glassPurchasePlanDataDTO.setSpecialRequirements(summaryVO.getSpecialRemark());

                BigDecimal weightSum = BigDecimal.ZERO;
                for (OriginalFilmDemandConsultDetailVO filmDemandConsultDetailVO : value) {
                    if (null == filmDemandConsultDetailVO.getWeight() || null == filmDemandConsultDetailVO.getDemandedQuantity()){
                        continue;
                    }
                    weightSum = weightSum.add(filmDemandConsultDetailVO.getWeight().multiply(filmDemandConsultDetailVO.getDemandedQuantity()));
                }
                glassPurchasePlanDataDTO.setWeight(weightSum);

                for (String demandMonth : demandedMonthList) {
                    GlassPurchasePlanDataDTO addDto = new GlassPurchasePlanDataDTO();
                    BeanUtils.copyProperties(glassPurchasePlanDataDTO, addDto);
                    BigDecimal amountArea = BigDecimal.ZERO;
                    if (demandDetailMap.containsKey(demandMonth)) {
                        List<OriginalFilmDemandConsultDetailVO> originalFilmDemandConsultDetailVOS = demandDetailMap.get(demandMonth);
                        // 面积 = 单片面积 * 需求数量
                        for (OriginalFilmDemandConsultDetailVO filmDemandConsultDetailVO : originalFilmDemandConsultDetailVOS) {
                            if (null == filmDemandConsultDetailVO.getArea() || null == filmDemandConsultDetailVO.getDemandedQuantity()){
                                continue;
                            }
                            amountArea = amountArea.add(filmDemandConsultDetailVO.getArea().multiply(filmDemandConsultDetailVO.getDemandedQuantity()));

                        }
//                        reduce = demandDetailMap.get(demandMonth).stream()
//                                .map(OriginalFilmDemandConsultDetailVO::getArea)
//                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    addDto.setDemandDate(DateUtils.stringToDate(demandMonth, "yyyy-MM"));
                    addDto.setDemandQuantity(amountArea);
                    addDto.setAmountArea(amountArea);
                    addList.add(addDto);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            glassPurchasePlanDataDao.deleteAll();
            Lists.partition(addList, 1000).forEach(this::doCreateBatch);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> release() {
        List<GlassPurchasePlanDataVO> glassPurchasePlanDataVOS = this.selectAll();

        // 生成版本
        GlassPurchasePlanVersionDTO glassPurchasePlanVersionDTO = new GlassPurchasePlanVersionDTO();
        String version = glassPurchasePlanVersionService.getVersionCode();
        glassPurchasePlanVersionDTO.setVersionCode(version);
        glassPurchasePlanVersionDTO.setVersionName(version);
        String versionId = UUID.randomUUID().toString();
        glassPurchasePlanVersionDTO.setId(versionId);

        // 生成明细
        List<GlassPurchasePlanDetailDTO> addDetailList = glassPurchasePlanDataVOS.stream().map(data -> {
            GlassPurchasePlanDetailDTO detailDTO = new GlassPurchasePlanDetailDTO();
            BeanUtils.copyProperties(data, detailDTO);
            detailDTO.setGlassPurchasePlanVersionId(versionId);
            return detailDTO;
        }).collect(Collectors.toList());

        // 版本落库
        glassPurchasePlanVersionService.doCreateWithPrimaryKey(glassPurchasePlanVersionDTO);

        // 批量落库（明细）
        if (CollectionUtils.isNotEmpty(addDetailList)) {
            Lists.partition(addDetailList, 1000).forEach(glassPurchasePlanDetailService::doCreateBatch);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public PageInfo<GlassPurchasePlanVO> selectPageCustom(GlassPurchasePlanDataPageParamsDTO dto) {

        List<GlassPurchasePlanVO> result = new ArrayList<>();

        String productThickness = dto.getProductThickness();
        List<BigDecimal> productThicknessList = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(productThickness)) {
            productThicknessList = Arrays.stream(productThickness.split(","))
                    .map(BigDecimal::new)
                    .collect(Collectors.toList());
        }

        List<GlassPurchasePlanDataVO> glassPurchasePlanDataVOList;
        if (StringUtils.isEmpty(dto.getGlassPurchasePlanVersionId())) {
            glassPurchasePlanDataVOList = this.selectVOByParams(ImmutableMap.of(
                    "stockPointCode", dto.getStockPointCode(),
                    "productCode", dto.getProductCode(),
                    "productColor", dto.getProductColor(),
                    "productThicknessList", productThicknessList));
        } else {
            glassPurchasePlanDataVOList = glassPurchasePlanDetailService.selectVOByParams(ImmutableMap.of(
                    "glassPurchasePlanVersionId", dto.getGlassPurchasePlanVersionId(),
                    "stockPointCode", dto.getStockPointCode(),
                    "productCode", dto.getProductCode(),
                    "productColor", dto.getProductColor(),
                    "productThicknessList", productThicknessList)).stream().map(data -> {
                GlassPurchasePlanDataVO glassPurchasePlanDataVO = new GlassPurchasePlanDataVO();
                BeanUtils.copyProperties(data, glassPurchasePlanDataVO);
                return glassPurchasePlanDataVO;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(glassPurchasePlanDataVOList)){
            return null;
        }

        List<String> demandedMonthList = glassPurchasePlanDataVOList.stream()
                .sorted(Comparator.comparing(GlassPurchasePlanDataVO::getDemandDate))
                .map(item -> DateUtils.dateToString(item.getDemandDate(), DateUtils.YEAR_MONTH))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());

        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<String> summaryIdList = glassPurchasePlanDataDao.selectSummaryId();
        PageInfo<String> summaryIdPageInfo = new PageInfo<>(summaryIdList);

        glassPurchasePlanDataVOList = glassPurchasePlanDataVOList.stream()
                .filter(data -> summaryIdList.contains(data.getOriginalFilmDemandConsultSummaryId()))
                .collect(Collectors.toList());

        for (Map.Entry<String, List<GlassPurchasePlanDataVO>> entry : glassPurchasePlanDataVOList.stream()
                .collect(Collectors.groupingBy(GlassPurchasePlanDataVO::getOriginalFilmDemandConsultSummaryId)).entrySet()) {
            List<GlassPurchasePlanDataVO> value = entry.getValue();

            // 列汇总
            List<GlassPurchasePlanVO> columnSummaryList = new ArrayList<>();

            for (Map.Entry<String, List<GlassPurchasePlanDataVO>> productEntry : value.stream()
                    .collect(Collectors.groupingBy(GlassPurchasePlanDataVO::getProductCode)).entrySet()) {

                List<GlassPurchasePlanDataVO> productValue = productEntry.getValue();
                GlassPurchasePlanDataVO glassPurchasePlanDataVO = productValue.get(0);
                Map<String, List<GlassPurchasePlanDataVO>> demandDetailMap = productValue.stream()
                        .collect(Collectors.groupingBy(data -> DateUtils.dateToString(data.getDemandDate(), DateUtils.YEAR_MONTH)));

                GlassPurchasePlanVO glassPurchasePlanVO = new GlassPurchasePlanVO();
                BeanUtils.copyProperties(glassPurchasePlanDataVO, glassPurchasePlanVO);
                glassPurchasePlanVO.setProductThicknessDrawing(glassPurchasePlanVO.getProductThickness());
                glassPurchasePlanVO.setProductSpec(glassPurchasePlanVO.getProductCode());

                glassPurchasePlanVO.setDemandedMonthList(demandedMonthList);

                Map<String, BigDecimal> monthDemandQuantity = new HashMap<>();
                Map<String, String> monthDemandId = new HashMap<>();
                for (String demandMonth : demandedMonthList) {
                    BigDecimal reduce = BigDecimal.ZERO;
                    if (demandDetailMap.containsKey(demandMonth)) {
                        reduce = demandDetailMap.get(demandMonth).stream()
                                .map(GlassPurchasePlanDataVO::getDemandQuantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    monthDemandQuantity.put(demandMonth, reduce);
                }
                glassPurchasePlanVO.setMonthDemandQuantity(monthDemandQuantity);

                for (GlassPurchasePlanDataVO purchasePlanDataVO : productValue) {
                    monthDemandId.put(DateUtils.dateToString(purchasePlanDataVO.getDemandDate(), "yyyy-MM"), purchasePlanDataVO.getId());
                }
                glassPurchasePlanVO.setMonthDemandId(monthDemandId);

                if (null != glassPurchasePlanDataVO.getAmountArea()) {
                    BigDecimal reduce = monthDemandQuantity.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    glassPurchasePlanVO.setAmountArea(reduce);
//                    glassPurchasePlanVO.setAmountArea(reduce.multiply(glassPurchasePlanDataVO.getAmountArea()));
                } else {
                    glassPurchasePlanVO.setAmountArea(BigDecimal.ZERO);
                }

                columnSummaryList.add(glassPurchasePlanVO);
                result.add(glassPurchasePlanVO);
            }

            // 获取第一个对象作为汇总载体
            GlassPurchasePlanVO glassPurchasePlanVO = new GlassPurchasePlanVO();
            glassPurchasePlanVO.setStockPointCode("汇总");

            Map<String, BigDecimal> summedDemand = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getMonthDemandQuantity)
                    .filter(Objects::nonNull)
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            monthDemand -> monthDemand.getValue() != null ? monthDemand.getValue() : BigDecimal.ZERO,
                            BigDecimal::add
                    ));
            glassPurchasePlanVO.setMonthDemandQuantity(summedDemand);

            BigDecimal sumAmountArea = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getAmountArea).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            glassPurchasePlanVO.setAmountArea(sumAmountArea);

            BigDecimal weightArea = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            glassPurchasePlanVO.setWeight(weightArea);

            result.add(glassPurchasePlanVO);
        }

        PageInfo<GlassPurchasePlanVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(summaryIdPageInfo.getTotal());
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setSize(result.size());
        pageInfo.setPages(summaryIdPageInfo.getPages());
        return pageInfo;
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, String versionId) {
        List<GlassPurchasePlanDataVO> glassPurchasePlanDataVOList;
        if (StringUtils.isEmpty(versionId)) {
            glassPurchasePlanDataVOList = this.selectVOByParams(new HashMap<>());
        } else {
            glassPurchasePlanDataVOList = glassPurchasePlanDetailService.selectVOByParams(ImmutableMap.of(
                    "glassPurchasePlanVersionId", versionId)).stream().map(data -> {
                GlassPurchasePlanDataVO glassPurchasePlanDataVO = new GlassPurchasePlanDataVO();
                BeanUtils.copyProperties(data, glassPurchasePlanDataVO);
                return glassPurchasePlanDataVO;
            }).collect(Collectors.toList());
        }

        // 年月维度
        List<String> demandedMonthList = glassPurchasePlanDataVOList.stream()
                .sorted(Comparator.comparing(GlassPurchasePlanDataVO::getDemandDate))
                .map(item -> DateUtils.dateToString(item.getDemandDate(), DateUtils.YEAR_MONTH))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("原片采购计划");

            // 创建表头
            createSummersHeaders(workbook, sheet, demandedMonthList);

            // 创建数据行
            int startRow = 1;
            for (Map.Entry<String, List<GlassPurchasePlanDataVO>> entry : glassPurchasePlanDataVOList.stream()
                    .collect(Collectors.groupingBy(GlassPurchasePlanDataVO::getOriginalFilmDemandConsultSummaryId)).entrySet()) {
                List<GlassPurchasePlanDataVO> value = entry.getValue();
                for (Map.Entry<String, List<GlassPurchasePlanDataVO>> productEntry : value.stream()
                        .collect(Collectors.groupingBy(GlassPurchasePlanDataVO::getProductCode)).entrySet()) {
                    createData(workbook, sheet, startRow, productEntry, demandedMonthList);
                    startRow = startRow + 1;
                }
                createSummersData(workbook, sheet, startRow, entry, demandedMonthList);
                startRow = startRow + 1;
            }
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("原片采购计划.xlsx", "UTF-8"));

            // 获取输出流并写入
            try (ServletOutputStream os = response.getOutputStream()) {
                workbook.write(os);
                // 刷新输出流
                os.flush();
            }
        }
    }

    @Override
    public String getObjectType() {
        return null;
        // return ObjectTypeEnum.GLASS_PURCHASE_PLAN_DATA.getCode();
    }

    @Override
    public List<GlassPurchasePlanDataVO> invocation(List<GlassPurchasePlanDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public String getMpblSpecData(String mixProductCode,
                                   List<ProductBomVersionVO> productBomVersionVOList,
                                   List<ProductBomVO> productBomVOList) {
        Map<String, List<ProductBomVO>> productBomGroup = productBomVOList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getIoProductCode()) &&
                        (item.getProductArea() != null ||
                                (item.getProductLength() != null &&
                                        item.getProductWidth() != null)))
                .collect(Collectors.groupingBy(ProductBomVO::getIoProductCode));

        Map<String, ProductBomVersionVO> productBomVersionVOMap = productBomVersionVOList.stream()
                .filter(item -> item.getProductArea() != null ||
                        (item.getProductLength() != null && item.getProductWidth() != null))
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));
        List<String> productCodeList = handleSpecialProductCodeList(Collections.singletonList(mixProductCode));
        for (String productCode : productCodeList) {
            // 毛坯
            NewProductStockPointVO blankProduct = new NewProductStockPointVO();
            NewProductStockPointVO finishedProduct = new NewProductStockPointVO();
            inventoryAlternativeRelationshipService.getBlankAndFinishedProductByBatch(productCode, blankProduct, finishedProduct,
                    productBomGroup, productBomVersionVOMap);
            if (null != blankProduct.getProductCode() && null != blankProduct.getProductWidth() && null != blankProduct.getProductLength()){
                return org.apache.commons.lang3.StringUtils.join(blankProduct.getProductLength().setScale(0, RoundingMode.DOWN), "*",
                        blankProduct.getProductWidth().setScale(0, RoundingMode.DOWN));
            }
        }
        return null;
    }

    private List<String> getTargetProducts(String key,
                                           Map<String, List<MaterialGrossDemandVO>> grossDemandMap,
                                           List<String> productFactoryList,
                                           int size) {
        // 确保size为正数，避免无效值
        size = Math.max(size, 1);

        List<String> targetProducts;

        // 处理*，ABB 和 ABT的毛需求都看
        List<String> productCodeList = handleSpecialProductCodeList(Collections.singletonList(key));
        List<MaterialGrossDemandVO> materialGrossDemandVOS = new ArrayList<>();
        for (String productCode : productCodeList) {
            if (grossDemandMap.containsKey(productCode)) {
                // 根据物料编码获取毛需求
                materialGrossDemandVOS.addAll(grossDemandMap.get(productCode));
            }
        }

        if (CollectionUtils.isNotEmpty(materialGrossDemandVOS)) {
            Map<String, BigDecimal> productDemandMap = materialGrossDemandVOS.stream()
                    .collect(Collectors.groupingBy(
                            MaterialGrossDemandVO::getProductFactoryCode,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    MaterialGrossDemandVO::getDemandQuantity,
                                    BigDecimal::add
                            )
                    ));

            List<Map.Entry<String, BigDecimal>> matchedEntries = productDemandMap.entrySet()
                    .stream()
                    .filter(e -> productFactoryList.contains(e.getKey()))
                    .collect(Collectors.toList());

            if (!matchedEntries.isEmpty()) {
                // 按需求数量降序排序，取前size个产品代码
                targetProducts = matchedEntries.stream()
                        .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                        .limit(size)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            } else {
                // 无匹配时从productFactoryList取前size个
                targetProducts = productFactoryList.stream()
                        .limit(size)
                        .collect(Collectors.toList());
            }
        } else {
            // 无对应毛需求时从productFactoryList取前size个
            targetProducts = productFactoryList.stream()
                    .limit(size)
                    .collect(Collectors.toList());
        }

        return targetProducts;
    }

    public static List<String> handleSpecialProductCodeList(List<String> productCodes) {
        List<String> newProductCodes = new ArrayList<>();
        for (String productCode : productCodes) {
            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                newProductCodes.add(sb.toString());
                sb.setCharAt(2, 'T');
                newProductCodes.add(sb.toString());
            } else {
                newProductCodes.add(productCode);
            }
        }
        return newProductCodes;
    }

    private void createSummersHeaders(Workbook workbook,
                                      Sheet sheet,
                                      List<String> demandedMonthList) {

        // 2. 创建表头
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 列标题
        Row headerRow = sheet.createRow(0);
        List<String> headersList = new ArrayList<>();
        Collections.addAll(headersList, "汽玻公司", "厚度", "图纸要求厚度", "颜色", "生产规格", "BOM", "毛坯", "车型", "类型");
        headersList.addAll(demandedMonthList);
        Collections.addAll(headersList, "合计m2", "吨", "质量等级", "规格淋子方向", "厚度偏差", "特殊要求", "备注");
        String[] headers = headersList.toArray(new String[0]);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void createData(Workbook workbook,
                            Sheet sheet,
                            int startRow,
                            Map.Entry<String, List<GlassPurchasePlanDataVO>> productEntry,
                            List<String> demandedMonthList) {
        List<GlassPurchasePlanDataVO> value = productEntry.getValue();
        GlassPurchasePlanDataVO glassPurchasePlanDataVO = value.get(0);
        Map<String, BigDecimal> demandDateMap = value.stream()
                .collect(Collectors.toMap(data ->
                        DateUtils.dateToString(data.getDemandDate(), DateUtils.YEAR_MONTH), GlassPurchasePlanDataVO::getDemandQuantity));
        BigDecimal sumAmountArea = value.stream().map(GlassPurchasePlanDataVO::getAmountArea)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal sumWeight = value.stream().map(GlassPurchasePlanDataVO::getWeight)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, glassPurchasePlanDataVO.getStockPointCode(), dataStyle);
        createCell(indexRow, 1, String.valueOf(glassPurchasePlanDataVO.getProductThickness()), dataStyle);
        createCell(indexRow, 2, String.valueOf(glassPurchasePlanDataVO.getProductThickness()), dataStyle);
        createCell(indexRow, 3, glassPurchasePlanDataVO.getProductColor(), dataStyle);
        createCell(indexRow, 4, glassPurchasePlanDataVO.getProductCode(), dataStyle);
        createCell(indexRow, 5, glassPurchasePlanDataVO.getProductCode(), dataStyle);
        if (null != glassPurchasePlanDataVO.getMpblSpec()){
            createCell(indexRow, 6, String.valueOf(glassPurchasePlanDataVO.getMpblSpec()), dataStyle);
        }
        createCell(indexRow, 7, glassPurchasePlanDataVO.getVehicleModelCode(), dataStyle);
        createCell(indexRow, 8, glassPurchasePlanDataVO.getLoadingPosition(), dataStyle);

        int col = 9;
        for (String demandedMonth : demandedMonthList) {
            BigDecimal demand = BigDecimal.ZERO;
            if (demandDateMap.containsKey(demandedMonth)) {
                demand = demandDateMap.get(demandedMonth);
            }
            createCell(indexRow, col, String.valueOf(demand.setScale(0, RoundingMode.UP)), dataStyle);
            col++;
        }

        createCell(indexRow, col, String.valueOf(sumAmountArea.setScale(0, RoundingMode.UP)), dataStyle);
        col++;

        createCell(indexRow, col, String.valueOf(sumWeight.setScale(0, RoundingMode.UP)), dataStyle);
        col++;

        createCell(indexRow, col, glassPurchasePlanDataVO.getQualityGrade(), dataStyle);
        col++;

        createCell(indexRow, col, glassPurchasePlanDataVO.getSpecificationDirection(), dataStyle);
        col++;

        createCell(indexRow, col, glassPurchasePlanDataVO.getProductThicknessDeviation(), dataStyle);
        col++;

        createCell(indexRow, col, glassPurchasePlanDataVO.getSpecialRequirements(), dataStyle);
        col++;

        createCell(indexRow, col, glassPurchasePlanDataVO.getRemark(), dataStyle);
    }

    private void createSummersData(Workbook workbook,
                                   Sheet sheet,
                                   int startRow,
                                   Map.Entry<String, List<GlassPurchasePlanDataVO>> entry,
                                   List<String> demandedMonthList) {
        List<GlassPurchasePlanDataVO> value = entry.getValue();
        GlassPurchasePlanDataVO glassPurchasePlanDataVO = value.get(0);

        Map<String, List<GlassPurchasePlanDataVO>> demandDateMap = value.stream()
                .collect(Collectors.groupingBy(data ->
                        DateUtils.dateToString(data.getDemandDate(), DateUtils.YEAR_MONTH)));
        BigDecimal sumAmountArea = value.stream()
                .map(GlassPurchasePlanDataVO::getAmountArea).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal sumWeight = value.stream()
                .map(GlassPurchasePlanDataVO::getWeight)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, "统计", dataStyle);
        createCell(indexRow, 1, String.valueOf(glassPurchasePlanDataVO.getProductThickness()), dataStyle);
        createCell(indexRow, 2, String.valueOf(glassPurchasePlanDataVO.getProductThickness()), dataStyle);
        createCell(indexRow, 3, glassPurchasePlanDataVO.getProductColor(), dataStyle);

        int col = 9;
        for (String demandedMonth : demandedMonthList) {
            BigDecimal demand = BigDecimal.ZERO;
            if (demandDateMap.containsKey(demandedMonth)) {
                demand = demandDateMap.get(demandedMonth).stream()
                        .map(GlassPurchasePlanDataVO::getDemandQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            createCell(indexRow, col, String.valueOf(demand.setScale(0, RoundingMode.UP)), dataStyle);
            col++;
        }

        createCell(indexRow, col, String.valueOf(sumAmountArea.setScale(0, RoundingMode.UP)), dataStyle);
        col++;

        createCell(indexRow, col, String.valueOf(sumWeight.setScale(0, RoundingMode.UP)), dataStyle);
    }


    private static void createCell(Row row, int col, String value, CellStyle style) {
        Cell cell = row.createCell(col);
        cell.setCellValue(value);
        if (style != null) cell.setCellStyle(style);
    }
}
