package com.yhl.scp.mrp.materialDemand.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandDTO;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandParam;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>MaterialGrossDemandController</code>
 * <p>
 * 材料计划毛需求控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:51:43
 */
@Slf4j
@Api(tags = "材料计划毛需求控制器")
@RestController
@RequestMapping("materialGrossDemand")
public class MaterialGrossDemandController extends BaseController {

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialGrossDemandVO>> page() {
        List<MaterialGrossDemandVO> materialGrossDemandList = materialGrossDemandService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialGrossDemandVO> pageInfo = new PageInfo<>(materialGrossDemandList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询2")
    @PostMapping(value = "page2")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialGrossDemandVO>> page2(@RequestBody MaterialGrossDemandParam materialGrossDemandParam) {
        PageInfo<MaterialGrossDemandVO> materialGrossDemandVOPageInfo = materialGrossDemandService.selectByPage2(materialGrossDemandParam);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialGrossDemandVOPageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialGrossDemandDTO materialGrossDemandDTO) {
        return materialGrossDemandService.doCreate(materialGrossDemandDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialGrossDemandDTO materialGrossDemandDTO) {
        return materialGrossDemandService.doUpdate(materialGrossDemandDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialGrossDemandService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialGrossDemandVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialGrossDemandService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "毛需求计算")
    @GetMapping(value = "computeDemand")
    @SuppressWarnings("unchecked")
    @BusinessMonitorLog(businessCode = "毛需求计算", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> computeDemand(@RequestParam(name = "mpsDemandRule") String mpsDemandRule) {
        materialGrossDemandService.doComputeDemand(SystemHolder.getScenario(), mpsDemandRule);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "模板导出")
    @GetMapping(value = "exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        materialGrossDemandService.exportTemplate(response);
    }

    @ApiOperation(value = "材料毛需求导入")
    @PostMapping(value = "importDemand")
    @BusinessMonitorLog(businessCode = "代采购需求上传", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> doImportGrossDemand(@RequestPart("file") MultipartFile file) {
        return materialGrossDemandService.doImportGrossDemand(file);
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "exportMaterialGrossDemand")
    public void exportMaterialGrossDemand(@RequestBody MaterialGrossDemandParam materialGrossDemandParam) {
        materialGrossDemandService.exportMaterialGrossDemand(response, materialGrossDemandParam);
    }

}
