package com.yhl.scp.mrp.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryOverdueStagnantVO</code>
 * <p>
 * 超期呆滞库存（历史）VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:46:20
 */
@ApiModel(value = "超期呆滞库存（历史）VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOverdueStagnantVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -92629705909423165L;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    @FieldInterpretation(value = "版本id")
    private String versionId;
    /**
     * 实时库存id
     */
    @ApiModelProperty(value = "实时库存id")
    @FieldInterpretation(value = "实时库存id")
    private String fdpInventoryBatchDetailId;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    @FieldInterpretation(value = "工艺类型")
    private String itemType;
    /**
     * 库龄超期界定天数
     */
    @ApiModelProperty(value = "库龄超期界定天数")
    @FieldInterpretation(value = "库龄超期界定天数")
    private BigDecimal overDeadlineDay;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    @FieldInterpretation(value = "库龄天数")
    private BigDecimal stockAgeDay;
    /**
     * 超期天数
     */
    @ApiModelProperty(value = "超期天数")
    @FieldInterpretation(value = "超期天数")
    private BigDecimal overdueDays;
    /**
     * 筛选范围内需求数量
     */
    @ApiModelProperty(value = "筛选范围内需求数量")
    @FieldInterpretation(value = "筛选范围内需求数量")
    private BigDecimal scopeDemandQuantity;
    /**
     * 库存耗尽日期
     */
    @ApiModelProperty(value = "库存耗尽日期")
    @FieldInterpretation(value = "库存耗尽日期")
    private Date inventoryDepletionDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subInventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    @FieldInterpretation(value = "子库存描述")
    private String subInventoryDescription;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @FieldInterpretation(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @FieldInterpretation(value = "批次")
    private String batch;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @FieldInterpretation(value = "现有量")
    private String currentQuantity;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @FieldInterpretation(value = "入库时间")
    private String assignedTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private String lastUpdateDate;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    @ApiModelProperty(value = "距离失效时间")
    @FieldInterpretation(value = "距离失效时间")
    private String distanceEnableDate;
    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @ExcelProperty(value = "物品分类")
    private String productClassify;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @FieldInterpretation(value = "物料id")
    private String productId;
    /**
     * 库存点id
     */
    @ApiModelProperty(value = "物料id")
    @FieldInterpretation(value = "物料id")
    private String stockPointId;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @FieldInterpretation(value = "物料类型")
    private String productType;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 总现存件数
     * 现有量 * 片/箱
     */
    @ApiModelProperty(value = "总现存件数")
    @FieldInterpretation(value = "总现存件数")
    private BigDecimal currentQuantitySum;
    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    @FieldInterpretation(value = "物料大类")
    private String productCategory;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private Integer expireDate;
    /**
     * 是否积压
     */
    @ApiModelProperty(value = "是否积压")
    @FieldInterpretation(value = "是否积压")
    private String isBacklog;

    /**
     * 物料分类大类
     */
    @ApiModelProperty(value = "物料分类大类")
    @FieldInterpretation(value = "物料分类大类")
    private String materialsMainClassification;
    /**
     * 物料分类小类
     */
    @ApiModelProperty(value = "物料分类小类")
    @FieldInterpretation(value = "物料分类小类")
    private String materialsSecondClassification;

    @Override
    public void clean() {

    }

}
