package com.yhl.scp.mrp.substitutionRelationship.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>GlassSubstitutionRelationshipDTO</code>
 * <p>
 * 原片替代关系表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:40:25
 */
@ApiModel(value = "原片替代关系表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GlassSubstitutionRelationshipDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 298406089072570899L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂")
    private String stockPointCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "本厂编码*")
    private String productCode;
    /**
     * 本厂名称
     */
    @ApiModelProperty(value = "本厂名称")
    private String productName;
    /**
     * 原物料编码
     */
    @ApiModelProperty(value = "原物料编码")
    @ExcelProperty(value = "ERP-BOM*")
    private String rawProductCode;
    /**
     * ERP-BOM(一切几毛坯)
     */
    @ApiModelProperty(value = "ERP-BOM(一切几毛坯)")
    @ExcelProperty(value = "ERP-BOM(一切几毛坯)")
    private BigDecimal glassInputFactor;
    /**
     * 原物料名称
     */
    @ApiModelProperty(value = "原物料名称")
    private String rawProductName;

    /**
     * 生产替代料
     */
    @ApiModelProperty(value = "生产替代料")
    @ExcelProperty(value = "生产BOM*")
    private String productionSubstituteProductCode;

    /**
     * 生产替代料
     */
    @ApiModelProperty(value = "生产替代料名称")
    private String productionSubstituteProductName;

    /**
     * 生产BOM(一切几毛坯)
     */
    @ApiModelProperty(value = "生产BOM(一切几毛坯)")
    @ExcelProperty(value = "生产BOM(一切几毛坯)")
    private BigDecimal productionInputFactor;

    /**
     * 替代料编码
     */
    @ApiModelProperty(value = "替代料编码")
    @ExcelProperty(value = "替代BOM*")
    private String substituteProductCode;

    /**
     * 替代料名称
     */
    @ApiModelProperty(value = "替代料名称")
    private String substituteProductName;

    /**
     * 替代BOM(一切几毛坯)
     */
    @ApiModelProperty(value = "替代BOM(一切几毛坯)")
    @ExcelProperty(value = "替代BOM(一切几毛坯)")
    private BigDecimal substituteInputFactor;
    /**
     * 颜色厚度
     */
    @ApiModelProperty(value = "颜色厚度")
    private String colorThickness;
    /**
     * 毛坯规格
     */
    @ApiModelProperty(value = "毛坯规格")
    private String blankSpec;
    /**
     * 需求计算规则
     */
    @ApiModelProperty(value = "需求计算规则")
    private String rule;
    /**
     * 产品尺寸
     */
    @ApiModelProperty(value = "产品尺寸")
    private String productSize;
    /**
     * 淋子方向
     */
    @ApiModelProperty(value = "淋子方向")
    private String linziDirection;
    /**
     * 毛坯单耗
     */
    @ApiModelProperty(value = "毛坯单耗")
    private BigDecimal blankInputFactor;

    /**
     * 切裁率
     */
    @ApiModelProperty(value = "切裁率")
    private BigDecimal cuttingRate;
    /**
     * 生产切裁率
     */
    @ApiModelProperty(value = "生产切裁率")
    private BigDecimal productionCuttingRate;
    /**
     * ERP-BOM切裁率
     */
    @ApiModelProperty(value = "ERP-BOM切裁率")
    private BigDecimal rawProductCuttingRate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    /**
     * 推荐替代
     */
    @ApiModelProperty(value = "推荐替代")
    private String recommendSubstitute;

    /**
     * ERP-BOM库存禁用
     */
    @ApiModelProperty(value = "ERP-BOM库存禁用")
    private String rawProductInventoryDisabled;

    private BigDecimal productArea;

}
