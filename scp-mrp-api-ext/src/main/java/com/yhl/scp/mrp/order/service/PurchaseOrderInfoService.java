package com.yhl.scp.mrp.order.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPurchaseOrder;
import com.yhl.scp.mrp.order.dto.PurchaseOrderInfoDTO;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <code>PurchaseOrderInfoService</code>
 * <p>
 * 应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-27 14:11:22
 */
public interface PurchaseOrderInfoService extends BaseService<PurchaseOrderInfoDTO, PurchaseOrderInfoVO> {

    /**
     * 查询所有
     *
     * @return list {@link PurchaseOrderInfoVO}
     */
    List<PurchaseOrderInfoVO> selectAll();
    List<PurchaseOrderInfoVO> selectByHeaderAndLine(Map<String, Object> params);

    BaseResponse<Void> syncPurchaseOrder(String tenantId);

    BaseResponse<Void> sync(String scenario, List<ErpPurchaseOrder> o);

    void doPurchaseOrderInfoJob(Integer moveMinute);

    void doClosePoJob(Integer moveMinute);

}
