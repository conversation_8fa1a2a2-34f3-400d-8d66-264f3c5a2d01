package com.yhl.scp.mrp.substitutionRelationship.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <code>GlassSubstitutionRelationshipVO</code>
 * <p>
 * 原片替代关系表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:40:27
 */
@ApiModel(value = "原片替代关系表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlassSubstitutionRelationshipVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 862012645433463374L;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @FieldInterpretation(value = "工厂")
    private String stockPointCode;
    /**
     * 原物料编码
     */
    @ApiModelProperty(value = "原物料编码")
    @FieldInterpretation(value = "原物料编码")
    private String rawProductCode;
    /**
     * 原物料名称
     */
    @ApiModelProperty(value = "原物料名称")
    @FieldInterpretation(value = "原物料名称")
    private String rawProductName;

    /**
     * 生产BOM
     */
    @ApiModelProperty(value = "生产BOM")
    @FieldInterpretation(value = "生产BOM")
    private String productionSubstituteProductCode;
    /**
     * 生产BOM名称
     */
    @ApiModelProperty(value = "生产BOM名称")
    @FieldInterpretation(value = "生产BOM名称")
    private String productionSubstituteProductName;
    /**
     * 生产BOM(一切几毛坯)
     */
    @ApiModelProperty(value = "生产BOM(一切几毛坯)")
    @FieldInterpretation(value = "生产BOM(一切几毛坯)")
    private BigDecimal productionInputFactor;

    /**
     * 替代料编码
     */
    @ApiModelProperty(value = "替代料编码")
    @FieldInterpretation(value = "替代料编码")
    private String substituteProductCode;
    /**
     * 替代料名称
     */
    @ApiModelProperty(value = "替代料名称")
    @FieldInterpretation(value = "替代料名称")
    private String substituteProductName;
    /**
     * 替代单耗
     */
    @ApiModelProperty(value = "替代单耗")
    @FieldInterpretation(value = "替代单耗")
    private BigDecimal substituteInputFactor;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 本厂名称
     */
    @ApiModelProperty(value = "本厂名称")
    @FieldInterpretation(value = "本厂名称")
    private String productName;
    /**
     * 颜色厚度
     */
    @ApiModelProperty(value = "颜色厚度")
    @FieldInterpretation(value = "颜色厚度")
    private String colorThickness;
    /**
     * 毛坯规格
     */
    @ApiModelProperty(value = "毛坯规格")
    @FieldInterpretation(value = "毛坯规格")
    private String blankSpec;
    /**
     * 需求计算规则
     */
    @ApiModelProperty(value = "需求计算规则")
    @FieldInterpretation(value = "需求计算规则")
    private String rule;
    /**
     * 产品尺寸
     */
    @ApiModelProperty(value = "产品尺寸")
    @FieldInterpretation(value = "产品尺寸")
    private String productSize;
    /**
     * 淋子方向
     */
    @ApiModelProperty(value = "淋子方向")
    @FieldInterpretation(value = "淋子方向")
    private String linziDirection;
    /**
     * 毛坯单耗
     */
    @ApiModelProperty(value = "毛坯单耗")
    @FieldInterpretation(value = "毛坯单耗")
    private BigDecimal blankInputFactor;
    /**
     * ERP-BOM(一切几毛坯)
     */
    @ApiModelProperty(value = "ERP-BOM(一切几毛坯)")
    @FieldInterpretation(value = "ERP-BOM(一切几毛坯)")
    private BigDecimal glassInputFactor;
    /**
     * 切裁率
     */
    @ApiModelProperty(value = "切裁率")
    @FieldInterpretation(value = "切裁率")
    private BigDecimal cuttingRate;
    /**
     * 生产切裁率
     */
    @ApiModelProperty(value = "生产切裁率")
    @FieldInterpretation(value = "生产切裁率")
    private BigDecimal productionCuttingRate;
    /**
     * ERP-BOM切裁率
     */
    @ApiModelProperty(value = "ERP-BOM切裁率")
    @FieldInterpretation(value = "ERP-BOM切裁率")
    private BigDecimal rawProductCuttingRate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    /**
     * 推荐替代
     */
    @ApiModelProperty(value = "推荐替代")
    @FieldInterpretation(value = "推荐替代")
    private String recommendSubstitute;

    /**
     * ERP-BOM库存禁用
     */
    @ApiModelProperty(value = "ERP-BOM库存禁用")
    @FieldInterpretation(value = "ERP-BOM库存禁用")
    private String rawProductInventoryDisabled;

    /**
     * 是否主料替代
     */
    @ApiModelProperty(value = "是否主料替代")
    @FieldInterpretation(value = "是否主料替代")
    private boolean whetherMainProductSubstitute = false;

    @Override
    public void clean() {

    }

}
