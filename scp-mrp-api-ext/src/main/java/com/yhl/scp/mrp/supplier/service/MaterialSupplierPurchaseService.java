package com.yhl.scp.mrp.supplier.service;

import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mrp.supplier.dto.MaterialSupplierPurchaseDTO;
import com.yhl.scp.mrp.supplier.dto.MaterialSupplierPurchaseParamDTO;
import com.yhl.scp.mrp.supplier.dto.SupplierPurchaseRatioEditDTO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>MrpMaterialSupplierPurchaseService</code>
 * <p>
 * 材料采购应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 14:40:17
 */
public interface MaterialSupplierPurchaseService extends BaseService<MaterialSupplierPurchaseDTO, MaterialSupplierPurchaseVO> {

    List<MaterialSupplierPurchaseVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link MaterialSupplierPurchaseVO}
     */
    List<MaterialSupplierPurchaseVO> selectAll();

    /**
     * 导出
     * @param response
     */
    void export(HttpServletResponse response);

    /**
     * 根据库存点编码查询
     * @param stockPointCodeList
     * @return
     */
    List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseVOByStockPointCode(List<String> stockPointCodeList);


    /**
     * 计算是否专用
     * @param dtoList
     */
    void calculatedSpecific(List<MaterialSupplierPurchaseDTO> dtoList);

    /**
     * 按供应商更新
     * @param dto
     */
    void doUpdateBySupplier(SupplierPurchaseRatioEditDTO dto);


    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleSupplierPurchase(List<SrmSupplierPurchase> srmSupplierPurchases);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncSupplierPurchase(String tenantId);

    void doCreateBatchWithPrimaryKey(List<MaterialSupplierPurchaseDTO> list);

    void doUpdateBatchSelective(List<MaterialSupplierPurchaseDTO> list);

    List<MaterialSupplierPurchaseVO> selectByPageNew(Pagination pagination, String sortParam, String queryCriteriaParam);

    BaseResponse<String> doUpload(MultipartFile file);

    void exportTemplate(HttpServletResponse response);

    List<MaterialSupplierPurchaseVO> pageCustom(MaterialSupplierPurchaseParamDTO paramDTO);
}
