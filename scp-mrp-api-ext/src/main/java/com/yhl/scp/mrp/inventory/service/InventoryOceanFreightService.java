package com.yhl.scp.mrp.inventory.service;

import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.scss.ScssInventoryOceanFreight;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mrp.inventory.dto.InventoryOceanFreightDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightVO;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryOceanFreightService</code>
 * <p>
 * 浮法海运_提单号应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04 10:53:04
 */
public interface InventoryOceanFreightService extends BaseService<InventoryOceanFreightDTO, InventoryOceanFreightVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryOceanFreightVO}
     */
    List<InventoryOceanFreightVO> selectAll();
    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleInventoryOceanFreight(List<InventoryOceanFreightShippedMapVO> o);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncInventoryOceanFreight(String tenantId );

    BaseResponse<Void> handlePoCreate(String scenario, List<ErpPoCreate> o);

    BaseResponse<Void> syncPoCreate(String tenantId);

    String doBatchCreatPo(List<String> ids);

    String syncAutoCreatPo(String tenantId,Map<String, Object> params);

}
