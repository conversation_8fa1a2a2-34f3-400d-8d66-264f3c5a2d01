package com.yhl.scp.mrp.material.transactions.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpMaterialTransactions;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.mrp.material.transactions.dto.MaterialTransactionsDTO;
import com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO;

import java.util.List;

/**
 * <code>MaterialTransactionsService</code>
 * <p>
 * 物料事务处理查询应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-18 14:44:45
 */
public interface MaterialTransactionsService extends BaseService<MaterialTransactionsDTO, MaterialTransactionsVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialTransactionsVO}
     */
    List<MaterialTransactionsVO> selectAll();

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleMaterialTransactions(List<ErpMaterialTransactions> erpMaterialTransactions);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncMaterialTransactions(String tenantId);



}
