package com.yhl.scp.mrp.material.distribute.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialDemandDTO</code>
 * <p>
 * MaterialDemandDTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30 10:47:29
 */
@ApiModel(value = "订单材料需求DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialDemandDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 380825751806161901L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    private String planVersionId;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    private Date demandTime;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    private String productCode;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 材料物品id
     */
    @ApiModelProperty(value = "材料物品id")
    private String materialProductId;
    /**
     * 材料code
     */
    @ApiModelProperty(value = "材料code")
    private String materialProductCode;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    private String materialType;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 未分配数量
     */
    @ApiModelProperty(value = "未分配数量")
    private BigDecimal unfulfilledQuantity;
    /**
     * 产生需求的订单ID
     */
    @ApiModelProperty(value = "产生需求的订单ID")
    private String demandOrderId;
    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private String operationId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandType;
    /**
     * 供应来源
     */
    @ApiModelProperty(value = "供应来源")
    private String supplySource;
    /**
     * 替代类型
     */
    @ApiModelProperty(value = "替代类型")
    private String replaceType;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    private String fulfillmentStatus;

    @ApiModelProperty(value = "单耗")
    private BigDecimal inputFactor;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
