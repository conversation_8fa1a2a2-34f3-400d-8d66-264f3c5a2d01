package com.yhl.scp.mrp.inventory.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.inventory.dto.InventoryOverdueStagnantDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryOverdueStagnantVO;
import com.yhl.scp.mrp.overdueInventory.dto.OverdueStagnantInventoryParamDTO;

import java.util.List;

/**
 * <code>InventoryOverdueStagnantService</code>
 * <p>
 * 超期呆滞库存（历史）应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:46:20
 */
public interface InventoryOverdueStagnantService extends BaseService<InventoryOverdueStagnantDTO, InventoryOverdueStagnantVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryOverdueStagnantVO}
     */
    List<InventoryOverdueStagnantVO> selectAll();

    void doCalculate(String scenario, OverdueStagnantInventoryParamDTO overdueStagnantInventoryParamDTO);

}
