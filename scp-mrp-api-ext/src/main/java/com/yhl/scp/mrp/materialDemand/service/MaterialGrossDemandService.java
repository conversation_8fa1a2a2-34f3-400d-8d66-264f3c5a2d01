package com.yhl.scp.mrp.materialDemand.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.excel.IExcelDynamicDataImport;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandDTO;
import com.yhl.scp.mrp.materialDemand.dto.MaterialGrossDemandParam;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialGrossDemandService</code>
 * <p>
 * 材料计划毛需求应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:52:32
 */
public interface MaterialGrossDemandService extends BaseService<MaterialGrossDemandDTO, MaterialGrossDemandVO>, IExcelDynamicDataImport {

    /**
     * 查询所有
     *
     * @return list {@link MaterialGrossDemandVO}
     */
    List<MaterialGrossDemandVO> selectAll();

    void doComputeDemand(String scenario, String mpsDemandRule);

    PageInfo<MaterialGrossDemandVO> selectByPage2(MaterialGrossDemandParam materialGrossDemandParam);

    void exportTemplate(HttpServletResponse response);

    BaseResponse<Void> doImportGrossDemand(MultipartFile file);

    void doDeleteByProductCodeList(List<String> productCodeList);

    void exportMaterialGrossDemand(HttpServletResponse response, MaterialGrossDemandParam materialGrossDemandParam);

    BaseResponse<Void> checkCapacityBalancePublishDate(String scenario);

    Map<String, Object> assemblyParams(MaterialGrossDemandParam materialGrossDemandParam);
}
