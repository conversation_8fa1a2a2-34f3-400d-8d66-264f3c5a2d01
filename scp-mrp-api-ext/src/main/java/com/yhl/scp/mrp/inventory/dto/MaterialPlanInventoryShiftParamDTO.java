package com.yhl.scp.mrp.inventory.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName MaterialPlanInventoryShiftParamDTO
 * @Description TODO
 * @Date 2024-12-04 15:27:06
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Data
public class MaterialPlanInventoryShiftParamDTO {

    @ApiModelProperty(value = "物料计划版本id")
    private String materialPlanVersionId;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "开始时间")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    @ApiModelProperty(value = "显示范围")
    private String displayRange;

    @ApiModelProperty(value = "警告提醒(采购后期末库存)")
    private String warningRemind;

    @ApiModelProperty(value = "警告提醒(下单前期末库存)")
    private String beforeWarningRemind;

    @ApiModelProperty(value = "每页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "第几页")
    private int pageNum = 1;

    @ApiModelProperty(value = "推移版本创建时间")
    private Date materialPlanVersionCreateTime;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "本厂编码")
    private String factoryCode;
}
