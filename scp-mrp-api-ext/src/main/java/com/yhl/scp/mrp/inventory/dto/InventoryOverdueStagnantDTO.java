package com.yhl.scp.mrp.inventory.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryOverdueStagnantDTO</code>
 * <p>
 * 超期呆滞库存（历史）DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:46:20
 */
@ApiModel(value = "超期呆滞库存（历史）DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InventoryOverdueStagnantDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 454229306215644308L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    private String versionId;
    /**
     * 实时库存id
     */
    @ApiModelProperty(value = "实时库存id")
    private String fdpInventoryBatchDetailId;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    private String itemType;
    /**
     * 库龄超期界定天数
     */
    @ApiModelProperty(value = "库龄超期界定天数")
    private BigDecimal overDeadlineDay;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    private BigDecimal stockAgeDay;
    /**
     * 超期天数
     */
    @ApiModelProperty(value = "超期天数")
    private BigDecimal overdueDays;
    /**
     * 筛选范围内需求数量
     */
    @ApiModelProperty(value = "筛选范围内需求数量")
    private BigDecimal scopeDemandQuantity;
    /**
     * 库存耗尽日期
     */
    @ApiModelProperty(value = "库存耗尽日期")
    private Date inventoryDepletionDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
