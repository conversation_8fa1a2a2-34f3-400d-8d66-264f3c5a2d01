package com.yhl.scp.dcp.sync.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiSyncCtrlDTO</code>
 * <p>
 * 外部api同步控制表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:06:51
 */
@ApiModel(value = "外部api同步控制表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SyncCtrlDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -70853101037600824L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 接口配置ID
     */
    @ApiModelProperty(value = "接口配置ID")
    private String apiConfigId;
    /**
     * 同步引用字段值
     */
    @ApiModelProperty(value = "同步引用字段值")
    private String referValue;
    /**
     * 分组字段值(多字段用#隔开)
     */
    @ApiModelProperty(value = "分组字段值(多字段用#隔开)")
    private String groupValue;
    /**
     * 最后同步时间
     */
    @ApiModelProperty(value = "最后同步时间")
    private Date syncTime;
    /**
     * 数据表版本
     */
    @ApiModelProperty(value = "数据表版本")
    private Integer versionValue;

}
