package com.yhl.scp.dcp.sync.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.sync.dto.SyncCtrlDTO;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;

import java.util.List;
import java.util.Map;

/**
 * <code>SyncCtrlService</code>
 * <p>
 * 外部api同步控制表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:07:33
 */
public interface SyncCtrlService extends BaseService<SyncCtrlDTO, SyncCtrlVO> {
    /**
     * 查询同步控制记录
     *
     * @param apiConfigVO
     * @param groupValue
     * @return
     */
    SyncCtrlVO getSyncCtrl(ApiConfigVO apiConfigVO, String groupValue);

    /**
     * 保存同步控制
     *
     * @param apiConfigVO
     * @param groupValue
     * @param referValue
     */
    void saveSyncCtrl(ApiConfigVO apiConfigVO, String groupValue, String referValue);
    /**
     * 保存同步时间
     *
     * @param apiConfigVO
     * @param groupValue
     */
    void saveSyncCtrlSyncTime(ApiConfigVO apiConfigVO, String groupValue);

    /**
     * 查询所有
     *
     * @return list {@link SyncCtrlVO}
     */
    List<SyncCtrlVO> selectAll();
}
