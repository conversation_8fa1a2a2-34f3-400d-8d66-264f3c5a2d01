package com.yhl.scp.mps.product.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.vo.ChainLineInventoryLogVO;

import java.util.List;

/**
 * <code>ChainLineInventoryLogService</code>
 * <p>
 * 链式生产线_中间表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-08 16:28:48
 */
public interface ChainLineInventoryLogService extends BaseService<ChainLineInventoryLogDTO, ChainLineInventoryLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link ChainLineInventoryLogVO}
     */
    List<ChainLineInventoryLogVO> selectAll();

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleChainLine(List<ChainLineInventoryLogDTO> chainLineList);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncChainLine(String tenantId);

}
