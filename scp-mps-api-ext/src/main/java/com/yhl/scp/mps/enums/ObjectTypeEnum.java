package com.yhl.scp.mps.enums;

import com.yhl.platform.common.enums.CommonEnum2;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ObjectTypeEnum</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 16:17:02
 */
public enum ObjectTypeEnum implements CommonEnum2 {

    MOLD_CHANGE_TIME("mold_change_time", "换模换型时间", "com.yhl.scp.mps.model.vo.MoldChangeTimeVO"),
    PRODUCTION_LOT("production_lot", "生产经济批量", "com.yhl.scp.mps.productionLot.vo.ProductionLotVO"),
    PRODUCTION_LEAD_TIME("production_lead_time", "生产提前期", "com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO"),
    PARAM_CAPACITY_BALANCE("param_capacity_balance", "产能平衡规则", "com.yhl.scp.mps.capacityBalance.vo.ParamCapacityBalanceVO"),
    CAPACITY_BALANCE_VERSION("capacity_balance_version", "产能平衡版本", "com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO"),
    COATING_MAINTENANCE_AMOUNT("coating_maintenance_amount", "镀膜保养量", "com.yhl.scp.mps.coating.vo.CoatingMaintenanceAmountVO"),
    PRODUCT_CANDIDATE_RESOURCE("product_candidate_resource", "产品资源生产关系", "com.yhl.scp.mps.product.vo.ProductCandidateResourceVO"),
    CAPACITY_LOAD("capacity_load", "产能负荷", "com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO"),
    CAPACITY_SUPPLY_RELATIONSHIP("capacity_supply_relationship", "产能供应关系", "com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO"),

    RESOURCE_OEE("resource_oee", "工序设备生产效率", "com.yhl.scp.mps.resource.vo.ResourceOeeVO"),
    PRODUCT_ADVANCE_BATCH_RULE("product_advance_batch_rule", "提前生产批次规则", "com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO"),
    DELIVERY_CHANGE_RECORD("delivery_change_record", "发货变更记录", "com.yhl.scp.mps.plan.vo.DeliveryChangeRecordVO"),
    MASTER_PLAN("master_plan", "主生产计划", "com.yhl.scp.mps.plan.vo.MasterPlanVO"),
    SUB_CARGO_LOCATION_INFORMATION("mps_sub_cargo_location", "子库存货位信息", "com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO"),
    HIGH_VALUE_MATERIALS("mps_high_value_materials", "高价值物料", "com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO"),//
    PRODUCTION_CAPACITY("production_capacity", "工序后库容量", "com.yhl.scp.mps.productionCapacity.vo.ProductionCapacityVO"),
    SPECIAL_OPERATION_PRODUCTION_LIMIT("production_limit", "特殊工艺产能约束", "com.yhl.scp.mps.productionLimit.vo.ProductionLimitVO"),
    MASTER_PLAN_DETAIL("master_plan_detail", "主生产计划明细", "com.yhl.scp.mps.plan.vo.MasterPlanDetailVO"),
    MASTER_PLAN_RELATION("master_plan_relation", "计划单关联", "com.yhl.scp.mps.plan.vo.MasterPlanRelationVO"),
    MASTER_PLAN_DELIVERY_RELATION("master_plan_delivery_relation", "货计划与主生产计划关系", "com.yhl.scp.mps.plan.vo.MasterPlanDeliveryRelationVO"),
    REPORTING_FEEDBACK("mps_pro_reporting_feedback", "生产报工反馈", "com.yhl.scp.mps.reportingFeedback.vo.MpsProReportingFeedbackVO"),
    ABNORMAL_FEEDBACK("mps_fdb_abnormal_feedback", "生产异常反馈", "com.yhl.scp.mps.proabnormalfeedback.vo.ProAbnormalFeedbackVO"),
    OUTSOURCE_TRANSFER_SUMMARY("v_mps_outsource_transfer_summary", "委外转产材料需求", "com.yhl.scp.mps.demandMaterial.vo.MpsOutsourceTransferSummaryVO"),
    COATING_CHANGE_TIME("mps_coating_change_time", "镀膜切换时间", "com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO"),
    OUTSOURCE_TRANSFER_MATERIAL_DETAIL("mps_outsource_transfer_material_detail", "委外转产材料需求明细", "com.yhl.scp.mps.demandMaterial.vo.MpsOutsourceTransferMaterialDetailVO"),
    PRODUCT_DEMAND_MONITOR("mps_pro_product_demand_monitor", "生产齐套生产需求", "com.yhl.scp.mps.productDemandMonitor.vo.MpsProProductDemandMonitorVO"),
    // MATERIAL_SUPPLY_MONITOR("pro_material_supply_monitor", "生产齐套供应需求", "com.yhl.scp.mps.productDemandMonitor.vo.ProMaterialSupplyMonitorVO"),
    SPECIAL_TECHNOLOGY_EQUIPMENT_EFFICIENCY("mps_special_technology_equipment_efficiency", "特殊工艺设备效率", "com.yhl.scp.mps.equipmentEfficiency.vo.EquipmentEfficiencyVO"),
    OUTSOURCE_TRANSFER_SUPPLY("outsource_transfer_supply", "委外转产汇总供料时间", "com.yhl.scp.mps.demand.vo.OutsourceTransferSupplyVO"),
    OUTSOURCE_TRANSFER_DEMAND_DETAIL("outsource_transfer_demand_detail", "委外转产材料需求明细", "com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO"),
    MASTER_PLAN_ISSUED_DATA("master_plan_issued_data", "主计划发布数据", "com.yhl.scp.mps.plan.vo.MasterPlanIssuedDataVO"),
    MASTER_PLAN_VERSION("master_plan_version", "主计划发布版本表", "com.yhl.scp.mps.plan.vo.MasterPlanVersionVO"),
    COATING_MAINTENANCE_SETTINGS("coating_maintenance_settings", "镀膜维保设置", "com.yhl.scp.mps.coating.vo.CoatingMaintenanceSettingsVO"),
    SDS_ADJUSTMENT_SUGGESTION("sds_adjustment_suggestion", "主计划调整建议", "com.yhl.scp.mps.adjustmantSuggestion.vo.SdsAdjustmentSuggestionVO"),
    DEMAND_EARLY_WARNING("mps_demand_early_warning", "需求异常预计信息", "com.yhl.scp.mps.demand.vo.DemandEarlyWarningVO"),
    DEMAND_PUBLISHED("demand_published","需求发布信息" ,"com.yhl.scp.mps.operationPublished.vo.DemandPublishedVO" ),
    FULFILLMENT_PUBLISHED("fulfillment_published","分配关系发布信息" ,"com.yhl.scp.mps.operationPublished.vo.FulfillmentPublishedVO" ),
    OPERATION_EXTEND_PUBLISHED("operation_extend_published","工序扩展发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationExtendPublishedVO" ),
    OPERATION_INPUT_PUBLISHED("operation_input_published","工序输入发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO" ),
    OPERATION_OUTPUT_PUBLISHED("operation_output_published","工序输出发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationOutputPublishedVO" ),
    OPERATION_PUBLISHED("operation_published","工序发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO" ),
    OPERATION_RESOURCE_PUBLISHED("operation_resource_published","工序资源发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationResourcePublishedVO" ),
    OPERATION_SUB_TASK_PUBLISHED("operation_sub_task_published","子工序任务发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationSubTaskPublishedVO" ),
    OPERATION_TASK_PUBLISHED("operation_task_published","工序任务发布信息" ,"com.yhl.scp.mps.operationPublished.vo.OperationTaskPublishedVO" ),
    SUPPLY_PUBLISHED("supply_published","供应发布信息" ,"com.yhl.scp.mps.operationPublished.vo.SupplyPublishedVO" ),
    WORK_ORDER_PUBLISHED("work_order_published","制造订单发布信息" ,"com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO" ),
    MASTER_PLAN_PUBLISHED_LOG("master_plan_published_log","主生产计划发布日志" ,"com.yhl.scp.mps.plan.vo.MasterPlanPublishedLogVO" ),

    CAPACITY_SUPPLY_RELATIONSHIP_EXCEPTION("capacity_supply_relationship_exception", "产能平衡异常", "com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipExceptionVO"),
    CAPACITY_SUPPLY_RELATIONSHIP_READ_ONLY("capacity_supply_relationship_read_only", "产能分配明细（只读）", "com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipReadOnlyVO"),

    MASTER_PLAN_PUBLISHED_APPROVAL("master_plan_published_approval", "资源权限", "com.yhl.scp.mps.published.vo.MasterPlanPublishedApprovalVO"),
    SDS_ORD_OPERATION_HISTORY_LOG("sds_ord_operation_history_log", "工序历史记录", "com.yhl.scp.mps.plan.vo.SdsOrdOperationHistoryLogVO"),
    DYNAMIC_DELIVERY_TRACKING_REPORT("dynamic_delivery_tracking_report", "动态交付跟踪工序任务明细反馈", "com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingReportVO"),
    DYNAMIC_DELIVERY_TRACKING("dynamic_delivery_tracking", "动态交付跟踪", "com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingVO"),
    DYNAMIC_DELIVERY_TRACKING_SUB_TASK("dynamic_delivery_tracking_sub_task", "动态交付跟踪工序任务", "com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO"),
    DYNAMIC_DELIVERY_TRACKING_TASK("dynamic_delivery_tracking_task", "动态交付跟踪工序任务明细", "com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO"),
    ;


    ObjectTypeEnum(String code, String desc, String mappingValue) {
        this.code = code;
        this.desc = desc;
        this.mappingValue = mappingValue;
    }

    /**
     * 表名 / 视图名
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 映射类
     */
    private String mappingValue;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getMappingValue() {
        return mappingValue;
    }

    void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }

    /**
     * 对象类型表名映射
     */
    public static final Map<String, String> OBJECT_TABLE_MAP = Arrays.stream(ObjectTypeEnum.values()).sequential()
            .collect(Collectors.toMap(ObjectTypeEnum::name, ObjectTypeEnum::getCode, (t1, t2) -> t2));

}
