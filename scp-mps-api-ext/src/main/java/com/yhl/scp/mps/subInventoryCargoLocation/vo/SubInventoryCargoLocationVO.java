package com.yhl.scp.mps.subInventoryCargoLocation.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SubInventoryCargoLocationVO</code>
 * <p>
 * VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-30 10:10:02
 */
@ApiModel(value = "VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubInventoryCargoLocationVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 539761233286152003L;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @FieldInterpretation(value = "公司")
    private String corporationCode;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String factoryName;
    /**
     * 仓库
     */
    @ApiModelProperty(value = "仓库")
    @FieldInterpretation(value = "仓库")
    private String stashCode;
    /**
     * 仓库描述
     */
    @ApiModelProperty(value = "仓库描述")
    @FieldInterpretation(value = "仓库描述")
    private String stashName;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpaceCode;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @FieldInterpretation(value = "货位描述")
    private String freightSpaceName;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    @FieldInterpretation(value = "来源")
    private String source;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    @FieldInterpretation(value = "是否有效")
    private String valid;
    /**
     * ERP货位
     */
    @ApiModelProperty(value = "ERP货位")
    @FieldInterpretation(value = "ERP货位")
    private String erpFreightSpaceCode;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @FieldInterpretation(value = "更新时间")
    private Date updateTime;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;

    @ApiModelProperty(value = "库存点类型")
    @FieldInterpretation(value = "库存点类型")
    private String stockPointType;

    @Override
    public void clean() {

    }

}
