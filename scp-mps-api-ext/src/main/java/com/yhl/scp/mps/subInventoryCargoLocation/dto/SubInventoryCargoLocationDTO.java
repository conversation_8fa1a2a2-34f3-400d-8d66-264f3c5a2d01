package com.yhl.scp.mps.subInventoryCargoLocation.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SubInventoryCargoLocationDTO</code>
 * <p>
 * DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-30 10:10:00
 */
@ApiModel(value = "DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubInventoryCargoLocationDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -75142263269402418L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 公司
     */
    @JSONField(name = "plantCode")
    @ApiModelProperty(value = "公司")
    private String corporationCode;
    /**
     * 库存点代码
     */
    @JSONField(name = "businessUnitt")
    @ApiModelProperty(value = "库存点代码")
    private String factoryCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存名称")
    private String factoryName;
    /**
     * 仓库
     */
    @JSONField(name = "warehouseCode")
    @ApiModelProperty(value = "仓库")
    private String stashCode;
    /**
     * 仓库描述
     */
    @JSONField(name = "wareDesc")
    @ApiModelProperty(value = "仓库描述")
    private String stashName;
    /**
     * 货位
     */
    @JSONField(name = "locatorCode")
    @ApiModelProperty(value = "货位")
    private String freightSpaceCode;
    /**
     * 货位描述
     */
    @JSONField(name = "locatorDesc")
    @ApiModelProperty(value = "货位描述")
    private String freightSpaceName;
    /**
     * 来源
     */
    @JSONField(name = "source")
    @ApiModelProperty(value = "来源")
    private String source;
    /**
     * 是否有效
     */
    @JSONField(name = "enableFlag")
    @ApiModelProperty(value = "是否有效")
    private String valid;
    /**
     * ERP货位
     */
    @JSONField(name = "erpLocatorCode")
    @ApiModelProperty(value = "ERP货位")
    private String erpFreightSpaceCode;
    /**
     * 更新时间
     */
    @JSONField(name = "lastUpdateDate")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
