package com.yhl.scp.mps.schedule.service;


import com.yhl.platform.common.LabelValue;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.sds.extension.order.vo.OperationVO;

import java.util.List;

/**
 * <code>OperationScheduleService</code>
 * <p>
 * OperationScheduleService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-09 11:20:26
 */
public interface HandworkScheduleService {
    /**
     * 手工排程
     * @param adjustmentParams 调整参数
     */
    void doHandworkSchedule(List<AdjustmentParam> adjustmentParams, PlanningHorizonVO planningHorizon, List<OperationVO> adjustOperationList,
                            List<String> detailIds, List<String> szWorkOrderIds);

    /**
     * 主计划编辑入口-校验接口
     * @param adjustmentParam
     */
    void doHandworkScheduleCheck(RzzAdjustmentParam adjustmentParam);

    /**
     * 主计划编辑入口
     * @param adjustmentParam
     */
    void doHandworkSchedule(RzzAdjustmentParam adjustmentParam);

    /**
     * 主计划编辑入口-批量（走手工调整默认顺延）
     * @param adjustmentParams
     */
    void doHandworkScheduleBatch(List<RzzAdjustmentParam> adjustmentParams);

    /**
     * 手工编辑，批量拖拽
     *
     * @param adjustmentParam
     */
    void doHandworkScheduleBatch(RzzAdjustmentParam adjustmentParam);



    List<LabelValue<String>> resourceDropDown(String operationId);

    /**
     * 主计划编辑(报工异常反馈), 工序顺延
     * @param ids
     */
	void doHandworkScheduleForFeedBack(List<String> ids);

}
