package com.yhl.scp.mps.fixtureRelation.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.mps.fixtureRelation.dto.ProductFixtureRelationDTO;
import com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO;

import java.util.List;

/**
 * <code>ProductFixtureRelationService</code>
 * <p>
 * 产品与工装关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-07 11:14:18
 */
public interface ProductFixtureRelationService extends BaseService<ProductFixtureRelationDTO, ProductFixtureRelationVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductFixtureRelationVO}
     */
    List<ProductFixtureRelationVO> selectAll();

    List<LabelValue<String>> getStandardResource(String organizationId, String standardResourceCode);

    List<LabelValue<String>> getPhysicalResourceById(String standardResourceId);

    /**
     * 定时任务同步模具工装
     *
     * @return
     */
    BaseResponse<Void> synMoldToolingGroupDir(String tenantId);

    /**
     * 同步模具工装
     *
     * @return
     */
    BaseResponse<Void> handleMoldToolingGroupDir(List<MesMoldToolingGroupDir> list);
}
