package com.yhl.scp.mps.algorithm.schedule.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <code>OperationData</code>
 * <p>
 * 工序
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-17 14:01:15
 */
@Data
public class RzzOperationData implements Serializable {

    private static final long serialVersionUID = -7122787179017943553L;

    /**
     * 工序ID
     */
    private String operationId;

    /**
     * 设置点计划资源id
     */
    private String currentPlannedResourceId;

    /**
     * 候选资源列表,主要包括制造效率信息
     */
    private List<OperationOnResource> operationOnResource;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 制造订单ID
     */
    private String workOrderId;

    /**
     * 主要用于计算跟后工序的间隔时间以及衔接方式
     */
    private List<InputMaterial> operationInputMaterial;

    /**
     * 主要用于计算跟后工序的间隔时间以及衔接方式
     */
    private List<OutputMaterial> operationOutputMaterial;

    /**
     * 与前工序最小间隔时间
     */
    private String minTimeConstraint;

    /**
     * 与前工序最大间隔时间
     */
    private String maxTimeConstraint;
    /**
     * 工序代码
     */
    private String operationCode;

    /**
     * 接续方式
     * ES("ES","前工作完工后，后工作方能开工"),
     * SS("SS","前工作开工后，后工作方能开工"),
     * SSEE("SSEE","前工作开工后，后工作方能开工，并且后工作不能比前工作提前完工")
     */
    private String connType;

    /**
     * 上次排程信息
     */
    private LastScheduled lastScheduled;


    /**
     * 最早开始时间 yyyy-MM-dd HH:mm:ss
     */
    private Date earliestBeginTime;

    /**
     * 最晚结束时间 yyyy-MM-dd HH:mm:ss
     */
    private Date latestEndTime;

    /**
     * 算法计算的最晚结束时间 "YYYY-MM-DD hh:mm:ss"
     */
    private String latestEndTimeCal;

    /**
     * 算法计算的最早开始时间 "YYYY-MM-DD hh:mm:ss"
     */
    private String earliestBeginTimeCal;

    /**
     * 状态
     * 1. processing
     * 2. unplanned
     * 3. locked
     * 4. complete
     * <p>
     * 1. 计划状态plan_status为已开始传processing
     * 2. plan_status为已完工传complete
     * 3. frozen为是，传locked
     * 4. 其余情况该字段不传值
     */
    private String status;

    /**
     * 父工序id
     */
    private String parentId;

    /**
     * 规格数据
     */
    private List<Map<String, Spec>> specs;

    /**
     * 弃用
     */
    private String prevOperationId;

    private String nextOperationId;

    private String planUnitId;

    private String routingId;

    private String routingStepId;

    private Integer routingStepSequenceNo;

    private Set<String> tags;

    private String standardStepId;

    private String productType;

    private String workWearCategory;

    private Duration setupDuration;

    private Duration processDuration;

    private Duration cleanupDuration;

    private Boolean whetherTime;

    private BigDecimal feedBackQuantity;

    private String planStatus;

}
