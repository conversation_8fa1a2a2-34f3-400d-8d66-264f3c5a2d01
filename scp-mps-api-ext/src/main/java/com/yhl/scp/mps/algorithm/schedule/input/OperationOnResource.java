package com.yhl.scp.mps.algorithm.schedule.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>OperationOnResource</code>
 * <p>
 * 候选资源列表,主要包括制造效率信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-22 14:01:21
 */
@Data
public class OperationOnResource implements Serializable {
    private static final long serialVersionUID = 7618562242478238649L;

    /**
     * 资源id
     */
    private String resourceId;
    /**
     * 候选资源id
     */
    private String operationOnResourceId;

    /**
     * 资源代码
     */
    private String resourceCode;

    /**
     * 设置时间
     */
    private TaskDuration setupDuration;
    /**
     * 制造时间
     */
    private TaskDuration processDuration;
    /**
     * 清理时间
     */
    private TaskDuration cleanupDuration;

    /**
     * 配套使用号
     */
    private String matchCode;
    /**
     * 替代工器具组号
     */
    private String altToolCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 投入频率
     */
    private TaskDuration prepareDur;

    /**
     * 最大制造批量
     */
    private Long maxMadeBatch;

    // /**
    //  * 规格列表  暂时不做处理
    //  */
    // private String specs;

    /**
     * 是否被禁用
     */
    private boolean disable;

    /**
     * 资源类型，主资源/工具资源
     * 1. mainResource
     * 2. toolResource
     */
    private String resourceType;

    /**
     * 生产线
     */
    private String line;
    /**
     * 设置必要资源量
     */
    private BigDecimal setupUnitBatchSize;
    /**
     * 制造必要资源量
     */
    private BigDecimal productionUnitBatchSize;
    /**
     * 清洗必要资源量
     */
    private BigDecimal cleanupUnitBatchSize;

    /**
     * 模具数量限制
     */
    private BigDecimal moldQuantityLimit;

}
