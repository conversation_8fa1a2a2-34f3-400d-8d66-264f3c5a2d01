package com.yhl.scp.mps.reportingFeedback.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReportFeedback;
import com.yhl.scp.mps.reportingFeedback.dto.MpsProReportingFeedbackDTO;
import com.yhl.scp.mps.reportingFeedback.vo.MpsProReportingFeedbackVO;

import java.util.List;

/**
 * <code>MpsProReportingFeedbackService</code>
 * <p>
 * 生产报工反馈应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-03 10:31:48
 */
public interface MpsProReportingFeedbackService extends BaseService<MpsProReportingFeedbackDTO, MpsProReportingFeedbackVO> {

    /**
     * 查询所有
     *
     * @return list {@link MpsProReportingFeedbackVO}
     */
    List<MpsProReportingFeedbackVO> selectAll();

    /**
     * mes数据接入
     * @param mesProFeedback
     */
    void doInsertBatch(List<MpsProReportingFeedbackDTO> mesProFeedback);

	List<String> selectProductOrderCodes(String productOrderCode);

    BaseResponse<Void> syncProReportingFeedback(String tenantId);

    /**
     * 将MpsProReportingFeedback数据转换为FeedbackProduction
     * @param reportingFeedbackDTOS
     */
    void doConvert(String scenario, List<MpsProReportingFeedbackDTO> reportingFeedbackDTOS);

    BaseResponse<Void> handleReportingFeedback(String scenario, List<MesReportFeedback> o);

    void doProcessStartOperation();

}
