package com.yhl.scp.mps.algorithm.schedule.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <code>TaskDuration</code>
 * <p>
 * 工序资源任务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-22 14:15:40
 */
@Data
public class TaskDuration implements Serializable {
    private static final long serialVersionUID = -7412682095884679707L;

    /**
     * 效率对象
     */
    private TaskDurationEffective taskDurationEffective;

    /**
     * 定时长秒
     */
    private int fixedDurSeconds;

}
