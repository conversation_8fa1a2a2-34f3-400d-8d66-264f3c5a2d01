package com.yhl.scp.dfp.warehouse.infrastructure.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;

public interface WarehouseReleaseToWarehouseDao extends BaseDao<WarehouseReleaseToWarehousePO, WarehouseReleaseToWarehouseVO> {

    List<WarehouseReleaseToWarehouseVO> selectInRoadWarehouse(@Param("productCodeList") List<String> demandProductCodeList
            , @Param("shipmentLocatorCodes") List<String> shipmentLocatorCodes);

    List<String> selectTargetStockLocation();

	List<WarehouseReleaseToWarehouseMonthVO> selectMonthVOByParams(@Param("params") Map<String, Object> params);

	List<WarehouseReleaseToWarehouseDayVO> selectDayVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据本厂编码批量查询
     *
     * @param oemCodeList
     * @param productCodeList
     * @param startYearMonth
     * @param endYearMonth
     * @return
     */
    List<WarehouseReleaseRecordMonthVO> selectMonthVOByItemCodes(@Param("oemCodeList") List<String> oemCodeList,
                                                                 @Param("productCodeList") List<String> productCodeList,
                                                                 @Param("startYearMonth") String startYearMonth,
                                                                 @Param("endYearMonth") String endYearMonth);

	List<WarehouseReleaseToWarehouseVO> selectNotReceiveInland(@Param("oemCodeList") List<String> oemCodes, 
			@Param("productCodeList") List<String> productCodes);

	List<WarehouseReceiveCheckVO> selectNotReceiveInlandStatistics(@Param("params") Map<String, Object> params);
}
