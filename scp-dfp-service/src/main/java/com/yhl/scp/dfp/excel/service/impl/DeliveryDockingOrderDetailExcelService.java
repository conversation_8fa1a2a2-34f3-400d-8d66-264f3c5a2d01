package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.deliverydockingorder.convertor.DeliveryDockingOrderDetailConvertor;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDetailDTO;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDetailDao;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderDetailService;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderService;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DeliveryDockingOrderDetailExcelService</code>
 * <p>
 * 发货对接单导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 15:26:03
 */
@Service
public class DeliveryDockingOrderDetailExcelService extends AbstractExcelService<DeliveryDockingOrderDetailDTO, DeliveryDockingOrderDetailPO, DeliveryDockingOrderDetailVO> {

    @Resource
    private DeliveryDockingOrderDetailService deliveryDockingOrderDetailService;

    @Resource
    private DeliveryDockingOrderService deliveryDockingOrderService;

    @Resource
    private DeliveryDockingOrderDetailDao deliveryDockingOrderDetailDao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;


    @Override
    public BaseDao<DeliveryDockingOrderDetailPO, DeliveryDockingOrderDetailVO> getBaseDao() {
        return deliveryDockingOrderDetailDao;
    }

    @Override
    public Function<DeliveryDockingOrderDetailDTO, DeliveryDockingOrderDetailPO> getDTO2POConvertor() {
        return DeliveryDockingOrderDetailConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<DeliveryDockingOrderDetailDTO> getDTOClass() {
        return DeliveryDockingOrderDetailDTO.class;
    }

    @Override
    public BaseService<DeliveryDockingOrderDetailDTO, DeliveryDockingOrderDetailVO> getBaseService() {
        return deliveryDockingOrderDetailService;
    }

    @Override
    protected void processData(List<DeliveryDockingOrderDetailDTO> dtoList, ImportContext importContext, ImportAnalysisResultHolder<DeliveryDockingOrderDetailDTO, DeliveryDockingOrderDetailPO> resultHolder) {
        Map<String, Object> extraMap = importContext.getExtraMap();
        // String oemCode = (String) extraMap.get("oemCode");
        // String yearMonth = (String) extraMap.get("yearMonth");
        // Date deliveryTime = DateUtils.stringToDate(yearMonth, "yyyyMMdd");
        String deliveryDockingNumber = (String) extraMap.get("deliveryDockingNumber");
        for (DeliveryDockingOrderDetailDTO deliveryDockingOrderDetailDTO : dtoList) {
            // deliveryDockingOrderDetailDTO.setOemCode(oemCode);
            // deliveryDockingOrderDetailDTO.setDeliveryTime(deliveryTime);
            deliveryDockingOrderDetailDTO.setDeliveryDockingNumber(deliveryDockingNumber);
        }
        ImportRelatedDataHolder<DeliveryDockingOrderDetailPO> relatedDataHolder = prepareData(dtoList);
        classificationData(importContext, dtoList, resultHolder, relatedDataHolder);
        fillIdForUpdateData(resultHolder.getUpdateList(), relatedDataHolder.getExistingDataMap());
    }

    @Override
    protected void fillIdForUpdateData(List<DeliveryDockingOrderDetailDTO> updateList, Map<String, DeliveryDockingOrderDetailPO> existingDataMap) {
        for (DeliveryDockingOrderDetailDTO modelDTO : updateList) {
            DeliveryDockingOrderDetailPO dockingOrderDetailPO = existingDataMap.get(modelDTO.getDeliveryDockingNumber() + "&" + modelDTO.getProductCode());
            if (Objects.isNull(dockingOrderDetailPO)) {
                continue;
            }
            modelDTO.setId(dockingOrderDetailPO.getId());
        }
    }

    @Override
    protected ImportRelatedDataHolder<DeliveryDockingOrderDetailPO> prepareData(List<DeliveryDockingOrderDetailDTO> deliveryDockingOrderDetailDTOS) {

        // 找到数据库现在所有的数据
        List<DeliveryDockingOrderDetailPO> alreadyExitData = deliveryDockingOrderDetailDao.selectByParams(new HashMap<>(2));
        Map<String, DeliveryDockingOrderDetailPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getDeliveryDockingNumber() + "&" + x.getProductCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("deliveryDockingNumber", "productCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<DeliveryDockingOrderDetailPO>builder()
                .existingData(alreadyExitData)
                .existingDataMap(codeToPOMap)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .build();
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<DeliveryDockingOrderDetailDTO, DeliveryDockingOrderDetailPO> resultHolder, ImportContext importContext) {
        List<DeliveryDockingOrderDetailDTO> insertList = resultHolder.getInsertList();
        List<DeliveryDockingOrderDetailDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    private void verifyPaternity(List<DeliveryDockingOrderDetailDTO> checkList, List<DataImportInfo> importLogList) {
        List<String> productCodeList = checkList.stream()
                .map(DeliveryDockingOrderDetailDTO::getProductCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(scenario.getData(), productCodeList);
        Map<String, List<NewProductStockPointVO>> productCodeMap = newProductStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        Iterator<DeliveryDockingOrderDetailDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            DeliveryDockingOrderDetailDTO detailDTO = iterator.next();
            if (isFieldEmpty(detailDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + detailDTO.getRowIndex() + ";[本厂编码]未填写");
                dataImportInfo.setDisplayIndex(detailDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(String.valueOf(detailDTO.getDeliveryQuantity()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + detailDTO.getRowIndex() + ";[发货数量]未填写");
                dataImportInfo.setDisplayIndex(detailDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(String.valueOf(detailDTO.getMustQuantity()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + detailDTO.getRowIndex() + ";[必发数量]未填写");
                dataImportInfo.setDisplayIndex(detailDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(detailDTO.getMaterialEquipment())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + detailDTO.getRowIndex() + ";[物料器具]未填写");
                dataImportInfo.setDisplayIndex(detailDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!productCodeMap.containsKey(detailDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + detailDTO.getRowIndex() + ";[本厂编码]不存在");
                dataImportInfo.setDisplayIndex(detailDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

}
