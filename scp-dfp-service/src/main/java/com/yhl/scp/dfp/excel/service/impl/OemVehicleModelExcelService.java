package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.oem.convertor.OemVehicleModelConvertor;
import com.yhl.scp.dfp.oem.dto.OemVehicleModelDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemVehicleModelPO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemVehicleModelExcelService</code>
 * <p>
 * 主机厂车型信息excel导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-24 17:28:40
 */
@Service
public class OemVehicleModelExcelService extends AbstractExcelService<OemVehicleModelDTO, OemVehicleModelPO, OemVehicleModelVO> {

    @Resource
    private OemVehicleModelService modelService;

    @Resource
    private OemVehicleModelDao oemVehicleModelDao;

    @Resource
    private OemService oemService;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseDao<OemVehicleModelPO, OemVehicleModelVO> getBaseDao() {
        return oemVehicleModelDao;
    }

    @Override
    public Function<OemVehicleModelDTO, OemVehicleModelPO> getDTO2POConvertor() {
        return OemVehicleModelConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<OemVehicleModelDTO> getDTOClass() {
        return OemVehicleModelDTO.class;
    }

    @Override
    public BaseService<OemVehicleModelDTO, OemVehicleModelVO> getBaseService() {
        return modelService;
    }

    @Override
    protected void fillIdForUpdateData(List<OemVehicleModelDTO> updateList, Map<String, OemVehicleModelPO> existingDataMap) {
        for (OemVehicleModelDTO modelDTO : updateList) {
            OemVehicleModelPO modelPO = existingDataMap.get(modelDTO.getOemCode() + "&" + modelDTO.getOemVehicleModelCode());
            if (Objects.isNull(modelPO)) {
                continue;
            }
            modelDTO.setId(modelPO.getId());
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<OemVehicleModelDTO, OemVehicleModelPO> resultHolder, ImportContext importContext) {
        List<OemVehicleModelDTO> insertList = resultHolder.getInsertList();
        List<OemVehicleModelDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<OemVehicleModelDTO> checkList, List<DataImportInfo> importLogList) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<String> vehicleModelCodeList = checkList.stream()
                .map(OemVehicleModelDTO::getOemVehicleModelCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<String> accessPositionList = checkList.stream()
                .map(OemVehicleModelDTO::getAccessPosition)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectByVehicleModelCode(scenario.getData(), vehicleModelCodeList);
        List<NewProductStockPointVO> newProductStockPointVOS2 = mdsFeign.selectByLoadPosition(scenario.getData(), accessPositionList);
        Map<String, List<NewProductStockPointVO>> vehicleCodeListMap = newProductStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));
        Map<String, List<NewProductStockPointVO>> loadPositionMap = newProductStockPointVOS2.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getLoadingPositionSub));
        Iterator<OemVehicleModelDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            OemVehicleModelDTO oemVehicleModelDTO = iterator.next();
            if (isFieldEmpty(oemVehicleModelDTO.getOemCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemVehicleModelDTO.getRowIndex() + ";[主机厂编码]未填写");
                dataImportInfo.setDisplayIndex(oemVehicleModelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(oemVehicleModelDTO.getOemVehicleModelCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemVehicleModelDTO.getRowIndex() + ";[车型编码]未填写");
                dataImportInfo.setDisplayIndex(oemVehicleModelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(oemVehicleModelDTO.getAccessPosition())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemVehicleModelDTO.getRowIndex() + ";[取数位置]未填写");
                dataImportInfo.setDisplayIndex(oemVehicleModelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!vehicleCodeListMap.containsKey(oemVehicleModelDTO.getOemVehicleModelCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemVehicleModelDTO.getRowIndex() + ";[车型编码]不存在");
                dataImportInfo.setDisplayIndex(oemVehicleModelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!loadPositionMap.containsKey(oemVehicleModelDTO.getAccessPosition())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + oemVehicleModelDTO.getRowIndex() + ";[取数位置]不存在");
                dataImportInfo.setDisplayIndex(oemVehicleModelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<OemVehicleModelPO> prepareData(List<OemVehicleModelDTO> oemVehicleModelDTOS) {
        // 找到数据库现在所有的数据
        List<OemVehicleModelPO> alreadyExitData = oemVehicleModelDao.selectByParams(new HashMap<>(2));
        Map<String, OemVehicleModelPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&"
                + x.getOemVehicleModelCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("oemCode", "oemVehicleModelCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<OemVehicleModelPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

}
