package com.yhl.scp.dfp.warehouse.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.PageUtils;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.stock.service.InventoryRecieveConfirmationService;
import com.yhl.scp.dfp.stock.vo.InventoryRecieveConfirmationVO;
import com.yhl.scp.dfp.warehouse.convertor.WarehouseReleaseToWarehouseConvertor;
import com.yhl.scp.dfp.warehouse.domain.entity.WarehouseReleaseToWarehouseDO;
import com.yhl.scp.dfp.warehouse.domain.service.WarehouseReleaseToWarehouseDomainService;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReceiveCheckDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseToWarehouseDao;
import com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WarehouseReleaseToWarehouseServiceImpl extends AbstractService implements WarehouseReleaseToWarehouseService {

    @Resource
    private WarehouseReleaseToWarehouseDao warehouseReleaseToWarehouseDao;

    @Resource
    private WarehouseReleaseToWarehouseDomainService warehouseReleaseToWarehouseDomainService;
    
    @Resource
    private OemService oemService;
    
    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    
    @Resource
    private NewMdsFeign newMdsFeign;
    
    @Resource
    private OemTransportTimeService oemTransportTimeService;
    
    @Resource
    private InventoryRecieveConfirmationService inventoryRecieveConfirmationService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        // 0.数据转换
        WarehouseReleaseToWarehouseDO warehouseReleaseToWarehouseDO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Do(warehouseReleaseToWarehouseDTO);
        WarehouseReleaseToWarehousePO warehouseReleaseToWarehousePO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Po(warehouseReleaseToWarehouseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseReleaseToWarehouseDomainService.validation(warehouseReleaseToWarehouseDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(warehouseReleaseToWarehousePO);
        warehouseReleaseToWarehouseDao.insert(warehouseReleaseToWarehousePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        // 0.数据转换
        WarehouseReleaseToWarehouseDO warehouseReleaseToWarehouseDO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Do(warehouseReleaseToWarehouseDTO);
        WarehouseReleaseToWarehousePO warehouseReleaseToWarehousePO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Po(warehouseReleaseToWarehouseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseReleaseToWarehouseDomainService.validation(warehouseReleaseToWarehouseDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(warehouseReleaseToWarehousePO);
        warehouseReleaseToWarehouseDao.update(warehouseReleaseToWarehousePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<WarehouseReleaseToWarehouseDTO> list) {
        List<WarehouseReleaseToWarehousePO> newList = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        warehouseReleaseToWarehouseDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<WarehouseReleaseToWarehouseDTO> list) {
        List<WarehouseReleaseToWarehousePO> newList = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        warehouseReleaseToWarehouseDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return warehouseReleaseToWarehouseDao.deleteBatch(idList);
        }
        return warehouseReleaseToWarehouseDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public WarehouseReleaseToWarehouseVO selectByPrimaryKey(String id) {
        WarehouseReleaseToWarehousePO po = warehouseReleaseToWarehouseDao.selectByPrimaryKey(id);
        return WarehouseReleaseToWarehouseConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "WAREHOUSE_RELEASE_TO_WAREHOUSE")
    public List<WarehouseReleaseToWarehouseVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "WAREHOUSE_RELEASE_TO_WAREHOUSE")
    public List<WarehouseReleaseToWarehouseVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<WarehouseReleaseToWarehouseVO> dataList = warehouseReleaseToWarehouseDao.selectByCondition(sortParam, queryCriteriaParam);
        WarehouseReleaseToWarehouseServiceImpl target = SpringBeanUtils.getBean(WarehouseReleaseToWarehouseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> selectByParams(Map<String, Object> params) {
        List<WarehouseReleaseToWarehousePO> list = warehouseReleaseToWarehouseDao.selectByParams(params);
        return WarehouseReleaseToWarehouseConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> invocation(List<WarehouseReleaseToWarehouseVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> getInRoad(List<String> demandProductCodeList,
                                                    List<String> shipmentLocatorCodes) {
        return warehouseReleaseToWarehouseDao.selectInRoadWarehouse(demandProductCodeList, shipmentLocatorCodes);
    }

    @Override
    public List<String> selectTargetStockLocation() {
        return warehouseReleaseToWarehouseDao.selectTargetStockLocation();
    }

	@Override
	public List<WarehouseReleaseToWarehouseMonthVO> selectMonthVOByParams(Map<String, Object> params) {
		return warehouseReleaseToWarehouseDao.selectMonthVOByParams(params);
	}

	@Override
	public List<WarehouseReleaseToWarehouseDayVO> selectDayVOByParams(Map<String, Object> params) {
		return warehouseReleaseToWarehouseDao.selectDayVOByParams(params);
	}

	@Override
	public List<WarehouseReleaseToWarehouseVO> selectNotReceiveInland(List<String> oemCodes,
			List<String> productCodes) {
		return warehouseReleaseToWarehouseDao.selectNotReceiveInland(oemCodes, productCodes);
	}

	@Override
	public PageInfo<WarehouseReceiveCheckVO> queryReceiveCheckPage(WarehouseReceiveCheckDTO warehouseReceiveCheckDTO) {
		//1.先查询所有国内主机厂
		List<String> inlandOemCodes = oemService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), "marketType" , OemTradeTypeEnum.IN.getCode()))
				.stream().map(OemVO::getOemCode).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(inlandOemCodes)) {
			return new PageInfo<>();
		}
		//获取是国内且是非MES模式的主机厂
		BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        Map<String, String> oemStockPointMaps = oemStockPointMapService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"oemCodeList" , inlandOemCodes,
    			"stockPointCodesNotInList", Arrays.asList(rangeData)))
        		.stream().collect(Collectors.toMap(OemStockPointMapVO::getOemCode, 
        				OemStockPointMapVO::getStockPointCode,(v1, v2) -> v1));
        List<String> stockPointCodes = new ArrayList<>(oemStockPointMaps.values());
        //获取数据来源是非MES的库存点
        if(oemStockPointMaps.isEmpty()) {
        	return new PageInfo<>();
        }
    	List<String> mesStockPointCodes = newMdsFeign.selectNewStockPointVOByParams(SystemHolder.getScenario(), 
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "stockPointCodes", stockPointCodes))
        		.stream().filter(e -> !"MES".equals(e.getInterfaceSource()))
        		.map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    	//获取对应的中转库发货数据
    	List<String> inlandNotMesOemCodes = new ArrayList<>();
    	for (Entry<String, String> entry : oemStockPointMaps.entrySet()) {
			if(mesStockPointCodes.contains(entry.getValue()) && !inlandNotMesOemCodes.contains(entry.getKey())) {
				inlandNotMesOemCodes.add(entry.getKey());
			}
		}
    	if(CollectionUtils.isEmpty(inlandNotMesOemCodes)) {
        	return new PageInfo<>();
        }
		//2.查询中转库这些主机厂的数据
    	Map<String, Object> queryMap = new HashMap<>();
    	queryMap.put("oemCodeList", inlandNotMesOemCodes);
    	queryMap.put("listNum", warehouseReceiveCheckDTO.getListNum());
    	queryMap.put("itemCode", warehouseReceiveCheckDTO.getItemCode());
    	queryMap.put("shipmentLocatorCode", warehouseReceiveCheckDTO.getShipmentLocatorCode());
    	queryMap.put("beginDate", DateUtils.dateToString(DateUtils.getDayFirstTime(DateUtils.moveDay(new Date(), -14)), 
    			DateUtils.COMMON_DATE_STR1));
    	queryMap.put("endDate", DateUtils.dateToString(DateUtils.getDayLastTime(new Date()), DateUtils.COMMON_DATE_STR1));
    	List<WarehouseReceiveCheckVO> notReceives = warehouseReleaseToWarehouseDao
    			.selectNotReceiveInlandStatistics(queryMap);
    	if(CollectionUtils.isEmpty(notReceives)) {
        	return new PageInfo<>();
        }
		//分页处理
		notReceives.sort(Comparator.comparing(WarehouseReceiveCheckVO::getListNum)
				.thenComparing(Comparator.comparing(WarehouseReceiveCheckVO::getItemCode))
				.thenComparing(Comparator.comparing(WarehouseReceiveCheckVO::getShipmentLocatorCode))
				);
		PageInfo pageInfo = PageUtils.getPageInfo(notReceives, warehouseReceiveCheckDTO.getPageNum(),
				warehouseReceiveCheckDTO.getPageSize());
		List<WarehouseReceiveCheckVO> pageList = pageInfo.getList();
		
		//获取发货清单号最大的到达时间
    	Map<String, Date> listNumMaxTimeMap = pageList.stream()
    		    .collect(Collectors.toMap( WarehouseReceiveCheckVO::getListNum,
    		        WarehouseReceiveCheckVO::getCreationDate,
    		        (date1, date2) -> date1.after(date2) ? date1 : date2
    		    ));
		//3.查询主机厂运输时间
    	List<String> cuurOemCodes = pageList.stream().map(WarehouseReceiveCheckVO::getOemCode).distinct()
    			.collect(Collectors.toList());
        List<OemTransportTimeVO> oemTransportTimeList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", cuurOemCodes));
        Map<String, List<OemTransportTimeVO>> oemTransportTimeMap = oemTransportTimeList.stream()
        		.collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
		//处理入库数量数据
        List<String> listNums = pageList.stream().map(WarehouseReceiveCheckVO::getListNum).distinct()
    			.collect(Collectors.toList());
        List<String> itemCodes = pageList.stream().map(WarehouseReceiveCheckVO::getItemCode).distinct()
    			.collect(Collectors.toList());
        List<String> shipmentLocatorCodes = pageList.stream().map(WarehouseReceiveCheckVO::getShipmentLocatorCode).distinct()
    			.collect(Collectors.toList());
        List<InventoryRecieveConfirmationVO> recieveConfirmationList = inventoryRecieveConfirmationService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"listNums" , listNums, "itemCodes" , itemCodes, "shipmentLocatorCodes", shipmentLocatorCodes));
        Map<String, InventoryRecieveConfirmationVO> recieveConfirmationMap = recieveConfirmationList.stream()
        		.collect(Collectors.toMap(e -> String.join("&", e.getListNum(), e.getItemCode(), e.getShipmentLocatorCode()),
        				e->e,(v1, v2) -> v1));
		pageList.forEach( e -> {
			e.setStoreQty(BigDecimal.ZERO);
			InventoryRecieveConfirmationVO inventoryRecieveConfirmationVO = recieveConfirmationMap
					.get(String.join("&", e.getListNum(), e.getItemCode(), e.getShipmentLocatorCode()));
			if(inventoryRecieveConfirmationVO != null) {
				e.setStoreQty(inventoryRecieveConfirmationVO.getStoreQty());
				e.setInventoryRecieveConfirmationId(inventoryRecieveConfirmationVO.getId());
			}
			e.setReceiveQty(e.getSumQty().subtract(e.getStoreQty()));
			e.setEstimatedArrivalTime(listNumMaxTimeMap.get(e.getListNum()));
			//处理预计到达时间
			List<OemTransportTimeVO> oemTransportTimeVOS = oemTransportTimeMap.get(e.getOemCode());
			if(CollectionUtils.isNotEmpty(oemTransportTimeVOS)) {
				OemTransportTimeVO oemTransportTimeVO =
	                    oemTransportTimeVOS.stream().max(Comparator.comparing(OemTransportTimeVO::getPriority))
	                    .orElse(new OemTransportTimeVO());
				BigDecimal transportationTime =  null == oemTransportTimeVO.getTransportationTime() ?
	                    BigDecimal.ZERO : oemTransportTimeVO.getTransportationTime();
				Integer moveMinute = transportationTime.multiply(BigDecimal.valueOf(60)).intValue();
				Date estimatedArrivalTime = listNumMaxTimeMap.get(e.getListNum());
				e.setEstimatedArrivalTime(DateUtils.moveMinute(estimatedArrivalTime, moveMinute));
			}
		});
		return pageInfo;
	}

	@Override
	public List<WarehouseReceiveCheckVO> selectNotReceiveInlandStatistics(Map<String, Object> queryMap) {
		return warehouseReleaseToWarehouseDao.selectNotReceiveInlandStatistics(queryMap);
	}

}
