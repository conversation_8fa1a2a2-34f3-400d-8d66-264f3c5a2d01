package com.yhl.scp.dfp.demand.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.common.enums.CategoryEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import com.yhl.scp.dfp.demand.convertor.ComprehensiveEvaluationCoefficientConvertor;
import com.yhl.scp.dfp.demand.domain.entity.ComprehensiveEvaluationCoefficientDO;
import com.yhl.scp.dfp.demand.domain.service.ComprehensiveEvaluationCoefficientDomainService;
import com.yhl.scp.dfp.demand.dto.ComprehensiveEvaluationCoefficientDTO;
import com.yhl.scp.dfp.demand.dto.ComprehensiveEvaluationCoefficientModifyDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastModifyDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastModifyDetailDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastReviewDTO;
import com.yhl.scp.dfp.demand.infrastructure.dao.ComprehensiveEvaluationCoefficientDao;
import com.yhl.scp.dfp.demand.infrastructure.po.ComprehensiveEvaluationCoefficientPO;
import com.yhl.scp.dfp.demand.service.ComprehensiveEvaluationCoefficientService;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandForecastReviewService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.vo.ComprehensiveEvaluationCoefficientVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.LevelDetailVO;
import com.yhl.scp.dfp.demand.vo.PartLevelVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>ComprehensiveEvaluationCoefficientServiceImpl</code>
 * <p>
 * 需求预测评审产线综合系数应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-15 11:21:52
 */
@Slf4j
@Service
public class ComprehensiveEvaluationCoefficientServiceImpl extends AbstractService implements ComprehensiveEvaluationCoefficientService {

    @Resource
    private ComprehensiveEvaluationCoefficientDao comprehensiveEvaluationCoefficientDao;

    @Resource
    private ComprehensiveEvaluationCoefficientDomainService comprehensiveEvaluationCoefficientDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private DemandForecastVersionService demandForecastVersionService;
    
    @Resource
    private DemandForecastEstablishmentService demandForecastEstablishmentService;
    
    @Resource
    private DemandForecastReviewService demandForecastReviewService;
    
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public BaseResponse<Void> doCreate(ComprehensiveEvaluationCoefficientDTO comprehensiveEvaluationCoefficientDTO) {
        // 0.数据转换
        ComprehensiveEvaluationCoefficientDO comprehensiveEvaluationCoefficientDO = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Do(comprehensiveEvaluationCoefficientDTO);
        ComprehensiveEvaluationCoefficientPO comprehensiveEvaluationCoefficientPO = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Po(comprehensiveEvaluationCoefficientDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        comprehensiveEvaluationCoefficientDomainService.validation(comprehensiveEvaluationCoefficientDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(comprehensiveEvaluationCoefficientPO);
        comprehensiveEvaluationCoefficientDao.insertWithPrimaryKey(comprehensiveEvaluationCoefficientPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ComprehensiveEvaluationCoefficientDTO comprehensiveEvaluationCoefficientDTO) {
        // 0.数据转换
        ComprehensiveEvaluationCoefficientDO comprehensiveEvaluationCoefficientDO = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Do(comprehensiveEvaluationCoefficientDTO);
        ComprehensiveEvaluationCoefficientPO comprehensiveEvaluationCoefficientPO = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Po(comprehensiveEvaluationCoefficientDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        comprehensiveEvaluationCoefficientDomainService.validation(comprehensiveEvaluationCoefficientDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(comprehensiveEvaluationCoefficientPO);
        comprehensiveEvaluationCoefficientDao.update(comprehensiveEvaluationCoefficientPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ComprehensiveEvaluationCoefficientDTO> list) {
        List<ComprehensiveEvaluationCoefficientPO> newList = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        comprehensiveEvaluationCoefficientDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ComprehensiveEvaluationCoefficientDTO> list) {
        List<ComprehensiveEvaluationCoefficientPO> newList = ComprehensiveEvaluationCoefficientConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        comprehensiveEvaluationCoefficientDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return comprehensiveEvaluationCoefficientDao.deleteBatch(idList);
        }
        return comprehensiveEvaluationCoefficientDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ComprehensiveEvaluationCoefficientVO selectByPrimaryKey(String id) {
        ComprehensiveEvaluationCoefficientPO po = comprehensiveEvaluationCoefficientDao.selectByPrimaryKey(id);
        return ComprehensiveEvaluationCoefficientConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_dfp_comprehensive_evaluation_coefficient")
    public List<ComprehensiveEvaluationCoefficientVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_dfp_comprehensive_evaluation_coefficient")
    public List<ComprehensiveEvaluationCoefficientVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ComprehensiveEvaluationCoefficientVO> dataList = comprehensiveEvaluationCoefficientDao.selectByCondition(sortParam, queryCriteriaParam);
        ComprehensiveEvaluationCoefficientServiceImpl target = springBeanUtils.getBean(ComprehensiveEvaluationCoefficientServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ComprehensiveEvaluationCoefficientVO> selectByParams(Map<String, Object> params) {
        List<ComprehensiveEvaluationCoefficientPO> list = comprehensiveEvaluationCoefficientDao.selectByParams(params);
        return ComprehensiveEvaluationCoefficientConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ComprehensiveEvaluationCoefficientVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.COMPREHENSIVE_EVALUATION_COEFFICIENT.getCode();
    }

    @Override
    public List<ComprehensiveEvaluationCoefficientVO> invocation(List<ComprehensiveEvaluationCoefficientVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

	@Override
	public BaseResponse<Void> saveCoefficient(ComprehensiveEvaluationCoefficientModifyDTO modifyDTO) {
		//首先校验需求预测版本是否已发布，已发布不支持修改
		DemandForecastVersionVO demandForecastVersionVO = demandForecastVersionService.selectByPrimaryKey(modifyDTO.getForecastVersionId());
		if(PublishStatusEnum.PUBLISHED.getCode().equals(demandForecastVersionVO.getVersionStatus())) {
			throw new BusinessException("当前需求预测版本已发布");
		}
		//数据校验
		checkUpdateDemand(modifyDTO.getDetailDTOS());
		//修改
		this.updateCoefficientByIds(modifyDTO.getDetailDTOS());
		//新增
		handleCreateVersion(modifyDTO);
		//获取新增的业务数据
		DemandForecastModifyDTO demandForecastModifyDTO = getDemandForecastModify(modifyDTO, demandForecastVersionVO);
		//新增业务数据
		demandForecastEstablishmentService.saveForecastVersion(demandForecastModifyDTO);
		return BaseResponse.success();
	}

	/**
	 * 获取新增的业务数据
	 * @param modifyDTO 综评系数
	 * @param demandForecastVersionVO 预测版本信息
	 * @return
	 */
	private DemandForecastModifyDTO getDemandForecastModify(ComprehensiveEvaluationCoefficientModifyDTO modifyDTO,
			DemandForecastVersionVO demandForecastVersionVO) {
		//查询物品对应的取数位置
		List<String> vehicleModelCodeList = modifyDTO.getDetailDTOS().stream().map(ComprehensiveEvaluationCoefficientDTO::getVehicleModelCode)
				.distinct().collect(Collectors.toList());
		List<NewProductStockPointVO> productInfos = newMdsFeign.selectByVehicleModelCode(getMdsScenario(), vehicleModelCodeList);
		Map<String, String> prodcutMap = new HashMap<>();
		productInfos.forEach( e -> {
			prodcutMap.put(e.getProductCode(), e.getLoadingPositionSub());
		});
		//处理零件层级数据业务预测数据 1.获取对应的客户预测，业务预测数据
		DemandForecastReviewDTO demandForecastReviewDTO = new DemandForecastReviewDTO();
		demandForecastReviewDTO.setOemCode(modifyDTO.getOemCode());
		demandForecastReviewDTO.setProductionLineCodeList(modifyDTO.getProductionLineCodeList());
		demandForecastReviewDTO.setVehicleModelCodeList(Arrays.asList(modifyDTO.getDetailDTOS().get(0).getVehicleModelCode()));
		demandForecastReviewDTO.setVersionCode(demandForecastVersionVO.getVersionCode());
        demandForecastReviewDTO.setDemandType(modifyDTO.getDemandType());
		List<PartLevelVO> partLevelDetailData = demandForecastReviewService.queryPartLevelDetailData(demandForecastReviewDTO);
		//维护业务预测数据
		List<ComprehensiveEvaluationCoefficientDTO> detailDTOS = modifyDTO.getDetailDTOS();
		List<DemandForecastModifyDetailDTO> demandForecastDetailDTOS = new ArrayList<>();
		for (ComprehensiveEvaluationCoefficientDTO detailDTO : detailDTOS) {
			//年月
			String coefficientMonth = detailDTO.getCoefficientMonth();
			BigDecimal comprehensiveEvaluationCoefficient = detailDTO.getComprehensiveEvaluationCoefficient();
			String vehicleModelCode = detailDTO.getVehicleModelCode();
			for (PartLevelVO partLevelVO : partLevelDetailData) {
				String productCode = partLevelVO.getProductCode();
				BigDecimal customerSaleQty = BigDecimal.ZERO;
				for (LevelDetailVO levelDetailVO : partLevelVO.getDetailVOList()) {
					//客户预测数据
					if(CategoryEnum.CUSTOMER_FORECAST.getDesc().equals(levelDetailVO.getCategory())) {
						String saleQuantity = levelDetailVO.getDetails().stream()
						.filter( e -> Objects.equals(coefficientMonth, e.getSaleDate()))
						.map(DynamicDataDetailVO::getSaleQuantity).collect(Collectors.toList()).get(0);
						customerSaleQty = new BigDecimal(saleQuantity);
					}
				}
				for (LevelDetailVO levelDetailVO : partLevelVO.getDetailVOList()) {
					if(CategoryEnum.DEMAND_FORECAST.getDesc().equals(levelDetailVO.getCategory())) {
						DynamicDataDetailVO dynamicDataDetailVO = levelDetailVO.getDetails().stream()
						.filter( e -> Objects.equals(coefficientMonth, e.getSaleDate()))
						.collect(Collectors.toList()).get(0);
						DemandForecastModifyDetailDTO add = new DemandForecastModifyDetailDTO();
						add.setId(dynamicDataDetailVO.getId());
						add.setVersionValue(dynamicDataDetailVO.getVersionValue());
						add.setForecastTime(coefficientMonth.substring(0, 4) + "-" + coefficientMonth.substring(4, 6)  + "-01");
						add.setVehicleCode(vehicleModelCode);
						add.setProductCode(productCode);
						add.setOemCode(modifyDTO.getOemCode());
						add.setForecastValue(customerSaleQty.multiply(comprehensiveEvaluationCoefficient)
								.setScale(0, BigDecimal.ROUND_UP));
						if(add.getId() == null) {
							add.setAccessPosition(prodcutMap.get(productCode));
						}
						demandForecastDetailDTOS.add(add);
					}
				}
			}
		}
		DemandForecastModifyDTO demandForecastModifyDTO = new DemandForecastModifyDTO();
		demandForecastModifyDTO.setDemandType(modifyDTO.getDemandType());
		demandForecastModifyDTO.setVersionId(modifyDTO.getForecastVersionId());
		demandForecastModifyDTO.setDetailDTOS(demandForecastDetailDTOS);
		return demandForecastModifyDTO;
	}
	
    private Boolean checkUpdateDemand(List<ComprehensiveEvaluationCoefficientDTO> detailDTOS) {
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return true;
        }

        List<ComprehensiveEvaluationCoefficientDTO> data = detailDTOS.stream()
                .filter(p -> p.getId() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(data)) {
            return true;
        }

        // 构建 detailMap 和 demandIds
        Map<String, Integer> detailMap = data.stream().filter(p -> p.getId() != null)
                .collect(Collectors.toMap(
                		ComprehensiveEvaluationCoefficientDTO::getId,
                		ComprehensiveEvaluationCoefficientDTO::getVersionValue,
                        (v1, v2) -> v1
                ));

        List<String> coefficientIds = data.stream()
                .map(ComprehensiveEvaluationCoefficientDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<ComprehensiveEvaluationCoefficientPO> establishmentPOS = comprehensiveEvaluationCoefficientDao.selectByPrimaryKeys(coefficientIds);

        if (CollectionUtils.isEmpty(establishmentPOS)) {
            throw new BusinessException("数据不存在");
        }
        for (ComprehensiveEvaluationCoefficientPO coefficientPO : establishmentPOS) {
            if (!coefficientPO.getVersionValue().equals(detailMap.get(coefficientPO.getId()))) {
                throw new BusinessException("修改失败，数据已被修改，请刷新后重试");
            }
        }
        return true;
    }
    
    private void handleCreateVersion(ComprehensiveEvaluationCoefficientModifyDTO modifyDTO) {
        List<ComprehensiveEvaluationCoefficientDTO> detailDTOS = modifyDTO.getDetailDTOS();
        List<ComprehensiveEvaluationCoefficientDTO> insertData = detailDTOS.stream().filter(p -> null == p.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insertData)) {
            log.info("需求预测编制无数据新增");
            return;
        }

        String versionId = modifyDTO.getForecastVersionId();
        for (ComprehensiveEvaluationCoefficientDTO data : insertData) {
        	data.setId(UUIDUtil.getUUID());
        	data.setOemCode(modifyDTO.getOemCode());
        	data.setForecastVersionId(versionId);
        }
        this.doCreateBatch(insertData);
        log.info("新增需求预测编制行数：{}", insertData.size());
    }

	@Override
	public int updateCoefficientByIds(List<ComprehensiveEvaluationCoefficientDTO> detailDTOS) {
		int result = 0;
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return result;
        }
        Date date = new Date();
        for (ComprehensiveEvaluationCoefficientDTO detailDTO : detailDTOS) {
        	if(StringUtils.isEmpty(detailDTO.getId())) {
        		continue;
        	}
        	detailDTO.setModifyTime(date);
            result += comprehensiveEvaluationCoefficientDao.updateCoefficientById(detailDTO);
        }
        return result;
	}
	
	private String getMdsScenario() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        return defaultScenario.getData();
    }

}
