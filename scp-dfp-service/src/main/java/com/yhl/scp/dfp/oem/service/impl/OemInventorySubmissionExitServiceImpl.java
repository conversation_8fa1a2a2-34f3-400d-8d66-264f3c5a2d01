package com.yhl.scp.dfp.oem.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.oem.convertor.OemInventorySubmissionExitConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemInventorySubmissionExitDO;
import com.yhl.scp.dfp.oem.domain.service.OemInventorySubmissionExitDomainService;
import com.yhl.scp.dfp.oem.dto.OemInventorySubmissionExitDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemInventorySubmissionExitDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemInventorySubmissionExitPO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionExitService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionExitVO;
import com.yhl.scp.ips.utils.BasePOUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>OemInventorySubmissionExitServiceImpl</code>
 * <p>
 * 中转库与主机厂库存提报(出口)应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:52:04
 */
@Slf4j
@Service
public class OemInventorySubmissionExitServiceImpl extends AbstractService implements OemInventorySubmissionExitService {

    @Resource
    private OemInventorySubmissionExitDao oemInventorySubmissionExitDao;

    @Resource
    private OemInventorySubmissionExitDomainService oemInventorySubmissionExitDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(OemInventorySubmissionExitDTO oemInventorySubmissionExitDTO) {
        // 0.数据转换
        OemInventorySubmissionExitDO oemInventorySubmissionExitDO = OemInventorySubmissionExitConvertor.INSTANCE.dto2Do(oemInventorySubmissionExitDTO);
        OemInventorySubmissionExitPO oemInventorySubmissionExitPO = OemInventorySubmissionExitConvertor.INSTANCE.dto2Po(oemInventorySubmissionExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemInventorySubmissionExitDomainService.validation(oemInventorySubmissionExitDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemInventorySubmissionExitPO);
        oemInventorySubmissionExitDao.insert(oemInventorySubmissionExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OemInventorySubmissionExitDTO oemInventorySubmissionExitDTO) {
        // 0.数据转换
        OemInventorySubmissionExitDO oemInventorySubmissionExitDO = OemInventorySubmissionExitConvertor.INSTANCE.dto2Do(oemInventorySubmissionExitDTO);
        OemInventorySubmissionExitPO oemInventorySubmissionExitPO = OemInventorySubmissionExitConvertor.INSTANCE.dto2Po(oemInventorySubmissionExitDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemInventorySubmissionExitDomainService.validation(oemInventorySubmissionExitDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemInventorySubmissionExitPO);
        oemInventorySubmissionExitDao.updateSelective(oemInventorySubmissionExitPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemInventorySubmissionExitDTO> list) {
        List<OemInventorySubmissionExitPO> newList = OemInventorySubmissionExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemInventorySubmissionExitDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OemInventorySubmissionExitDTO> list) {
        List<OemInventorySubmissionExitPO> newList = OemInventorySubmissionExitConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemInventorySubmissionExitDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemInventorySubmissionExitDao.deleteBatch(idList);
        }
        return oemInventorySubmissionExitDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemInventorySubmissionExitVO selectByPrimaryKey(String id) {
        OemInventorySubmissionExitPO po = oemInventorySubmissionExitDao.selectByPrimaryKey(id);
        return OemInventorySubmissionExitConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_oem_inventory_submission_exit")
    public List<OemInventorySubmissionExitVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_oem_inventory_submission_exit")
    public List<OemInventorySubmissionExitVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemInventorySubmissionExitVO> dataList = oemInventorySubmissionExitDao.selectByCondition(sortParam, queryCriteriaParam);
        OemInventorySubmissionExitServiceImpl target = springBeanUtils.getBean(OemInventorySubmissionExitServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemInventorySubmissionExitVO> selectByParams(Map<String, Object> params) {
        List<OemInventorySubmissionExitPO> list = oemInventorySubmissionExitDao.selectByParams(params);
        return OemInventorySubmissionExitConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemInventorySubmissionExitVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM_INVENTORY_SUBMISSION_EXIT.getCode();
    }

    @Override
    public List<OemInventorySubmissionExitVO> invocation(List<OemInventorySubmissionExitVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
