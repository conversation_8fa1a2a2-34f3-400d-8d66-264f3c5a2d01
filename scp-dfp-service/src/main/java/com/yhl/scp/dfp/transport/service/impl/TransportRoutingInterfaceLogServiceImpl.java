package com.yhl.scp.dfp.transport.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.transport.vo.TransportRoutingVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.transport.convertor.TransportRoutingInterfaceLogConvertor;
import com.yhl.scp.dfp.transport.domain.entity.TransportRoutingInterfaceLogDO;
import com.yhl.scp.dfp.transport.domain.service.TransportRoutingInterfaceLogDomainService;
import com.yhl.scp.dfp.transport.dto.TransportRoutingInterfaceLogDTO;
import com.yhl.scp.dfp.transport.infrastructure.dao.TransportRoutingInterfaceLogDao;
import com.yhl.scp.dfp.transport.infrastructure.po.TransportRoutingInterfaceLogPO;
import com.yhl.scp.dfp.transport.service.TransportRoutingInterfaceLogService;
import com.yhl.scp.dfp.transport.vo.TransportRoutingInterfaceLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>TransportRoutingInterfaceLogServiceImpl</code>
 * <p>
 * mes_运输路径_留存应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 17:36:56
 */
@Slf4j
@Service
public class TransportRoutingInterfaceLogServiceImpl extends AbstractService implements TransportRoutingInterfaceLogService {

    @Resource
    private TransportRoutingInterfaceLogDao transportRoutingInterfaceLogDao;

    @Resource
    private TransportRoutingInterfaceLogDomainService transportRoutingInterfaceLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(TransportRoutingInterfaceLogDTO transportRoutingInterfaceLogDTO) {
        // 0.数据转换
        TransportRoutingInterfaceLogDO transportRoutingInterfaceLogDO = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Do(transportRoutingInterfaceLogDTO);
        TransportRoutingInterfaceLogPO transportRoutingInterfaceLogPO = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Po(transportRoutingInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        transportRoutingInterfaceLogDomainService.validation(transportRoutingInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(transportRoutingInterfaceLogPO);
        transportRoutingInterfaceLogDao.insert(transportRoutingInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(TransportRoutingInterfaceLogDTO transportRoutingInterfaceLogDTO) {
        // 0.数据转换
        TransportRoutingInterfaceLogDO transportRoutingInterfaceLogDO = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Do(transportRoutingInterfaceLogDTO);
        TransportRoutingInterfaceLogPO transportRoutingInterfaceLogPO = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Po(transportRoutingInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        transportRoutingInterfaceLogDomainService.validation(transportRoutingInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(transportRoutingInterfaceLogPO);
        transportRoutingInterfaceLogDao.update(transportRoutingInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<TransportRoutingInterfaceLogDTO> list) {
        List<TransportRoutingInterfaceLogPO> newList = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        transportRoutingInterfaceLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<TransportRoutingInterfaceLogDTO> list) {
        List<TransportRoutingInterfaceLogPO> newList = TransportRoutingInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        transportRoutingInterfaceLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return transportRoutingInterfaceLogDao.deleteBatch(idList);
        }
        return transportRoutingInterfaceLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public TransportRoutingInterfaceLogVO selectByPrimaryKey(String id) {
        TransportRoutingInterfaceLogPO po = transportRoutingInterfaceLogDao.selectByPrimaryKey(id);
        return TransportRoutingInterfaceLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "TRANSPORT_ROUTING_INTERFACE_LOG")
    public List<TransportRoutingInterfaceLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "TRANSPORT_ROUTING_INTERFACE_LOG")
    public List<TransportRoutingInterfaceLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<TransportRoutingInterfaceLogVO> dataList = transportRoutingInterfaceLogDao.selectByCondition(sortParam, queryCriteriaParam);
        TransportRoutingInterfaceLogServiceImpl target = springBeanUtils.getBean(TransportRoutingInterfaceLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<TransportRoutingInterfaceLogVO> selectByParams(Map<String, Object> params) {
        List<TransportRoutingInterfaceLogPO> list = transportRoutingInterfaceLogDao.selectByParams(params);
        return TransportRoutingInterfaceLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<TransportRoutingInterfaceLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.TRANSPORT_ROUTING_INTERFACE_LOG.getCode();
    }

    @Override
    public List<TransportRoutingInterfaceLogVO> invocation(List<TransportRoutingInterfaceLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    @Override
    public BaseResponse<Void> syncTransportRoutings(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_TRANSPORT_ROUTE_FOR_BPIM");
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.TRANSPORT_ROUTING.getCode(), params);
        return BaseResponse.success("同步成功");
    }
}
