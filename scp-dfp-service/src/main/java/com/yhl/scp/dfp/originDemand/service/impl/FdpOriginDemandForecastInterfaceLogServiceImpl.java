package com.yhl.scp.dfp.originDemand.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.originDemand.convertor.FdpOriginDemandForecastInterfaceLogConvertor;
import com.yhl.scp.dfp.originDemand.domain.entity.FdpOriginDemandForecastInterfaceLogDO;
import com.yhl.scp.dfp.originDemand.domain.service.FdpOriginDemandForecastInterfaceLogDomainService;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.infrastructure.dao.FdpOriginDemandForecastInterfaceLogDao;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandForecastInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>FdpOriginDemandForecastInterfaceLogServiceImpl</code>
 * <p>
 * Edi装车需求GRP接口同步记录表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:26:15
 */
@Slf4j
@Service
public class FdpOriginDemandForecastInterfaceLogServiceImpl extends AbstractService implements FdpOriginDemandForecastInterfaceLogService {

    @Resource
    private FdpOriginDemandForecastInterfaceLogDao fdpOriginDemandForecastInterfaceLogDao;

    @Resource
    private FdpOriginDemandForecastInterfaceLogDomainService fdpOriginDemandForecastInterfaceLogDomainService;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private OemService oemService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Override
    public BaseResponse<Void> doCreate(FdpOriginDemandForecastInterfaceLogDTO fdpOriginDemandForecastInterfaceLogDTO) {
        // 0.数据转换
        FdpOriginDemandForecastInterfaceLogDO fdpOriginDemandForecastInterfaceLogDO = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Do(fdpOriginDemandForecastInterfaceLogDTO);
        FdpOriginDemandForecastInterfaceLogPO fdpOriginDemandForecastInterfaceLogPO = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Po(fdpOriginDemandForecastInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpOriginDemandForecastInterfaceLogDomainService.validation(fdpOriginDemandForecastInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(fdpOriginDemandForecastInterfaceLogPO);
        fdpOriginDemandForecastInterfaceLogDao.insert(fdpOriginDemandForecastInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(FdpOriginDemandForecastInterfaceLogDTO fdpOriginDemandForecastInterfaceLogDTO) {
        // 0.数据转换
        FdpOriginDemandForecastInterfaceLogDO fdpOriginDemandForecastInterfaceLogDO = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Do(fdpOriginDemandForecastInterfaceLogDTO);
        FdpOriginDemandForecastInterfaceLogPO fdpOriginDemandForecastInterfaceLogPO = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Po(fdpOriginDemandForecastInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpOriginDemandForecastInterfaceLogDomainService.validation(fdpOriginDemandForecastInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(fdpOriginDemandForecastInterfaceLogPO);
        fdpOriginDemandForecastInterfaceLogDao.update(fdpOriginDemandForecastInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<FdpOriginDemandForecastInterfaceLogDTO> list) {
        List<FdpOriginDemandForecastInterfaceLogPO> newList = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        fdpOriginDemandForecastInterfaceLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<FdpOriginDemandForecastInterfaceLogDTO> list) {
        List<FdpOriginDemandForecastInterfaceLogPO> newList = FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        fdpOriginDemandForecastInterfaceLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return fdpOriginDemandForecastInterfaceLogDao.deleteBatch(idList);
        }
        return fdpOriginDemandForecastInterfaceLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public FdpOriginDemandForecastInterfaceLogVO selectByPrimaryKey(String id) {
        FdpOriginDemandForecastInterfaceLogPO po = fdpOriginDemandForecastInterfaceLogDao.selectByPrimaryKey(id);
        return FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG")
    public List<FdpOriginDemandForecastInterfaceLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG")
    public List<FdpOriginDemandForecastInterfaceLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<FdpOriginDemandForecastInterfaceLogVO> dataList = fdpOriginDemandForecastInterfaceLogDao.selectByCondition(sortParam, queryCriteriaParam);
        FdpOriginDemandForecastInterfaceLogServiceImpl target = SpringBeanUtils.getBean(FdpOriginDemandForecastInterfaceLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<FdpOriginDemandForecastInterfaceLogVO> selectByParams(Map<String, Object> params) {
        List<FdpOriginDemandForecastInterfaceLogPO> list = fdpOriginDemandForecastInterfaceLogDao.selectByParams(params);
        return FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<FdpOriginDemandForecastInterfaceLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<FdpOriginDemandForecastInterfaceLogVO> selectByKids(List<String> kidList) {
        if (CollectionUtils.isNotEmpty(kidList)) {
            List<FdpOriginDemandForecastInterfaceLogPO> list = fdpOriginDemandForecastInterfaceLogDao.selectByKids(kidList);
            return FdpOriginDemandForecastInterfaceLogConvertor.INSTANCE.po2Vos(list);
        }
        return Collections.emptyList();
    }

    @Override
    public int doDeleteByType(String importType) {
        return fdpOriginDemandForecastInterfaceLogDao.deleteByType(importType);
    }

    @Override
    public BaseResponse<Void> syncOriginDemandForecastLog(List<FdpOriginDemandForecastInterfaceLogDTO> list, String importType) {

        if (CollectionUtils.isEmpty(list)) {
            log.error("{}预测数据为空", importType);
            return BaseResponse.error("预测数据为空");
        }

        Map<String, Object> oemParams = MapUtil.newHashMap();
        oemParams.put("enable", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectVOByParams(oemParams);
        try {
            if ("GRP".equals(importType)) {
                list.forEach(dto -> {

                    oemVOS.stream().filter(t -> Objects.equals(t.getErpCustomerId(), dto.getEbsCustomerId()) && Objects
                            .equals(t.getErpSiteId(), dto.getEbsSiteId())).findFirst().ifPresent(oemVO -> {
                        dto.setOemCode(oemVO.getOemCode());
                    });

                });
                doDeleteByType(importType);
                doCreateBatch(list);

            } else {
                Map<String, Object> params = MapUtil.newHashMap();
                params.put("importType", importType);

                List<String> kidList = list.stream().map(FdpOriginDemandForecastInterfaceLogDTO::getKid).distinct()
                        .collect(Collectors.toList());
                List<String> productCodes = list.stream().map(FdpOriginDemandForecastInterfaceLogDTO::getItemNum).distinct()
                        .collect(Collectors.toList());

                BaseResponse<List<PartRelationMapVO>> partResponse = partRelationMapService.getDataByCodes(productCodes);

                List<PartRelationMapVO> partRelationMapVOS = partResponse.getData();
                Map<String, PartRelationMapVO> partRelationMap =CollectionUtils.isEmpty(partRelationMapVOS)?
                        MapUtil.newHashMap():
                partRelationMapVOS.stream().collect(Collectors.toMap(PartRelationMapVO::getProductCode , Function.identity(), (v1, v2) -> v1));

                List<String> partNums = partRelationMapVOS.stream().map(PartRelationMapVO::getPartNumber).distinct().collect(Collectors.toList());
                List<NakedGlassVO> nakedGlassVOS = mpsFeign.selectNakedGlassByFinishedProducts(partNums);

                Map<String, NakedGlassVO> nakedGlassVOMap =CollectionUtils.isEmpty(nakedGlassVOS)?
                        MapUtil.newHashMap():
                        nakedGlassVOS.stream().collect(Collectors.toMap(NakedGlassVO::getFinishedProduct, Function.identity(), (v1, v2) -> v1));

                List<FdpOriginDemandForecastInterfaceLogVO> oldForecastList = this.selectByKids(kidList);

                Map<String, FdpOriginDemandForecastInterfaceLogVO> oldForecastMap = CollectionUtils.isEmpty(oldForecastList) ?
                        MapUtil.newHashMap() :
                        oldForecastList.stream().collect(Collectors.toMap(FdpOriginDemandForecastInterfaceLogVO::getKid,
                                Function.identity(), (v1, v2) -> v1));

                BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
                HashMap<String, Object> stockParams = MapUtil.newHashMap();
                stockParams.put("enable", YesOrNoEnum.YES.getCode());

                List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getData(), stockParams);

                List<FdpOriginDemandForecastInterfaceLogDTO> insertList = Lists.newArrayList();
                List<FdpOriginDemandForecastInterfaceLogDTO> updateList = Lists.newArrayList();

                for (FdpOriginDemandForecastInterfaceLogDTO dto : list) {
                    if(!partRelationMap.containsKey(dto.getItemNum())){
                        log.error("没有找到对应的零件号本厂编码，物料编码：{}", dto.getItemNum());
                        continue;
                    }else {
                        if(Objects.isNull(partRelationMap.get(dto.getItemNum()).getPartNumber())){
                            log.error("没有找到对应的零件号，该物料编码：{}", dto.getItemNum());
                            continue;
                        }
                    }
                    dto.setCustomerItemNum(partRelationMap.get(dto.getItemNum()).getPartNumber());//获取对应的零件号
                    if(!nakedGlassVOMap.containsKey(dto.getCustomerItemNum())){
                        log.error("没有找到对应的裸玻映射信息，客户零件号：{}，本厂编码：{}", dto.getCustomerItemNum(),dto.getItemNum());
                        continue;
                    }
                    List<NewStockPointVO> fyeStockPoints = newStockPointVOS.stream().filter(t ->
                            Objects.equals(t.getOrganizeId(), dto.getOrgId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(fyeStockPoints)) {
                        log.error("没有找到FYE的库存点，库存点orgId：{}", dto.getOrgId());
                        continue;
                    }
                    String customerCode = fyeStockPoints.get(0).getCustomerCode();
                    oemVOS.stream().filter(x -> Objects.equals(x.getErpEdiLocation(), dto.getCustomerCode())
                                    && Objects.equals(x.getErpPlantCode(), dto.getCustomerCode())
                                    && Objects.equals(x.getErpCustomerCode(), customerCode)).findFirst()
                            .ifPresent(x -> dto.setOemCode(x.getOemCode()));
                    if (Objects.isNull(dto.getOemCode())) {
                        log.error("没有找到FYE主机厂信息，客户编码：{}", customerCode);
                        continue;
                    }
                    if (oldForecastMap.containsKey(dto.getKid())) {
                        FdpOriginDemandForecastInterfaceLogVO old = oldForecastMap.get(dto.getKid());
                        dto.setId(old.getId());
                        updateList.add(dto);
                        log.info("找到kid：{}，更新数据", dto.getKid());
                    } else {
                        log.info("找不到kid：{}，新增数据", dto.getKid());
                        insertList.add(dto);
                    }
                }

                if (CollectionUtils.isNotEmpty(updateList)) {
                    doDeleteByType(importType);
                    List<List<FdpOriginDemandForecastInterfaceLogDTO>> partition =
                            com.google.common.collect.Lists.partition(updateList, 5000);
                    partition.forEach(this::doUpdateBatch);
                    log.info("批量更新需求预测中间表完成，总计：{}", updateList.size());
                }
                if (CollectionUtils.isNotEmpty(insertList)) {
                    List<List<FdpOriginDemandForecastInterfaceLogDTO>> partition =
                            com.google.common.collect.Lists.partition(insertList, 5000);
                    partition.forEach(this::doCreateBatch);
                    log.info("批量创建需求预测中间表完成,总计：{}", insertList.size());
                }
            }
        } catch (Exception e) {
            log.error("同步原始需求预测中间表数据异常", e.getMessage());
            e.printStackTrace();
            throw new BusinessException("同步原始需求预测中间表数据异常", e.getMessage());
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<FdpOriginDemandForecastInterfaceLogVO> selectVOByParams(Map<String, Object> params) {
        return fdpOriginDemandForecastInterfaceLogDao.selectVOByParams(params);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG.getCode();
    }

    @Override
    public List<FdpOriginDemandForecastInterfaceLogVO> invocation(List<FdpOriginDemandForecastInterfaceLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
