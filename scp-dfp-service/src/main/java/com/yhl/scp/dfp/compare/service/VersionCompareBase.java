package com.yhl.scp.dfp.compare.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemPO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

/**
 * <code>VersionCompareBase</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-29 17:01:43
 */
public class VersionCompareBase {

    @Resource
    private OemDao oemDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    /**
     * 获取主机厂信息
     *
     * @param oemCodes
     * @return
     */
    public Map<String, String> getOemInfo(List<String> oemCodes) {
        List<OemPO> oemList = oemDao.selectByOemCodes(oemCodes);
        return oemList.stream().filter(e -> StringUtils.isNotEmpty(e.getOemCode()) && StringUtils.isNotEmpty(e.getOemName()))
                .collect(Collectors.toMap(OemPO::getOemCode, OemPO::getOemName, (k1, k2) -> k1));
    }

    /**
     * 获取车型信息
     */
    public Map<String, String> getVehicleModelInfo(List<String> productCodes) {
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String defaultScenario = defaultScenario1.getData();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(defaultScenario, productCodes);
        return newProductStockPointVOS.stream().filter(item -> StringUtils.isNotBlank(item.getProductCode())
                && StringUtils.isNotBlank(item.getVehicleModelCode())).collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (k1, k2) -> k1));
    }
}
