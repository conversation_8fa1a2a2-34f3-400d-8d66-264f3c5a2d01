package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.oem.service.OemAddressInventoryLogService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OemAddressJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-08 20:33:57
 */
@Component
@Slf4j
public class OemAddressJob {
    @Resource
    private SpringBeanUtils springBeanUtils;
    @XxlJob("oemAddressJob")
    private ReturnT<String> oemAddressJob() {
        IpsNewFeign ipsNewFeign=springBeanUtils.getBean(IpsNewFeign.class);
        NewMdsFeign mdsFeign=springBeanUtils.getBean(NewMdsFeign.class);
        OemAddressInventoryLogService oemAddressInventoryLogService=springBeanUtils.getBean(OemAddressInventoryLogService.class);
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步库存点job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            oemAddressInventoryLogService.syncOemAddress(scenario.getTenantId());

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步产品与库存点job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
