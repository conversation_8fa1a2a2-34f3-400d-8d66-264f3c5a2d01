package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dfp.part.convertor.PartRelationMapConvertor;
import com.yhl.scp.dfp.part.dto.PartRelationMapDTO;
import com.yhl.scp.dfp.part.infrastructure.dao.PartRelationMapDao;
import com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>PartRelationMapExcelService</code>
 * <p>
 * 零件映射关系Excel导出
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 13:43:45
 */
@Service
public class PartRelationMapExcelService extends AbstractExcelService<PartRelationMapDTO, PartRelationMapPO, PartRelationMapVO> {

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private PartRelationMapDao partRelationMapDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseDao<PartRelationMapPO, PartRelationMapVO> getBaseDao() {
        return partRelationMapDao;
    }

    @Override
    public Function<PartRelationMapDTO, PartRelationMapPO> getDTO2POConvertor() {
        return PartRelationMapConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<PartRelationMapDTO> getDTOClass() {
        return PartRelationMapDTO.class;
    }

    @Override
    public BaseService<PartRelationMapDTO, PartRelationMapVO> getBaseService() {
        return partRelationMapService;
    }

    @Override
    protected void fillIdForUpdateData(List<PartRelationMapDTO> list, Map<String, PartRelationMapPO> map) {
        for (PartRelationMapDTO partRelationMapDTO : list) {
            PartRelationMapPO partRelationMapPO = map.get(partRelationMapDTO.getProductCode() + "&" + partRelationMapDTO.getPartNumber());
            if (Objects.isNull(partRelationMapPO)) {
                continue;
            }
            partRelationMapDTO.setId(partRelationMapPO.getId());
            partRelationMapDTO.setVersionValue(partRelationMapPO.getVersionValue());
        }
    }

    @Override
    protected ImportRelatedDataHolder<PartRelationMapPO> prepareData(List<PartRelationMapDTO> relationMapDTOS) {
        // 找到数据库现在所有的数据
        List<PartRelationMapPO> alreadyExitData = partRelationMapDao.selectByParams(new HashMap<>(2));
        Map<String, PartRelationMapPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getProductCode() + "&" + x.getPartNumber(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("productCode", "partNumber");
        // 外键字段
        List<String> foreignKeys = ListUtil.of("productCode");

        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        String scenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId()).getData();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario, queryMap);
        List<SimpleVO> productSimpleVOS = CollectionUtils.isEmpty(newProductStockPointVOS) ?
                Lists.newArrayList() :
                newProductStockPointVOS.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getProductCode());
                    simpleVO.setCode(x.getProductCode());
                    return simpleVO;
                }).collect(Collectors.toList());
        foreignDataMap.put("productCode", productSimpleVOS);
        return ImportRelatedDataHolder.<PartRelationMapPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    @Override
    protected void doInsert(ImportAnalysisResultHolder<PartRelationMapDTO, PartRelationMapPO> resultHolder, ImportContext importContext) {
        super.doInsert(resultHolder, importContext);
    }
}
