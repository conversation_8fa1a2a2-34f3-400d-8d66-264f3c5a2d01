package com.yhl.scp.dfp.oem.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.oem.dto.OemAddressInventoryLogDTO;
import com.yhl.scp.dfp.oem.service.OemAddressInventoryLogService;
import com.yhl.scp.dfp.oem.vo.OemAddressInventoryLogVO;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OemAddressInventoryLogController</code>
 * <p>
 * 主机厂地址_中间表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-13 21:08:27
 */
@Slf4j
@Api(tags = "主机厂地址_中间表控制器")
@RestController
@RequestMapping("oemAddressInventoryLog")
public class OemAddressInventoryLogController extends BaseController {

    @Resource
    private OemAddressInventoryLogService oemAddressInventoryLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OemAddressInventoryLogVO>> page() {
        List<OemAddressInventoryLogVO> oemAddressInventoryLogList =
                oemAddressInventoryLogService.selectByPage(getPagination(),
                        getSortParam(), getQueryCriteriaParam());
        PageInfo<OemAddressInventoryLogVO> pageInfo = new PageInfo<>(oemAddressInventoryLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OemAddressInventoryLogDTO oemAddressInventoryLogDTO) {
        return oemAddressInventoryLogService.doCreate(oemAddressInventoryLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OemAddressInventoryLogDTO oemAddressInventoryLogDTO) {
        return oemAddressInventoryLogService.doUpdate(oemAddressInventoryLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        oemAddressInventoryLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OemAddressInventoryLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemAddressInventoryLogService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "箱体信息同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncBoxInfo() {
        return oemAddressInventoryLogService.syncOemAddress(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "中转库下拉")
    @PostMapping(value = "transitStockDropdown")
    public BaseResponse<List<LabelValue<String>>> transitStockDropdown(
            @RequestParam(value = "transitStockLike", required = false) String transitStockLike) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                oemAddressInventoryLogService.selectTransitStockDropdown(transitStockLike, null));
    }

    @ApiOperation(value = "货位下拉")
    @PostMapping(value = "stockLocationDropdown")
    public BaseResponse<List<LabelValue<String>>> transitStockDropdown(
            @RequestParam(value = "transitStockCode") String transitStockCode,
            @RequestParam(value = "stockLocationLike", required = false) String stockLocationLike) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                oemAddressInventoryLogService.selectStockLocationDropdown(transitStockCode, stockLocationLike));
    }

    @ApiOperation(value = "主机厂地址下拉")
    @GetMapping(value = "oemAddressDropdown")
    public BaseResponse<List<LabelValue<String>>> oemAddressDropdown(
            @RequestParam(value = "transitStockCode") String transitStockCode,
            @RequestParam(value = "stockLocationCode") String stockLocationCode,
            @RequestParam(value = "oemAddressLike", required = false) String oemAddressLike) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemAddressInventoryLogService
                .selectOemAddressDropdown(transitStockCode, stockLocationCode, oemAddressLike));
    }
    
    @ApiOperation(value = "客户地址下拉")
    @GetMapping(value = "selectCustomerAddressDropdown")
    public BaseResponse<List<LabelValue<String>>> selectCustomerAddressDropdown(
            @RequestParam(value = "customerCode", required = false) String customerCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemAddressInventoryLogService
                .selectCustomerAddressDropdown(customerCode));
    }

}
