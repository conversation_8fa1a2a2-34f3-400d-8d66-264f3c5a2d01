package com.yhl.scp.dfp.oem.infrastructure.po;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

/**
 * <code>OemPO</code>
 * <p>
 * 主机厂档案PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
public class OemPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 652577475275554238L;

    /**
     * 生产组织编码
     */
    private String saleOrgId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂名称
     */
    private String oemName;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 地点编码
     */
    private String locationCode;
    /**
     * 地址1
     */
    private String locationArea1;
    /**
     * 地址2
     */
    private String locationArea2;
    /**
     * 地址3
     */
    private String locationArea3;
    /**
     * 地址4
     */
    private String locationArea4;
    /**
     * 付款条件
     */
    private String paymentTerm;
    /**
     * 运输条款
     */
    private String transitClause;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 市场属性
     */
    private String marketType;
    /**
     * 目标货位
     */
    private String targetStockLocation;
    /**
     * 版本值
     */
    private Integer versionValue;
    /**
     * ediLocation
     */
    private String ediLocation;
    /**
     * 工厂
     */
    private String plantCode;
    /**
     * erp客户id
     */
    private String ebsCustomerId;
    /**
     * erp地点id
     */
    private String ebsSiteId;
    /**
     * erp地址用途id
     */
    private String shipToSiteUseId;
    /**
     * 国家
     */
    private String siteCountry;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * edi标志
     */
    private String ediFlag;
    
    /**
     * 理货单模式，MES，GRP
     */
    private String tallyOrderMode;
    
    /**
     * 自提类型：自提，非自提
     */
    private String pickUpType;
    
    /**
     * ERP地址ID
     */
    private String erpCustomerAddressId;
    
    /**
     * 中转库目标货位
     */
    private String transferTargetStockLocation;
    /**
     * EDI时间类型
     */
    private String ediDateType;

    public String getSaleOrgId() {
        return saleOrgId;
    }

    public void setSaleOrgId(String saleOrgId) {
        this.saleOrgId = saleOrgId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOemName() {
        return oemName;
    }

    public void setOemName(String oemName) {
        this.oemName = oemName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getTransitClause() {
        return transitClause;
    }

    public void setTransitClause(String transitClause) {
        this.transitClause = transitClause;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getLocationArea1() {
        return locationArea1;
    }

    public void setLocationArea1(String locationArea1) {
        this.locationArea1 = locationArea1;
    }

    public String getLocationArea2() {
        return locationArea2;
    }

    public void setLocationArea2(String locationArea2) {
        this.locationArea2 = locationArea2;
    }

    public String getLocationArea3() {
        return locationArea3;
    }

    public void setLocationArea3(String locationArea3) {
        this.locationArea3 = locationArea3;
    }

    public String getLocationArea4() {
        return locationArea4;
    }

    public void setLocationArea4(String locationArea4) {
        this.locationArea4 = locationArea4;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getMarketType() {
        return marketType;
    }

    public void setMarketType(String marketType) {
        this.marketType = marketType;
    }

    public String getTargetStockLocation() {
        return targetStockLocation;
    }

    public void setTargetStockLocation(String targetStockLocation) {
        this.targetStockLocation = targetStockLocation;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getEdiLocation() {
        return ediLocation;
    }

    public void setEdiLocation(String ediLocation) {
        this.ediLocation = ediLocation;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getEbsSiteId() {
        return ebsSiteId;
    }

    public void setEbsSiteId(String ebsSiteId) {
        this.ebsSiteId = ebsSiteId;
    }

    public String getShipToSiteUseId() {
        return shipToSiteUseId;
    }

    public void setShipToSiteUseId(String shipToSiteUseId) {
        this.shipToSiteUseId = shipToSiteUseId;
    }

    public String getSiteCountry() {
        return siteCountry;
    }

    public void setSiteCountry(String siteCountry) {
        this.siteCountry = siteCountry;
    }

    public String getEbsCustomerId() {
        return ebsCustomerId;
    }

    public void setEbsCustomerId(String ebsCustomerId) {
        this.ebsCustomerId = ebsCustomerId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getEdiFlag() {
        return ediFlag;
    }

    public void setEdiFlag(String ediFlag) {
        this.ediFlag = ediFlag;
    }

	public String getTallyOrderMode() {
		return tallyOrderMode;
	}

	public void setTallyOrderMode(String tallyOrderMode) {
		this.tallyOrderMode = tallyOrderMode;
	}

	public String getPickUpType() {
		return pickUpType;
	}

	public void setPickUpType(String pickUpType) {
		this.pickUpType = pickUpType;
	}

	public String getErpCustomerAddressId() {
		return erpCustomerAddressId;
	}

	public void setErpCustomerAddressId(String erpCustomerAddressId) {
		this.erpCustomerAddressId = erpCustomerAddressId;
	}

	public String getTransferTargetStockLocation() {
		return transferTargetStockLocation;
	}

	public void setTransferTargetStockLocation(String transferTargetStockLocation) {
		this.transferTargetStockLocation = transferTargetStockLocation;
	}

    public String getEdiDateType() {
        return ediDateType;
    }

    public void setEdiDateType(String ediDateType) {
        this.ediDateType = ediDateType;
    }
}
