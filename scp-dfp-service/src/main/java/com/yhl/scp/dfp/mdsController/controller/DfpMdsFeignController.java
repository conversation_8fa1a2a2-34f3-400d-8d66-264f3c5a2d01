package com.yhl.scp.dfp.mdsController.controller;

import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.production.vo.ProductionOrganizeVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName DfpMdsFeignController
 * @Description TODO
 * @Date 2024-09-19 14:00:55
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Api(tags = "DFP调用MDS相关接口")
@RestController
@RequestMapping("")
public class DfpMdsFeignController {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @ApiOperation("获取库存点所有数据")
    @GetMapping("newStockPoint/dropDown")
    public BaseResponse<List<NewStockPointVO>> dropDownNewStockPoint() {
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId());
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        return BaseResponse.success(newMdsFeign.selectStockPointByParams(scenario.getData(), queryMap));
    }

    @ApiOperation("获取库存点物品所有数据")
    @GetMapping("newProductStockPoint/dropDown")
    public BaseResponse<List<LabelValue<String>>> dropDownNewProductStockPoints(@RequestParam("oemCode") String oemCode) {
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        queryMap.put("oemCode", oemCode);
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(oemStockPointMapVOS)) {
            return BaseResponse.success(result);
        }
        List<String> stockPointCodes = oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId());
        Map<String, Object> productParams = MapUtil.newHashMap();
        productParams.put("enabled", YesOrNoEnum.YES.getCode());
        productParams.put("stockPointCodes", stockPointCodes);
        result.addAll(newMdsFeign.selectProductStockPointLabelValueByParams(scenario.getData(), productParams));
        return BaseResponse.success(result);
    }

    @ApiOperation("获取本厂编码数据")
    @GetMapping("newProductStockPoint/selectProductCodeLike")
    public BaseResponse<List<LabelValue<String>>> dropDownProductCodeLike() {
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), SystemHolder.getTenantId());
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        return BaseResponse.success(newMdsFeign.selectProductCodeLikeByParams(scenario.getData(), queryMap));
    }
}



