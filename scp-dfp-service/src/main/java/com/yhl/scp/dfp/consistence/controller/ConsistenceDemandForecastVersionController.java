package com.yhl.scp.dfp.consistence.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastSavePublishDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastVersionDTO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastReferenceVersionVO;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <code>ConsistenceDemandForecastVersionController</code>
 * <p>
 * 一致性业务预测版本控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Api(tags = "一致性业务预测版本控制器")
@RestController
@RequestMapping("consistenceDemandForecastVersion")
public class ConsistenceDemandForecastVersionController extends BaseController {

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ConsistenceDemandForecastVersionVO>> page() {
        List<ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionList = consistenceDemandForecastVersionService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ConsistenceDemandForecastVersionVO> pageInfo = new PageInfo<>(consistenceDemandForecastVersionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO) {
        return consistenceDemandForecastVersionService.doCreate(consistenceDemandForecastVersionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO) {
        return consistenceDemandForecastVersionService.doUpdate(consistenceDemandForecastVersionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        consistenceDemandForecastVersionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ConsistenceDemandForecastVersionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastVersionService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        consistenceDemandForecastVersionService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "分级查询")
    @GetMapping(value = "treeQuery")
    public BaseResponse<List<ConsistenceDemandForecastVersionVO>> treeQuery() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastVersionService.treeQuery());
    }

    @ApiOperation(value = "一致性业务预测版本号获取")
    @GetMapping(value = "getVersionCodes")
    public BaseResponse<List<ConsistenceDemandForecastVersionVO>> getVersionCodes(@RequestParam(required = false) String planPeriod) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastVersionService.getVersionCodes(planPeriod));
    }

    @ApiOperation(value = "主机厂编码来源指定")
    @GetMapping(value = "getOemCodeOrigin")
    public BaseResponse<List<String>> getOemCodeOrigin(@RequestParam(name = "versionId", required = false) String versionId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastVersionService.getOemCodeOrigin(versionId));
    }

    @ApiOperation(value = "一致性业务预测版本发布")
    @GetMapping(value = "publishVersion")
    @BusinessMonitorLog(businessCode = "需求预测发布", moduleCode = "DFP", businessFrequency = "MONTH")
    public BaseResponse<Void> publishVersion(@RequestParam(name = "versionId", required = false) String versionId) {
        consistenceDemandForecastVersionService.publishVersion(versionId);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "一致性业务预测版本查询-产能平衡使用")
    @GetMapping(value = "selectVersionInfoByCapacityBalance")
    public BaseResponse<List<ConsistenceDemandForecastVersionVO>> selectVersionInfoByCapacityBalance() {
        return BaseResponse.success(consistenceDemandForecastVersionService.selectVersionInfoByCapacityBalance());
    }

    @ApiOperation(value = "一致性业务预测版本下拉")
    @GetMapping(value = "dropDown")
    public BaseResponse<List<LabelValue<String>>> dropDown() {
        return consistenceDemandForecastVersionService.dropDownVersion();
    }
    
    @ApiOperation(value = "获取最新一致性业务预测版本号")
    @GetMapping(value = "getMaxVersionCodes")
    public BaseResponse<String> getMaxVersionCodes(@RequestParam(required = false) String planPeriod) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastVersionService.getMaxVersionCodes(planPeriod));
    }
    
    @ApiOperation(value = "版本新增")
    @PostMapping(value = "createVersion")
    public BaseResponse<Void> doCreateVersion(@RequestBody ConsistenceDemandForecastVersionDTO consistenceDemandForecastVersionDTO) {
        consistenceDemandForecastVersionDTO.setId(UUIDUtil.getUUID());
        consistenceDemandForecastVersionService.doCreateVersion(consistenceDemandForecastVersionDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        consistenceDemandForecastVersionService.export(response);
    }

    @ApiOperation(value = "获取参考预测版本号")
    @GetMapping(value = "getReferenceForecastVersion")
    public BaseResponse<ConsistenceDemandForecastReferenceVersionVO> getReferenceForecastVersion(@RequestParam("planPeriod") String planPeriod) {
        return BaseResponse.success(consistenceDemandForecastVersionService.getReferenceForecastVersion(planPeriod));
    }

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @ApiOperation(value = "创建并发布")
    @PostMapping(value = "createAndPublish")
    public BaseResponse<String> createAndPublish(@RequestBody ConsistenceDemandForecastSavePublishDTO consistenceDemandForecastSavePublishDTO) {
        String scenario = SystemHolder.getScenario();
        String key = RedisKeyManageEnum.CONSISTENCE_DEMAND_FORECAST_SAVE_AND_PUBLISH.getKey().replace("{scenario}", scenario);
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(key, key, 30, TimeUnit.MINUTES);
        if (Objects.isNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("一致性需求预测创建发布中, 请勿重复执行");
        }
        try {
            String versionId = consistenceDemandForecastVersionService.doCreateAndPublish(consistenceDemandForecastSavePublishDTO);
            return BaseResponse.success(BaseResponse.OP_SUCCESS, versionId);
        } catch (Exception e) {
            log.error("获取版本号失败", e);
            return BaseResponse.error(BaseResponse.OP_FAILURE, "获取版本号失败");
        } finally {
            redisTemplate.delete(key);
        }
    }

}