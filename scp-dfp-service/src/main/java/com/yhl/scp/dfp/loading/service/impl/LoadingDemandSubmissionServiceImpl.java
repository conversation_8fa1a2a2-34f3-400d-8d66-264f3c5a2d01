package com.yhl.scp.dfp.loading.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.calendar.enums.RepeatFrequencyEnum;
import com.yhl.scp.dfp.calendar.convertor.ResourceCalendarConvertor;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.dfp.calendar.domain.service.DfpResourceCalendarDomainService;
import com.yhl.scp.dfp.calendar.service.DfpCalendarRuleService;
import com.yhl.scp.dfp.calendar.vo.CalendarRuleVO;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.*;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.excel.listener.ExcelDynamicDataListener;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionConvertor;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionDetailConvertor;
import com.yhl.scp.dfp.loading.domain.entity.LoadingDemandSubmissionDO;
import com.yhl.scp.dfp.loading.domain.service.LoadingDemandSubmissionDomainService;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDetailDTO;
import com.yhl.scp.dfp.loading.enums.FileDataSourceEnum;
import com.yhl.scp.dfp.loading.enums.UploadStatusEnum;
import com.yhl.scp.dfp.loading.handler.LoadingComponentHandler;
import com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionDao;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionPO;
import com.yhl.scp.dfp.loading.service.DemandForecastAttachmentsService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.DemandParsedVO;
import com.yhl.scp.dfp.loading.vo.DemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemLlmPromptService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemLlmPromptVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandForecastInterfaceLogService;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.utils.BpimDateUtil;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;
import com.yhl.scp.dfp.version.service.IDemandVersionCreate;
import com.yhl.scp.dfp.version.service.factory.DemandVersionCreateFactory;
import com.yhl.scp.ips.api.vo.LlmConfigVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.Dept;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <code>LoadingDemandSubmissionServiceImpl</code>
 * <p>
 * 装车需求提报应用实现
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:12:19
 */
@Slf4j
@Service
public class LoadingDemandSubmissionServiceImpl extends AbstractService implements LoadingDemandSubmissionService {

    @Resource
    private LoadingDemandSubmissionDao loadingDemandSubmissionDao;

    @Resource
    private LoadingDemandSubmissionDomainService loadingDemandSubmissionDomainService;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    @Lazy
    private LoadingComponentHandler loadingComponentHandler;

    @Resource
    private OriginDemandVersionService originDemandVersionService;
    @Resource
    private OemService oemService;

    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Resource
    private DemandVersionCreateFactory demandVersionCreateFactory;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Value("${loadSubmission.file.path:/usr/local/loadSubmission}")
    private String filePath;

    @Resource
    private FdpOriginDemandInterfaceLogService originDemandService;

    @Resource
    private FdpOriginDemandForecastInterfaceLogService originDemandForecastService;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private DemandForecastVersionService demandForecastVersionService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private DfpCalendarRuleService dfpCalendarRuleService;

    @Resource
    private DfpResourceCalendarDomainService dfpResourceCalendarDomainService;

    @Resource
    private OemLlmPromptService oemLlmPromptService;

    @Resource
    private DemandForecastAttachmentsService demandForecastAttachmentsService;


    @Override
    public BaseResponse<Void> doCreate(LoadingDemandSubmissionDTO loadingDemandSubmissionDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDO loadingDemandSubmissionDO = LoadingDemandSubmissionConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDTO);
        LoadingDemandSubmissionPO loadingDemandSubmissionPO = LoadingDemandSubmissionConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDomainService.validation(loadingDemandSubmissionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(loadingDemandSubmissionPO);
        if (StringUtils.isBlank(loadingDemandSubmissionPO.getId())) {
            loadingDemandSubmissionPO.setId(UUIDUtil.getUUID());
        }
        loadingDemandSubmissionDao.insert(loadingDemandSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(LoadingDemandSubmissionDTO loadingDemandSubmissionDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDO loadingDemandSubmissionDO = LoadingDemandSubmissionConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDTO);
        LoadingDemandSubmissionPO loadingDemandSubmissionPO = LoadingDemandSubmissionConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDomainService.validation(loadingDemandSubmissionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(loadingDemandSubmissionPO);
        loadingDemandSubmissionDao.update(loadingDemandSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<LoadingDemandSubmissionDTO> list) {
        List<LoadingDemandSubmissionPO> newList = LoadingDemandSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        loadingDemandSubmissionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<LoadingDemandSubmissionDTO> list) {
        List<LoadingDemandSubmissionPO> newList = LoadingDemandSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        loadingDemandSubmissionDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return loadingDemandSubmissionDao.deleteBatch(idList);
        }
        return loadingDemandSubmissionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public LoadingDemandSubmissionVO selectByPrimaryKey(String id) {
        LoadingDemandSubmissionPO po = loadingDemandSubmissionDao.selectByPrimaryKey(id);
        return LoadingDemandSubmissionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION")
    public List<LoadingDemandSubmissionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION")
    public List<LoadingDemandSubmissionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<LoadingDemandSubmissionVO> dataList = loadingDemandSubmissionDao.selectByCondition(sortParam, queryCriteriaParam);
        LoadingDemandSubmissionServiceImpl target = SpringBeanUtils.getBean(LoadingDemandSubmissionServiceImpl.class);

        //处理版本查询参数
        Map<String, Object> params = new HashMap<>();
        if(StringUtils.isNotEmpty(queryCriteriaParam)){
        	int index = queryCriteriaParam.indexOf("version_id");
            if (index != -1) {
                params.put("queryCriteriaParam", queryCriteriaParam.substring(index));
            }
        }
        return target.invocation(dataList, params, this.getInvocationName());
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectByParams(Map<String, Object> params) {
        List<LoadingDemandSubmissionPO> list = loadingDemandSubmissionDao.selectByParams(params);
        return LoadingDemandSubmissionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.LOADING_DEMAND_SUBMISSION.getCode();
    }

    @Override
    public List<LoadingDemandSubmissionVO> invocation(List<LoadingDemandSubmissionVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        List<String> submissionIds = dataList.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(submissionIds)) {
            return dataList;
        }
        Map<String, Object> detailParam = MapUtil.newHashMap();
        detailParam.put("submissionIds", submissionIds);
        List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(detailParam);
        if (CollectionUtils.isEmpty(submissionDetailVOS)) {
            return dataList;
        }
        Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap = submissionDetailVOS.stream()
                .collect(
                        Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId, Collectors.toList()));

        //获取用户权限下所有的装车需求提报数据
        Date maxImportDate = null;
        if(params != null && params.containsKey("queryCriteriaParam")) {
        	List<LoadingDemandSubmissionVO> allDataList = loadingDemandSubmissionDao.selectByCondition(null,
        			params.get("queryCriteriaParam").toString());
        	 Optional<Date> maxImportTime = allDataList.stream()
                     .filter(vo -> vo.getImportTime() != null) // 判空过滤
                     .map(LoadingDemandSubmissionVO::getImportTime)
                     .max(Date::compareTo); // 取最大日期
        	 if(maxImportTime.isPresent()) {
        		 maxImportDate = maxImportTime.get();
        	 }
        }

        for (LoadingDemandSubmissionVO submissionVO : dataList) {
            List<LoadingDemandSubmissionDetailVO> detailVOS = submissionDetailMap.get(submissionVO.getId());
            if (CollectionUtils.isEmpty(detailVOS)) {
                continue;
            }
            submissionVO.setDayData(detailVOS.stream().filter(x -> GranularityEnum.DAY.getCode().equals(x.getSubmissionType())).sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime)).collect(Collectors.toList()));
            submissionVO.setMonthData(detailVOS.stream().filter(x -> GranularityEnum.MONTH.getCode().equals(x.getSubmissionType())).sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime)).collect(Collectors.toList()));
            // 添加日期汇总数据计算
            BigDecimal daySummarySum = submissionVO.getDayData().stream().filter(e-> e.getDemandQuantity() != null)
            		.map(LoadingDemandSubmissionDetailVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            submissionVO.setDaySummary(daySummarySum);
            Map<String, List<LoadingDemandSubmissionDetailVO>> loadingDemandSubmissionDetailMapOfTime = submissionVO.getDayData().stream().collect(Collectors.groupingBy(
                    detail -> DateUtils.dateToString(DateUtils.stringToDate(detail.getDemandTime()), DateUtils.COMMON_DATE_STR3)));

            Map<String, BigDecimal> daySummary = getDemandTimeQuantityMap(loadingDemandSubmissionDetailMapOfTime);
            List<LoadingDemandSubmissionDetailVO> daySummaryList = daySummary.entrySet().stream()
                    .map(entry -> {
                        LoadingDemandSubmissionDetailVO vo = new LoadingDemandSubmissionDetailVO();
                        vo.setDemandTime(entry.getKey());
                        vo.setDemandQuantity(entry.getValue());
                        return vo;
                    })
                    .sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime))
                    .collect(Collectors.toList());

            submissionVO.setDaySummaryData(daySummaryList);
            //1.ABS((“导入的汇总”-“历史汇总”)/“历史汇总”) * 100% >= 15%，则产品编码字段的字体颜色变为红色
            BigDecimal historyDemandQuantity = submissionVO.getHistoryDemandQuantity();
            if(historyDemandQuantity != null && historyDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
            	BigDecimal totalDemandQuantity = submissionVO.getDayData().stream()
                        .map(LoadingDemandSubmissionDetailVO::getDemandQuantity).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            	BigDecimal diffDemandQuantity = totalDemandQuantity.subtract(historyDemandQuantity).abs();
            	BigDecimal diffDemandQuantityRate = diffDemandQuantity.multiply(BigDecimal.valueOf(100))
            			.divide(historyDemandQuantity, 0, BigDecimal.ROUND_UP);
            	if(diffDemandQuantityRate.compareTo(BigDecimal.valueOf(15)) >= 0) {
            		submissionVO.setBackgroundColor("RED");
            	}
            }
            //2.如果导入时间 = 创建时间，则该行数据的产品编码字段的底色变为绿色，优先级2
            Date importTime = submissionVO.getImportTime();
            if(importTime != null && Objects.equals(DateUtils.dateToString(importTime, "yyyy-MM-dd HH:mm"),
            		DateUtils.dateToString(submissionVO.getCreateTime(), "yyyy-MM-dd HH:mm"))) {
            	submissionVO.setBackgroundColor("GREEN");
            }
            //3.如果导入时间 ≠ 导入时间所有数据中最新的导入时间，则该行数据的产品编码字段的底色变为浅黄色， 优先级1
            if(maxImportDate != null && importTime != null && !Objects.equals(DateUtils.dateToString(submissionVO.getImportTime(), "yyyy-MM-dd HH:mm"),
            		DateUtils.dateToString(maxImportDate, "yyyy-MM-dd HH:mm"))) {
            	submissionVO.setBackgroundColor("YELLOW");
            }
        }
        return dataList;
    }

    private Map<String, BigDecimal> getDemandTimeQuantityMap(Map<String, List<LoadingDemandSubmissionDetailVO>> listMap) {
        Map<String, BigDecimal> daySummary = new HashMap<>();
        for (Map.Entry<String, List<LoadingDemandSubmissionDetailVO>> entry : listMap.entrySet()) {
            List<LoadingDemandSubmissionDetailVO> value = entry.getValue();
            List<LoadingDemandSubmissionDetailVO> demandQuantityNullList = value.stream().filter(s -> null == s.getDemandQuantity()).collect(Collectors.toList());
            if (demandQuantityNullList.size() == value.size()) {
                daySummary.put(entry.getKey(), null);
                continue;
            }

            BigDecimal dayQuantitySum = BigDecimal.ZERO;
            for (LoadingDemandSubmissionDetailVO detail : value) {
                if (detail.getDemandQuantity() != null) {
                    dayQuantitySum = dayQuantitySum.add(detail.getDemandQuantity());
                }
            }
            daySummary.put(entry.getKey(), dayQuantitySum);
        }
        return daySummary;
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response, String templateType) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "装车需求提报模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("主机厂编码"));
        headers.add(Collections.singletonList("产品编码"));
        headers.add(Collections.singletonList("客户零件号"));
        if ("ROW".equals(templateType)) {

            BpimDateUtil.getDatesForDays(31).forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3))));
            BpimDateUtil.getDatesForMonths(13).forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.YEAR_MONTH))));

        } else if ("COL".equals(templateType)) {
            headers.add(Collections.singletonList("日期"));
            headers.add(Collections.singletonList("数量"));

            // 新增代码：生成最近30天和12个月的数据
            List<List<String>> dataRows = new ArrayList<>();

            // 生成最近30天的数据
            BpimDateUtil.getDatesForNextNDays(30).forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3)); // 日期放在第4列
                dataRows.add(row);
            });

            // 生成12个月的数据
            BpimDateUtil.getDatesForNextNMonths(12).forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, DateUtils.dateToString(day, DateUtils.YEAR_MONTH)); // 日期放在第4列
                dataRows.add(row);
            });

        }

        EasyExcelFactory.write(out).sheet("装车需求提报").head(headers).registerWriteHandler(new CustomColumnWidthHandler()).doWrite(Collections.emptyList());
    }

    @Override
    public BaseResponse<Void> submissionData(LoadingDemandSubmissionReqDTO reqDTO) {
        try {
            String submissionType = reqDTO.getSubmissionType();
            String originVersionId = reqDTO.getOriginVersionId();
            List<LoadingDemandSubmissionReqDetailDTO> detailDTOList = reqDTO.getDetailDTOList();
            if (CollectionUtils.isEmpty(detailDTOList)) {
                return BaseResponse.success("请提报相关数据");
            }
            if (SubmissionTypeEnum.API.getCode().equals(submissionType)) {
                List<String> oemCodeList = detailDTOList.stream().map(LoadingDemandSubmissionReqDetailDTO::getOemCode).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(oemCodeList)) {
                    return BaseResponse.success("请提报相关数据");
                }
                BaseResponse<List<Scenario>> scenario = ipsNewFeign.getScenariosByModuleCode(RzzSystemModuleEnum.DFP.getCode());
                if (CollectionUtils.isEmpty(scenario.getData())) {
                    return BaseResponse.error("未找到对应的场景");
                }
                List<OemVO> oemVOS = oemService.selectProductEdiFlag(YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getCode());
                if (CollectionUtils.isEmpty(oemVOS)) {
                    return BaseResponse.error("未找到对应的主机厂");
                }
                List<OemVO> ediOemList = oemVOS.stream().filter(oemVO -> oemCodeList.stream().anyMatch(t -> t.equals(oemVO.getOemCode()))).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(ediOemList)) {
                    return BaseResponse.error("所选主机厂不支持EDI接口同步");
                }
                return apiSubmission(ediOemList, originVersionId, scenario.getData().get(0));
            } else if (SubmissionTypeEnum.FILE.getCode().equals(submissionType)) {
                StringBuilder totalErrMsg = new StringBuilder();
                for (LoadingDemandSubmissionReqDetailDTO detailDTO : detailDTOList) {
                    String oemCode = detailDTO.getOemCode();
                    String contentType = detailDTO.getContentType();
                    File submissionFile = getSubmissionFile(originVersionId, oemCode, contentType);
                    if (StringUtils.isBlank(contentType)) {
                        return BaseResponse.error("参数异常");
                    }
                    if (Objects.isNull(submissionFile)) {
                        return BaseResponse.error("文件为空");
                    }
                    // TODO 更新原始版本的文件下载链接
                    originDemandVersionService.updateSubmissionType(originVersionId, oemCode, SubmissionTypeEnum.FILE.getCode());
                    // 增量和全量进行数据新增
                    // 初始化监听器
                    Map<String, String> extMap = MapUtil.newHashMap();
                    extMap.put("originVersionId", originVersionId);
                    extMap.put("oemCode", oemCode);
                    extMap.put("importType", reqDTO.getImportType());
                    extMap.put("contentType", contentType);
                    ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 2, 0, extMap);
                    // 解析数据
                    try (FileInputStream inputStream = new FileInputStream(submissionFile)) {
                        EasyExcelFactory.read(inputStream, excelDynamicDataListener).sheet().doReadSync();
                    }
                    String errMsg = excelDynamicDataListener.getErrMsg();
                    if (StringUtils.isNotBlank(errMsg)) {
                        totalErrMsg.append(errMsg);
                        totalErrMsg.append("\n");
                    }
                }
                if (StringUtils.isBlank(totalErrMsg.toString())) {
                    return BaseResponse.success("模板提报成功");
                } else {
                    return BaseResponse.error(String.format("模板提报成功，部分失败：%s", totalErrMsg));
                }
            } else {
                return BaseResponse.error("提报类型错误");
            }
        } catch (Exception ex) {
            String errMsg = String.format("提报失败:%s", ex.getMessage());
            log.error(errMsg);
            return BaseResponse.error(errMsg);
        }
    }

    private void deleteData(String originVersionId) {
        loadingDemandSubmissionDao.deleteData(originVersionId, null);
    }

    private File getSubmissionFile(String originVersionId, String oemCode, String contentType) {
        String directoryStr = filePath + File.separator + originVersionId + File.separator + oemCode + File.separator + contentType;
        File directory = new File(directoryStr);
        File latestFile = null;
        long latestModTime = 0;
        // 确保路径存在且是一个目录
        if (directory.exists() && directory.isDirectory()) {
            // 遍历目录下的所有文件和文件夹
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    // 只考虑文件，忽略目录
                    if (file.isFile()) {
                        try {
                            BasicFileAttributes attr = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                            long modTime = attr.lastModifiedTime().toMillis();
                            if (modTime > latestModTime) {
                                latestModTime = modTime;
                                latestFile = file;
                            }
                        } catch (IOException e) {
                            // 处理读取属性时可能发生的异常，这里只是简单地打印出来
                            log.error(e.getMessage());
                        }
                    }
                }
            }
        }
        return latestFile;
    }

    @Override
    public BaseResponse<Void> uploadFiles(String originVersionId, String oemCode, String contentType, MultipartFile originFile, MultipartFile submissionFile) {
        if (submissionFile.isEmpty()) {
            return BaseResponse.error(String.format("主机厂：%s提报文件不存在", oemCode));
        }
        String directoryStr = filePath + File.separator + originVersionId + File.separator + oemCode;
        File directory = new File(directoryStr);
        if (directory.mkdirs()) {
            log.info("oemCode:{}创建目录成功", oemCode);
        } else {
            log.error("oemCode:{}创建目录失败或目录已存在", oemCode);
        }
        try {
            // 构建文件保存路径
            String submissionPathStr = directoryStr + File.separator + contentType;
            File submissionDirectory = new File(submissionPathStr);
            @SuppressWarnings("unused") boolean delete = submissionDirectory.delete();
            if (submissionDirectory.mkdirs()) {
                log.info("contentType:{}创建目录成功", contentType);
            } else {
                log.error("contentType:{}创建目录失败或目录已存在", contentType);
            }
            String submissionFileStr = submissionPathStr + File.separator + submissionFile.getOriginalFilename();
            Path submissionPath = Paths.get(submissionFileStr);
            submissionFile.transferTo(submissionPath);
            if (!originFile.isEmpty()) {
                String originPathStr = directoryStr + File.separator + "ORIGIN";
                File originDirectory = new File(originPathStr);
                if (originDirectory.mkdirs()) {
                    log.info("contentType:{}创建目录成功", "ORIGIN");
                } else {
                    log.error("contentType:{}创建目录失败或目录已存在", "ORIGIN");
                }
                String originFileStr = originPathStr + File.separator + originFile.getOriginalFilename();
                Path originPath = Paths.get(originFileStr);
                originFile.transferTo(originPath);
            }
        } catch (IOException ex) {
            return BaseResponse.error("文件上传失败");
        }
        return BaseResponse.success("文件上传成功");
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        String scenario = SystemHolder.getScenario();
        List<String> collect = new ArrayList<>(headers.values());
        if (!collect.contains("主机厂编码") || !collect.contains("产品编码") || !collect.contains("客户零件号")) {
            throw new BusinessException("导入数据模板错误，请检查");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new BusinessException("模板数据为空,没有数据");
        }
        String originVersionId = extMap.get("originVersionId");
        String contentType = extMap.get("contentType");
        String templateType = extMap.get("templateType");
        String demandCategory = extMap.get("projectDemandType");
        String checkFlag = extMap.get("checkFlag");

        // 处理第二种模板格式
        if ("COL".equals(templateType)) {
            Map<Integer, String> newHeaders = new LinkedHashMap<>();
            Map<String, Map<Integer, String>> newRowMap = new LinkedHashMap<>();
            // 复制固定列
            for (int i = 0; i < 3; i++) {
                newHeaders.put(i, headers.get(i));
            }
            // 创建日期列
            Set<String> uniqueDates = new TreeSet<>();
            for (Map<Integer, String> row : data) {
                uniqueDates.add(row.get(3));
            }
            // 添加日期列到新的headers
            int columnIndex = 3;
            for (String date : uniqueDates) {
                newHeaders.put(columnIndex++, date);
            }

            for (Map<Integer, String> row : data) {
                String key = (row.get(0) + "|" + row.get(1) + "|" + row.get(2)).trim().toLowerCase();
                Map<Integer, String> newRow = newRowMap.computeIfAbsent(key, k -> {
                    Map<Integer, String> r = new LinkedHashMap<>();
                    for (int i = 0; i < newHeaders.size(); i++) {
                        r.put(i, i < 3 ? row.get(i).trim() : null);
                    }
                    return r;
                });

                String date = row.get(3).trim();
                String quantity = row.get(4);
                int dateColumnIndex = new ArrayList<>(newHeaders.values()).indexOf(date);
                newRow.put(dateColumnIndex, StringUtils.isBlank(quantity) ? null : quantity.trim());
            }
            // 更新headers和data
            headers.clear();
            headers.putAll(newHeaders);
            data.clear();
            data.addAll(newRowMap.values());
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOList = oemService.selectByParams(queryMap);

        Set<String> oemCodes = getOemNamesFromData(data);
        if (CollectionUtils.isEmpty(oemCodes)) {
            throw new BusinessException("未能从数据中获取有效的主机厂编码");
        }

        Map<String, String> oemCodeToNameMap = oemVOList.stream().filter(Objects::nonNull).filter(oemVO -> oemVO.getOemCode() != null && oemVO.getOemName() != null).collect(Collectors.toMap(OemVO::getOemCode, OemVO::getOemName, (v1, v2) -> v1));
        Set<String> notFindOemCodes = oemCodes.stream().filter(e -> !oemCodeToNameMap.containsKey(e)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notFindOemCodes)) {
            throw new BusinessException("未能匹配到有效的主机厂名称:" + String.join(",", notFindOemCodes));
        }

        //校验主机厂，产品绑定关系
        List<String> productCodes = data.stream().map(row -> row.get(1)).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(), "SALE_ORGANIZATION", "INTERNAL", null);
        String saleRangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productInfoList = mdsFeign.selectProductStockPointByParams(scenario, ImmutableMap.of(
//    			"enabled", YesOrNoEnum.YES.getCode(),
//    			"stockPointCode", rangeData,
                "productCodes", productCodes));

        Map<String, NewProductStockPointVO> productInfoMap = productInfoList.stream().collect(Collectors.toMap(e -> e.getProductCode() + "&" + e.getStockPointCode(), Function.identity(), (v1, v2) -> v1));

        Map<String, List<String>> fg2SaMap = mdsFeign.selectFg2SaMap(scenario, productCodes);
        List<String> saCodes = fg2SaMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<String> allProductCodes = new ArrayList<>();
        allProductCodes.addAll(saCodes);
        allProductCodes.addAll(productCodes);
        //  提取 productCode 并记录对应的行号
        Map<String, Integer> productCodeRowMap = new HashMap<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String productCode = rowMap.get(1);
            if (StringUtils.isNotBlank(productCode)) {
                productCodeRowMap.put(productCode, i + 2);
            }
        }
        List<String> productStockPointBaseCodes = mdsFeign.selectProductStockPointBaseByParams(scenario, ImmutableMap.of("productCodeList", allProductCodes)).stream().map(MdsProductStockPointBaseVO::getProductCode).distinct().collect(Collectors.toList());

        Set<String> unmatchedProducts = new HashSet<>();
        for (String productCode : productCodes) {
            if (!productStockPointBaseCodes.contains(productCode)) {
                unmatchedProducts.add(productCode);
            }
        }

        if (!unmatchedProducts.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder();
            for (String productCode : unmatchedProducts) {
                Integer rowNum = productCodeRowMap.get(productCode);
                errorMsg.append(String.format("行数：%d，产品编码%s未能匹配到有效的产品工艺数据，请联系工艺维护%n", rowNum, productCode));
            }
            throw new BusinessException(errorMsg.toString());
        }
        List<OemVehicleModelVO> oemVehicleModelList = oemVehicleModelService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "oemCodeList", new ArrayList<>(oemCodes)));
        Map<String, List<String>> vehicleOmesMap = oemVehicleModelList.stream().collect(Collectors.groupingBy(OemVehicleModelVO::getOemVehicleModelCode, Collectors.mapping(OemVehicleModelVO::getOemCode, Collectors.toList())));

        //获取当前月的下标和今天到月底的下标
        List<Integer> monthDayIndexList = getMonthDayIndexList(headers);
        Integer currentMonthIndex = monthDayIndexList.get(monthDayIndexList.size() - 1);
        monthDayIndexList.remove(monthDayIndexList.size() - 1);
        List<Integer> errorIndex = new ArrayList<>();
        List<Integer> checkIndex = new ArrayList<>();
        List<String> checkProductCodes = new ArrayList<>();
        List<String> checkOemCodes = new ArrayList<>();

        //获取生产组织库存点
        BaseResponse<ScenarioBusinessRangeVO> productOrgRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(), "PRODUCT_ORGANIZATION", "INTERNAL", null);
        String productOrgRangeDate = productOrgRange.getData().getRangeData();
        List<String> productOrgStockPointCodes = Arrays.asList(productOrgRangeDate.split(","));
        List<String> productOrgProductIds = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String omeCode = rowMap.get(0);
            String productCode = rowMap.get(1);
            NewProductStockPointVO productInfo = productInfoMap.get(productCode + "&" + saleRangeData);
            int rowNum = i + 2;
            if (productInfo == null) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "物料未维护，请联系工艺维护");
            }
            if (!YesOrNoEnum.YES.getCode().equals(productInfo.getEnabled())) {
                throw new BusinessException("行数：" + rowNum + ",产品编码不是有效的，请联系工艺维护");
            }
            if (!productStockPointBaseCodes.contains(productCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "不存在工艺基础数据，请联系工艺维护");
            }
            if (fg2SaMap.containsKey(productCode)) {
                List<String> saCodeList = fg2SaMap.get(productCode);
                List<String> diffSection = new ArrayList<>(com.yhl.platform.common.utils.CollectionUtils.getDiffSection(saCodeList, productStockPointBaseCodes));
                if (CollectionUtils.isNotEmpty(diffSection)) {
                    String join = String.join(",", diffSection);
                    throw new BusinessException("行数：" + rowNum + ",供应类型为制造的物料编码" + join + "不存在工艺基础数据，请联系工艺维护");
                }
            }
            String vehicleModelCode = productInfo.getVehicleModelCode();
            if (StringUtils.isEmpty(vehicleModelCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "对应车型为空,请检查");
            }
            List<String> omeCodes = vehicleOmesMap.get(vehicleModelCode);
            if (CollectionUtils.isEmpty(omeCodes) || !omeCodes.contains(omeCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码 " + productCode + "对应车型与主机厂:" + omeCode + "(" + oemCodeToNameMap.get(omeCode) + ")关联为空,请检查");
            }
            //判断采购计划类别的库存点是否在生产组织库存点
            String stockPointCode = productInfo.getPoCategory().split("\\.")[1];
            if (productOrgStockPointCodes.contains(stockPointCode) && productInfoMap.containsKey(productCode + "&" + stockPointCode) && !productOrgProductIds.contains(productInfo.getId())) {
                productOrgProductIds.add(productInfoMap.get(productCode + "&" + stockPointCode).getId());
            }

            String currMonthQty = rowMap.get(currentMonthIndex);
            if (StringUtils.isEmpty(currMonthQty)) {
                //校验存量数据
                if (!checkOemCodes.contains(omeCode)) {
                    checkOemCodes.add(omeCode);
                }
                if (!checkProductCodes.contains(productCode)) {
                    checkProductCodes.add(productCode);
                }
                checkIndex.add(i);
                continue;
            }
            //校验当月发货的汇总量（如今天是5月15日，汇总量为2025-05-15 至2025-05-31）是否大于当月预测量
            BigDecimal totalQty = BigDecimal.ZERO;
            for (Integer dayIndex : monthDayIndexList) {
                String dayQty = rowMap.get(dayIndex);
                if (StringUtils.isNotEmpty(dayQty)) {
                    totalQty = totalQty.add(new BigDecimal(dayQty));
                }
            }
            if (totalQty.compareTo(new BigDecimal(currMonthQty)) > 0) {
                errorIndex.add(rowNum);
            }
        }

        //校验产品工艺路径
        Map<String, String> checkPorductRoutingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productOrgProductIds)) {
            long startTime = System.currentTimeMillis();
            checkPorductRoutingMap = mdsFeign.checkPorductRouting(scenario, productOrgProductIds);
            log.info("装车需求提报导入-校验工艺路径数据耗时：{}", (System.currentTimeMillis() - startTime));
        }
        List<String> errorMsgList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String productCode = rowMap.get(1);
            int rowNum = i + 2;
            if (checkPorductRoutingMap.containsKey(productCode)) {
                errorMsgList.add("行数：" + rowNum + ",产品编码:" + productCode + "," + checkPorductRoutingMap.get(productCode));
                checkPorductRoutingMap.remove(productCode);
            }
        }
        //如果MAP中还有元素，那就是输入物品中的工艺类型为制造的产品
        boolean firstFlag = true;
        for (Entry<String, String> checkPorductEntry : checkPorductRoutingMap.entrySet()) {
            if (firstFlag) {
                errorMsgList.add("供应类型为制造的物料, 产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
            } else {
                errorMsgList.add("产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
            }
            firstFlag = false;
        }
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            throw new BusinessException("导入失败：" + String.join("<br/>", errorMsgList));
        }

        //进行发货数量和预测校验
        if (CollectionUtils.isNotEmpty(checkOemCodes) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
            //校验存量数据
            Map<String, Object> loadingParamMap = new HashMap<>();
            loadingParamMap.put("versionId", originVersionId);
            loadingParamMap.put("enabled", YesOrNoEnum.YES.getCode());
            loadingParamMap.put("productCodes", checkProductCodes);
            loadingParamMap.put("oemCodes", checkOemCodes);
            loadingParamMap.put("demandCategory", demandCategory);
            List<LoadingDemandSubmissionVO> oldLodLoadingDemandSubmissionVOS = this.selectByParams(loadingParamMap);
            Map<String, String> oldLodLoadingDemandSubmissionMap = oldLodLoadingDemandSubmissionVOS.stream().collect(Collectors.toMap(e -> e.getOemCode() + "&" + e.getProductCode(), LoadingDemandSubmissionVO::getId, (v1, v2) -> v1));
            //查询获取历史的装车需求详细信息（只差月份）
            Map<String, BigDecimal> submissionDetailVOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oldLodLoadingDemandSubmissionVOS)) {
                List<String> oldSubmissionIds = oldLodLoadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
                List<LoadingDemandSubmissionDetailVO> oldLodLoadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "submissionType", GranularityEnum.MONTH.getCode(), "demandTime", DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH), "submissionIds", oldSubmissionIds));
                submissionDetailVOMap = oldLodLoadingDemandSubmissionDetailVOS.stream().filter(e -> e.getDemandQuantity() != null).collect(Collectors.toMap(LoadingDemandSubmissionDetailVO::getSubmissionId, LoadingDemandSubmissionDetailVO::getDemandQuantity, (v1, v2) -> v1));
            }
            for (int i = 0; i < data.size(); i++) {
                if (!checkIndex.contains(i)) {
                    continue;
                }
                Integer rowNum = i + 2;
                Map<Integer, String> rowMap = data.get(i);
                String omeCode = rowMap.get(0);
                String productCode = rowMap.get(1);
                String submissionId = oldLodLoadingDemandSubmissionMap.get(omeCode + "&" + productCode);
                if (submissionId == null) {
                    continue;
                }
                BigDecimal totalQty = submissionDetailVOMap.getOrDefault(submissionId, BigDecimal.ZERO);
                BigDecimal totalDayQty = BigDecimal.ZERO;
                for (Integer dayIndex : monthDayIndexList) {
                    String dayQty = rowMap.get(dayIndex);
                    if (StringUtils.isNotEmpty(dayQty)) {
                        totalDayQty = totalDayQty.add(new BigDecimal(dayQty));
                    }
                }
                if (totalDayQty.compareTo(totalQty) > 0) {
                    errorIndex.add(rowNum);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorIndex) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
            Collections.sort(errorIndex);
            String errorIndexMsg = errorIndex.stream().map(Object::toString).collect(Collectors.joining(", "));
            throw new BusinessException("行数" + errorIndexMsg + "，发货数量大于预测数量，如确认请点击确定按钮");
        }

        if ("full".equals(extMap.get("importType"))) {
            // 全量导入 先删除需求提报全部数据
            List<String> waitDeleteIds = loadingDemandSubmissionDetailService.selectByOriginIdAndOemCode(originVersionId, null, contentType);
            this.deleteData(originVersionId);
            // 删除历史提报详情数据
            loadingDemandSubmissionDetailService.doDelete(waitDeleteIds);
        }

        // 对每个oemCode分别处理数据
        extMap.put("oemCode", String.join(",", oemCodes));
        loadingComponentHandler.handle(contentType, headers, data, extMap);
    }

    /**
     * 获取当前月的下标和今天到月底的下标
     *
     * @param headers 表头列表
     */
    private List<Integer> getMonthDayIndexList(Map<Integer, String> headers) {
        LocalDate today = LocalDate.now();
        // 获取本月的最后一天
        LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 创建列表存储每天的字符串格式
        List<String> currMonthDayList = new ArrayList<>();
        // 从今天开始遍历到最后一天
        for (LocalDate date = today; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
            currMonthDayList.add(date.format(formatter));
        }
        String currYearMonth = DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH);
        Integer currYearMonthIndex = 0;
        List<Integer> indexList = new ArrayList<>();
        for (Entry<Integer, String> headerEntry : headers.entrySet()) {
            if (currMonthDayList.contains(headerEntry.getValue())) {
                indexList.add(headerEntry.getKey());
            }
            if (currYearMonth.equals(headerEntry.getValue())) {
                currYearMonthIndex = headerEntry.getKey();
            }
        }
        indexList.add(currYearMonthIndex);
        return indexList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        List<String> submissionIds = versionDTOList.stream().map(RemoveVersionDTO::getId).distinct().collect(Collectors.toList());
        // 删除主数据
        loadingDemandSubmissionDao.deleteBatch(submissionIds);
        // 删除明细数据
        loadingDemandSubmissionDetailService.deleteBySubmissionIds(submissionIds);
        return 1;
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectByProductCode(List<String> productCodeList) {
        List<LoadingDemandSubmissionVO> dataList = loadingDemandSubmissionDao.selectByProductCode(productCodeList);
        LoadingDemandSubmissionServiceImpl target = SpringBeanUtils.getBean(LoadingDemandSubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public void downloadFile(String originVersionId, String oemCode, HttpServletResponse response) {
        String versionOemFilePath = filePath + File.separator + originVersionId + File.separator + oemCode;
        try {
            versionOemFilePath = IOUtils.cleanseDir(false, versionOemFilePath);
            if (versionOemFilePath.endsWith("/")) {
                versionOemFilePath = versionOemFilePath.substring(0, versionOemFilePath.length() - 1);
            }
            log.info("versionOemFilePath is {}", versionOemFilePath);
            File file = new File(versionOemFilePath);
            if (file.exists()) {
                response.setContentType("application/zip");
                response.setHeader("Content-Disposition", "attachment; filename=" + oemCode + ".zip");
                OutputStream os = response.getOutputStream();
                ZipUtil.zip(os, Charset.defaultCharset(), false, null, file);
            } else {
                log.error("文件目录不存在！");
                throw new BusinessException("文件目录不存在！");
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> updateLoadingSubmissionDetail(List<LoadingDemandSubmissionDetailDTO> loadingDemandSubmissionDetailDTOs) {
        if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailDTOs)) {
            return BaseResponse.error("请至少选择一条数据");
        }
        // for (LoadingDemandSubmissionDetailDTO dtos : loadingDemandSubmissionDetailDTOs) {
        //     if (dtos.getDemandQuantity() == null) {
        //         return BaseResponse.error("数据不能为空");
        //     }
        // }
        loadingDemandSubmissionDetailDTOs.forEach(x -> loadingDemandSubmissionDetailService.updateQuantityById(x.getId(), x.getDemandQuantity()));
        return BaseResponse.success("修改成功");
    }

    private Set<String> getOemNamesFromData(List<Map<Integer, String>> data) {
        return data.stream().map(row -> row.get(0)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    @Override
    public BaseResponse<Void> importDemand(String originVersionId, String importType, String templateType, String projectDemandType, String checkFlag, MultipartFile submissionFile) {
        Map<String, String> extMap = MapUtil.newHashMap();
        extMap.put("originVersionId", originVersionId);
        extMap.put("importType", importType);
        extMap.put("checkFlag", checkFlag);
        extMap.put("projectDemandType", projectDemandType);
        try {
            if (submissionFile.isEmpty()) {
                throw new BusinessException("文件为空");
            }
            ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 2, 0, extMap);
            EasyExcelFactory.read(submissionFile.getInputStream(), excelDynamicDataListener).sheet().doReadSync();
        } catch (Exception e) {
            log.error("需求提报失败:", e);
            if (e.getLocalizedMessage().contains("发货数量大于预测数量")) {
                throw new BusinessException("{0}", e.getLocalizedMessage());
            }
            throw new BusinessException("导入失败, {0}", e.getLocalizedMessage());
        }

        String successInfo = extMap.get("successInfo");
        String errorInfo = extMap.get("errorInfo");
        if (StringUtils.isBlank(errorInfo)) {
            return BaseResponse.success(successInfo);
        } else {
            return BaseResponse.error(successInfo + errorInfo);
        }

    }

    @SuppressWarnings("unused")
    List<String> doCreateOrigin(String scenario, String dayPeriod, String generateType) {
        VersionCreateDTO versionCreateDTO = new VersionCreateDTO();
        versionCreateDTO.setScenario(scenario);
        versionCreateDTO.setPlanPeriod(dayPeriod);
        versionCreateDTO.setVersionType(VersionTypeEnum.ORIGIN_DEMAND.getCode());
        versionCreateDTO.setGenerateType(generateType);
        versionCreateDTO.setOemCodeResource(new ArrayList<>());
        // 获取最新的原始需求数据
        String originDemandVersionId = originDemandVersionService.selectLatestVersionId();
        if (StringUtils.isNotBlank(originDemandVersionId)) {
            versionCreateDTO.setTargetOriginDemandVersionId(originDemandVersionId);
        }
        IDemandVersionCreate demandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(versionCreateDTO.getVersionType());
        // 生成原始需求版本, 只包含版本号和二级id, list元素0是版本号、元素1是二级版本ID
        return demandVersionCreate.doDemandVersionCreateNew(versionCreateDTO, null);
    }

    @Override
    public synchronized BaseResponse<Void> doCreateNewVersion(String scenario, String generateType) {
        log.info("开始新建版本++++++++++++++++++++++++++++++++++++++++++++++++");
        // 基础数据
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMM");
        Date date = new Date();
        String dayPeriod = dayFormat.format(date);
        String monthPeriod = monthFormat.format(date);

        // 拼装原始需求版本数据
        VersionCreateDTO versionCreateDTO = new VersionCreateDTO();
        versionCreateDTO.setScenario(scenario);
        versionCreateDTO.setPlanPeriod(dayPeriod);
        versionCreateDTO.setVersionType(VersionTypeEnum.ORIGIN_DEMAND.getCode());
        versionCreateDTO.setGenerateType(generateType);
        versionCreateDTO.setOemCodeResource(new ArrayList<>());
        // 获取最新的原始需求数据
        String originDemandVersionId = originDemandVersionService.selectLatestVersionId();
        if (StringUtils.isNotBlank(originDemandVersionId)) {
            versionCreateDTO.setTargetOriginDemandVersionId(originDemandVersionId);
        }
        List<String> excludeProductCodes = loadingDemandSubmissionDao.selectDisabledProductCodes();
        versionCreateDTO.setExcludeProductCodes(excludeProductCodes);
        IDemandVersionCreate demandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(versionCreateDTO.getVersionType());
        // 生成原始需求版本, 只包含版本号和二级id, list元素0是版本号、元素1是二级版本ID
        List<String> list = demandVersionCreate.doDemandVersionCreateNew(versionCreateDTO, null);
        String originVersionCode = list.get(0);
        String originVersionId = list.get(1);

        // 创建版本ID和版本号
        String dailyVersionId = UUIDUtil.getUUID();
        String dailyVersionCode = getVersionCode(VersionTypeEnum.CLEAN_DEMAND.getCode(), originVersionCode);
        String deliveryVersionId = UUIDUtil.getUUID();
        String deliveryVersionCode = getVersionCode(VersionTypeEnum.DELIVERY_PLAN.getCode(), dailyVersionCode);
        String rollingVersionId = UUIDUtil.getUUID();
        String rollingVersionCode = getVersionCode(VersionTypeEnum.CLEAN_FORECAST.getCode(), originVersionCode);
        String forecastVersionId = UUIDUtil.getUUID();
        String forecastVersionCode = getVersionCode(VersionTypeEnum.DEMAND_FORECAST.getCode(), rollingVersionCode);
        String userId = SystemHolder.getUserId();
        CompletableFuture<Void> dailyFuture = CompletableFuture.runAsync(() -> {
            // 日需求版本创建
            // 拼装日需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO dailyVersionCreateDTO = new VersionCreateDTO();
            dailyVersionCreateDTO.setCreator(userId);
            dailyVersionCreateDTO.setScenario(scenario);
            dailyVersionCreateDTO.setVersionType(VersionTypeEnum.CLEAN_DEMAND.getCode());
            dailyVersionCreateDTO.setPlanPeriod(dayPeriod);
            dailyVersionCreateDTO.setGenerateType(generateType);
            dailyVersionCreateDTO.setOemCodeResource(new ArrayList<>());
            // 目标原始需求版本ID
            dailyVersionCreateDTO.setPreviousOriginVersionId(originDemandVersionId);
            dailyVersionCreateDTO.setTargetOriginDemandVersionId(originVersionId);
            dailyVersionCreateDTO.setCurrentVersionId(dailyVersionId);
            dailyVersionCreateDTO.setCurrentVersionCode(dailyVersionCode);
            dailyVersionCreateDTO.setExcludeProductCodes(excludeProductCodes);
            // 生成日需求版本
            IDemandVersionCreate dayDemandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(dailyVersionCreateDTO.getVersionType());
            dayDemandVersionCreate.doDemandVersionCreateParallel(dailyVersionCreateDTO);
        });

        CompletableFuture<Void> deliveryFuture = CompletableFuture.runAsync(() -> {
            // 发货计划版本创建
            // 拼装发货计划需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO deliveryPlanVersionCreateDTO = new VersionCreateDTO();
            deliveryPlanVersionCreateDTO.setCreator(userId);
            deliveryPlanVersionCreateDTO.setScenario(scenario);
            deliveryPlanVersionCreateDTO.setVersionType(VersionTypeEnum.DELIVERY_PLAN.getCode());
            deliveryPlanVersionCreateDTO.setPlanPeriod(dayPeriod);
            deliveryPlanVersionCreateDTO.setGenerateType(generateType);
            deliveryPlanVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            deliveryPlanVersionCreateDTO.setTargetCleanDemandVersionId(dailyVersionId);
            deliveryPlanVersionCreateDTO.setCurrentVersionId(deliveryVersionId);
            deliveryPlanVersionCreateDTO.setCurrentVersionCode(deliveryVersionCode);
            // 获取最新的发货计划需求数据
            Map<String, Object> deliveryPlanMap = new HashMap<>();
            DeliveryPlanVersionVO deliveryPlanVersionVO = deliveryPlanVersionService.selectLatestVersionByParams(deliveryPlanMap);
            if (null != deliveryPlanVersionVO) {
                log.info("获取最新的发货计划版本: {}", deliveryPlanVersionVO.getVersionCode());
                deliveryPlanVersionCreateDTO.setTargetDeliveryPlanVersionId(deliveryPlanVersionVO.getId());
                deliveryPlanVersionCreateDTO.setTargetVersionCode(deliveryPlanVersionVO.getVersionCode());
            }
            deliveryPlanVersionCreateDTO.setExcludeProductCodes(excludeProductCodes);
            // 生成发货计划需求版本
            IDemandVersionCreate deliveryPlanVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(deliveryPlanVersionCreateDTO.getVersionType());
            deliveryPlanVersionCreate.doDemandVersionCreateParallel(deliveryPlanVersionCreateDTO);
        });

        CompletableFuture<Void> rollingFuture = CompletableFuture.runAsync(() -> {
            // 滚动需求版本创建
            // 拼装滚动需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO rollingVersionCreateDTO = new VersionCreateDTO();
            rollingVersionCreateDTO.setCreator(userId);
            rollingVersionCreateDTO.setScenario(scenario);
            rollingVersionCreateDTO.setVersionType(VersionTypeEnum.CLEAN_FORECAST.getCode());
            rollingVersionCreateDTO.setPlanPeriod(monthPeriod);
            rollingVersionCreateDTO.setGenerateType(generateType);
            rollingVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            // 目标原始需求版本ID
            rollingVersionCreateDTO.setPreviousOriginVersionId(originDemandVersionId);
            rollingVersionCreateDTO.setTargetOriginDemandVersionId(originVersionId);
            rollingVersionCreateDTO.setCurrentVersionId(rollingVersionId);
            rollingVersionCreateDTO.setCurrentVersionCode(rollingVersionCode);
            rollingVersionCreateDTO.setExcludeProductCodes(excludeProductCodes);
            // 生成滚动需求版本
            IDemandVersionCreate monthDemandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(rollingVersionCreateDTO.getVersionType());
            monthDemandVersionCreate.doDemandVersionCreateParallel(rollingVersionCreateDTO);
        });

        CompletableFuture<Void> forecastFuture = CompletableFuture.runAsync(() -> {
            // 需求预测版本创建
            // 拼装业务预测数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO demandForecastVersionCreateDTO = new VersionCreateDTO();
            demandForecastVersionCreateDTO.setCreator(userId);
            demandForecastVersionCreateDTO.setScenario(scenario);
            demandForecastVersionCreateDTO.setVersionType(VersionTypeEnum.DEMAND_FORECAST.getCode());
            demandForecastVersionCreateDTO.setPlanPeriod(monthPeriod);
            demandForecastVersionCreateDTO.setGenerateType(generateType);
            demandForecastVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            demandForecastVersionCreateDTO.setTargetCleanForecastVersionId(rollingVersionId);
            demandForecastVersionCreateDTO.setTargetCleanAlgorithmVersionId(getLatestAlgorithmVersionId());
            demandForecastVersionCreateDTO.setCurrentVersionId(forecastVersionId);
            demandForecastVersionCreateDTO.setCurrentVersionCode(forecastVersionCode);

            String targetDemandForecastVersionId = demandForecastVersionService.selectLatestVersionId();
            demandForecastVersionCreateDTO.setTargetDemandForecastVersionId(targetDemandForecastVersionId);
            demandForecastVersionCreateDTO.setExcludeProductCodes(excludeProductCodes);
            // 生成业务预测数据
            IDemandVersionCreate demandForecastVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(demandForecastVersionCreateDTO.getVersionType());
            demandForecastVersionCreate.doDemandVersionCreateParallel(demandForecastVersionCreateDTO);
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(dailyFuture, deliveryFuture, rollingFuture, forecastFuture);
        allFutures.join();
        return BaseResponse.success();
    }

    private String getLatestAlgorithmVersionId() {
        Map<String, Object> params = new HashMap<>();
        params.put("versionStatus", VersionStatusEnum.PUBLISHED.getCode());
        params.put("versionType", VersionTypeEnum.CLEAN_ALGORITHM.getCode());
        List<DemandVersionVO> demandVersionVOS = demandVersionService.selectMaxVersionByParams(params);
        if (CollectionUtils.isNotEmpty(demandVersionVOS)) {
            return demandVersionVOS.get(0).getId();
        }
        return "";
    }

    @Override
    public BaseResponse<Void> syncEdi(Scenario scenario) {
        try {
            Map<String, Object> queryParams = MapUtil.newHashMap();
            queryParams.put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode());
            queryParams.put("enabled", YesOrNoEnum.YES.getCode());

            List<NewStockPointVO> newStockPointVOList = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(), queryParams);
            List<String> grpStockList = newStockPointVOList.stream().filter(t -> Objects.equals(t.getEdiMode(), ApiSourceEnum.GRP.getCode())).map(NewStockPointVO::getOrganizeId).distinct().collect(Collectors.toList());
            List<String> mesStockList = newStockPointVOList.stream().filter(t -> Objects.equals(t.getEdiMode(), ApiSourceEnum.MES.getCode())).map(NewStockPointVO::getOrganizeId).distinct().collect(Collectors.toList());
            log.info("开始同步GRP接口，库存点数据，库存点参数：{}", grpStockList);
            if (CollectionUtils.isNotEmpty(grpStockList)) {
                for (String grpStock : grpStockList) {
                    List<CompletableFuture<Void>> grpFutures = new ArrayList<>();

                    Map demandParam = MapUtil.builder("organizeId", grpStock).build();
                    grpFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.LOADING_DEMAND.getCode(), demandParam)));
                    grpFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.LOADING_FORECAST.getCode(), demandParam)));
                    CompletableFuture.allOf(grpFutures.toArray(new CompletableFuture[0])).join();
                }

            } else {
                log.info("grp接口库存点数据为空");
            }
            log.info("同步GRP接口完成");
            log.info("开始同步MES接口，库存点数据，库存点参数：{}", mesStockList);
            if (CollectionUtils.isNotEmpty(mesStockList)) {

                for (String mesStock : mesStockList) {
                    List<CompletableFuture<Void>> mesFutures = new ArrayList<>();

                    Map demandParam = MapUtil.builder("organizeId", mesStock).build();
                    mesFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MES_SHIP_DEMAND.getCode(), demandParam)));
                    mesFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MES_FORECAST_DEMAND.getCode(), demandParam)));
                    CompletableFuture.allOf(mesFutures.toArray(new CompletableFuture[0])).join();
                }

            } else {
                log.info("mes接口库存点数据为空");
            }
        } catch (Exception e) {
            return BaseResponse.error("同步失败");
        }
        log.info("同步MES接口完成");

        return BaseResponse.success("同步成功");
    }

    /**
     * 收集所有需要同步的产品编码
     */
    private Set<String> collectProductCodesForSync(List<OemVO> ediOemList) {
        Set<String> productCodes = new HashSet<>();
        
        for (OemVO oem : ediOemList) {
            String oemCode = oem.getOemCode();
            try {
                // 查询该主机厂的原始需求数据
                Map<String, Object> queryParams = Collections.singletonMap("oemCode", oemCode);
                
                // 收集日需求的产品编码
                List<FdpOriginDemandInterfaceLogVO> demandLogs = originDemandService.selectVOByParams(queryParams);
                if (CollectionUtils.isNotEmpty(demandLogs)) {
                    demandLogs.stream()
                            .filter(log -> isEdiEnabled(log.getProductEdiFlag(), oem.getEdiFlag()))
                            .map(FdpOriginDemandInterfaceLogVO::getItemNum)
                            .filter(StringUtils::isNotBlank)
                            .forEach(productCodes::add);
                }
                
                // 收集预测需求的产品编码
                List<FdpOriginDemandForecastInterfaceLogVO> forecastLogs = originDemandForecastService.selectVOByParams(queryParams);
                if (CollectionUtils.isNotEmpty(forecastLogs)) {
                    forecastLogs.stream()
                            .filter(log -> isEdiEnabled(log.getProductEdiFlag(), oem.getEdiFlag()))
                            .map(FdpOriginDemandForecastInterfaceLogVO::getItemNum)
                            .filter(StringUtils::isNotBlank)
                            .forEach(productCodes::add);
                }
                
            } catch (Exception e) {
                log.error("收集主机厂{}的产品编码失败：{}", oemCode, e.getMessage(), e);
            }
        }
        
        return productCodes;
    }

    /**
     * 批量执行工艺路径校验
     */
    private BaseResponse<Void> validateProductRoutingBatch(Scenario scenario, List<String> productCodes) {
        try {
            if (CollectionUtils.isEmpty(productCodes)) {
                log.info("没有需要校验的产品编码");
                return BaseResponse.success("校验完成");
            }
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario.getDataBaseName(), StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode(),  "INTERNAL", null);
            String saleRangeData = scenarioBusinessRange.getData().getRangeData();
            
            // 获取产品库存点信息
            List<NewProductStockPointVO> productInfoList = mdsFeign.selectProductStockPointByParams(scenario.getDataBaseName(), ImmutableMap.of(
                    "productCodes", productCodes));
            Map<String, NewProductStockPointVO> productInfoMap = productInfoList.stream()
                    .collect(Collectors.toMap(e -> e.getProductCode() + "&" + e.getStockPointCode(), Function.identity(), (v1, v2) -> v1));

            // 获取生产组织库存点
            BaseResponse<ScenarioBusinessRangeVO> productOrgRange = ipsNewFeign.getScenarioBusinessRange(scenario.getDataBaseName(), StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode(), "INTERNAL", null);
            String productOrgRangeDate = productOrgRange.getData().getRangeData();
            List<String> productOrgStockPointCodes = Arrays.asList(productOrgRangeDate.split(","));
            
            // 收集需要校验的物料ID
            List<String> productOrgProductIds = new ArrayList<>();
            for (String productCode : productCodes) {
                NewProductStockPointVO productInfo = productInfoMap.get(productCode + "&" + saleRangeData);
                if (productInfo != null) {
                    // 判断采购计划类别的库存点是否在生产组织库存点
                    String stockPointCode = productInfo.getPoCategory().split("\\.")[1];
                    if (productOrgStockPointCodes.contains(stockPointCode) && 
                        productInfoMap.containsKey(productCode + "&" + stockPointCode) && 
                        !productOrgProductIds.contains(productInfo.getId())) {
                        productOrgProductIds.add(productInfoMap.get(productCode + "&" + stockPointCode).getId());
                    }
                }
            }

            // 如果没有需要校验的物料ID，直接返回成功
            if (CollectionUtils.isEmpty(productOrgProductIds)) {
                log.info("没有需要校验的物料ID");
                return BaseResponse.error("校验完成,没有需要校验的物料ID");
            }

            // 工艺路径校验，传物料ID
            Map<String, String> validationResult = mdsFeign.checkPorductRouting(scenario.getDataBaseName(), productOrgProductIds);

            if (MapUtils.isNotEmpty(validationResult)) {
                // 有校验错误
                StringBuilder errorMsg = new StringBuilder("工艺路径校验发现以下问题：\n");
                validationResult.forEach((productCode, error) ->
                        errorMsg.append("产品编码：").append(productCode).append("，错误：").append(error).append("\n"));

                log.error("工艺路径校验失败：{}", errorMsg.toString());
                return BaseResponse.error(errorMsg.toString());
            }

            return BaseResponse.success("工艺路径校验通过");

        } catch (Exception e) {
            log.error("执行工艺路径校验时发生异常：{}", e.getMessage(), e);
            return BaseResponse.error("工艺路径校验异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Void> syncDemand(LoadingDemandSubmissionReqDTO reqDTO,Scenario scenario) {
        try {
            log.info("开始同步需求，版本ID: {}, 主机厂数量: {}", reqDTO.getOriginVersionId(),
                    reqDTO.getDetailDTOList() != null ? reqDTO.getDetailDTOList().size() : 0);

            // 1. 参数验证和基础数据准备
            SyncDemandContext context = prepareSyncDemandContext(reqDTO,scenario);
            if (context.hasError()) {
                return BaseResponse.error(context.getErrorMessage());
            }

            // 2.预加载所有主机厂的日历规则
            Map<String, List<CalendarRuleVO>> allRulesByOem = preloadAllCalendarRulesForOems(context.getEdiOemList());

            // 3. 预加载所有主机厂的EDI原始数据（日需求和预测）
            List<String> oemCodes = context.getEdiOemList().stream().map(OemVO::getOemCode).collect(Collectors.toList());
            Map<String, List<FdpOriginDemandInterfaceLogVO>> allDemandLogsByOem = preloadAllDemandLogs(oemCodes);
            Map<String, List<FdpOriginDemandForecastInterfaceLogVO>> allForecastLogsByOem = preloadAllForecastLogs(oemCodes);

            // 4. 批量处理所有主机厂的需求同步
            List<String> errorMessages = context.getEdiOemList().parallelStream()
                    .map(oem -> {
                        // 为当前主机厂获取其预加载的规则列表
                        List<CalendarRuleVO> preloadedRules = allRulesByOem.getOrDefault(oem.getOemCode(), Collections.emptyList());
                        // 为当前主机厂获取其预加载的EDI数据
                        List<FdpOriginDemandInterfaceLogVO> demandLogs = allDemandLogsByOem.getOrDefault(oem.getOemCode(), Collections.emptyList());
                        List<FdpOriginDemandForecastInterfaceLogVO> forecastLogs = allForecastLogsByOem.getOrDefault(oem.getOemCode(), Collections.emptyList());
                        return processSingleOemDemandSync(oem, context, preloadedRules, demandLogs, forecastLogs);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 5. 返回结果
            BaseResponse<Void> result = buildSyncResult(errorMessages);
            if (result.getSuccess()) {
                log.info("需求同步完成，版本ID: {}, 处理主机厂数量: {}", reqDTO.getOriginVersionId(),
                        context.getEdiOemList().size());
            }
            return result;
        } catch (Exception e) {
            log.error("需求同步失败: {}", e.getMessage(), e);
            return BaseResponse.error("需求同步失败: " + e.getMessage());
        }
    }

    /**
     * 一次性预加载所有相关主机厂的日历规则
     */
    private Map<String, List<CalendarRuleVO>> preloadAllCalendarRulesForOems(List<OemVO> ediOemList) {
        if (CollectionUtils.isEmpty(ediOemList)) {
            return Collections.emptyMap();
        }
        try {
            // 1. 收集所有OEM Code
            List<String> oemCodes = ediOemList.stream().map(OemVO::getOemCode).collect(Collectors.toList());

            // 2. 一次性查询所有相关主机厂的日历规则
            Map<String, Object> batchRuleParams = new HashMap<>();
            batchRuleParams.put("oemCodeList", oemCodes);
            batchRuleParams.put("enabled", YesOrNoEnum.YES.getCode());
            List<CalendarRuleVO> allCalendarRules = dfpCalendarRuleService.selectByParams(batchRuleParams);

            log.info("一次性预加载了 {} 个主机厂的 {} 条日历规则", oemCodes.size(), allCalendarRules.size());

            // 3. 将查询到的规则按主机厂编码分组
            return allCalendarRules.stream().collect(Collectors.groupingBy(CalendarRuleVO::getOemCode));
        } catch (Exception e) {
            log.error("预加载所有主机厂日历规则失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 预加载所有相关主机厂的日需求数据
     */
    private Map<String, List<FdpOriginDemandInterfaceLogVO>> preloadAllDemandLogs(List<String> oemCodes) {
        if (CollectionUtils.isEmpty(oemCodes)) {
            return Collections.emptyMap();
        }
        try {
            Map<String, Object> queryParams = Collections.singletonMap("oemCodeList", oemCodes);
            List<FdpOriginDemandInterfaceLogVO> allLogs = originDemandService.selectVOByParams(queryParams);
            log.info("预加载了 {} 个主机厂的 {} 条日需求数据", oemCodes.size(), allLogs.size());
            return allLogs.stream().collect(Collectors.groupingBy(FdpOriginDemandInterfaceLogVO::getOemCode));
        } catch (Exception e) {
            log.error("预加载所有主机厂日需求数据失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 预加载所有相关主机厂的预测需求数据
     */
    private Map<String, List<FdpOriginDemandForecastInterfaceLogVO>> preloadAllForecastLogs(List<String> oemCodes) {
        if (CollectionUtils.isEmpty(oemCodes)) {
            return Collections.emptyMap();
        }
        try {
            Map<String, Object> queryParams = Collections.singletonMap("oemCodeList", oemCodes);
            List<FdpOriginDemandForecastInterfaceLogVO> allLogs = originDemandForecastService.selectVOByParams(queryParams);
            log.info("预加载了 {} 个主机厂的 {} 条预测需求数据", oemCodes.size(), allLogs.size());
            return allLogs.stream().collect(Collectors.groupingBy(FdpOriginDemandForecastInterfaceLogVO::getOemCode));
        } catch (Exception e) {
            log.error("预加载所有主机厂预测需求数据失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 准备需求同步上下文
     */
    private SyncDemandContext prepareSyncDemandContext(LoadingDemandSubmissionReqDTO reqDTO,Scenario scenario) {
        SyncDemandContext context = new SyncDemandContext();

        // 提取OEM编码列表
        List<String> oemCodeList = reqDTO.getDetailDTOList().stream()
                .map(LoadingDemandSubmissionReqDetailDTO::getOemCode)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oemCodeList)) {
            context.setErrorMessage("请提报相关数据");
            return context;
        }

        context.setScenario(scenario);

        // 获取支持EDI的主机厂列表
        List<OemVO> ediOemList = getEdiSupportedOemList(oemCodeList);
        if (CollectionUtils.isEmpty(ediOemList)) {
            context.setErrorMessage("所选主机厂不支持EDI接口同步");
            return context;
        }

        // 获取原始需求版本信息
        OriginDemandVersionVO demandVersionVO = originDemandVersionService.selectByPrimaryKey(reqDTO.getOriginVersionId());
        if (Objects.isNull(demandVersionVO)) {
            context.setErrorMessage("无原始需求版本");
            return context;
        }

        // 计算时间范围
        DateRange dateRange = calculateDateRange(demandVersionVO.getPlanPeriod());

        // 获取现有提报数据
        List<LoadingDemandSubmissionVO> submissionTree = getExistingSubmissionData(reqDTO.getOriginVersionId());

        // 设置上下文
        context.setOriginVersionId(reqDTO.getOriginVersionId());
        context.setEdiOemList(ediOemList);
        context.setDateRange(dateRange);
        context.setSubmissionTree(submissionTree);

        return context;
    }
    /**
     * 获取支持EDI的主机厂列表
     */
    private List<OemVO> getEdiSupportedOemList(List<String> oemCodeList) {
        List<OemVO> oemVOS = oemService.selectProductEdiFlag(YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getCode());
        if (CollectionUtils.isEmpty(oemVOS)) {
            return Collections.emptyList();
        }

        return oemVOS.stream()
                .filter(oemVO -> oemCodeList.contains(oemVO.getOemCode()))
                .collect(Collectors.toList());
    }

    /**
     * 计算日期范围
     */
    private DateRange calculateDateRange(String planPeriod) {
        Date currentDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
        int period = 30;
        int monthPeriod = 12;

        Date startDate = currentDate;
        Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(currentDate, period);

        // 设置月份起始日期为当月1号0点
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDate);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date monthStartDate = cal.getTime();

        // 设置月份结束日期为12个月后的1号0点
        cal.add(Calendar.MONTH, monthPeriod);
        Date monthEndDate = cal.getTime();

        return new DateRange(startDate, endDate, monthStartDate, monthEndDate, period, monthPeriod);
    }

    /**
     * 获取现有提报数据
     */
    private List<LoadingDemandSubmissionVO> getExistingSubmissionData(String originVersionId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("versionId", originVersionId);
        List<LoadingDemandSubmissionVO> submissionVOS = this.selectByParams(params);
        return invocation(submissionVOS, null, null);
    }

    /**
     * 处理单个主机厂的需求同步
     * @return 错误信息，如果成功则返回null
     */
    private String processSingleOemDemandSync(OemVO oem, SyncDemandContext context,
                                              List<CalendarRuleVO> preloadedRules,
                                              List<FdpOriginDemandInterfaceLogVO> demandLogs,
                                              List<FdpOriginDemandForecastInterfaceLogVO> forecastLogs) {
        String oemCode = oem.getOemCode();

        try {
            // 1. 获取并转换本次同步的EDI数据
            DemandDataContainer demandData = fetchAndConvertEdiData(oemCode, oem, context.getScenario(), preloadedRules, demandLogs, forecastLogs);
            if (demandData.isEmpty()) {
                log.info("主机厂 {} 没有新的EDI数据，跳过本次同步", oemCode);
                return null;
            }

            // 2. 确定本次同步所涉及的所有产品
            Set<String> productsInSync = Stream.concat(demandData.getDayList().stream(), demandData.getMergedMonthList().stream())
                    .map(item -> (item.getPartNumber() != null ? item.getPartNumber() : "") + "|" + item.getProductCode())
                    .collect(Collectors.toSet());

            // 3. 从数据库中找到这些产品对应的、已存在的提报头ID
            List<LoadingDemandSubmissionVO> currentOemSubmissions = context.getSubmissionTree().stream()
                    .filter(submission -> oemCode.equals(submission.getOemCode()))
                    .collect(Collectors.toList());

            List<String> submissionIdsToDeleteDetailsFor = currentOemSubmissions.stream()
                    .filter(s -> {
                        String key = (s.getPartNumber() != null ? s.getPartNumber() : "") + "|" + s.getProductCode();
                        return productsInSync.contains(key);
                    })
                    .map(BaseVO::getId)
                    .collect(Collectors.toList());

            // 4. 只删除本次同步所涉及产品的旧明细，防止重复
            if (CollectionUtils.isNotEmpty(submissionIdsToDeleteDetailsFor)) {
                log.info("同步开始，为版本 {} 的主机厂 {} 精准删除 {} 个提报头下的所有明细数据",
                        context.getOriginVersionId(), oemCode, submissionIdsToDeleteDetailsFor.size());
                log.debug("待删除明细数据的提报头IDs: {}", submissionIdsToDeleteDetailsFor);

                // 直接删除这些提报头下的所有明细数据，确保不会有重复数据
                // deleteBySubmissionIds方法会删除指定提报头ID下的所有明细数据
                int deletedCount = loadingDemandSubmissionDetailService.deleteBySubmissionIds(submissionIdsToDeleteDetailsFor);
                log.info("成功删除 {} 条明细数据", deletedCount);
            }

            // 5. 处理数据（更新/新增头，准备待插入的新明细）
            DataProcessingContainer container = new DataProcessingContainer();
            processSubmissionData(currentOemSubmissions, demandData, demandLogs, context, container);

            // 6. 执行数据库写入操作
            executeDataOperations(container, context, oemCode, demandLogs);

            return null;
        } catch (Exception e) {
            log.error("处理主机厂{}需求同步失败: {}", oemCode, e.getMessage(), e);
            return String.format("主机厂%s同步失败: %s", oemCode, e.getMessage());
        }
    }
    /**
     * 获取EDI数据并转换
     */
    private DemandDataContainer fetchAndConvertEdiData(String oemCode, OemVO oem, Scenario scenario,
                                                       List<CalendarRuleVO> preloadedRules,
                                                       List<FdpOriginDemandInterfaceLogVO> originDemandVOS,
                                                       List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS) {

        // 转换日需求数据
        List<DemandDataItem> dayList = convertDayDemandData(originDemandVOS, oemCode, oem);

        // 转换月需求数据（已包含合并逻辑）
        List<DemandDataItem> monthList = convertMonthDemandData(originDemandVOS, originDemandForecastVOS, oemCode, oem, scenario, preloadedRules);

        return new DemandDataContainer(dayList, monthList);
    }

    /**
     * 转换日需求数据
     */
    private List<DemandDataItem> convertDayDemandData(List<FdpOriginDemandInterfaceLogVO> originDemandVOS,
                                                      String oemCode, OemVO oem) {
        List<DemandDataItem> dayList = new ArrayList<>();

        for (FdpOriginDemandInterfaceLogVO demandLog : originDemandVOS) {
            // 检查EDI标识
            if (!isEdiEnabled(demandLog.getProductEdiFlag(), oem.getEdiFlag())) {
                log.info("该物料没有EDI标识，需求同步跳过：{}", demandLog.getItemNum());
                continue;
            }

            DemandDataItem temp = new DemandDataItem();
            temp.setOemCode(oemCode);
            temp.setPartNumber(demandLog.getCustItemNum());
            temp.setProductCode(demandLog.getItemNum());
            temp.setSubmissionType(demandLog.getSubmissionType());
            temp.setDemandQuantity(demandLog.getShipQty());
            temp.setDemandTime(DateUtils.dateToString(demandLog.getOriginalShipTime(), DateUtils.COMMON_DATE_STR1));
            temp.setReleaseStatus(demandLog.getReleaseStatus());

            dayList.add(temp);
        }

        return dayList;
    }

    /**
     * 计算产品的欠交数量
     * 计算当前日期以前，状态不为CLOSED，ship_qty - shipped_qty > 0的记录汇总
     */
    private BigDecimal calculateOweQuantityForProduct(String productCode, List<FdpOriginDemandInterfaceLogVO> allOemDemandLogs) {
        try {
            Date currentDate = new Date();
            BigDecimal totalOweQuantity = BigDecimal.ZERO;

            for (FdpOriginDemandInterfaceLogVO record : allOemDemandLogs) {
                // 检查是否为当前日期以前的数据
                if (!productCode.equals(record.getItemNum()) || record.getOriginalShipTime() == null || !record.getOriginalShipTime().before(currentDate)) {
                    continue;
                }

                // 检查状态不为CLOSED
                if (StatusEnum.CLOSED.getCode().equals(record.getReleaseStatus())) {
                    continue;
                }

                // 计算欠交数量：ship_qty - shipped_qty > 0
                BigDecimal shipQty = record.getShipQty() != null ? record.getShipQty() : BigDecimal.ZERO;
                BigDecimal shippedQty = record.getShippedQty() != null ? record.getShippedQty() : BigDecimal.ZERO;
                BigDecimal oweQty = shipQty.subtract(shippedQty);

                if (oweQty.compareTo(BigDecimal.ZERO) > 0) {
                    totalOweQuantity = totalOweQuantity.add(oweQty);
                }
            }

            log.info("产品{}的欠交数量：{}", productCode, totalOweQuantity);
            return totalOweQuantity;

        } catch (Exception e) {
            log.error("从内存计算产品{}欠交数量失败：{}", productCode, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 转换月需求数据
     */
    private List<DemandDataItem> convertMonthDemandData(List<FdpOriginDemandInterfaceLogVO> originDemandVOS,
                                                        List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS,
                                                        String oemCode, OemVO oem, Scenario scenario,
                                                        List<CalendarRuleVO> preloadedRules) {
        if (CollectionUtils.isEmpty(originDemandVOS) && CollectionUtils.isEmpty(originDemandForecastVOS)) {
            return Collections.emptyList();
        }

        // 零件关系映射，只有在需要时才查询
        Map<String, String> partNumberCache = new HashMap<>();

        // 车型编码映射数据
        Map<String, String> vehicleModelCache = prepareVehicleModelCache(originDemandForecastVOS, scenario);

        // 装车日历数据
        Map<String, List<ResourceCalendarVO>> calendarCache = prepareCalendarCache(vehicleModelCache, oemCode, originDemandForecastVOS, preloadedRules);

        // 步骤1：先取日需求中，状态不为closed的，根据主机厂编码+产品编码，找到最大的original_ship_time
        Map<String, Date> maxShipTimeMap = findMaxShipTimeFromDayDemand(oemCode, originDemandVOS, oem);

        // 步骤1：处理历史部分数据（只处理日需求数据中<=max_ship_time的部分）
        List<DemandDataItem> step1Results = processDataBeforeMaxShipTime(oemCode, maxShipTimeMap, partNumberCache, oem, originDemandVOS);

        // 步骤2：处理未来部分数据（预测数据中>max_ship_time的部分）
        List<DemandDataItem> step2Results = processFutureData(originDemandForecastVOS, oemCode, maxShipTimeMap,
                                                               partNumberCache, vehicleModelCache, calendarCache, oem);

        // 步骤3：汇总1、2每个月的数量，作为月预测数据
        return mergeMonthlyResults(step1Results, step2Results);
    }

    /**
     * 步骤1：从日需求中找到每个主机厂编码+产品编码的最大original_ship_time
     */
    private Map<String, Date> findMaxShipTimeFromDayDemand(String oemCode, List<FdpOriginDemandInterfaceLogVO> originDemandVOS, OemVO oem) {
        Map<String, Date> maxShipTimeMap = new HashMap<>();
        
        try {
            if (CollectionUtils.isEmpty(originDemandVOS)) {
                return maxShipTimeMap;
            }

            // 根据主机厂配置决定使用哪个时间字段
            String ediDateType = oem.getEdiDateType();
            boolean useDeliveryTime = !"SH".equals(ediDateType); // 如果是DL或为空，则默认为true
            final Function<FdpOriginDemandInterfaceLogVO, Date> dateExtractor = useDeliveryTime
                    ? FdpOriginDemandInterfaceLogVO::getOriginalDeliveryTime
                    : FdpOriginDemandInterfaceLogVO::getOriginalShipTime;

            // 按主机厂编码+产品编码分组，找到每组的最大original_ship_time
            Map<String, List<FdpOriginDemandInterfaceLogVO>> groupedData = originDemandVOS.stream()
                    .filter(item -> !StatusEnum.CLOSED.getCode().equals(item.getReleaseStatus()))
                    .filter(item -> dateExtractor.apply(item) != null)
                    .collect(Collectors.groupingBy(item -> oemCode + "_" + item.getItemNum()));

            log.info("分组后数据：{}组", groupedData.size());
            
            for (Map.Entry<String, List<FdpOriginDemandInterfaceLogVO>> entry : groupedData.entrySet()) {
                String key = entry.getKey();
                Date maxDate = entry.getValue().stream()
                        .map(dateExtractor)
                        .max(Date::compareTo)
                        .orElse(null);
                
                if (maxDate != null) {
                    maxShipTimeMap.put(key, maxDate);
                    
                    String dateFieldName = useDeliveryTime ? "original_delivery_time" : "original_ship_time";
                    // 特殊调试日志
                    if (key.contains("00038TDR01005-A01") && key.contains("120229_001")) {
                        log.error("【特殊调试】findMaxShipTime - 找到目标产品{}的最大{}: {}", key, dateFieldName, DateUtils.dateToString(maxDate, DateUtils.COMMON_DATE_STR1));
                    }
                    
                    log.info("找到{}的最大{}: {}", key, dateFieldName, DateUtils.dateToString(maxDate, DateUtils.COMMON_DATE_STR1));
                }
            }

        } catch (Exception e) {
            log.error("查询日需求最大时间失败：{}", e.getMessage(), e);
        }

        return maxShipTimeMap;
    }

    /**
     * 步骤1：处理小于最大original_ship_time的数据，按月分组汇总
     */
    private List<DemandDataItem> processDataBeforeMaxShipTime(String oemCode, Map<String, Date> maxShipTimeMap,
                                                              Map<String, String> partNumberCache, OemVO oem,
                                                              List<FdpOriginDemandInterfaceLogVO> originDemandVOS) {
        List<DemandDataItem> result = new ArrayList<>();
        
        log.info("步骤1开始处理，maxShipTimeMap大小：{}", maxShipTimeMap.size());
        
        try {
            // 过滤出所有在max_ship_time之前（含当天）的日需求数据
            List<FdpOriginDemandInterfaceLogVO> historicalData = originDemandVOS.stream()
                    .filter(item -> !StatusEnum.CLOSED.getCode().equals(item.getReleaseStatus()))
                    .filter(item -> {
                        String key = oemCode + "_" + item.getItemNum();
                        Date maxShipTime = maxShipTimeMap.get(key);
                        // 只有当一个产品存在max_ship_time时，才参与历史计算
                        return maxShipTime != null && item.getOriginalShipTime() != null && !item.getOriginalShipTime().after(maxShipTime);
                    })
                    .filter(item -> isEdiEnabled(item.getProductEdiFlag(), oem.getEdiFlag()))
                    .collect(Collectors.toList());

            // 按产品和月份分组汇总
            Map<String, List<FdpOriginDemandInterfaceLogVO>> groupedByProductAndMonth = historicalData.stream()
                    .collect(Collectors.groupingBy(item ->
                            item.getItemNum() + "#" + DateUtils.dateToString(item.getOriginalShipTime(), DateUtils.YEAR_MONTH)));

            for (Map.Entry<String, List<FdpOriginDemandInterfaceLogVO>> entry : groupedByProductAndMonth.entrySet()) {
                List<FdpOriginDemandInterfaceLogVO> monthItems = entry.getValue();
                if (CollectionUtils.isEmpty(monthItems)) continue;

                FdpOriginDemandInterfaceLogVO firstItem = monthItems.get(0);
                String productCode = firstItem.getItemNum();
                String month = DateUtils.dateToString(firstItem.getOriginalShipTime(), DateUtils.YEAR_MONTH);

                BigDecimal totalQuantity = monthItems.stream()
                        .map(FdpOriginDemandInterfaceLogVO::getShipQty)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    DemandDataItem item = new DemandDataItem();
                    item.setOemCode(oemCode);
                    item.setProductCode(productCode);
                    item.setPartNumber(getPartNumberFromDayDemand(firstItem, partNumberCache));
                    item.setSubmissionType(GranularityEnum.MONTH.getCode());
                    item.setDemandTime(month);
                    item.setDemandQuantity(totalQuantity);
                    result.add(item);

                    // 特殊调试日志
                    if ("00038TDR01005-A01".equals(productCode) && "120229_001".equals(oemCode)) {
                        log.error("【特殊调试】目标产品 - 步骤1汇总：产品{} {}月 数量{}", productCode, month, totalQuantity);
                    }

                    log.info("步骤1汇总：产品{} {}月 数量{}", productCode, month, totalQuantity);
                }
            }

        } catch (Exception e) {
            log.error("处理小于最大时间的数据失败：{}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 处理未来部分数据（预测数据中>max_ship_time的部分）
     */
    private List<DemandDataItem> processFutureData(List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS,
                                                   String oemCode, Map<String, Date> maxShipTimeMap, // Renamed for clarity
                                                   Map<String, String> partNumberCache,
                                                   Map<String, String> vehicleModelCache,
                                                   Map<String, List<ResourceCalendarVO>> calendarCache,
                                                   OemVO oem) {
        List<DemandDataItem> result = new ArrayList<>();

        for (FdpOriginDemandForecastInterfaceLogVO forecastLog : originDemandForecastVOS) {
            // 检查EDI标识
            if (!isEdiEnabled(forecastLog.getProductEdiFlag(), oem.getEdiFlag())) {
                log.info("该物料没有EDI标识，预测需求同步跳过：{}", forecastLog.getItemNum());
                continue;
            }

            // 获取该产品的最大original_ship_time
            String key = oemCode + "_" + forecastLog.getItemNum();
            Date maxShipTime = maxShipTimeMap.get(key);

            // 只处理类型为'P'的预测
            if (!"P".equals(forecastLog.getScheduleType())) {
                continue;
            }

            // 逻辑: 单点预测
            if (forecastLog.getOriginalSchStDate() == null || forecastLog.getOriginalSchEndDate() == null) {
                if (forecastLog.getOriginalShipTime() != null && (maxShipTime == null || forecastLog.getOriginalShipTime().after(maxShipTime))) {
                    // 纯未来的单点预测
                    result.addAll(processForecastDataWithTimeFilter(forecastLog, oemCode, maxShipTime, partNumberCache, vehicleModelCache, calendarCache));
                }
                continue;
            }

            //  时间段预测
            Date forecastStartDate = forecastLog.getOriginalSchStDate();
            Date forecastEndDate = forecastLog.getOriginalSchEndDate();

            // 纯未来预测 (开始日期 > max_ship_time)
            if (maxShipTime == null || forecastStartDate.after(maxShipTime)) {
                result.addAll(processForecastDataWithTimeFilter(forecastLog, oemCode, maxShipTime, partNumberCache, vehicleModelCache, calendarCache));
            }
            // 跨期预测 (开始日期 <= max_ship_time, 但结束日期 > max_ship_time)
            else if (forecastEndDate.after(maxShipTime)) {
                result.addAll(processForecastDataWithTimeFilter(forecastLog, oemCode, maxShipTime, partNumberCache, vehicleModelCache, calendarCache));
            }
        }

        return result;
    }

    /**
     * 处理预测数据的时间和数量分配逻辑（带时间过滤）
     */
    private List<DemandDataItem> processForecastDataWithTimeFilter(FdpOriginDemandForecastInterfaceLogVO forecastLog,
                                                                   String oemCode, Date maxShipTime,
                                                                   Map<String, String> partNumberCache,
                                                                   Map<String, String> vehicleModelCache,
                                                                   Map<String, List<ResourceCalendarVO>> calendarCache) {
        List<DemandDataItem> result = new ArrayList<>();

        // 情况1：original_sch_st_date为空或original_sch_end_date为空
        if (forecastLog.getOriginalSchStDate() == null || forecastLog.getOriginalSchEndDate() == null) {
            if (forecastLog.getOriginalShipTime() != null) {
                // 已经确保时间>maxShipTime，直接处理
                DemandDataItem item = createBaseDemandDataItem(forecastLog, oemCode, partNumberCache);
                if (item != null) {
                    // 使用original_ship_time的年月作为装车需求提报的月预测时间
                    item.setDemandTime(DateUtils.dateToString(forecastLog.getOriginalShipTime(), DateUtils.YEAR_MONTH));
                    item.setDemandQuantity(forecastLog.getQty());
                    result.add(item);
                    log.info("未来预测情况1：产品{} {}月 数量{}", forecastLog.getItemNum(), item.getDemandTime(), item.getDemandQuantity());
                }
            }
        } else {
            // 情况2：开始时间和结束时间都不为空
            Date startDate = forecastLog.getOriginalSchStDate();
            Date endDate = forecastLog.getOriginalSchEndDate();

            // 已经确保时间>maxShipTime，直接处理
            // 检查是否跨月
            String startMonth = DateUtils.dateToString(startDate, DateUtils.YEAR_MONTH);
            String endMonth = DateUtils.dateToString(endDate, DateUtils.YEAR_MONTH);

            if (startMonth.equals(endMonth)) {
                // 同一个月，直接聚合
                DemandDataItem item = createBaseDemandDataItem(forecastLog, oemCode, partNumberCache);
                if (item != null) {
                    item.setDemandTime(startMonth);
                    item.setDemandQuantity(forecastLog.getQty());
                    result.add(item);
                    log.info("未来预测情况2同月：产品{} {}月 数量{}", forecastLog.getItemNum(), item.getDemandTime(), item.getDemandQuantity());
                }
            } else {
                // 跨月处理，需要通过装车日历分配数量
                List<DemandDataItem> distributedItems = distributeQuantityByCalendar(forecastLog, oemCode, startDate, endDate,
                        partNumberCache, vehicleModelCache, calendarCache, maxShipTime);
                result.addAll(distributedItems);
                log.info("未来预测情况2跨月：产品{} 分配到{}个月", forecastLog.getItemNum(), distributedItems.size());
            }
        }

        return result;
    }

    /**
     * 步骤3：汇总1、2每个月的数量，作为月预测数据
     */
    private List<DemandDataItem> mergeMonthlyResults(List<DemandDataItem> step1Results, List<DemandDataItem> step2Results) {
        log.info("开始合并月度结果，历史数据{}条，未来数据{}条", step1Results.size(), step2Results.size());

        // 特殊调试：检查目标产品的数据
        String targetProduct = "00038TDR01005-A01";
        step1Results.stream()
                .filter(item -> targetProduct.equals(item.getProductCode()))
                .forEach(item -> log.error("【特殊调试】历史数据合并 - 产品{} {}月 数量{}",
                        item.getProductCode(), item.getDemandTime(), item.getDemandQuantity()));

        step2Results.stream()
                .filter(item -> targetProduct.equals(item.getProductCode()))
                .forEach(item -> log.error("【特殊调试】未来数据合并 - 产品{} {}月 数量{}",
                        item.getProductCode(), item.getDemandTime(), item.getDemandQuantity()));

        List<DemandDataItem> allResults = new ArrayList<>();
        allResults.addAll(step1Results);
        allResults.addAll(step2Results);

        if (CollectionUtils.isEmpty(allResults)) {
            return Collections.emptyList();
        }

        // 按主机厂编码+产品编码+零件号+月份分组汇总
        Map<String, List<DemandDataItem>> groupedResults = allResults.stream()
                .collect(Collectors.groupingBy(item -> 
                    item.getOemCode() + "_" + item.getProductCode() + "_" + 
                    (item.getPartNumber() != null ? item.getPartNumber() : "") + "_" + item.getDemandTime()));

        List<DemandDataItem> finalResults = new ArrayList<>();

        for (Map.Entry<String, List<DemandDataItem>> entry : groupedResults.entrySet()) {
            List<DemandDataItem> items = entry.getValue();
            if (CollectionUtils.isNotEmpty(items)) {
                DemandDataItem firstItem = items.get(0);
                
                // 汇总数量
                BigDecimal totalQuantity = items.stream()
                        .map(DemandDataItem::getDemandQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    DemandDataItem mergedItem = new DemandDataItem();
                    mergedItem.setOemCode(firstItem.getOemCode());
                    mergedItem.setProductCode(firstItem.getProductCode());
                    mergedItem.setPartNumber(firstItem.getPartNumber());
                    mergedItem.setSubmissionType(GranularityEnum.MONTH.getCode());
                    mergedItem.setDemandTime(firstItem.getDemandTime());
                    mergedItem.setDemandQuantity(totalQuantity);
                    finalResults.add(mergedItem);

                    // 特殊调试日志
                    if (targetProduct.equals(firstItem.getProductCode())) {
                        log.error("【特殊调试】最终汇总 - 产品{} {}月 总数量{}", firstItem.getProductCode(), firstItem.getDemandTime(), totalQuantity);
                    }

                    log.info("最终汇总：产品{} {}月 总数量{}", firstItem.getProductCode(), firstItem.getDemandTime(), totalQuantity);
                }
            }
        }

        return finalResults;
    }

    /**
     * 从日需求数据中获取零件号
     */
    private String getPartNumberFromDayDemand(FdpOriginDemandInterfaceLogVO dayDemand, Map<String, String> partNumberCache) {
        if (ApiSourceEnum.MES.getCode().equals(dayDemand.getImportType())) {
            return getPartNumberWithCache(dayDemand.getItemNum(), partNumberCache);
        } else {
            return dayDemand.getCustItemNum();
        }
    }

    /**
     * 准备车型编码映射数据
     */
    private Map<String, String> prepareVehicleModelCache(List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS, Scenario scenario) {
        Map<String, String> vehicleModelCache = new HashMap<>();

        try {
            // 收集所有需要查询的产品编码
            Set<String> productCodes = originDemandForecastVOS.stream()
                    .filter(log -> "P".equals(log.getScheduleType()))
                    .map(FdpOriginDemandForecastInterfaceLogVO::getItemNum)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(productCodes)) {
                return vehicleModelCache;
            }

            // 批量查询车型编码映射关系
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("productCodes", new ArrayList<>(productCodes));
            queryParams.put("enabled", YesOrNoEnum.YES.getCode());

            List<NewProductStockPointVO> productStockPoints = mdsFeign.selectProductStockPointByParams(scenario.getDataBaseName(), queryParams);
            if (CollectionUtils.isNotEmpty(productStockPoints)) {
                vehicleModelCache = productStockPoints.stream()
                        .filter(product -> StringUtils.isNotBlank(product.getProductCode()) && StringUtils.isNotBlank(product.getVehicleModelCode()))
                        .collect(Collectors.toMap(
                                NewProductStockPointVO::getProductCode,
                                NewProductStockPointVO::getVehicleModelCode,
                                (existingValue, newValue) -> newValue));
            }

            log.info("预先准备了{}个产品编码的车型映射数据", productCodes.size());

        } catch (Exception e) {
            log.error("预先准备车型编码映射数据失败：{}", e.getMessage(), e);
        }

        return vehicleModelCache;
    }

    /**
     * 准备装车日历数据
     */
    private Map<String, List<ResourceCalendarVO>> prepareCalendarCache(Map<String, String> vehicleModelCache,
                                                                       String oemCode,
                                                                       List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS,
                                                                       List<CalendarRuleVO> preloadedOemRules) {
        Map<String, List<ResourceCalendarVO>> calendarCache = new HashMap<>();

        try {
            // 查询日历的车型编码
            Set<String> vehicleModelCodes = new HashSet<>(vehicleModelCache.values());
            vehicleModelCodes.removeIf(StringUtils::isBlank);

            if (CollectionUtils.isEmpty(vehicleModelCodes)) {
                return calendarCache;
            }

            // 确定日历查询的时间范围
            Date minDate = originDemandForecastVOS.stream()
                    .filter(log -> log.getOriginalSchStDate() != null)
                    .map(FdpOriginDemandForecastInterfaceLogVO::getOriginalSchStDate)
                    .min(Date::compareTo)
                    .orElse(new Date());

            Date maxDate = originDemandForecastVOS.stream()
                    .filter(log -> log.getOriginalSchEndDate() != null)
                    .map(FdpOriginDemandForecastInterfaceLogVO::getOriginalSchEndDate)
                    .max(Date::compareTo)
                    .orElse(new Date());

            // 1. 按车型编码分组预加载的规则
            Map<String, List<CalendarRuleVO>> rulesByVehicleModel = preloadedOemRules.stream()
                    .filter(rule -> StringUtils.isNotBlank(rule.getPhysicalResourceIds()))
                    .collect(Collectors.groupingBy(CalendarRuleVO::getPhysicalResourceIds));

            // 2. 遍历每个需要的车型，获取装车日历
            for (String vehicleModelCode : vehicleModelCodes) {
                String cacheKey = oemCode + "_" + vehicleModelCode;
                List<CalendarRuleVO> rulesForModel = rulesByVehicleModel.getOrDefault(vehicleModelCode, Collections.emptyList());

                // 优先从dfp_cal_resource_calendar表查询装车日历
                List<ResourceCalendarVO> calendarList = getCalendarFromDatabase(vehicleModelCode, minDate, maxDate, rulesForModel, oemCode);

                calendarCache.put(cacheKey, calendarList);
            }

            log.info("从预加载数据中为 {} 个车型准备了装车日历", vehicleModelCodes.size());

        } catch (Exception e) {
            log.error("预先准备装车日历数据失败：{}", e.getMessage(), e);
        }

        return calendarCache;
    }

    /**
     * 从数据库获取装车日历，如果数据日期区间在日历规则范围内，则去dfp_cal_resource_calendar表取对应具体的装车日历
     * 如果日期不在这个区间，或者没找到对应装车日历规则的话，那么就按照默认的规则来生成装车日历
     */
    private List<ResourceCalendarVO> getCalendarFromDatabase(String vehicleModelCode, Date startDate, Date endDate,
                                                           List<CalendarRuleVO> rulesForModel, String oemCode) {
        try {
            // 1. 检查数据日期区间是否在日历规则范围内
            boolean dateInRuleRange = false;
            CalendarRuleVO applicableRule = null;

            if (CollectionUtils.isNotEmpty(rulesForModel)) {
                for (CalendarRuleVO rule : rulesForModel) {
                    if (rule.getStartDate() != null && rule.getEndDate() != null) {
                        // 检查数据日期区间是否与规则日期区间有交集
                        if (!(endDate.before(rule.getStartDate()) || startDate.after(rule.getEndDate()))) {
                            dateInRuleRange = true;
                            applicableRule = rule;
                            break;
                        }
                    }
                }
            }

            // 2. 如果日期在规则范围内，尝试从dfp_cal_resource_calendar表查询
            if (dateInRuleRange && applicableRule != null) {
                log.info("数据日期区间在日历规则范围内，从dfp_cal_resource_calendar表查询装车日历，主机厂：{}，车型：{}", oemCode, vehicleModelCode);

                // 从dfp_cal_resource_calendar表查询装车日历
                List<ResourceCalendarDO> resourceCalendarDOS = dfpResourceCalendarDomainService.getResourceCalendar(
                        Collections.singletonList(vehicleModelCode), // 标准资源ID
                        Collections.singletonList(vehicleModelCode), // 物理资源ID
                        startDate,
                        endDate);

                if (CollectionUtils.isNotEmpty(resourceCalendarDOS)) {
                    log.info("从dfp_cal_resource_calendar表查询到{}条装车日历记录，主机厂：{}，车型：{}",
                            resourceCalendarDOS.size(), oemCode, vehicleModelCode);
                    return ResourceCalendarConvertor.INSTANCE.dos2Vos(resourceCalendarDOS);
                } else {
                    log.info("dfp_cal_resource_calendar表中未找到对应装车日历，使用规则生成，主机厂：{}，车型：{}", oemCode, vehicleModelCode);
                    return generateCalendarFromRules(rulesForModel, oemCode, vehicleModelCode, startDate, endDate);
                }
            } else {
                // 3. 如果日期不在规则范围内或没有找到对应规则，使用默认规则生成装车日历
                log.info("数据日期区间不在日历规则范围内或未找到对应规则，使用默认规则生成装车日历，主机厂：{}，车型：{}", oemCode, vehicleModelCode);
                return generateDefaultWorkDays(startDate, endDate);
            }

        } catch (Exception e) {
            log.error("从数据库获取装车日历失败，主机厂：{}，车型：{}，错误：{}", oemCode, vehicleModelCode, e.getMessage(), e);
            // 异常情况下使用默认规则
            return generateDefaultWorkDays(startDate, endDate);
        }
    }

    /**
     * 创建基础需求数据项
     */
    private DemandDataItem createBaseDemandDataItem(FdpOriginDemandForecastInterfaceLogVO forecastLog,
                                                    String oemCode, Map<String, String> partNumberCache) {
        DemandDataItem item = new DemandDataItem();
        item.setOemCode(oemCode);
        item.setSubmissionType(forecastLog.getSubmissionType());
        item.setProductCode(forecastLog.getItemNum());

        // 设置零件号
        if (ApiSourceEnum.MES.getCode().equals(forecastLog.getImportType())) {
            String partNumber = getPartNumberWithCache(forecastLog.getItemNum(), partNumberCache);
            if (partNumber == null) {
                log.warn("未找到产品编码{}对应的客户零件号，跳过该记录", forecastLog.getItemNum());
                return null;
            }
            item.setPartNumber(partNumber);
        } else {
            item.setPartNumber(forecastLog.getCustomerItemNum());
        }

        return item;
    }

    /**
     * 通过装车日历分配跨月数量
     */
    private List<DemandDataItem> distributeQuantityByCalendar(FdpOriginDemandForecastInterfaceLogVO forecastLog,
                                                              String oemCode, Date startDate, Date endDate,
                                                              Map<String, String> partNumberCache,
                                                              Map<String, String> vehicleModelCache,
                                                              Map<String, List<ResourceCalendarVO>> calendarCache,
                                                              Date maxShipTime) {
        List<DemandDataItem> result = new ArrayList<>();

        try {
            // 从缓存中获取该编码对应的车型
            String vehicleModelCode = vehicleModelCache.get(forecastLog.getItemNum());
            if (StringUtils.isBlank(vehicleModelCode)) {
                log.warn("未找到产品编码{}对应的车型编码，使用原始时间分配", forecastLog.getItemNum());
                return createFallbackDistribution(forecastLog, oemCode, startDate, endDate, partNumberCache);
            }

            // 从缓存中获取装车日历
            String cacheKey = oemCode + "_" + vehicleModelCode;
            List<ResourceCalendarVO> calendarList = calendarCache.get(cacheKey);
            if (CollectionUtils.isEmpty(calendarList)) {
                log.warn("未找到主机厂{}车型{}的装车日历，使用原始时间分配", oemCode, vehicleModelCode);
                return createFallbackDistribution(forecastLog, oemCode, startDate, endDate, partNumberCache);
            }

            // 1. 过滤出【原始】时间范围内的日历数据，用于计算每日均摊量
            List<ResourceCalendarVO> totalPeriodCalendarList = calendarList.stream()
                    .filter(calendar -> calendar.getWorkDay() != null)
                    .filter(calendar -> !calendar.getWorkDay().before(startDate) && !calendar.getWorkDay().after(endDate))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(totalPeriodCalendarList)) {
                log.warn("指定时间范围内没有工作日，使用原始时间分配");
                return createFallbackDistribution(forecastLog, oemCode, startDate, endDate, partNumberCache);
            }

            // 2. 使用新算法计算整个期间的每日分配额
            Map<Date, BigDecimal> dailyAllotmentMap = calculateDailyAllotments(forecastLog.getQty(), totalPeriodCalendarList);

            // 3. 确定有效计算的开始日期（处理跨期场景）
            final Date effectiveStartDate = (maxShipTime != null && startDate.before(maxShipTime))
                    ? org.apache.commons.lang3.time.DateUtils.addDays(maxShipTime, 1)
                    : startDate;

            // 4. 过滤出【有效】时间范围（maxShipTime之后）的日历数据
            List<ResourceCalendarVO> effectiveCalendarList = totalPeriodCalendarList.stream()
                    .filter(calendar -> !calendar.getWorkDay().before(effectiveStartDate))
                    .collect(Collectors.toList());

            // 5. 使用每日分配额按月聚合
            result = aggregateByMonth(forecastLog, oemCode, effectiveCalendarList, partNumberCache, dailyAllotmentMap);

        } catch (Exception e) {
            log.error("分配跨月数量时发生错误，使用原始时间分配：{}", e.getMessage(), e);
            result = createFallbackDistribution(forecastLog, oemCode, startDate, endDate, partNumberCache);
        }

        return result;
    }

    /**
     * 使用累计向上取整算法，为工作日列表计算每日的整数分配额。
     * @param totalQty 总数量
     * @param workDays 工作日列表
     * @return 键是工作日日期，值是当天分配的整数数量
     */
    private Map<Date, BigDecimal> calculateDailyAllotments(BigDecimal totalQty, List<ResourceCalendarVO> workDays) {
        Map<Date, BigDecimal> allotmentMap = new HashMap<>();
        int totalWorkDays = workDays.size();
        if (totalWorkDays == 0) {
            return allotmentMap;
        }

        // 为避免中间步骤的舍入误差，使用高精度计算平均值
        BigDecimal avgQuantity = totalQty.divide(BigDecimal.valueOf(totalWorkDays), 10, RoundingMode.HALF_UP);
        BigDecimal previousCeiling = BigDecimal.ZERO;

        // 对工作日进行排序，以确保累积计算的正确性
        workDays.sort(Comparator.comparing(ResourceCalendarVO::getWorkDay));

        for (int i = 0; i < totalWorkDays; i++) {
            Date currentWorkDay = workDays.get(i).getWorkDay();
            // 计算到当日的累计平均值
            BigDecimal cumulativeAvg = avgQuantity.multiply(BigDecimal.valueOf(i + 1));
            // 向上取整得到累计整数值
            BigDecimal currentCeiling = cumulativeAvg.setScale(0, RoundingMode.CEILING);

            // 当天的分配额是当天与前一天累计整数值的差
            BigDecimal dailyAllotment = currentCeiling.subtract(previousCeiling);

            allotmentMap.put(currentWorkDay, dailyAllotment);
            previousCeiling = currentCeiling;
        }

        return allotmentMap;
    }

    private List<DemandDataItem> aggregateByMonth(FdpOriginDemandForecastInterfaceLogVO forecastLog,
                                                  String oemCode, List<ResourceCalendarVO> calendarList,
                                                  Map<String, String> partNumberCache,
                                                  Map<Date, BigDecimal> dailyAllotmentMap) {
        if (CollectionUtils.isEmpty(calendarList)) {
            return Collections.emptyList();
        }

        // 按月分组日历数据
        Map<String, List<ResourceCalendarVO>> monthCalendarMap = calendarList.stream()
                .collect(Collectors.groupingBy(calendar ->
                        DateUtils.dateToString(calendar.getWorkDay(), DateUtils.YEAR_MONTH)));

        List<DemandDataItem> result = new ArrayList<>();
        // 按月聚合数量
        for (Map.Entry<String, List<ResourceCalendarVO>> entry : monthCalendarMap.entrySet()) {
            String month = entry.getKey();
            List<ResourceCalendarVO> monthWorkDays = entry.getValue();

            // 累加当月所有工作日的分配额
            BigDecimal monthQuantity = monthWorkDays.stream()
                    .map(workDay -> dailyAllotmentMap.getOrDefault(workDay.getWorkDay(), BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 数量为0也生成记录
            DemandDataItem item = createBaseDemandDataItem(forecastLog, oemCode, partNumberCache);
            if (item != null) {
                item.setDemandTime(month);
                item.setDemandQuantity(monthQuantity);
                result.add(item);
            }
        }
        return result;
    }

    private List<DemandDataItem> createFallbackDistribution(FdpOriginDemandForecastInterfaceLogVO forecastLog,
                                                            String oemCode, Date startDate, Date endDate,
                                                            Map<String, String> partNumberCache) {
        List<DemandDataItem> result = new ArrayList<>();

        // 简单按月平均分配
        String startMonth = DateUtils.dateToString(startDate, DateUtils.YEAR_MONTH);
        String endMonth = DateUtils.dateToString(endDate, DateUtils.YEAR_MONTH);

        if (startMonth.equals(endMonth)) {
            // 同一个月
            DemandDataItem item = createBaseDemandDataItem(forecastLog, oemCode, partNumberCache);
            if (item != null) {
                item.setDemandTime(startMonth);
                item.setDemandQuantity(forecastLog.getQty());
                result.add(item);
            }
            return result;
        }

        // 跨月，按天数比例分配
        long totalDays = cn.hutool.core.date.DateUtil.between(startDate, endDate, cn.hutool.core.date.DateUnit.DAY) + 1;
        if (totalDays <= 0) {
            return result;
        }

        // 计算每个月占用的天数
        Map<String, Long> daysInMonth = new LinkedHashMap<>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        while (!cal.getTime().after(endDate)) {
            String month = DateUtils.dateToString(cal.getTime(), DateUtils.YEAR_MONTH);
            daysInMonth.put(month, daysInMonth.getOrDefault(month, 0L) + 1);
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }

        BigDecimal totalQty = forecastLog.getQty();
        BigDecimal remainingQty = totalQty;
        List<String> sortedMonths = new ArrayList<>(daysInMonth.keySet());

        for (int i = 0; i < sortedMonths.size(); i++) {
            String month = sortedMonths.get(i);
            long days = daysInMonth.get(month);

            DemandDataItem item = createBaseDemandDataItem(forecastLog, oemCode, partNumberCache);
            if (item != null) {
                item.setDemandTime(month);
                // 为保证总量不变，最后一个月分配剩余数量
                BigDecimal monthQty = (i == sortedMonths.size() - 1) ? remainingQty :
                        totalQty.multiply(BigDecimal.valueOf(days))
                                .divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);
                item.setDemandQuantity(monthQty);
                result.add(item);
                remainingQty = remainingQty.subtract(monthQty);
            }
        }
        return result;
    }


    /**
     * 根据给定的日历规则列表生成工作日
     */
    private List<ResourceCalendarVO> generateCalendarFromRules(List<CalendarRuleVO> calendarRules,
                                                               String oemCode, String vehicleModelCode,
                                                                       Date startDate, Date endDate) {
        try {
            // 如果没有找到任何有效规则，则使用默认规则（周一到周五上班）
            if (CollectionUtils.isEmpty(calendarRules)) {
                log.info("未找到主机厂{}车型{}的装车日历规则，使用默认规则（周末休息）", oemCode, vehicleModelCode);
                return generateDefaultWorkDays(startDate, endDate);
            }

            List<ResourceCalendarVO> workDays = new ArrayList<>();
            // 遍历查询范围内的每一天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            while (!calendar.getTime().after(endDate)) {
                Date currentDate = calendar.getTime();

                // 为当天寻找一个适用的规则
                Optional<CalendarRuleVO> applicableRuleOpt = calendarRules.stream()
                        .filter(rule -> rule.getStartDate() != null && rule.getEndDate() != null)
                        .filter(rule -> !currentDate.before(rule.getStartDate()) && !currentDate.after(rule.getEndDate()))
                        .findFirst();

                boolean isWorkDay;
                if (applicableRuleOpt.isPresent()) {
                    // 如果找到规则，根据规则判断是否为工作日
                    isWorkDay = isWorkDayByRule(currentDate, applicableRuleOpt.get());
                } else {
                    // 如果没有找到适用规则，使用默认逻辑（周末休息）
                    int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                    isWorkDay = (dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY);
                }

                if (isWorkDay) {
                    ResourceCalendarVO workDay = new ResourceCalendarVO();
                    workDay.setWorkDay(currentDate);
                    applicableRuleOpt.ifPresent(workDay::setCalendarRule);
                    workDays.add(workDay);
                }

                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            return workDays;

        } catch (Exception e) {
            log.error("根据规则生成主机厂{}车型{}的装车日历失败：{}", oemCode, vehicleModelCode, e.getMessage(), e);
            // 异常情况下使用默认规则
            return generateDefaultWorkDays(startDate, endDate);
        }
    }

    /**
     * 根据日历规则判断指定日期是否为工作日
     */
    private boolean isWorkDayByRule(Date date, CalendarRuleVO calendarRule) {
        String repeatFrequency = calendarRule.getRepeatFrequency();
        String frequencyPattern = calendarRule.getFrequencyPattern();

        if (StringUtils.isBlank(frequencyPattern)) {
            return false;
        }

        if (RepeatFrequencyEnum.WEEKLY.getCode().equals(repeatFrequency)) {
            // 按周重复：判断是周几
            String weekDay = DateUtils.getWeekDayOfDate(date);
            return frequencyPattern.contains(weekDay);
        } else if (RepeatFrequencyEnum.MONTHLY.getCode().equals(repeatFrequency)) {
            // 按月重复：判断是几号
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            String dayOfMonth = String.valueOf(calendar.get(Calendar.DAY_OF_MONTH));
            return frequencyPattern.contains(dayOfMonth);
        }

        return false;
    }

    /**
     * 默认工作日历（周末休息）
     */
    private List<ResourceCalendarVO> generateDefaultWorkDays(Date startDate, Date endDate) {
        List<ResourceCalendarVO> workDays = new ArrayList<>();

        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            while (!calendar.getTime().after(endDate)) {
                Date currentDate = calendar.getTime();

                // 默认规则：周一到周五为工作日
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                if (dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY) {
                    ResourceCalendarVO workDay = new ResourceCalendarVO();
                    workDay.setWorkDay(currentDate);
                    workDays.add(workDay);
                }

                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

        } catch (Exception e) {
            log.error("生成默认工作日失败：{}", e.getMessage(), e);
        }

        return workDays;
    }

    /**
     * 检查EDI是否启用
     */
    private boolean isEdiEnabled(String productEdiFlag, String oemEdiFlag) {
        if (Objects.equals(YesOrNoEnum.NO.getCode(), productEdiFlag)) {
            return false;
        }
        if (Objects.isNull(productEdiFlag)) {
            return Objects.equals(oemEdiFlag, YesOrNoEnum.YES.getCode());
        }
        return true;
    }

    /**
     * 零件号查询
     */
    private String getPartNumberWithCache(String productCode, Map<String, String> cache) {
        return cache.computeIfAbsent(productCode, this::queryPartNumberByProductCode);
    }

    /**
     * 查询产品编码对应的零件号
     */
    private String queryPartNumberByProductCode(String productCode) {
        Map<String, Object> params = Collections.singletonMap("enabled", YesOrNoEnum.YES.getCode());
        List<PartRelationMapVO> relationMapVOS = partRelationMapService.selectByParams(params);

        return relationMapVOS.stream()
                .filter(relation -> productCode.equals(relation.getProductCode()))
                .map(PartRelationMapVO::getPartNumber)
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查并更新欠交数量
     */
    private void checkAndUpdateOweQuantity(LoadingDemandSubmissionVO submission,
                                           List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                           DataProcessingContainer container) {
        String productCode = submission.getProductCode();

        // 重新计算欠交数量
        BigDecimal newOweQuantity = calculateOweQuantityForProduct(productCode, oemDemandLogs);

        // 比较当前欠交数量和新计算的欠交数量
        BigDecimal currentOweQuantity = submission.getOweQuantity() != null ? submission.getOweQuantity() : BigDecimal.ZERO;

        // 如果欠交数量不一致，则需要更新
        if (newOweQuantity.compareTo(currentOweQuantity) != 0) {
            log.info("需求提报头{}的欠交数量发生变化，从{}更新为{}", submission.getId(), currentOweQuantity, newOweQuantity);

            LoadingDemandSubmissionDTO updateDTO = new LoadingDemandSubmissionDTO();
            updateDTO.setId(submission.getId());
            updateDTO.setOweQuantity(newOweQuantity);
            updateDTO.setVersionValue(submission.getVersionValue());

            // 添加到更新列表
            container.getUpdateSubmissionList().add(updateDTO);
        }
    }

    /**
     * 合并处理现有和新增提报数据
     */
    private void processSubmissionData(List<LoadingDemandSubmissionVO> currentOemSubmissions,
                                       DemandDataContainer demandData, List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                       SyncDemandContext context,
                                       DataProcessingContainer container) {

        // 1. 现有提报数据
        Map<String, LoadingDemandSubmissionVO> existingSubmissionMap = currentOemSubmissions.stream()
                .collect(Collectors.toMap(
                        submission -> (submission.getPartNumber() != null ? submission.getPartNumber() : "") + "|" + submission.getProductCode(),
                        Function.identity(),
                        (existing, replacement) -> existing // In case of duplicates, keep the first one
                ));

        // 2. 将所有传入的EDI数据（日和月）按产品进行分组
        Map<String, List<DemandDataItem>> incomingDataMap = Stream.concat(demandData.getDayList().stream(), demandData.getMergedMonthList().stream())
                .collect(Collectors.groupingBy(item -> (item.getPartNumber() != null ? item.getPartNumber() : "") + "|" + item.getProductCode()));

        // 3. 遍历每一组传入的数据（每一组代表一个产品）
        for (Map.Entry<String, List<DemandDataItem>> entry : incomingDataMap.entrySet()) {
            String key = entry.getKey();
            List<DemandDataItem> itemList = entry.getValue();

            if (existingSubmissionMap.containsKey(key)) {
                // --- 更新 ---
                log.info("找到现有提报数据，执行更新逻辑 for key: {}", key);
                LoadingDemandSubmissionVO existingSubmission = existingSubmissionMap.get(key);

                // 检查并更新欠交数量
                checkAndUpdateOweQuantity(existingSubmission, oemDemandLogs, container);

                // 创建新的明细数据
                for (DemandDataItem temp : itemList) {
                    try {
                        createDetailFromTemp(temp, existingSubmission.getId(), existingSubmission.getOweQuantity(), oemDemandLogs, context, container);
                    } catch (Exception e) {
                        log.error("更新提报详情时出错, demandTime={}, error={}", temp.getDemandTime(), e.getMessage(), e);
                    }
                }

            } else {
                // --- 新增 ---
                log.info("未找到现有提报数据，执行新增逻辑 for key: {}", key);
                createNewSubmissionWithDetails(itemList, oemDemandLogs, context, container);
            }
        }
    }
    /**
     * 创建新提报及其详情
     */
    private void createNewSubmissionWithDetails(List<DemandDataItem> tempList, List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                                SyncDemandContext context,
                                                DataProcessingContainer container) {
        DemandDataItem first = tempList.get(0);

        // 计算欠交数量（只针对日需求）
        BigDecimal oweQuantity = calculateOweQuantityForProduct(first.getProductCode(), oemDemandLogs);

        // 创建主记录
        LoadingDemandSubmissionDTO submission = new LoadingDemandSubmissionDTO();
        submission.setId(UUIDUtil.getUUID());
        submission.setOemCode(first.getOemCode());
        submission.setPartNumber(first.getPartNumber());
        submission.setProductCode(first.getProductCode());
        submission.setVersionId(context.getOriginVersionId());
        submission.setEnabled(YesOrNoEnum.YES.getCode());
        submission.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
        submission.setOweQuantity(oweQuantity);

        container.getInsertSubmissionList().add(submission);

        // 创建详情记录
        for (DemandDataItem temp : tempList) {
            try {
                createDetailFromTemp(temp, submission.getId(), oweQuantity, oemDemandLogs, context, container);
            } catch (Exception e) {
                log.error("处理提报数据时出错, demandTime={}, error={}", temp.getDemandTime(), e.getMessage(), e);
            }
        }
    }

    /**
     * 从临时数据创建详情记录
     */
    private void createDetailFromTemp(DemandDataItem temp,
                                      String submissionId,
                                      BigDecimal oweQuantity,
                                      List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                      SyncDemandContext context,
                                      DataProcessingContainer container) {

        // 获取当前日期字符串（格式：yyyy-MM-dd）
        String currentDateStr = DateUtils.dateToString(context.getDateRange().getStartDate(), DateUtils.COMMON_DATE_STR3);

        LoadingDemandSubmissionDetailDTO detail = createDetailDTO(
                submissionId,
                temp.getDemandTime(),
                temp.getDemandQuantity(),
                temp.getSubmissionType()
        );

        if (Objects.equals(GranularityEnum.DAY.getCode(), temp.getSubmissionType())) {
            Date demandDate = DateUtils.stringToDate(detail.getDemandTime(), DateUtils.COMMON_DATE_STR1);
            if (isDateInRange(demandDate, context.getDateRange().getStartDate(), context.getDateRange().getEndDate())) {
                BigDecimal originalDemandQuantity = Objects.equals(StatusEnum.CLOSED.getCode(), temp.getReleaseStatus()) ?
                        BigDecimal.ZERO : temp.getDemandQuantity();

                // 将temp的demandTime转换为yyyy-MM-dd格式进行比较
                String tempDateStr = DateUtils.dateToString(
                    DateUtils.stringToDate(temp.getDemandTime(), DateUtils.COMMON_DATE_STR1),
                    DateUtils.COMMON_DATE_STR3
                );

                log.info("新建提报日期比较 - 需求日期: {} -> {}, 当前日期: {}, 原始需求数量: {}, 欠交数量: {}",
                        temp.getDemandTime(), tempDateStr, currentDateStr, originalDemandQuantity, oweQuantity);

                // 如果是当前日期，将欠交数量加到需求数量中
                BigDecimal finalDemandQuantity = originalDemandQuantity;
                if (Objects.equals(tempDateStr, currentDateStr) && oweQuantity != null && oweQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    finalDemandQuantity = originalDemandQuantity.add(oweQuantity);
                    log.info("✓ 新建提报当前日期{}的需求数量从{}调整为{}（加上欠交数量{}）",
                            currentDateStr, originalDemandQuantity, finalDemandQuantity, oweQuantity);
                } else {
                    log.info("✗ 新建提报不满足累加条件 - 日期匹配: {}, 欠交数量>0: {}",
                            Objects.equals(tempDateStr, currentDateStr),
                            oweQuantity != null && oweQuantity.compareTo(BigDecimal.ZERO) > 0);
                }

                detail.setDemandQuantity(finalDemandQuantity);
                container.getInsertDetailList().add(detail);
            }
        } else {
            Date demandMonth = DateUtils.stringToDate(detail.getDemandTime(), DateUtils.YEAR_MONTH);
            if (isDateInRange(demandMonth, context.getDateRange().getMonthStartDate(), context.getDateRange().getMonthEndDate())) {
                container.getInsertDetailList().add(detail);
            }
        }
    }

    /**
     * 创建详情DTO
     */
    private LoadingDemandSubmissionDetailDTO createDetailDTO(String submissionId, String demandTime,
                                                             BigDecimal demandQuantity, String submissionType) {
        LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
        detailDTO.setSubmissionId(submissionId);
        detailDTO.setDemandTime(demandTime);
        detailDTO.setDemandQuantity(demandQuantity);
        detailDTO.setSubmissionType(submissionType);
        detailDTO.setId(UUIDUtil.getUUID());
        detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
        return detailDTO;
    }



    /**
     * 检查日期是否在范围内
     */
    private boolean isDateInRange(Date date, Date startDate, Date endDate) {
        return Objects.nonNull(date) &&
                date.compareTo(startDate) >= 0 &&
                date.compareTo(endDate) <= 0;
    }

    /**
     * 执行数据库操作
     */
    private void executeDataOperations(DataProcessingContainer container, SyncDemandContext context, String oemCode, List<FdpOriginDemandInterfaceLogVO> oemDemandLogs) {
        // 1. 创建新的提报记录
        if (CollectionUtils.isNotEmpty(container.getInsertSubmissionList())) {
            doCreateBatch(container.getInsertSubmissionList());
        }

        // 2. 更新现有提报记录的欠交数量
        if (CollectionUtils.isNotEmpty(container.getUpdateSubmissionList())) {
            doUpdateBatch(container.getUpdateSubmissionList());
        }

        // 4. 补充缺失的日期和月份数据
        supplementMissingDates(container, context, oemCode, oemDemandLogs);

        // 5. 创建详情记录
        if (CollectionUtils.isNotEmpty(container.getInsertDetailList())) {
            log.info("开始创建 {} 条新的明细数据", container.getInsertDetailList().size());
            loadingDemandSubmissionDetailService.doCreateBatch(container.getInsertDetailList());
            log.info("成功创建 {} 条新的明细数据", container.getInsertDetailList().size());
        }
    }
    /**
     * 补充缺失的日期和月份数据
     */
    private void supplementMissingDates(DataProcessingContainer container, SyncDemandContext context, String oemCode, List<FdpOriginDemandInterfaceLogVO> oemDemandLogs) {
        DateRange dateRange = context.getDateRange();

        // 为新创建的提报补充缺失日期
        for (LoadingDemandSubmissionDTO submission : container.getInsertSubmissionList()) {
            supplementMissingDatesForSubmission(submission.getId(), submission.getProductCode(),
                    submission.getOemCode(), oemDemandLogs, container, dateRange);
        }

        // 为现有提报补充缺失日期
        for (LoadingDemandSubmissionVO submission : context.getSubmissionTree().stream().filter(s -> oemCode.equals(s.getOemCode())).collect(Collectors.toList())) {
            supplementMissingDatesForSubmission(submission.getId(), submission.getProductCode(),
                    submission.getOemCode(), oemDemandLogs, container, dateRange);
        }
    }

    /**
     * 为单个提报补充缺失日期
     */
    private void supplementMissingDatesForSubmission(String submissionId,
                                                     String productCode,
                                                     String oemCode,
                                                     List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                                     DataProcessingContainer container,
                                                     DateRange dateRange) {

        List<LoadingDemandSubmissionDetailDTO> existingDetails = container.getInsertDetailList().stream()
                .filter(detail -> submissionId.equals(detail.getSubmissionId()))
                .collect(Collectors.toList());

        // 补充缺失的日期
        supplementMissingDays(submissionId, productCode, oemCode, oemDemandLogs, existingDetails, container, dateRange);

        // 补充缺失的月份
        supplementMissingMonths(submissionId, existingDetails, container, dateRange);
    }

    /**
     * 补充缺失的日期
     */
    private void supplementMissingDays(String submissionId,
                                       String productCode,
                                       String oemCode,
                                       List<FdpOriginDemandInterfaceLogVO> oemDemandLogs,
                                       List<LoadingDemandSubmissionDetailDTO> existingDetails,
                                       DataProcessingContainer container,
                                       DateRange dateRange) {

        Set<String> existingDays = existingDetails.stream()
                .filter(detail -> GranularityEnum.DAY.getCode().equals(detail.getSubmissionType()))
                .map(LoadingDemandSubmissionDetailDTO::getDemandTime)
                .collect(Collectors.toSet());

        // 获取当前日期字符串（yyyy-MM-dd）
        String currentDateStr = DateUtils.dateToString(dateRange.getStartDate(), DateUtils.COMMON_DATE_STR3);

        // 计算欠交数量（用于当前日期累加）
        BigDecimal oweQuantity = calculateOweQuantityForProduct(productCode, oemDemandLogs);

        log.info("补充缺失日期 - 当前日期: {}, 产品编码: {}, 主机厂编码: {}, 欠交数量: {}",
                currentDateStr, productCode, oemCode, oweQuantity);

        // 批量生成缺失的日期
        List<LoadingDemandSubmissionDetailDTO> missingDays = IntStream.range(0, dateRange.getPeriod())
                .mapToObj(i -> org.apache.commons.lang3.time.DateUtils.addDays(dateRange.getStartDate(), i))
                .map(date -> DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3))
                .filter(dayStr -> !existingDays.contains(dayStr))
                .map(dayStr -> {
                    // 如果是当前日期且有欠交数量，则累加欠交数量
                    BigDecimal demandQuantity = BigDecimal.ZERO;
                    if (Objects.equals(dayStr, currentDateStr) && oweQuantity != null && oweQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        demandQuantity = oweQuantity;
                        log.info("补充缺失日期 - 当前日期{}累加欠交数量: {}", currentDateStr, oweQuantity);
                    } else {
                        log.info("不补充缺失日期 - 日期{}不累加欠交数量，日期匹配: {}, 欠交数量>0: {}",
                                dayStr, Objects.equals(dayStr, currentDateStr),
                                oweQuantity != null && oweQuantity.compareTo(BigDecimal.ZERO) > 0);
                    }
                    return createDetailDTO(submissionId, dayStr, demandQuantity, GranularityEnum.DAY.getCode());
                })
                .collect(Collectors.toList());

        container.getInsertDetailList().addAll(missingDays);
    }

    /**
     * 补充缺失的月份
     */
    private void supplementMissingMonths(String submissionId,
                                         List<LoadingDemandSubmissionDetailDTO> existingDetails,
                                         DataProcessingContainer container,
                                         DateRange dateRange) {

        Set<String> existingMonths = existingDetails.stream()
                .filter(detail -> GranularityEnum.MONTH.getCode().equals(detail.getSubmissionType()))
                .map(LoadingDemandSubmissionDetailDTO::getDemandTime)
                .collect(Collectors.toSet());

        // 批量生成缺失的月份
        List<LoadingDemandSubmissionDetailDTO> missingMonths = IntStream.range(0, dateRange.getMonthPeriod())
                .mapToObj(i -> org.apache.commons.lang3.time.DateUtils.addMonths(dateRange.getMonthStartDate(), i))
                .map(date -> DateUtils.dateToString(date, DateUtils.YEAR_MONTH))
                .filter(monthStr -> !existingMonths.contains(monthStr))
                .map(monthStr -> createDetailDTO(submissionId, monthStr, BigDecimal.ZERO, GranularityEnum.MONTH.getCode()))
                .collect(Collectors.toList());

        container.getInsertDetailList().addAll(missingMonths);
    }

    /**
     * 构建同步结果
     */
    private BaseResponse<Void> buildSyncResult(List<String> errorMessages) {
        if (CollectionUtils.isEmpty(errorMessages)) {
            return BaseResponse.success("装车需求提报成功");
        } else {
            String errorMsg = String.join("</br>", errorMessages);
            return BaseResponse.error(String.format("装车需求提报部分失败：%s", errorMsg));
        }
    }

    // ==================== 内部支持类 ====================

    /**
     * 需求同步上下文
     */
    private static class SyncDemandContext {
        private String originVersionId;
        private List<OemVO> ediOemList;
        private DateRange dateRange;
        private List<LoadingDemandSubmissionVO> submissionTree;
        private String errorMessage;
        private Scenario scenario;

        public boolean hasError() {
            return org.apache.commons.lang3.StringUtils.isNotBlank(errorMessage);
        }

        public String getOriginVersionId() { return originVersionId; }
        public void setOriginVersionId(String originVersionId) { this.originVersionId = originVersionId; }

        public List<OemVO> getEdiOemList() { return ediOemList; }
        public void setEdiOemList(List<OemVO> ediOemList) { this.ediOemList = ediOemList; }

        public DateRange getDateRange() { return dateRange; }
        public void setDateRange(DateRange dateRange) { this.dateRange = dateRange; }

        public List<LoadingDemandSubmissionVO> getSubmissionTree() { return submissionTree; }
        public void setSubmissionTree(List<LoadingDemandSubmissionVO> submissionTree) { this.submissionTree = submissionTree; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public Scenario getScenario() { return scenario; }
        public void setScenario(Scenario scenario) { this.scenario = scenario; }
    }

    /**
     * 日期范围
     */
    private static class DateRange {
        private final Date startDate;
        private final Date endDate;
        private final Date monthStartDate;
        private final Date monthEndDate;
        private final int period;
        private final int monthPeriod;

        public DateRange(Date startDate, Date endDate, Date monthStartDate, Date monthEndDate, int period, int monthPeriod) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.monthStartDate = monthStartDate;
            this.monthEndDate = monthEndDate;
            this.period = period;
            this.monthPeriod = monthPeriod;
        }

        public Date getStartDate() { return startDate; }
        public Date getEndDate() { return endDate; }
        public Date getMonthStartDate() { return monthStartDate; }
        public Date getMonthEndDate() { return monthEndDate; }
        public int getPeriod() { return period; }
        public int getMonthPeriod() { return monthPeriod; }
    }

    /**
     * 需求数据容器
     */
    private static class DemandDataContainer {
        private final List<DemandDataItem> dayList;
        private final List<DemandDataItem> mergedMonthList;

        public DemandDataContainer(List<DemandDataItem> dayList, List<DemandDataItem> mergedMonthList) {
            this.dayList = dayList != null ? dayList : Collections.emptyList();
            this.mergedMonthList = mergedMonthList != null ? mergedMonthList : Collections.emptyList();
        }

        public boolean isEmpty() {
            return CollectionUtils.isEmpty(dayList) && CollectionUtils.isEmpty(mergedMonthList);
        }

        public List<DemandDataItem> getDayList() { return dayList; }
        public List<DemandDataItem> getMergedMonthList() { return mergedMonthList; }
    }

    /**
     * 数据处理容器
     */
    private static class DataProcessingContainer {
        private final List<LoadingDemandSubmissionDTO> insertSubmissionList = Lists.newArrayList();
        private final List<LoadingDemandSubmissionDTO> updateSubmissionList = Lists.newArrayList();
        private final List<LoadingDemandSubmissionDetailDTO> insertDetailList = Lists.newArrayList();
        private final List<String> deleteDetailIdList = Lists.newArrayList();

        public List<LoadingDemandSubmissionDTO> getInsertSubmissionList() { return insertSubmissionList; }
        public List<LoadingDemandSubmissionDTO> getUpdateSubmissionList() { return updateSubmissionList; }
        public List<LoadingDemandSubmissionDetailDTO> getInsertDetailList() { return insertDetailList; }
        public List<String> getDeleteDetailIdList() { return deleteDetailIdList; }
    }

    /**
     * 需求数据项 - 用于EDI数据转换的临时对象
     */
    private static class DemandDataItem {
        private String oemCode;
        private String partNumber;
        private String productCode;
        private String demandTime;
        private BigDecimal demandQuantity;
        private String submissionType;
        private String releaseStatus;

        public String getOemCode() { return oemCode; }
        public void setOemCode(String oemCode) { this.oemCode = oemCode; }

        public String getPartNumber() { return partNumber; }
        public void setPartNumber(String partNumber) { this.partNumber = partNumber; }

        public String getProductCode() { return productCode; }
        public void setProductCode(String productCode) { this.productCode = productCode; }

        public String getDemandTime() { return demandTime; }
        public void setDemandTime(String demandTime) { this.demandTime = demandTime; }

        public BigDecimal getDemandQuantity() { return demandQuantity; }
        public void setDemandQuantity(BigDecimal demandQuantity) { this.demandQuantity = demandQuantity; }

        public String getSubmissionType() { return submissionType; }
        public void setSubmissionType(String submissionType) { this.submissionType = submissionType; }
        public String getReleaseStatus() { return releaseStatus; }
        public void setReleaseStatus(String releaseStatus) { this.releaseStatus = releaseStatus; }
    }

    /**
     * 获取新版本代码
     *
     * @param versionType 需求类型
     * @param versionCode 需求代码
     * @return java.lang.String
     */
    public static String getVersionCode(String versionType, String versionCode) {
        String newVersionCode;
        switch (versionType) {
            case "CLEAN_DEMAND":
                newVersionCode = "STD-" + versionCode;
                break;
            case "CLEAN_FORECAST":
                newVersionCode = "LTD-" + versionCode;
                break;
            case "DEMAND_FORECAST":
                newVersionCode = "DFC-" + versionCode;
                break;
            case "DELIVERY_PLAN":
                newVersionCode = "DLP-" + versionCode;
                break;
            default:
                newVersionCode = "";
        }
        return newVersionCode;
    }

    @Override
    public BaseResponse<Void> apiSubmission(List<OemVO> ediOemList, String originVersionId, Scenario scenario) {
        //接口同步EDI数据
        syncEdi(scenario);
        LoadingDemandSubmissionReqDTO reqDTO = new LoadingDemandSubmissionReqDTO();
        reqDTO.setOriginVersionId(originVersionId);
        for (OemVO oemVO:ediOemList){
            LoadingDemandSubmissionReqDetailDTO detailDTO = new LoadingDemandSubmissionReqDetailDTO();
            detailDTO.setOemCode(oemVO.getOemCode());
            reqDTO.getDetailDTOList().add(detailDTO);
        }
        return syncDemand(reqDTO,scenario);
    }

    //获取最新版本需求提报月度预测数量
    @Override
    public Map<String, BigDecimal> getProductDemandQuantity(String demandTime) {
        // 将 demandTime 从年月格式（例如 202412）转换为年-月格式（例如 2024-12）
        if (demandTime != null && demandTime.length() == 6) {
            demandTime = demandTime.substring(0, 4) + "-" + demandTime.substring(4);
        }
        // 获取数据
        String latestVersionId = originDemandVersionService.selectLatestVersionId();
        Map<String, Object> param = new HashMap<>(2);
        param.put("versionId", latestVersionId);
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = selectByParams(param);

        Map<String, LoadingDemandSubmissionVO> submissionMap = loadingDemandSubmissionVOS.stream().collect(Collectors.toMap(LoadingDemandSubmissionVO::getId, Function.identity()));
        List<String> submissionIds = new ArrayList<>(submissionMap.keySet());

        //查询月度详情数据
        Map<String, Object> detailQueryMap = MapUtil.newHashMap();
        detailQueryMap.put("submissionIds", submissionIds);
        detailQueryMap.put("submissionType", GranularityEnum.MONTH.getCode());
        detailQueryMap.put("demandTime", demandTime);
        List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(detailQueryMap);
        Map<String, List<LoadingDemandSubmissionDetailVO>> detailMap = loadingDemandSubmissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
        //汇总数量
        Map<String, BigDecimal> productDemandQuantityMap = new HashMap<>();
        for (String submissionId : submissionIds) {
            LoadingDemandSubmissionVO loadingDemandSubmissionVO = submissionMap.get(submissionId);
            if (null == loadingDemandSubmissionVO) {
                continue;
            }
            List<LoadingDemandSubmissionDetailVO> detailList = detailMap.get(submissionId);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            BigDecimal demandQuantity = BigDecimal.ZERO;
            for (LoadingDemandSubmissionDetailVO detail : detailList) {
                demandQuantity = demandQuantity.add(detail.getDemandQuantity() == null ? BigDecimal.ZERO : detail.getDemandQuantity());
            }

            productDemandQuantityMap.put(loadingDemandSubmissionVO.getProductCode(), demandQuantity);
        }
        return productDemandQuantityMap;
    }

    @Override
    public BaseResponse<Object> getDemandTypeEnum() {
        String userId = SystemHolder.getUserId();
        if (StringUtils.isEmpty(userId)) {
            return BaseResponse.error("获取不到该用户数据");
        }
        //获取该用户的部门
        List<Dept> dept = ipsNewFeign.getDeptByCurrentUserId(userId);
        if (dept.isEmpty()) {
            return BaseResponse.error("当前用户没有匹配到组织，请进行维护。");
        } else {
            Optional<Dept> first = dept.stream().filter(x -> StringUtils.isEmpty(x.getDeptPath())).findFirst();
            if (first.isPresent()) {
                return BaseResponse.error("该用户对应的组织数据没有路径字段为空，请维护组织表。");
            }
        }
        //判断是否属于物流还是项目
        List<Dept> list1 = dept.stream().filter(x -> x.getDeptPath().contains("物流")).collect(Collectors.toList());
        List<Dept> list2 = dept.stream().filter(x -> x.getDeptPath().contains("项目")).collect(Collectors.toList());
        List<LabelValue<String>> objectLabelValue = new ArrayList<>();
        //物流部则返回量产需求
        if (!list1.isEmpty()) {
            objectLabelValue.add(new LabelValue<>(ProductionDemandTypeEnum.OUTPUT_DEMAND.getDesc(), ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
        }
        //项目部则返回项目需求
        if (!list2.isEmpty()) {
            objectLabelValue.add(new LabelValue<>(ProductionDemandTypeEnum.PROJECT_DEMAND.getDesc(), ProductionDemandTypeEnum.PROJECT_DEMAND.getCode()));
        }
        if (objectLabelValue.isEmpty()) {
            return BaseResponse.error("该用户对应的组织没有需求类型的权限");
        } else {
            return BaseResponse.success(objectLabelValue);
        }
    }

    @Override
    public List<String> selectDisabledProductCodes() {
        return loadingDemandSubmissionDao.selectDisabledProductCodes();
    }


    /**
     * 解析客户原始需求文件
     *
     * @param oemId          主机厂ID
     * @param multipartFiles 需要解析的原始需求文件
     * @return
     */
    @Override
    public BaseResponse<List<DemandParsedVO>> parseDemandFile(String originVersionId, String oemId, MultipartFile[] multipartFiles) {
        BaseResponse<Triple<OemVO, OemLlmPromptVO, LlmConfigVO>> validateParseDemandFileResult = validateParseDemandFile(originVersionId, oemId, multipartFiles);
        if (!validateParseDemandFileResult.getSuccess()) {
            return BaseResponse.error(validateParseDemandFileResult.getMsg());
        }
        Triple<OemVO, OemLlmPromptVO, LlmConfigVO> tripleData = validateParseDemandFileResult.getData();
        BaseResponse parseDemandFileResponse = sendParseDemandFileRequest(tripleData, multipartFiles);
        if (!parseDemandFileResponse.getSuccess()) {
            return BaseResponse.error(parseDemandFileResponse.getMsg());
        }
        // 上传原始需求文件
        this.demandForecastAttachmentsService.uploadFile(originVersionId, tripleData.getLeft().getOemCode(), UploadStatusEnum.SUCCESS.getCode(), 
        		FileDataSourceEnum.ORIGIN_DEMAND_VERSION.getCode(), multipartFiles[0]);
        return parseDemandFileResponse;
    }

    /**
     * 发送解析原始文件请求
     *
     * @param tripleData
     * @param multipartFiles
     * @return
     */
    private BaseResponse<Object> sendParseDemandFileRequest(Triple<OemVO, OemLlmPromptVO, LlmConfigVO> tripleData, MultipartFile[] multipartFiles) {
        OemVO oemVO = tripleData.getLeft();
        OemLlmPromptVO oemLlmPromptVO = tripleData.getMiddle();
        LlmConfigVO llmConfigVO = tripleData.getRight();

        Map params = MapUtil.builder().put("tenantCode", SystemHolder.getTenantCode()).put("apiSource", ApiSourceEnum.LLM.getCode()).put("llmConfigId", oemLlmPromptVO.getLlmConfigId()).put("prompt", oemLlmPromptVO.getPrompt()).map();
        // 判断大模型的厂商，确定大模型的处理器
        if ("GOOGLE".equalsIgnoreCase(llmConfigVO.getProvider())) {
            params.put("apiCategory", ApiCategoryEnum.LLM_GEMINI_DEMAND_FILE_PARSE.getCode());
        }

        BaseResponse<String> parseResponse = newDcpFeign.callWithAttach(JSON.toJSONString(params), multipartFiles);
        if (!parseResponse.getSuccess()) {
            return BaseResponse.error(parseResponse.getMsg());
        }
        String parsedData = parseResponse.getData();
//        String parsedData = "主机厂编码,地点,客户零件号,有效日期起,有效日期止,数量,状态\n" + ",GL 1C1,**********,2025/07/14 18:00,2025/08/14 23:59,0,P\n" + ",GL 1C1,**********,2025/08/21 18:00,2025/08/21 23:59,4020,P\n" + ",GL 1C1,**********,2025/09/03 00:00,2025/09/09 23:59,2640,P\n" + ",GL 1C1,**********,2025/09/10 00:00,2025/09/16 23:59,2640,P\n" + ",GL 1C1,**********,2025/09/17 00:00,2025/09/23 23:59,2580,P\n" + ",GL 1C1,**********,2025/09/24 00:00,2025/09/30 23:59,2640,P\n" + ",GL 1C1,**********,2025/10/01 00:00,2025/10/07 23:59,2820,P\n" + ",GL 1C1,**********,2025/10/08 00:00,2025/10/14 23:59,2760,P\n" + ",GL 1C1,**********,2025/10/15 00:00,2025/10/21 23:59,2820,P\n" + ",GL 1C1,**********,2025/10/22 00:00,2025/10/28 23:59,2760,P\n" + ",GL 1C1,**********,2025/10/29 00:00,2025/11/04 23:59,120,P\n" + ",GL 1C1,**********,2025/11/12 00:00,2025/11/18 23:59,2220,P\n" + ",GL 1C1,**********,2025/11/19 00:00,2025/11/25 23:59,2940,P\n" + ",GL 1C1,**********,2025/11/26 00:00,2024/09/01 23:59,2940,P\n" + ",GL 1C1,**********,2024/09/02 00:00,2024/09/08 23:59,2940,P\n" + ",GL 1C1,**********,2024/09/09 00:00,2024/09/15 23:59,2940,P\n" + ",GL 1C1,**********,2024/09/16 00:00,2024/09/22 23:59,3000,P\n" + ",GL 1C1,**********,2024/09/23 00:00,2024/09/29 23:59,2880,P\n" + ",GL 1C1,**********,2024/09/30 00:00,2024/10/06 23:59,3000,P\n" + ",GL 1C1,**********,2024/10/07 00:00,2024/10/13 23:59,2880,P\n" + ",GL 1C1,**********,2024/10/14 00:00,2024/10/20 23:59,960,P\n" + ",GL 1C1,**********,2024/10/21 00:00,2024/10/27 23:59,480,P\n" + ",GL 1C1,1441273380,2025/08/14 18:00,2025/08/14 23:59,2520,P\n" + ",GL 1C1,1441273380,2025/08/21 18:00,2025/08/21 23:59,4020,P\n" + ",GL 1C1,1441273380,2025/09/03 00:00,2025/09/09 23:59,2640,P\n" + ",GL 1C1,1441273380,2025/09/10 00:00,2025/09/16 23:59,2640,P\n" + ",GL 1C1,1441273380,2025/09/17 00:00,2025/09/23 23:59,2640,P\n" + ",GL 1C1,1441273380,2025/09/24 00:00,2025/09/30 23:59,2640,P\n" + ",GL 1C1,1441273380,2025/10/01 00:00,2025/10/07 23:59,2760,P\n" + ",GL 1C1,1441273380,2025/10/08 00:00,2025/10/14 23:59,2820,P\n" + ",GL 1C1,1441273380,2025/10/15 00:00,2025/10/21 23:59,2760,P\n" + ",GL 1C1,1441273380,2025/10/22 00:00,2025/10/28 23:59,2820,P\n" + ",GL 1C1,1441273380,2025/10/29 00:00,2025/11/04 23:59,60,P\n" + ",GL 1C1,1441273380,2025/11/12 00:00,2025/11/18 23:59,2280,P\n" + ",GL 1C1,1441273380,2025/11/19 00:00,2025/11/25 23:59,2940,P\n" + ",GL 1C1,1441273380,2025/11/26 00:00,2024/09/01 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/09/02 00:00,2024/09/08 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/09/09 00:00,2024/09/15 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/09/16 00:00,2024/09/22 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/09/23 00:00,2024/09/29 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/09/30 00:00,2024/10/06 23:59,2940,P\n" + ",GL 1C1,1441273380,2024/10/07 00:00,2024/10/13 23:59,2880,P\n" + ",GL 1C1,1441273380,2024/10/14 00:00,2024/10/20 23:59,960,P\n" + ",GL 1C1,1441273380,2024/10/21 00:00,2024/10/27 23:59,480,P ";
        log.info("客户需求解析返回数据:{}", parsedData);

        List<DemandParsedVO> demandParsedVOS = EasyExcel.read(new ByteArrayInputStream(parsedData.getBytes(StandardCharsets.UTF_8)), DemandParsedVO.class, null).excelType(ExcelTypeEnum.CSV.CSV).sheet().doReadSync();
        demandParsedVOS.stream().forEach(demandParsedVO -> {
            if (StrUtil.isEmpty(demandParsedVO.getOemCode())) {
                demandParsedVO.setOemCode(oemVO.getOemCode());
            }
        });
        return BaseResponse.success(demandParsedVOS);
    }

    /**
     * 验证解析需求文件的有效性
     *
     * @param oemId 主机厂ID
     * @return
     */
    private BaseResponse<Triple<OemVO, OemLlmPromptVO, LlmConfigVO>> validateParseDemandFile(String originVersionId, String oemId, MultipartFile[] multipartFiles) {
        if (multipartFiles == null || multipartFiles.length <= 0) {
            return BaseResponse.error("需要解析的原始需求文件列表不允许为空！");
        }
        if (multipartFiles.length > 1) {
            return BaseResponse.error("当前仅支持解析一份原始需求文件！");
        }

        OemVO oemVO = this.oemService.selectByPrimaryKey(oemId);
        if (oemVO == null || !YesOrNoEnum.YES.getCode().equalsIgnoreCase(oemVO.getEnabled())) {
            return BaseResponse.error("该主机厂不存在或状态异常！");
        }
        Map<String, Object> paramMap = MapUtil.of("oemId", oemId);
        List<OemLlmPromptVO> oemLlmPromptVOS = this.oemLlmPromptService.selectByParams(paramMap);
        if (CollUtil.isEmpty(oemLlmPromptVOS)) {
            return BaseResponse.error(StrUtil.format("未找到该主机厂[ID:{}]对应的大模型提示词配置!", oemId));
        }
        OriginDemandVersionVO originDemandVersionVO = this.originDemandVersionService.selectByPrimaryKey(originVersionId);
        if (originDemandVersionVO == null || !YesOrNoEnum.YES.getCode().equalsIgnoreCase(originDemandVersionVO.getEnabled())) {
            return BaseResponse.error("该原始需求版本不存在或状态异常！");
        }
        OemLlmPromptVO oemLlmPromptVO = oemLlmPromptVOS.get(0);
        BaseResponse<LlmConfigVO> llmConfigResponse = this.ipsNewFeign.getLlmConfig(oemLlmPromptVO.getLlmConfigId());
        if (!llmConfigResponse.getSuccess()) {
            return BaseResponse.error(StrUtil.format("查询大模型配置返回异常!(ID:{},错误:{})", oemLlmPromptVO.getLlmConfigId(), llmConfigResponse.getMsg()));
        }

        LlmConfigVO llmConfigVO = llmConfigResponse.getData();
        if (llmConfigVO == null || !YesOrNoEnum.YES.getCode().equalsIgnoreCase(llmConfigVO.getEnabled())) {
            return BaseResponse.error("大模型配置不存在或状态异常！");
        }

        return BaseResponse.success(new MutableTriple<>(oemVO, oemLlmPromptVO, llmConfigVO));
    }

    /**
     * 保存解析后的需求数据
     *
     * @param demandSubmissionVO 需求解析结果
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Void> saveParsedDemand(DemandSubmissionVO demandSubmissionVO) {
        BaseResponse<OemVO> oemResponse = validateSaveParsedDemand(demandSubmissionVO);
        if (!oemResponse.getSuccess()) {
            return BaseResponse.error(oemResponse.getMsg());
        }
        OemVO oemVO = oemResponse.getData();

        // 转换原始需求数据
        List<LoadingDemandSubmissionDTO> loadingDemandSubmissionDTOS = parsedDemand2LoadingDemandSubmission(oemVO, demandSubmissionVO);
        return saveLoadingDemandSubmission(loadingDemandSubmissionDTOS);
    }

    /**
     * 保存需求提报数据
     *
     * @param loadingDemandSubmissionDTOS
     * @return
     */
    private BaseResponse<Void> saveLoadingDemandSubmission(List<LoadingDemandSubmissionDTO> loadingDemandSubmissionDTOS) {
        for (LoadingDemandSubmissionDTO demandSubmissionItem : loadingDemandSubmissionDTOS) {
            // 先按当前版本、主机厂、零件查询是否有提报记录，如有，则进行更新
            Map queryParamMap = MapUtil.builder().put("versionId", demandSubmissionItem.getVersionId()).put("demandCategory", demandSubmissionItem.getDemandCategory()).put("oemCode", demandSubmissionItem.getOemCode()).put("partNumber", demandSubmissionItem.getPartNumber()).put("productCode", demandSubmissionItem.getProductCode()).build();
            List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = this.selectByParams(queryParamMap);
            if (CollUtil.isNotEmpty(loadingDemandSubmissionVOS)) {
                // 存在，则将明细挂到原记录上面
                LoadingDemandSubmissionVO oneLoadingDemandSubmissionVO = loadingDemandSubmissionVOS.get(0);
                demandSubmissionItem.getDetails().stream().forEach(loadingDemandSubmissionDetailDTO -> {
                    loadingDemandSubmissionDetailDTO.setSubmissionId(oneLoadingDemandSubmissionVO.getId());
                });
                for (LoadingDemandSubmissionDetailDTO loadingDemandSubmissionDetailDTO : demandSubmissionItem.getDetails()) {
                    Map queryDetailParamMap = MapUtil.builder().put("submissionId", oneLoadingDemandSubmissionVO.getId()).put("submissionType", loadingDemandSubmissionDetailDTO.getSubmissionType()).put("demandTime", loadingDemandSubmissionDetailDTO.getDemandTime()).build();
                    List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = this.loadingDemandSubmissionDetailService.selectByParams(queryDetailParamMap);
                    if (CollUtil.isNotEmpty(loadingDemandSubmissionDetailVOS)) {
                        LoadingDemandSubmissionDetailVO oneLoadingDemandSubmissionDetailVO = loadingDemandSubmissionDetailVOS.get(0);
                        LoadingDemandSubmissionDetailDTO dbloadingDemandSubmissionDetailDTO = LoadingDemandSubmissionDetailConvertor.INSTANCE.vo2Dto(oneLoadingDemandSubmissionDetailVO);
                        dbloadingDemandSubmissionDetailDTO.setDemandQuantity(loadingDemandSubmissionDetailDTO.getDemandQuantity());
                        this.loadingDemandSubmissionDetailService.doUpdate(dbloadingDemandSubmissionDetailDTO);
                    } else {
                        this.loadingDemandSubmissionDetailService.doCreate(loadingDemandSubmissionDetailDTO);
                    }
                }
            } else {
                this.doCreate(demandSubmissionItem);
                this.loadingDemandSubmissionDetailService.doCreateBatch(demandSubmissionItem.getDetails());
            }
        }
        return BaseResponse.success();
    }

    /**
     * 原始需求解析结果转换为装车需求提报记录
     *
     * @param demandSubmissionVO
     * @return
     */
    private List<LoadingDemandSubmissionDTO> parsedDemand2LoadingDemandSubmission(OemVO oemVO, DemandSubmissionVO demandSubmissionVO) {
        List<LoadingDemandSubmissionDTO> loadingDemandSubmissionDTOS = new ArrayList<>();
        List<String> custPartNums = demandSubmissionVO.getDemandDatas().stream().map(DemandParsedVO::getCustItemNum).distinct().collect(Collectors.toList());
        BaseResponse<List<PartRelationMapVO>> partRelationMapResponse = this.partRelationMapService.getByPartNums(custPartNums);
        Map<String, String> custPartMap = new HashMap<>();
        if (partRelationMapResponse.getSuccess() && CollUtil.isNotEmpty(partRelationMapResponse.getData())) {
            custPartMap = partRelationMapResponse.getData().stream().collect(Collectors.toMap(PartRelationMapVO::getPartNumber, PartRelationMapVO::getProductCode, (existing, replacement) -> replacement  // 重复时保留后者
            ));
        }
        // 按照客户零件号分组
        Map<String, List<DemandParsedVO>> demandParsedGroupMap = demandSubmissionVO.getDemandDatas().stream().collect(Collectors.groupingBy(DemandParsedVO::getCustItemNum));
        for (Entry<String, List<DemandParsedVO>> demandParsedGroupEntry : demandParsedGroupMap.entrySet()) {
            // 解析需求提报头
            LoadingDemandSubmissionDTO loadingDemandSubmissionDTO = new LoadingDemandSubmissionDTO();
            loadingDemandSubmissionDTO.setId(UUIDUtil.getUUID());//主键ID
            loadingDemandSubmissionDTO.setOemCode(oemVO.getOemCode());//主机厂代码
            loadingDemandSubmissionDTO.setPartNumber(demandParsedGroupEntry.getKey());//客户零件号
            loadingDemandSubmissionDTO.setProductCode(custPartMap.get(demandParsedGroupEntry.getKey()));//本厂编号
            loadingDemandSubmissionDTO.setVersionId(demandSubmissionVO.getOriginVersionId());//原始需求版本号
            loadingDemandSubmissionDTO.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());//量产需求
            loadingDemandSubmissionDTO.setEnabled(YesOrNoEnum.YES.getCode());//有效标志

            Map<String, List<DemandParsedVO>> demandParsedMonthGroupMap = demandParsedGroupEntry.getValue().stream().filter(demandParsedVO -> DateUtil.compare(demandParsedVO.getDemandDateFrom(), new Date(), DatePattern.NORM_MONTH_PATTERN) >= 0).collect(Collectors.groupingBy(DemandParsedVO::buildGroup));
            // 解析明细
            List<LoadingDemandSubmissionDetailDTO> detailList = new ArrayList<>();
            for (Entry<String, List<DemandParsedVO>> demandParsedMonthGroupEntry : demandParsedMonthGroupMap.entrySet()) {
                // 装车需求明细
                List<DemandParsedVO> demandParsedMonthGroupItems = demandParsedMonthGroupEntry.getValue();
                detailList.add(transferLoadingDemandDetail(loadingDemandSubmissionDTO, demandParsedMonthGroupItems, custPartMap));
            }
            loadingDemandSubmissionDTO.setDetails(detailList);

            loadingDemandSubmissionDTOS.add(loadingDemandSubmissionDTO);
        }
        return loadingDemandSubmissionDTOS;
    }

    /**
     * 实体转换（将解析后的原始需求转成装车需求提取实体）
     *
     * @param loadingDemandSubmissionDTO 需求提报主表
     * @param demandParsedVOs            原始需求解析记录列表
     * @param custPartMap                客户零件映射集
     * @return
     */
    private LoadingDemandSubmissionDetailDTO transferLoadingDemandDetail(LoadingDemandSubmissionDTO loadingDemandSubmissionDTO, List<DemandParsedVO> demandParsedVOs, Map<String, String> custPartMap) {
        DemandParsedVO firstDemandParsedVO = demandParsedVOs.get(0);

        LoadingDemandSubmissionDetailDTO loadingDemandSubmissionDetailDTO = new LoadingDemandSubmissionDetailDTO();
        loadingDemandSubmissionDetailDTO.setSubmissionId(loadingDemandSubmissionDTO.getId());
        loadingDemandSubmissionDetailDTO.setDemandTime(DateUtil.format(firstDemandParsedVO.getDemandDateFrom(), DatePattern.NORM_MONTH_PATTERN));

        // 分组后累计数量集合
        BigDecimal totalQuantity = demandParsedVOs.stream().map(DemandParsedVO::getQuantity).filter(Objects::nonNull).collect(Collectors.reducing(BigDecimal.ZERO, BigDecimal::add));
        loadingDemandSubmissionDetailDTO.setDemandQuantity(totalQuantity);

        loadingDemandSubmissionDetailDTO.setSubmissionType(GranularityEnum.MONTH.getCode());
        loadingDemandSubmissionDetailDTO.setId(UUIDUtil.getUUID());
        loadingDemandSubmissionDetailDTO.setEnabled(YesOrNoEnum.YES.getCode());

        return loadingDemandSubmissionDetailDTO;
    }

    /**
     * 校验保存原始需求文件解析后的数据
     *
     * @param demandSubmissionVO 需求解析结果
     * @return
     */
    private BaseResponse<OemVO> validateSaveParsedDemand(DemandSubmissionVO demandSubmissionVO) {
        OemVO oemVO = this.oemService.selectByPrimaryKey(demandSubmissionVO.getOemId());
        return BaseResponse.success(oemVO);
    }
}
