package com.yhl.scp.dfp.warehouse.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.warehouse.dto.AbroadWarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.dto.EnRouteDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.service.AbroadWarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.AbroadWarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.warehouse.convertor.WarehouseReleaseRecordConvertor;
import com.yhl.scp.dfp.warehouse.domain.entity.WarehouseReleaseRecordDO;
import com.yhl.scp.dfp.warehouse.domain.service.WarehouseReleaseRecordDomainService;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseRecordPO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>WarehouseReleaseRecordServiceImpl</code>
 * <p>
 * 仓库发货记录应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-31 10:17:29
 */
@Slf4j
@Service
public class WarehouseReleaseRecordServiceImpl extends AbstractService implements WarehouseReleaseRecordService {

    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;

    @Resource
    private WarehouseReleaseRecordDomainService warehouseReleaseRecordDomainService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Resource
    private AbroadWarehouseReleaseRecordService abroadWarehouseReleaseRecordService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    private final String range_category = "EXTERNAL_RES";
    private final String range_type = "WAREHOUSE_RELEASE_MAPPING";

    @Override
    public BaseResponse<Void> doCreate(WarehouseReleaseRecordDTO warehouseReleaseRecordDTO) {
        // 0.数据转换
        WarehouseReleaseRecordDO warehouseReleaseRecordDO = WarehouseReleaseRecordConvertor.INSTANCE.dto2Do(warehouseReleaseRecordDTO);
        WarehouseReleaseRecordPO warehouseReleaseRecordPO = WarehouseReleaseRecordConvertor.INSTANCE.dto2Po(warehouseReleaseRecordDTO);
        // 1.数据校验
        warehouseReleaseRecordDomainService.validation(warehouseReleaseRecordDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(warehouseReleaseRecordPO);
        warehouseReleaseRecordDao.insertWithPrimaryKey(warehouseReleaseRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(WarehouseReleaseRecordDTO warehouseReleaseRecordDTO) {
        // 0.数据转换
        WarehouseReleaseRecordDO warehouseReleaseRecordDO = WarehouseReleaseRecordConvertor.INSTANCE.dto2Do(warehouseReleaseRecordDTO);
        WarehouseReleaseRecordPO warehouseReleaseRecordPO = WarehouseReleaseRecordConvertor.INSTANCE.dto2Po(warehouseReleaseRecordDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseReleaseRecordDomainService.validation(warehouseReleaseRecordDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(warehouseReleaseRecordPO);
        warehouseReleaseRecordDao.update(warehouseReleaseRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<WarehouseReleaseRecordDTO> list) {
        List<WarehouseReleaseRecordPO> newList = WarehouseReleaseRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        warehouseReleaseRecordDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<WarehouseReleaseRecordDTO> list) {
        List<WarehouseReleaseRecordPO> newList = WarehouseReleaseRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        warehouseReleaseRecordDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return warehouseReleaseRecordDao.deleteBatch(idList);
        }
        return warehouseReleaseRecordDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public WarehouseReleaseRecordVO selectByPrimaryKey(String id) {
        WarehouseReleaseRecordPO po = warehouseReleaseRecordDao.selectByPrimaryKey(id);
        return WarehouseReleaseRecordConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_warehouse_release_record")
    public List<WarehouseReleaseRecordVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_warehouse_release_record")
    public List<WarehouseReleaseRecordVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<WarehouseReleaseRecordVO> dataList = warehouseReleaseRecordDao.selectByCondition(sortParam, queryCriteriaParam);
        WarehouseReleaseRecordServiceImpl target = SpringBeanUtils.getBean(WarehouseReleaseRecordServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectByParams(Map<String, Object> params) {
        List<WarehouseReleaseRecordPO> list = warehouseReleaseRecordDao.selectByParams(params);
        return WarehouseReleaseRecordConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectGroup() {
        List<WarehouseReleaseRecordPO> list = warehouseReleaseRecordDao.selectGroup();
        return WarehouseReleaseRecordConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseReleaseRecordVO> getInRoad(List<String> demandProductCodeList,
                                                    List<String> shipmentLocatorCodes) {
        return warehouseReleaseRecordDao.selectInRoadWarehouse(demandProductCodeList, shipmentLocatorCodes);
    }

    @Override
    public List<LabelValue<String>> selectTargetStockLocation() {
        List<String> result = new ArrayList<>();
        List<String> strings = warehouseReleaseRecordDao.selectTargetStockLocation();
        List<String> targetStockLocation = warehouseReleaseToWarehouseService.selectTargetStockLocation();
        if (CollectionUtils.isEmpty(strings) && CollectionUtils.isEmpty(targetStockLocation)) {
            return Lists.newArrayList();
        } else if (CollectionUtils.isEmpty(strings)) {
            result.addAll(targetStockLocation);
        } else if (CollectionUtils.isEmpty(targetStockLocation)) {
            result.addAll(strings);
        } else {
            result.addAll(strings);
            targetStockLocation.forEach(item -> {
                if (!result.contains(item)) {
                    result.add(item);
                }
            });
        }
        return result.stream().map(item -> new LabelValue<>(item, item)).collect(Collectors.toList());
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void syncMesData() {
        // todo 等待接口地址及返回格式确认
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.WAREHOUSE_RELEASE_RECORD.getCode();
    }

    @Override
    public List<WarehouseReleaseRecordVO> invocation(List<WarehouseReleaseRecordVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return warehouseReleaseRecordDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<WarehouseReleaseRecordVO> actualDelivery(Date beginTime, Date endTime, String type) {
        return warehouseReleaseRecordDao.actualDelivery(beginTime, endTime, type);
    }

    @Override
    public BaseResponse<Void> syncWareHouseReleaseData(List<WarehouseReleaseRecordDTO> list, String code, String scenario) {
        List<WarehouseReleaseRecordDTO> dtoS = new ArrayList<>();
        List<WarehouseReleaseRecordDTO> updateDtoS = new ArrayList<>();
        List<WarehouseReleaseToWarehouseDTO> insertToWarehouseDTOS = new ArrayList<>();
        List<WarehouseReleaseToWarehouseDTO> updateToWarehouseDTOS = new ArrayList<>();
        //获取场景业务范围 根据当前租户找到虚拟用户
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, range_type, range_category, null);
        ScenarioBusinessRangeVO businessRangeVO = scenarioBusinessRange.getData();
        if (businessRangeVO == null) {
            log.error("在场景业务范围表中没有该租户{}对应的虚拟客户的映射。", scenario);
            throw new BusinessException("在场景业务范围表中没有该租户" + scenario + "对应的虚拟客户的映射");
        }
        String virtualWarehouse = businessRangeVO.getRangeData();
        if (StringUtils.isEmpty(virtualWarehouse)) {
            log.error("在场景业务范围表中改租户{}对应的虚拟客户为空，请联系管理员进行维护", scenario);
            throw new BusinessException("在场景业务范围表中改租户" + scenario + "对应的虚拟客户为空，请联系管理员进行维护");
        }
        //获取同步的所有kid
        Map<String, WarehouseReleaseRecordDTO> syncDataMap = list.stream().collect(Collectors.toMap(
                item -> item.getKid(),
                item -> item,
                (item1, item2) -> item1.getLastUpdateDate().compareTo(item2.getLastUpdateDate()) > 0 ? item1 : item2
        ));
        Set<String> kids = syncDataMap.keySet();
        //匹配数据库是有旧数据
        HashMap<String, Object> map = new HashMap<>(3);
        map.put("kids", kids);
        map.put("sourceType", list.get(0).getSourceType());
        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = selectByParams(map);
        Map<String, WarehouseReleaseRecordVO> oldDataMap = warehouseReleaseRecordVOS.stream().collect(Collectors.toMap(
                item -> item.getKid(),
                item -> item
        ));
        //获取旧数据的kid集合
        Set<String> oldKids = oldDataMap.keySet();
        //匹配至中转库表格是否由旧数据
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseVOS = warehouseReleaseToWarehouseService.selectByParams(map);
        Map<String, WarehouseReleaseToWarehouseVO> toWarehouseVOMap = warehouseReleaseToWarehouseVOS.stream().collect(Collectors.toMap(
                item -> item.getKid(),
                item -> item
        ));
        //获取kid集合
        Set<String> toWarehouseKids = toWarehouseVOMap.keySet();
        //数据遍历
        for (WarehouseReleaseRecordDTO dto : syncDataMap.values()) {
            // 例如： 上海的数据分三种，排除上海发到中转库的数据 SJXN指客户
            if (oldKids.contains(dto.getKid())) {
                WarehouseReleaseRecordVO vo = oldDataMap.get(dto.getKid());
                dto.setId(vo.getId());
                dto.setCreateTime(vo.getCreateTime());
                dto.setCreator(vo.getCreator());
                dto.setVersionValue(vo.getVersionValue());
                dto.setEnabled(vo.getEnabled());
                dto.setActualArrivePortTime(vo.getActualArrivePortTime());
                dto.setActualCompletionTime(vo.getActualCompletionTime());
                dto.setEstimatedArrivePortTime(vo.getEstimatedArrivePortTime());
                dto.setEstimatedCompletionTime(vo.getEstimatedCompletionTime());
                if ("Y".equals(vo.getIsReceive())) {
                    dto.setIsReceive(vo.getIsReceive());
                }
                updateDtoS.add(dto);
            } else {
                if (code.equals(dto.getPlantCode())) {
                    if (!virtualWarehouse.equals(dto.getShipmentWarehouseCode())) {
                        WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO = new WarehouseReleaseToWarehouseDTO();
                        BeanUtils.copyProperties(dto, warehouseReleaseToWarehouseDTO);
                        if (toWarehouseKids.contains(dto.getKid())) {
                            WarehouseReleaseToWarehouseVO oldData = toWarehouseVOMap.get(dto.getKid());
                            warehouseReleaseToWarehouseDTO.setId(oldData.getId());
                            warehouseReleaseToWarehouseDTO.setCreateTime(oldData.getCreateTime());
                            warehouseReleaseToWarehouseDTO.setCreator(oldData.getCreator());
                            warehouseReleaseToWarehouseDTO.setVersionValue(oldData.getVersionValue());
                            warehouseReleaseToWarehouseDTO.setRemark(oldData.getRemark());
                            warehouseReleaseToWarehouseDTO.setEnabled(oldData.getEnabled());
                            warehouseReleaseToWarehouseDTO.setActualArrivePortTime(oldData.getActualArrivePortTime());
                            warehouseReleaseToWarehouseDTO.setActualCompletionTime(oldData.getActualCompletionTime());
                            warehouseReleaseToWarehouseDTO.setEstimatedArrivePortTime(oldData.getEstimatedArrivePortTime());
                            warehouseReleaseToWarehouseDTO.setEstimatedCompletionTime(oldData.getEstimatedCompletionTime());
                            updateToWarehouseDTOS.add(warehouseReleaseToWarehouseDTO);
                        } else {
                            insertToWarehouseDTOS.add(warehouseReleaseToWarehouseDTO);
                        }
                        continue;
                    }
                }
                dtoS.add(dto);
            }
            if (dtoS.size() >= 5000) {
                doCreateBatch(dtoS);
                dtoS.clear();
            }
            if (updateDtoS.size() >= 5000) {
                doUpdateBatch(updateDtoS);
                updateDtoS.clear();
            }
            if (insertToWarehouseDTOS.size() >= 5000) {
                warehouseReleaseToWarehouseService.doCreateBatch(insertToWarehouseDTOS);
                insertToWarehouseDTOS.clear();
            }
            if (updateToWarehouseDTOS.size() >= 5000) {
                warehouseReleaseToWarehouseService.doUpdateBatch(updateToWarehouseDTOS);
                updateToWarehouseDTOS.clear();
            }
        }
        if (!dtoS.isEmpty()) {
            doCreateBatch(dtoS);
        }
        if (!updateDtoS.isEmpty()) {
            doUpdateBatch(updateDtoS);
        }
        if (!insertToWarehouseDTOS.isEmpty()) {
            warehouseReleaseToWarehouseService.doCreateBatch(insertToWarehouseDTOS);
        }
        if (!updateToWarehouseDTOS.isEmpty()) {
            warehouseReleaseToWarehouseService.doUpdateBatch(updateToWarehouseDTOS);
        }
        return BaseResponse.success("数据同步成功");
    }

    @Override
    public BaseResponse<Void> syncData(String beginTime, String endTime, String tenantCode) {
        try {
            log.info("开始同步收发货记录");
            //获取MES仓库收发货记录日志数据
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantCode();
                map.put("triggerType", YesOrNoEnum.YES.getCode());
            }
            map.put("lastUpdateDate", beginTime);
            map.put("endDate", endTime);
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.WAREHOUSE_RELEASE.getCode(), map);

            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步仓库收发货报错,{}", e.getMessage());
            throw new BusinessException("同步零仓库收发货报错", e.getMessage());
        }
    }

    @Override
    public List<WarehouseReleaseRecordMonthVO> selectMonthVOByParams(Map<String, Object> params) {
        return warehouseReleaseRecordDao.selectMonthVOByParams(params);
    }

    @Override
    public List<WarehouseReleaseRecordMonthVO> selectMonthVOByParamsGroupOem(Map<String, Object> params) {
        return warehouseReleaseRecordDao.selectMonthVOByParamsGroupOem(params);
    }

    /**
     * 同步FYSL发货记录
     *
     * @param tenantCode
     * @param orgId
     * @return
     */
    @Override
    public BaseResponse<Void> syncFYSLData(String tenantCode, String orgId) {
        try {
            log.info("开始同步FYSL发货记录");
            //获取MES仓库收发货记录日志数据
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("organizeId", orgId);
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.SHIPPING_RECORD.getCode(), map);

            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步FYSL发货记录报错,{}", e.getMessage());
            throw new BusinessException("同步FYSL发货记录报错", e.getMessage());
        }
    }

    /**
     * 具体的同步FYSL发货记录逻辑
     *
     * @param arrayList
     * @return
     */
    @Override
    public BaseResponse<Void> syncWareHouseReleaseFYSLData(List<WarehouseReleaseRecordDTO> arrayList) {
        try {
            //新增集合
            List<WarehouseReleaseRecordDTO> insertList = new ArrayList<>();
            //更新集合
            List<WarehouseReleaseRecordDTO> updateList = new ArrayList<>();
            //获取id集合
            Set<String> idSet = arrayList.stream().map(WarehouseReleaseRecordDTO::getId).collect(Collectors.toSet());
            Map<String, Object> map = MapUtil.newHashMap();
            map.put("kids", idSet);
            map.put("sourceType", ApiSourceEnum.ERP.getCode());
            //获取数据库中的旧数据
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = this.selectByParams(map);
            Map<String, WarehouseReleaseRecordVO> collect = warehouseReleaseRecordVOS.stream().collect(Collectors.toMap(
                    x -> x.getKid(),
                    x -> x
            ));
            //获取ERP旧数据中kid的集合
            Set<String> oldKidSet = collect.keySet();
            //数据遍历
            for (WarehouseReleaseRecordDTO dto : arrayList) {
                if (oldKidSet.contains(dto.getKid())) {
                    WarehouseReleaseRecordVO vo = collect.get(dto.getKid());
                    dto.setId(vo.getId());
                    dto.setCreateTime(vo.getCreateTime());
                    dto.setCreator(vo.getCreator());
                    dto.setVersionValue(vo.getVersionValue());
                    dto.setEnabled(vo.getEnabled());
                    updateList.add(dto);
                    if (updateList.size() >= 2000) {
                        doUpdateBatch(updateList);
                        updateList.clear();
                    }
                } else {
                    insertList.add(dto);
                    if (insertList.size() >= 2000) {
                        doCreateBatch(insertList);
                        insertList.clear();
                    }
                }
            }
            if (!insertList.isEmpty()) {
                doCreateBatch(insertList);
            }
            if (!updateList.isEmpty()) {
                doUpdateBatch(updateList);
            }
            return BaseResponse.success("同步完成");
        } catch (Exception e) {
            log.error("同步FYSL收货计划数据失败：{}", e.getMessage());
            return BaseResponse.error("同步FYSL收货计划数据失败" + e.getMessage());
        }
    }

    /**
     * 同步FYDS在途数据
     *
     * @param tenantCode
     * @return
     */
    @Override
    public BaseResponse<Void> syncEnRouteData(String tenantCode) {
        try {
            log.info("开始同步FYDS在途数据");
            //获取当前数据库中是否接收为N，并且实际完成时间为空的数据
            HashMap<String, Object> wareHouseMap = MapUtil.newHashMap();
            wareHouseMap.put("isReceive", "N");
            wareHouseMap.put("actualCompletionTimeNull", YesOrNoEnum.YES.getCode());
            wareHouseMap.put("reqNumNotNull", YesOrNoEnum.YES.getCode());
            wareHouseMap.put("sourceType", ApiSourceEnum.MES.getCode());
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = this.selectByParams(wareHouseMap);
            if (!warehouseReleaseRecordVOS.isEmpty()) {
                List<WarehouseReleaseRecordVO> groupByThreeData = warehouseReleaseRecordVOS.stream().collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                vo -> vo.getReqNum() + "" + vo.getContainerNum() + "" + vo.getShipCompany(),
                                vo -> vo,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
                //获取MES仓库收发货记录日志数据
                HashMap<String, Object> map = MapUtil.newHashMap();
                map.put("list", groupByThreeData);
                if (StringUtils.isEmpty(tenantCode)) {
                    tenantCode = SystemHolder.getTenantCode();
                }
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                        ApiCategoryEnum.EN_ROUTE.getCode(), map);
            }
            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步FYDS在途数据报错,{}", e.getMessage());
            throw new BusinessException("同步FYDS在途数据报错,{}", e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> syncFYDSData(List<EnRouteDTO> list) {
        //批量更新数据的集合
        List<WarehouseReleaseRecordDTO> updateList = new ArrayList<>();

        for (EnRouteDTO dto : list) {
            //查询数据
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("reqNum", dto.getReqNum());
            if (StringUtils.isEmpty(dto.getContainerNum())) {
                map.put("containerNumNull", YesOrNoEnum.YES.getCode());
            } else {
                map.put("containerNum", dto.getContainerNum());
            }
            if (StringUtils.isEmpty(dto.getShipCompany())) {
                map.put("shipCompanyNull", YesOrNoEnum.YES.getCode());
            } else {
                map.put("shipCompany", dto.getShipCompany());
            }
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = this.selectByParams(map);
            if (!warehouseReleaseRecordVOS.isEmpty()) {
                //四个时间字段赋值，并且转化实体类
                warehouseReleaseRecordVOS.stream().forEach(x -> {
                    WarehouseReleaseRecordDTO warehouseReleaseRecordDTO = new WarehouseReleaseRecordDTO();
                    x.setEstimatedArrivePortTime(dto.getEstimatedArrivePortTime());
                    x.setActualArrivePortTime(dto.getActualArrivePortTime());
                    x.setEstimatedCompletionTime(dto.getEstimatedCompletionTime());
                    x.setActualCompletionTime(dto.getActualCompletionTime());
                    if (!Objects.isNull(x.getActualCompletionTime())) {
                        x.setIsReceive("Y");
                    }
                    BeanUtils.copyProperties(x, warehouseReleaseRecordDTO);
                    updateList.add(warehouseReleaseRecordDTO);
                });
                if (updateList.size() >= 1000) {
                    doUpdateBatch(updateList);
                    updateList.clear();
                }
            }
        }
        if (!updateList.isEmpty()) {
            doUpdateBatch(updateList);
        }
        return BaseResponse.success("同步在途时间完成");
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectGroupByItemCodes(List<String> itemCodes) {
        List<WarehouseReleaseRecordPO> list = warehouseReleaseRecordDao.selectGroupByItemCodes(itemCodes);
        return WarehouseReleaseRecordConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordSumQtyByDate(List<String> productCodes, String startDate, String endDate) {
        return warehouseReleaseRecordDao.selectWarehouseReleaseRecordSumQtyByDate(productCodes, startDate, endDate);
    }

    @Override
    public BaseResponse<Void> syncFYEWarehouseData(String scenario, String plateCode, String tenantCode, String beginTime, String endTime) {
        try {
            log.info("开始同步FYE仓库收发货数据");
            //获取MES仓库收发货记录日志数据
            HashMap<String, Object> map = MapUtil.newHashMap();
            //手动操作: tenantCode和scenario为空
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantCode();
                map.put("triggerType", YesOrNoEnum.YES.getCode());
                scenario = SystemHolder.getScenario();
            }
            map.put("plateCode", plateCode);
            map.put("scenario1", scenario);
            map.put("lastUpdateDate", beginTime);
            map.put("endDate", endTime);
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.FYE_WAREHOUSE_RELEASE.getCode(), map);

            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步FYE仓库收发货报错,{}", e.getMessage());
            throw new BusinessException("同步FYE仓库收发货报错", e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> syncFYEWareHouseReleaseData(List<AbroadWarehouseReleaseRecordDTO> list) {
        try {
            List<AbroadWarehouseReleaseRecordDTO> dtoS = new ArrayList<>();
            List<AbroadWarehouseReleaseRecordDTO> updateDtoS = new ArrayList<>();
            //获取同步的所有kid
            Map<String, AbroadWarehouseReleaseRecordDTO> syncDataMap = list.stream().collect(Collectors.toMap(
                    item -> item.getKid(),
                    item -> item,
                    (item1, item2) -> item1.getLastUpdateDate().compareTo(item2.getLastUpdateDate()) > 0 ? item1 : item2
            ));
            Set<String> kids = syncDataMap.keySet();
            //匹配数据库是有旧数据
            HashMap<String, Object> map = new HashMap<>(3);
            map.put("kids", kids);
            map.put("sourceType", list.get(0).getSourceType());
            List<AbroadWarehouseReleaseRecordVO> warehouseReleaseRecordVOS = abroadWarehouseReleaseRecordService.selectByParams(map);
            Map<String, AbroadWarehouseReleaseRecordVO> oldDataMap = warehouseReleaseRecordVOS.stream().collect(Collectors.toMap(
                    item -> item.getKid(),
                    item -> item
            ));
            //获取旧数据的kid集合
            Set<String> oldKids = oldDataMap.keySet();
            //数据遍历
            for (AbroadWarehouseReleaseRecordDTO dto : syncDataMap.values()) {
                if (oldKids.contains(dto.getKid())) {
                    AbroadWarehouseReleaseRecordVO vo = oldDataMap.get(dto.getKid());
                    dto.setId(vo.getId());
                    dto.setCreateTime(vo.getCreateTime());
                    dto.setCreator(vo.getCreator());
                    dto.setVersionValue(vo.getVersionValue());
                    dto.setEnabled(vo.getEnabled());
                    dto.setActualArrivePortTime(vo.getActualArrivePortTime());
                    dto.setActualCompletionTime(vo.getActualCompletionTime());
                    dto.setEstimatedArrivePortTime(vo.getEstimatedArrivePortTime());
                    dto.setEstimatedCompletionTime(vo.getEstimatedCompletionTime());
                    if ("Y".equals(vo.getIsReceive())) {
                        dto.setIsReceive(vo.getIsReceive());
                    }
                    updateDtoS.add(dto);
                } else {
                    dtoS.add(dto);
                }
            }
            if (!dtoS.isEmpty()) {
                BulkOperationUtils.bulkUpdateOrCreate(dtoS, abroadWarehouseReleaseRecordService::doCreateBatch, 2000);
            }
            if (!updateDtoS.isEmpty()) {
                BulkOperationUtils.bulkUpdateOrCreate(updateDtoS, abroadWarehouseReleaseRecordService::doUpdateBatch, 2000);
            }
            return BaseResponse.success("数据同步成功");
        } catch (Exception e) {
            log.error("同步FYE仓库收发货数据报错,{}", e.getMessage());
            if (Objects.nonNull(e.getCause())) {
                log.error("同步FYE仓库收发货数据报错原因,{}", e.getCause().toString());
            }
            throw new BusinessException("同步FYE仓库收发货数据报错" + e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> syncFYEData(String beginTime, String endTime) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(), "SALE_ORGANIZATION", "INTERNAL", null);
        ScenarioBusinessRangeVO data = scenarioBusinessRange.getData();
        if (Objects.isNull(data)) {
            log.error("在auth_scenario_business_range表中匹配不到当前租户{}的销售组织数据", SystemHolder.getScenario());
            return BaseResponse.error("在auth_scenario_business_range表中匹配不到当前租户的销售组织数据，请联系管理员维护");
        }
        return this.syncFYEWarehouseData(null, data.getRangeData(), null, beginTime, endTime);
    }
}