package com.yhl.scp.dfp.report.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Lists;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.excel.RangeMergeStrategy;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.github.pagehelper.page.PageMethod;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.utils.MiscellaneousUtils;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.report.infrastructure.dao.CustomerOrderWeeklyReportDao;
import com.yhl.scp.dfp.report.service.CustomerOrderWeeklyReportService;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <code>CustomerOrderWeeklyReportServiceImpl</code>
 * <p>
 * CustomerOrderWeeklyReportServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 19:23:52
 */
@Service
@Slf4j
public class CustomerOrderWeeklyReportServiceImpl implements CustomerOrderWeeklyReportService {

    @Resource
    private CustomerOrderWeeklyReportDao customerOrderWeeklyReportDao;

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private OemService oemService;

    @Override
    public List<LabelValue<String>> selectVersionDropdown() {
        return customerOrderWeeklyReportDao.selectVersionDropdown();
    }

    @Override
    public List<String> selectWeekSequence(String versionId) {
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        Date createTime = version.getCreateTime();
        List<String> result = new ArrayList<>();
        int weekDay = ipsFeign.getByCollectionCode("CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY").stream()
                .map(x -> Integer.parseInt(x.getCollectionValue())).findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY"));
        for (int i = 1; i <= 4; i++) {
            Date date = DateUtils.moveCalendar(createTime, Calendar.DAY_OF_YEAR, 7 * i);
            result.add(selectWeekSequence(date, weekDay));
        }
        return result;
    }

    public static String selectWeekSequence(Date date, int weekDay) {
        // 创建日历实例
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 获取当前是周几
        int currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        // 计算目标日期与当前日期的差距
        int daysToAdd = weekDay - currentDayOfWeek + 1;
        // 调整日期
        calendar.add(Calendar.DAY_OF_WEEK, daysToAdd);
        // 格式化日期为yyyy-MM-dd格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    @SuppressWarnings("unused")
    public List<String> selectWeekSequenceBackUp(String versionId) {
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        Date createTime = version.getCreateTime();
        List<String> result = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            Date date = DateUtils.moveCalendar(createTime, Calendar.DAY_OF_YEAR, 7 * i);
            int weekSequence = getWeekSequence(MiscellaneousUtils.date2LocalDate(date));
            if (weekSequence < 10) {
                result.add("W0" + weekSequence);
            } else {
                result.add("W" + weekSequence);
            }
        }
        return result;
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByVersionId(String versionId) {
        return customerOrderWeeklyReportDao.selectByVersionId(versionId);
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByPage(Pagination pagination, String sortParam,
                                                          String queryCriteriaParam, String versionId) {
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        PageMethod.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition4WeeklyReport(sortParam, queryCriteriaParam, version);
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByCondition4WeeklyReport(String sortParam, String queryCriteriaParam,
                                                                            ConsistenceDemandForecastVersionVO version) {
        String planPeriod = version.getPlanPeriod();
        Date planPeriodFirstDay = DateUtils.moveCalendar(DateUtils
                .stringToDate(planPeriod + "01", "yyyyMMdd"), Calendar.MONTH, 1);
        Date planPeriodLastDay = DateUtils.getMonthLastDay(planPeriodFirstDay);
        String firstDay = DateUtils.dateToString(planPeriodFirstDay);
        String lastDay = DateUtils.dateToString(planPeriodLastDay);
        Date createTime = version.getCreateTime();
        String scenario = SystemHolder.getScenario();
        List<CustomerOrderWeeklyReportVO> dataList = customerOrderWeeklyReportDao.selectByCondition(sortParam,
                queryCriteriaParam, firstDay, lastDay);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        // 分组聚合
        Map<String, List<CustomerOrderWeeklyReportVO>> weeklyGroup = dataList.stream().collect(Collectors
                .groupingBy(x -> String.join(Constants.DELIMITER, x.getOemCode(),
                        x.getOemName(), x.getCustomerCode(), x.getVehicleModelCode(), x.getAccessPosition())));
        List<CustomerOrderWeeklyReportVO> newDataList = new ArrayList<>();
        for (Map.Entry<String, List<CustomerOrderWeeklyReportVO>> entry : weeklyGroup.entrySet()) {
            String key = entry.getKey();
            String[] keyArray = key.split(Constants.DELIMITER);
            List<CustomerOrderWeeklyReportVO> value = entry.getValue();
            BigDecimal customerForecastQuantity =
                    value.stream().map(CustomerOrderWeeklyReportVO::getCustomerForecastQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal demandForecastQuantity =
                    value.stream().map(CustomerOrderWeeklyReportVO::getDemandForecastQuantity).filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
            CustomerOrderWeeklyReportVO build = CustomerOrderWeeklyReportVO.builder()
                    .oemCode(keyArray[0]).oemName(keyArray[1]).customerCode(keyArray[2]).vehicleModelCode(keyArray[3])
                    .accessPosition(keyArray[4]).customerForecastQuantity(customerForecastQuantity)
                    .demandForecastQuantity(demandForecastQuantity).build();
            newDataList.add(build);
        }

        List<String> oemCodes = newDataList.stream().map(CustomerOrderWeeklyReportVO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> vehicleModelCodes = newDataList.stream().map(CustomerOrderWeeklyReportVO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());
        // 查询版本数据
        String weekDay = ipsFeign.getByCollectionCode("CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY").stream()
                .map(CollectionValueVO::getCollectionValue).findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY"));
        List<Date> timeSeries = getTimeSeries(createTime, Integer.parseInt(weekDay));
        List<String> dateList = timeSeries.stream().map(DateUtils::dateToString)
                .distinct().sorted().collect(Collectors.toList());
        Map<String, String> date2VersionIdMap = consistenceDemandForecastVersionDao.selectVersionByDateList(dateList)
                        .stream().collect(Collectors.toMap(x ->
                        DateUtils.dateToString(x.getCreateTime()), ConsistenceDemandForecastVersionVO::getId,
                        (v1, v2) -> v1));

        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodes" , oemCodes));
        List<String> mtsOemCodes = oemList.stream().filter(e -> OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode).collect(Collectors.toList());
        List<String> notMtsOemCodes = oemList.stream().filter(e -> !OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode).collect(Collectors.toList());

        List<Date> dateItemList = convert2MondayList(timeSeries);
        Date firstMonday = dateItemList.get(0);
        Date lastMonday = dateItemList.get(3);
        Date lastSunday = DateUtils.moveCalendar(lastMonday, Calendar.DAY_OF_YEAR, 6);
        Date minTime = planPeriodFirstDay.getTime() <= firstMonday.getTime() ?
                new Date(planPeriodFirstDay.getTime()) : new Date(firstMonday.getTime());
        Date maxTime = planPeriodLastDay.getTime() <= lastSunday.getTime() ?
                new Date(lastSunday.getTime()) : new Date(planPeriodLastDay.getTime());
        ExecutorService executor = Executors.newFixedThreadPool(8);
        try {
            // 查询明细数据
            CompletableFuture<Map<String, BigDecimal>> week1Future = getWeekFuture(timeSeries.get(0), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week2Future = getWeekFuture(timeSeries.get(1), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week3Future = getWeekFuture(timeSeries.get(2), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week4Future = getWeekFuture(timeSeries.get(3), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> warehouseReleaseFuture =
                    getWarehouseReleaseFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> warehouseToWarehouseFuture =
                    getWarehouseToWarehouseFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> deliveryPlanPublishedFuture =
                    getDeliveryPlanPublishedFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);

            CompletableFuture.allOf(week1Future, week2Future, week3Future, week4Future, warehouseReleaseFuture,
                    warehouseToWarehouseFuture, deliveryPlanPublishedFuture);
            Map<String, BigDecimal> week1Map = week1Future.join();
            Map<String, BigDecimal> week2Map = week2Future.join();
            Map<String, BigDecimal> week3Map = week3Future.join();
            Map<String, BigDecimal> week4Map = week4Future.join();
            List<CustomerOrderWeeklyReportSimpleVO> warehouseReleaseList = warehouseReleaseFuture.join();
            List<CustomerOrderWeeklyReportSimpleVO> warehouseToWarehouseList = warehouseToWarehouseFuture.join();
            List<CustomerOrderWeeklyReportSimpleVO> deliveryPlanPublishedList = deliveryPlanPublishedFuture.join();
            // 月发货
            Map<String, BigDecimal> warehouseReleaseMonthlyQtyMap = getQtyMapByTimePeriod(warehouseReleaseList,
                    planPeriodFirstDay, planPeriodLastDay);
            Map<String, BigDecimal> warehouseToWarehouseMonthlyQtyMap = getQtyMapByTimePeriod(warehouseToWarehouseList,
                    planPeriodFirstDay, planPeriodLastDay);
            Map<String, BigDecimal> deliveryMonthlyQtyMap = getQtyMapByTimePeriod(deliveryPlanPublishedList,
                    planPeriodFirstDay, planPeriodLastDay);
            // 周发货
            List<Map<String, BigDecimal>> warehouseReleaseWeeklyQtyMapList = new ArrayList<>();
            List<Map<String, BigDecimal>> warehouseToWarehouseWeeklyQtyMapList = new ArrayList<>();
            List<Map<String, BigDecimal>> deliveryWeeklyQtyMapList = new ArrayList<>();
            for (Date weekStartDate : dateItemList) {
                Date weekEndDate = DateUtils.moveCalendar(weekStartDate, Calendar.DAY_OF_YEAR, 6);
                Map<String, BigDecimal> warehouseReleaseWeeklyQtyMap;
                Map<String, BigDecimal> warehouseToWarehouseWeeklyQtyMap = new HashMap<>();
                if(weekStartDate.compareTo(DateUtils.getMonthFirstDay(new Date())) >= 0) {
                	//开始时间大于等于当月开始时间取中转库发货
                	warehouseReleaseWeeklyQtyMap =
                			getQtyMapByTimePeriodForOme(warehouseReleaseList, weekStartDate, weekEndDate, notMtsOemCodes);
                    warehouseToWarehouseWeeklyQtyMap =
                    		getQtyMapByTimePeriodForOme(warehouseToWarehouseList, weekStartDate, weekEndDate, mtsOemCodes);
                }else {
                	//开始时间早于当月时间,且结束时间也小于当月开始时间
                	if(weekEndDate.compareTo(DateUtils.getMonthFirstDay(new Date())) < 0) {
                		warehouseReleaseWeeklyQtyMap =
                                getQtyMapByTimePeriod(warehouseReleaseList, weekStartDate, weekEndDate);
                	}else {
                		//开始时间早于当月时间,且结束时间大于等于当月开始时间
                		Date lastMonthLastDay = DateUtils.getMonthLastDay(DateUtils.moveMonth(new Date(), -1));
                		warehouseReleaseWeeklyQtyMap =
                                getQtyMapByTimePeriod(warehouseReleaseList, weekStartDate, lastMonthLastDay);
                        Date currMonthFirstDay = DateUtils.getMonthFirstDay(new Date());
                        warehouseToWarehouseWeeklyQtyMap =
                        		getQtyMapByTimePeriodForOme(warehouseToWarehouseList, currMonthFirstDay, weekEndDate, mtsOemCodes);
                        //当前月非MTS的
                        Map<String, BigDecimal> notMtsCurrMonthMap = getQtyMapByTimePeriodForOme(warehouseReleaseList,
                        		currMonthFirstDay, weekEndDate, notMtsOemCodes);
                        for (Entry<String, BigDecimal> entrySet : notMtsCurrMonthMap.entrySet()) {
                        	String entrySetKey = entrySet.getKey();
							if(warehouseReleaseWeeklyQtyMap.containsKey(entrySetKey)) {
								BigDecimal qty = warehouseReleaseWeeklyQtyMap.get(entrySetKey);
								warehouseReleaseWeeklyQtyMap.put(entrySet.getKey(), qty.add(entrySet.getValue()));
							}
						}
                	}
                }
                warehouseReleaseWeeklyQtyMapList.add(warehouseReleaseWeeklyQtyMap);
                warehouseToWarehouseWeeklyQtyMapList.add(warehouseToWarehouseWeeklyQtyMap);
                Map<String, BigDecimal> deliveryWeeklyQtyMap =
                        getQtyMapByTimePeriod(deliveryPlanPublishedList, weekStartDate, weekEndDate);
                deliveryWeeklyQtyMapList.add(deliveryWeeklyQtyMap);
            }

            // 核心逻辑
            for (CustomerOrderWeeklyReportVO item : newDataList) {
                String oemCode = item.getOemCode();
                String vehicleModelCode = item.getVehicleModelCode();
                BigDecimal customerForecastQuantity = item.getCustomerForecastQuantity();
                String unionKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode);

                BigDecimal week1ForecastQuantity = week1Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek1ForecastQuantity(week1ForecastQuantity);
                item.setWeek1ForecastDeviation(null);
                BigDecimal week2ForecastQuantity = week2Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek2ForecastQuantity(week2ForecastQuantity);
                item.setWeek2ForecastDeviation(null);
                BigDecimal week3ForecastQuantity = week3Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek3ForecastQuantity(week3ForecastQuantity);
                item.setWeek3ForecastDeviation(null);
                BigDecimal week4ForecastQuantity = week4Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek4ForecastQuantity(week4ForecastQuantity);
                item.setWeek4ForecastDeviation(null);
                // 当前月取中转库，历史月取仓库收发货记录
                BigDecimal monthlyShippedQty;
                if (DateUtils.getMonthFirstDay(new Date()).compareTo(planPeriodFirstDay) <= 0) {
                    // 判断当前主机厂业务类型为MTS取中转库，非MTS取仓库
                	if(mtsOemCodes.contains(oemCode)) {
                		monthlyShippedQty = warehouseToWarehouseMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                	}else {
                		monthlyShippedQty = warehouseReleaseMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                	}
                }else {
                	monthlyShippedQty = warehouseReleaseMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                }
                item.setMonthlyShippedQuantity(monthlyShippedQty);
                BigDecimal monthlyDeliveryQty = deliveryMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setMonthlyDeliveryQuantity(monthlyDeliveryQty);

                processWeekData(0, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek1ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek1DeliveryQuantity);
                processWeekData(1, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek2ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek2DeliveryQuantity);
                processWeekData(2, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek3ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek3DeliveryQuantity);
                processWeekData(3, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek4ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek4DeliveryQuantity);

                if (customerForecastQuantity != null && customerForecastQuantity.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal week1ForecastDeviation = week1ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek1ForecastDeviation(week1ForecastDeviation);
                    BigDecimal week2ForecastDeviation = week2ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek2ForecastDeviation(week2ForecastDeviation);
                    BigDecimal week3ForecastDeviation = week3ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek3ForecastDeviation(week3ForecastDeviation);
                    BigDecimal week4ForecastDeviation = week4ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek4ForecastDeviation(week4ForecastDeviation);
                    BigDecimal forecastAchievementRate = item.getMonthlyDeliveryQuantity()
                            .add(item.getMonthlyShippedQuantity())
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setForecastAchievementRate(forecastAchievementRate);
                }
            }
            // 增加合计行
            return groupAndAggregation(newDataList);
        } finally {
            shutdownExecutorService(executor);
        }
    }

    private void processWeekData(
            int index, String unionKey,
            List<Map<String, BigDecimal>> warehouseReleaseWeeklyQtyMapList,
            List<Map<String, BigDecimal>> warehouseToWarehouseWeeklyQtyMapList,
            List<Map<String, BigDecimal>> deliveryWeeklyQtyMapList,
            CustomerOrderWeeklyReportVO item,
            BiConsumer<CustomerOrderWeeklyReportVO, BigDecimal> shippedSetter,
            BiConsumer<CustomerOrderWeeklyReportVO, BigDecimal> deliverySetter
    ) {
        BigDecimal releaseQty = warehouseReleaseWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);
        BigDecimal toWarehouseQty = warehouseToWarehouseWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);
        BigDecimal shippedQty = releaseQty.add(toWarehouseQty);
        BigDecimal deliveryQty = deliveryWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);

        shippedSetter.accept(item, shippedQty);
        deliverySetter.accept(item, deliveryQty);
    }


    public static Map<String, BigDecimal> getQtyMapByTimePeriod(
            List<CustomerOrderWeeklyReportSimpleVO> simpleList, Date startTime, Date endTime) {
        return simpleList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode())
                        && StringUtils.isNotBlank(x.getVehicleModelCode())
                        && Objects.nonNull(x.getQuantity())
                        && startTime.getTime() <= x.getTimePoint().getTime()
                        && x.getTimePoint().getTime() <= endTime.getTime()).collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER,
                                x.getOemCode(), x.getVehicleModelCode()), Collectors.reducing(BigDecimal.ZERO,
                                CustomerOrderWeeklyReportSimpleVO::getQuantity, BigDecimal::add)));
    }

    public static Map<String, BigDecimal> getQtyMapByTimePeriodForOme(
            List<CustomerOrderWeeklyReportSimpleVO> simpleList, Date startTime, Date endTime,
            List<String> oemCodes) {
        return simpleList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode())
                		&& oemCodes.contains(x.getOemCode())
                        && StringUtils.isNotBlank(x.getVehicleModelCode())
                        && Objects.nonNull(x.getQuantity())
                        && startTime.getTime() <= x.getTimePoint().getTime()
                        && x.getTimePoint().getTime() <= endTime.getTime()).collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER,
                                x.getOemCode(), x.getVehicleModelCode()), Collectors.reducing(BigDecimal.ZERO,
                                CustomerOrderWeeklyReportSimpleVO::getQuantity, BigDecimal::add)));
    }

    public static List<Date> convert2MondayList(List<Date> timeSeries) {
        return timeSeries.stream().map(x ->
                MiscellaneousUtils.localDate2Date(MiscellaneousUtils.date2LocalDate(x).with(DayOfWeek.MONDAY)))
                .collect(Collectors.toList());
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getWarehouseReleaseFuture(Date shippedStartDate,
                                                                                                 Date shippedEndDate,
                                                                                                 List<String> oemCodes,
                                                                                                 List<String> vehicleModelCodes,
                                                                                                 String scenario,
                                                                                                 ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectWarehouseReleaseRecordList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getWarehouseToWarehouseFuture(Date shippedStartDate,
                                                                                                     Date shippedEndDate,
                                                                                                     List<String> oemCodes,
                                                                                                     List<String> vehicleModelCodes,
                                                                                                     String scenario,
                                                                                                     ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectWarehouseToWarehouseRecordList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getDeliveryPlanPublishedFuture(Date shippedStartDate,
                                                                                                     Date shippedEndDate,
                                                                                                     List<String> oemCodes,
                                                                                                     List<String> vehicleModelCodes,
                                                                                                     String scenario,
                                                                                                     ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectDeliveryPlanPublishedList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    /**
     * 分组聚合增加合计行
     *
     * @param dataList 原始数据
     * @return java.util.List<com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO>
     */
    private static List<CustomerOrderWeeklyReportVO> groupAndAggregation(List<CustomerOrderWeeklyReportVO> dataList) {
        // 1. 按类别分组并计算每组总和
        Map<String, BigDecimal> categorySums = dataList.stream()
                .collect(Collectors.groupingBy(CustomerOrderWeeklyReportVO::getCustomerCode,
                        Collectors.reducing(BigDecimal.ZERO, CustomerOrderWeeklyReportVO::getCustomerForecastQuantity,
                                BigDecimal::add)));
        Map<String, List<CustomerOrderWeeklyReportVO>> collect = dataList.stream().collect(Collectors
                .groupingBy(CustomerOrderWeeklyReportVO::getCustomerCode));
        // 2. 按类别排序（可自定义排序规则）
        List<Map.Entry<String, BigDecimal>> sortedCategories = new ArrayList<>(categorySums.entrySet());
        sortedCategories.sort(Map.Entry.<String, BigDecimal>comparingByValue().reversed());
        // 3. 构建结果列表：包含原始数据和每组的合计
        List<CustomerOrderWeeklyReportVO> result = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : sortedCategories) {
            String category = entry.getKey();
            BigDecimal sumQty = entry.getValue();
            List<CustomerOrderWeeklyReportVO> items = collect.get(category);
            items.sort(Comparator.comparing(CustomerOrderWeeklyReportVO::getCustomerForecastQuantity).reversed());
            // 添加该类别的原始数据（按原始顺序）
            result.addAll(items);
            CustomerOrderWeeklyReportVO summary = new CustomerOrderWeeklyReportVO();
            summary.setCustomerCode(category + "合计");
            summary.setCustomerForecastQuantity(sumQty);
            BigDecimal totalDemandForecastQuantity = BigDecimal.ZERO;
            BigDecimal week1ForecastQuantity = BigDecimal.ZERO;
            BigDecimal week2ForecastQuantity = BigDecimal.ZERO;
            BigDecimal week3ForecastQuantity = BigDecimal.ZERO;
            BigDecimal week4ForecastQuantity = BigDecimal.ZERO;
            BigDecimal week1ShippedQuantity = BigDecimal.ZERO;
            BigDecimal week2ShippedQuantity = BigDecimal.ZERO;
            BigDecimal week3ShippedQuantity = BigDecimal.ZERO;
            BigDecimal week4ShippedQuantity = BigDecimal.ZERO;
            BigDecimal monthlyShippedQuantity = BigDecimal.ZERO;
            BigDecimal week1DeliveryQuantity = BigDecimal.ZERO;
            BigDecimal week2DeliveryQuantity = BigDecimal.ZERO;
            BigDecimal week3DeliveryQuantity = BigDecimal.ZERO;
            BigDecimal week4DeliveryQuantity = BigDecimal.ZERO;
            BigDecimal monthlyDeliveryQuantity = BigDecimal.ZERO;
            for (CustomerOrderWeeklyReportVO item : items) {
                totalDemandForecastQuantity = totalDemandForecastQuantity.add(Objects.isNull(item.getDemandForecastQuantity())
                        ? BigDecimal.ZERO : item.getDemandForecastQuantity());
                week1ForecastQuantity = week1ForecastQuantity.add(Objects.isNull(item.getWeek1ForecastQuantity())
                         ? BigDecimal.ZERO : item.getWeek1ForecastQuantity());
                week2ForecastQuantity = week2ForecastQuantity.add(Objects.isNull(item.getWeek2ForecastQuantity())
                         ? BigDecimal.ZERO : item.getWeek2ForecastQuantity());
                week3ForecastQuantity = week3ForecastQuantity.add(Objects.isNull(item.getWeek3ForecastQuantity())
                         ? BigDecimal.ZERO : item.getWeek3ForecastQuantity());
                week4ForecastQuantity = week4ForecastQuantity.add(Objects.isNull(item.getWeek4ForecastQuantity())
                         ? BigDecimal.ZERO : item.getWeek4ForecastQuantity());
                week1ShippedQuantity = week1ShippedQuantity.add(Objects.isNull(item.getWeek1ShippedQuantity())
                         ? BigDecimal.ZERO : item.getWeek1ShippedQuantity());
                week2ShippedQuantity = week2ShippedQuantity.add(Objects.isNull(item.getWeek2ShippedQuantity())
                         ? BigDecimal.ZERO : item.getWeek2ShippedQuantity());
                week3ShippedQuantity = week3ShippedQuantity.add(Objects.isNull(item.getWeek3ShippedQuantity())
                         ? BigDecimal.ZERO : item.getWeek3ShippedQuantity());
                week4ShippedQuantity = week4ShippedQuantity.add(Objects.isNull(item.getWeek4ShippedQuantity())
                         ? BigDecimal.ZERO : item.getWeek4ShippedQuantity());
                monthlyShippedQuantity = monthlyShippedQuantity.add(Objects.isNull(item.getMonthlyShippedQuantity())
                         ? BigDecimal.ZERO : item.getMonthlyShippedQuantity());
                week1DeliveryQuantity = week1DeliveryQuantity.add(Objects.isNull(item.getWeek1DeliveryQuantity())
                         ? BigDecimal.ZERO : item.getWeek1DeliveryQuantity());
                week2DeliveryQuantity = week2DeliveryQuantity.add(Objects.isNull(item.getWeek2DeliveryQuantity())
                         ? BigDecimal.ZERO : item.getWeek2DeliveryQuantity());
                week3DeliveryQuantity = week3DeliveryQuantity.add(Objects.isNull(item.getWeek3DeliveryQuantity())
                         ? BigDecimal.ZERO : item.getWeek3DeliveryQuantity());
                week4DeliveryQuantity = week4DeliveryQuantity.add(Objects.isNull(item.getWeek4DeliveryQuantity())
                         ? BigDecimal.ZERO : item.getWeek4DeliveryQuantity());
                monthlyDeliveryQuantity = monthlyDeliveryQuantity.add(Objects.isNull(item.getMonthlyDeliveryQuantity())
                        ? BigDecimal.ZERO : item.getMonthlyDeliveryQuantity());
            }
            summary.setDemandForecastQuantity(totalDemandForecastQuantity);
            summary.setWeek1ForecastQuantity(week1ForecastQuantity);
            summary.setWeek2ForecastQuantity(week2ForecastQuantity);
            summary.setWeek3ForecastQuantity(week3ForecastQuantity);
            summary.setWeek4ForecastQuantity(week4ForecastQuantity);

            summary.setWeek1ShippedQuantity(week1ShippedQuantity);
            summary.setWeek2ShippedQuantity(week2ShippedQuantity);
            summary.setWeek3ShippedQuantity(week3ShippedQuantity);
            summary.setWeek4ShippedQuantity(week4ShippedQuantity);
            summary.setMonthlyShippedQuantity(monthlyShippedQuantity);

            summary.setWeek1DeliveryQuantity(week1DeliveryQuantity);
            summary.setWeek2DeliveryQuantity(week2DeliveryQuantity);
            summary.setWeek3DeliveryQuantity(week3DeliveryQuantity);
            summary.setWeek4DeliveryQuantity(week4DeliveryQuantity);
            summary.setMonthlyDeliveryQuantity(monthlyDeliveryQuantity);
            summary.setStatisticsRow(true);
            // 添加该类别的合计项（标记为合计）
            result.add(summary);
        }
        return result;
    }

    public static final String IO_EXECUTOR = "ioExecutor";
    /**
     * 关闭线程池
     *
     * @param executor 多线程执行器
     */
    private void shutdownExecutorService(ExecutorService executor) {
        // Helper method to shut down executor services (same as before)
        if (executor != null && !executor.isShutdown()) {
            try {
                log.info("Shutting down {}...", IO_EXECUTOR);
                executor.shutdown();
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("{} did not terminate in 10 seconds, forcing shutdown.", IO_EXECUTOR);
                    executor.shutdownNow();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.error("{} did not terminate even after shutdownNow.", IO_EXECUTOR);
                    }
                }
                log.info("{} shut down successfully.", IO_EXECUTOR);
            } catch (InterruptedException ie) {
                log.error("Shutdown of {} interrupted.", IO_EXECUTOR, ie);
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 获取Future
     *
     * @param dateItem           日期
     * @param date2VersionIdMap  日期版本ID映射
     * @param planPeriodFirstDay 计划周期第一天
     * @param planPeriodLastDay  计划周期最后一天
     * @param scenario           场景名
     * @param executor           执行器
     * @return java.util.concurrent.CompletableFuture<java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private CompletableFuture<Map<String, BigDecimal>> getWeekFuture(Date dateItem, Map<String, String> date2VersionIdMap,
                                                                     String planPeriodFirstDay,
                                                                     String planPeriodLastDay,
                                                                     String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            String date = DateUtils.dateToString(dateItem);
            if (!date2VersionIdMap.containsKey(date)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            String versionId = date2VersionIdMap.get(date);
            Map<String, BigDecimal> dataMap = customerOrderWeeklyReportDao.selectByCondition(null,
                    MiscellaneousUtils.assembleEqCondition("versionId", versionId), planPeriodFirstDay,
                            planPeriodLastDay).stream().collect(Collectors.groupingBy(x ->
                            String.join(Constants.DELIMITER, x.getOemCode(), x.getVehicleModelCode()),
                            Collectors.reducing(BigDecimal.ZERO,
                                    CustomerOrderWeeklyReportVO::getCustomerForecastQuantity, BigDecimal::add)));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataMap;
        }, executor);
    }

    /**
     * 获取周次
     *
     * @param localDate 日期
     * @return int 周次
     */
    public static int getWeekSequence(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            localDate = LocalDate.now();
        }
        return localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
    }

    /**
     * 根据周几，输出后四周周一日期
     *
     * @param fromDate 起始日期
     * @param weekDay  周几
     * @return java.util.List<java.util.Date>
     */
    public static List<Date> getTimeSeries(Date fromDate, int weekDay) {
        // 转换为DayOfWeek枚举
        DayOfWeek targetDayOfWeek = DayOfWeek.of(weekDay);
        // 获取当前日期
        LocalDate currentDate = MiscellaneousUtils.date2LocalDate(fromDate);
        // 获取当前周指定星期的日期
        LocalDate currentWeekTargetDay = currentDate.with(targetDayOfWeek);
        // 如果当前日期已经过了指定的星期，则获取下一周的日期
        currentWeekTargetDay = currentWeekTargetDay.plusWeeks(1);
        // 创建日期列表
        List<Date> dateList = new ArrayList<>();
        // 添加当前周的日期
        dateList.add(Date.from(currentWeekTargetDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        // 添加接下来三周的相同星期的日期
        for (int i = 1; i <= 3; i++) {
            LocalDate localDate = currentWeekTargetDay.plusWeeks(i);
            dateList.add(Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        return dateList;
    }

    @SneakyThrows
    @Override
    public void exportCustomerOrderWeeklyReport(String sortParam, String queryCriteriaParam, String versionId,
                                                HttpServletResponse response) {
        // 查询数据
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        List<CustomerOrderWeeklyReportVO> dataList = this.selectByCondition4WeeklyReport(sortParam, queryCriteriaParam, version);
        List<String> weeks = selectWeekSequence(versionId);
        // 创建合并区域列表
        List<CellRangeAddress> mergeRegions = new ArrayList<>();

        List<List<String>> headers = initHeaders(weeks);
        List<List<Object>> data = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            CustomerOrderWeeklyReportVO item = dataList.get(i);
            List<Object> rowData = getRowData(item);
            data.add(rowData);
        }
        /*int startRow = 1; // 数据从第2行开始（索引1）
        boolean sameOemCode;
        int oemCodeIndex = 0;
        boolean sameOemName;
        int oemNameIndex = 0;
        boolean sameCustomerCode;
        int customerCodeIndex = 0;
        String lastOemCode = "";
        String lastOemName = "";
        String lastCustomerCode = "";
        Map<Integer, Integer> mergeOemCodeRegions = new HashMap<>();
        Map<Integer, Integer> mergeOemNameRegions = new HashMap<>();
        Map<Integer, Integer> mergeCustomerCodeRegions = new HashMap<>();
        // 遍历数据，为每个条目创建三行数据（客户预测、业务预测、同期销量）
        for (int i = 0; i < dataList.size(); i++) {
            CustomerOrderWeeklyReportVO item = dataList.get(i);
            List<Object> rowData = getRowData(item);
            data.add(rowData);

            if (i + 1 <= startRow) {
                lastOemCode = item.getOemCode() == null ? "" : item.getOemCode();
                lastOemName = item.getOemName() == null ? "" : item.getOemName();
                lastCustomerCode = item.getCustomerCode() == null ? "" : item.getCustomerCode();
                continue;
            }
            sameOemCode = lastOemCode.equals(item.getOemCode());
            sameOemName = lastOemName.equals(item.getOemName());
            sameCustomerCode = lastCustomerCode.equals(item.getCustomerCode());
            if (sameOemCode) {
                mergeOemCodeRegions.put(oemCodeIndex, i + 1);
            } else {
                oemCodeIndex = i + 1;
            }
            if (sameOemCode && sameOemName) {
                mergeOemNameRegions.put(oemNameIndex, i + 1);
            } else {
                oemNameIndex = i + 1;
            }
            if (sameOemCode && sameOemName && sameCustomerCode) {
                mergeCustomerCodeRegions.put(customerCodeIndex, i + 1);
            } else {
                customerCodeIndex = i + 1;
            }
            lastOemCode = item.getOemCode() == null ? "" : item.getOemCode();
            lastOemName = item.getOemName() == null ? "" : item.getOemName();
            lastCustomerCode = item.getCustomerCode() == null ? "" : item.getCustomerCode();
        }
        for (Entry<Integer, Integer> entry : mergeOemCodeRegions.entrySet()) {
            Integer startRow1 = entry.getKey();
            Integer endRow1 = entry.getValue();
            mergeRegions.add(new CellRangeAddress(startRow1, endRow1, 0, 0)); // 主机厂编码
        }
        for (Entry<Integer, Integer> entry : mergeOemNameRegions.entrySet()) {
            Integer startRow1 = entry.getKey();
            Integer endRow1 = entry.getValue();
            mergeRegions.add(new CellRangeAddress(startRow1, endRow1, 1, 1)); // 主机厂名称
        }
        for (Entry<Integer, Integer> entry : mergeCustomerCodeRegions.entrySet()) {
            Integer startRow1 = entry.getKey();
            Integer endRow1 = entry.getValue();
            mergeRegions.add(new CellRangeAddress(startRow1, endRow1, 2, 2)); // 客户简称
        }*/

        // 设置响应头
        EasyExcelUtil.initResponse(response, "客户订单周报");

        boolean hasData = CollectionUtils.isNotEmpty(data);
        ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream()).head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler());
        // 创建头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setWrapped(true); // 设置自动换行

        // 创建内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 设置自动换行
        // 创建策略
        HorizontalCellStyleStrategy styleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        excelWriterBuilder.registerWriteHandler(styleStrategy);
        if (hasData) {
            excelWriterBuilder.registerWriteHandler(new RangeMergeStrategy(mergeRegions));
        }
        // 设置单元格合并
        excelWriterBuilder.sheet("Sheet1").doWrite(data);
    }

    @SuppressWarnings("unused")
    private boolean isSummaryRow(List<Object> row) {
        // 假设合计行的第三个元素（C列）包含“合计”字样
        if (row.size() > 2 && row.get(2) instanceof String) {
            String customerCode = (String) row.get(2);
            return customerCode.contains("合计");
        }
        return false;
    }

    private static List<Object> getRowData(CustomerOrderWeeklyReportVO item) {
        List<Object> rowData = new ArrayList<>();
        rowData.add(item.getOemCode());
        rowData.add(item.getCustomerCode());
        rowData.add(item.getOemName());
        rowData.add(item.getVehicleModelCode());
        rowData.add(item.getCustomerForecastQuantity());
        rowData.add(item.getDemandForecastQuantity());
        rowData.add(item.getWeek1ForecastQuantity());
        rowData.add(toPercentage(item.getWeek1ForecastDeviation(),0));
        rowData.add(item.getWeek2ForecastQuantity());
        rowData.add(toPercentage(item.getWeek2ForecastDeviation(), 0));
        rowData.add(item.getWeek3ForecastQuantity());
        rowData.add(toPercentage(item.getWeek3ForecastDeviation(), 0));
        rowData.add(item.getWeek4ForecastQuantity());
        rowData.add(toPercentage(item.getWeek4ForecastDeviation(), 0));
        rowData.add("已发：" + item.getMonthlyShippedQuantity() + "\n待发：" + item.getMonthlyDeliveryQuantity());
        rowData.add(toPercentage(item.getForecastAchievementRate(), 0));
        rowData.add("已发：" + item.getWeek1ShippedQuantity()/* + "\n待发：" + item.getWeek1DeliveryQuantity()*/);
        rowData.add("已发：" + item.getWeek2ShippedQuantity()/* + "\n待发：" + item.getWeek2DeliveryQuantity()*/);
        rowData.add("已发：" + item.getWeek3ShippedQuantity()/* + "\n待发：" + item.getWeek3DeliveryQuantity()*/);
        rowData.add("已发：" + item.getWeek4ShippedQuantity()/* + "\n待发：" + item.getWeek4DeliveryQuantity()*/);
        rowData.add(item.getAccessPosition());
        return rowData;
    }

    private List<List<String>> initHeaders(List<String> weeks) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Lists.newArrayList("主机厂编码", "主机厂编码"));
        headers.add(Lists.newArrayList("客户简称", "客户简称"));
        headers.add(Lists.newArrayList("主机厂名称", "主机厂名称"));
        headers.add(Lists.newArrayList("车型", "车型"));
        headers.add(Lists.newArrayList("客户预测", "客户预测"));
        headers.add(Lists.newArrayList("业务预测", "业务预测"));
        for (String week : weeks) {
            headers.add(Lists.newArrayList("客户月预测更新", week));
            headers.add(Lists.newArrayList("客户月预测更新", "变动率"));
        }
        headers.add(Lists.newArrayList("月发货", "月合计"));
        headers.add(Lists.newArrayList("月发货", "预测达成率"));
        for (String week : weeks) {
            headers.add(Lists.newArrayList("周发货量", week));
        }
        headers.add(Lists.newArrayList("取数装车位置", "取数装车位置"));
        return headers;
    }

    public static String toPercentage(BigDecimal number, int decimalPlaces) {
        if (Objects.isNull(number)) {
            return null;
        }
        double num = number.doubleValue();
        // 构建格式字符串，如"#0.00%"表示保留两位小数
        StringBuilder pattern = new StringBuilder("#0.");
        for (int i = 0; i < decimalPlaces; i++) {
            pattern.append("0");
        }
        pattern.append("%");

        DecimalFormat df = new DecimalFormat(pattern.toString());
        return df.format(num);
    }

}