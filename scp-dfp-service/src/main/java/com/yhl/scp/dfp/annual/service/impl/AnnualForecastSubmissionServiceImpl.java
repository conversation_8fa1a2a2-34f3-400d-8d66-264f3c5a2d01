package com.yhl.scp.dfp.annual.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.annual.convertor.AnnualForecastSubmissionConvertor;
import com.yhl.scp.dfp.annual.domain.entity.AnnualForecastSubmissionDO;
import com.yhl.scp.dfp.annual.domain.service.AnnualForecastSubmissionDomainService;
import com.yhl.scp.dfp.annual.dto.AnnualForecastSubmissionDTO;
import com.yhl.scp.dfp.annual.dto.AnnualForecastSubmissionDetailDTO;
import com.yhl.scp.dfp.annual.infrastructure.dao.AnnualForecastSubmissionDao;
import com.yhl.scp.dfp.annual.infrastructure.dao.AnnualForecastSubmissionDetailDao;
import com.yhl.scp.dfp.annual.infrastructure.po.AnnualForecastSubmissionPO;
import com.yhl.scp.dfp.annual.service.AnnualForecastSubmissionDetailService;
import com.yhl.scp.dfp.annual.service.AnnualForecastSubmissionService;
import com.yhl.scp.dfp.annual.vo.AnnualForecastSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.sale.service.SaleOrganizeService;
import com.yhl.scp.dfp.sale.vo.SaleOrganizeVO;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.production.vo.NewProductionOrganizationVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AnnualForecastSubmissionServiceImpl extends AbstractService implements AnnualForecastSubmissionService {

    @Resource
    private AnnualForecastSubmissionDao annualForecastSubmissionDao;

    @Resource
    private AnnualForecastSubmissionDomainService annualForecastSubmissionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OemService oemService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SaleOrganizeService saleOrganizeService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private AnnualForecastSubmissionDetailService annualForecastSubmissionDetailService;

    @Resource
    private AnnualForecastSubmissionDetailDao annualForecastSubmissionDetailDao;

    @Override
    public BaseResponse<Void> doCreate(AnnualForecastSubmissionDTO annualForecastSubmissionDTO) {
        // 0.数据转换
        AnnualForecastSubmissionDO annualForecastSubmissionDO = AnnualForecastSubmissionConvertor.INSTANCE.dto2Do(annualForecastSubmissionDTO);
        AnnualForecastSubmissionPO annualForecastSubmissionPO = AnnualForecastSubmissionConvertor.INSTANCE.dto2Po(annualForecastSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        annualForecastSubmissionDomainService.validation(annualForecastSubmissionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(annualForecastSubmissionPO);
        annualForecastSubmissionDao.insert(annualForecastSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(AnnualForecastSubmissionDTO annualForecastSubmissionDTO) {
        // 0.数据转换
        AnnualForecastSubmissionDO annualForecastSubmissionDO = AnnualForecastSubmissionConvertor.INSTANCE.dto2Do(annualForecastSubmissionDTO);
        AnnualForecastSubmissionPO annualForecastSubmissionPO = AnnualForecastSubmissionConvertor.INSTANCE.dto2Po(annualForecastSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        annualForecastSubmissionDomainService.validation(annualForecastSubmissionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(annualForecastSubmissionPO);
        annualForecastSubmissionDao.update(annualForecastSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<AnnualForecastSubmissionDTO> list) {
        List<AnnualForecastSubmissionPO> newList = AnnualForecastSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        annualForecastSubmissionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<AnnualForecastSubmissionDTO> list) {
        List<AnnualForecastSubmissionPO> newList = AnnualForecastSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        annualForecastSubmissionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return annualForecastSubmissionDao.deleteBatch(idList);
        }
        return annualForecastSubmissionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public AnnualForecastSubmissionVO selectByPrimaryKey(String id) {
        AnnualForecastSubmissionPO po = annualForecastSubmissionDao.selectByPrimaryKey(id);
        return AnnualForecastSubmissionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "ANNUAL_FORECAST_SUBMISSION")
    public List<AnnualForecastSubmissionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "ANNUAL_FORECAST_SUBMISSION")
    public List<AnnualForecastSubmissionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<AnnualForecastSubmissionVO> dataList = annualForecastSubmissionDao.selectByCondition(sortParam, queryCriteriaParam);
        AnnualForecastSubmissionServiceImpl target = springBeanUtils.getBean(AnnualForecastSubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<AnnualForecastSubmissionVO> selectByParams(Map<String, Object> params) {
        List<AnnualForecastSubmissionPO> list = annualForecastSubmissionDao.selectByParams(params);
        return AnnualForecastSubmissionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<AnnualForecastSubmissionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    @SneakyThrows
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "年度预测提报模板");
        int submissionYear = DateUtils.getYearByDate(new Date());
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("子公司*"));
        headers.add(Collections.singletonList("市场类型"));
        headers.add(Collections.singletonList("客户编码*"));
        headers.add(Collections.singletonList("产品编码*"));
        headers.add(Collections.singletonList("零件号"));
        headers.add(Collections.singletonList("内部车型代号（车型编码）*"));
        headers.add(Collections.singletonList("生产事业部"));
        headers.add(Collections.singletonList("生产工厂*"));
        headers.add(Collections.singletonList("一月销量（片）"));
        headers.add(Collections.singletonList("二月销量（片）"));
        headers.add(Collections.singletonList("三月销量（片）"));
        headers.add(Collections.singletonList("四月销量（片）"));
        headers.add(Collections.singletonList("五月销量（片）"));
        headers.add(Collections.singletonList("六月销量（片）"));
        headers.add(Collections.singletonList("七月销量（片）"));
        headers.add(Collections.singletonList("八月销量（片）"));
        headers.add(Collections.singletonList("九月销量（片）"));
        headers.add(Collections.singletonList("十月销量（片）"));
        headers.add(Collections.singletonList("十一月销量(片)"));
        headers.add(Collections.singletonList("十二月销量(片)"));
        for (int i = 2; i <= 5; i++) {
            headers.add(Collections.singletonList((submissionYear + i) + "年数量（片）"));
        }
        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ANNUAL_FORECAST_SUBMISSION.getCode();
    }

    @Override
    public List<AnnualForecastSubmissionVO> invocation(List<AnnualForecastSubmissionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        String submissionYear = extMap.get("submissionYear");
        String importFlag = extMap.get("importFlag");
        Integer actualSubmissionYear;
        if (StringUtils.isNotBlank(submissionYear)) {
            actualSubmissionYear = Integer.valueOf(submissionYear);
        } else {
            actualSubmissionYear = DateUtils.getYearByDate(new Date());
        }
        // 查询历史年度提报信息
        List<AnnualForecastSubmissionPO> existAnnualForecastSubmissions = annualForecastSubmissionDao.selectByParams(new HashMap<>(2));
        Map<String, String> existAnnualForecastSubmissionMap = existAnnualForecastSubmissions.stream().collect(
                Collectors.toMap(x -> StringUtils.joinWith("_", x.getSaleOrgId(), x.getCustomerCode(), x.getProductCode(), x.getVehicleModelCode(), x.getProductionOrgCode(),x.getImportFlag()),
                        AnnualForecastSubmissionPO::getId, (k1, k2) -> k1));
        List<AnnualForecastSubmissionDTO> insertSubmissionDTOS = Lists.newArrayList();
        List<AnnualForecastSubmissionDTO> updateSubmissionDTOS = Lists.newArrayList();
        List<AnnualForecastSubmissionDetailDTO> insertSubmissionDetailDTOS = Lists.newArrayList();
        // 查询销售组织
        List<SaleOrganizeVO> saleOrganizeVOS = saleOrganizeService.selectAll();
        Map<String, String> saleOrganizeMap = saleOrganizeVOS.stream().collect(Collectors.toMap(SaleOrganizeVO::getSaleOrgCode, SaleOrganizeVO::getId, (v1, v2) -> v1));
        validateImportData(data, saleOrganizeMap);
        for (Map<Integer, String> rowData : data) {
            String saleOrgCode = rowData.get(0);
            String saleOrgId = saleOrganizeMap.get(saleOrgCode);
            String customerCode = rowData.get(2);
            String productCode = rowData.get(3);
            String vehicleModelCode = rowData.get(5);
            String productionOrgCode = rowData.get(7);
            String mainKey = StringUtils.joinWith("_", saleOrgId, customerCode, productCode, vehicleModelCode, productionOrgCode, importFlag);
            String oldId = existAnnualForecastSubmissionMap.get(mainKey);
            AnnualForecastSubmissionDTO annualForecastSubmissionDTO = new AnnualForecastSubmissionDTO();
            if (StringUtils.isBlank(oldId)) {
                String newId = UUIDUtil.getUUID();
                annualForecastSubmissionDTO.setId(newId);
                annualForecastSubmissionDTO.setSaleOrgId(saleOrgId);
                annualForecastSubmissionDTO.setMarketType(rowData.get(1));
                annualForecastSubmissionDTO.setProductCode(productCode);
                annualForecastSubmissionDTO.setPartNumber(rowData.get(4));
                annualForecastSubmissionDTO.setVehicleModelCode(vehicleModelCode);
                annualForecastSubmissionDTO.setProductionDivision(rowData.get(6));
                annualForecastSubmissionDTO.setProductionOrgCode(productionOrgCode);
                annualForecastSubmissionDTO.setImportFlag(importFlag);
                annualForecastSubmissionDTO.setCustomerCode(customerCode);
                insertSubmissionDTOS.add(annualForecastSubmissionDTO);
            } else {
                AnnualForecastSubmissionVO oldAnnualForecastSubmissionVO = this.selectByPrimaryKey(oldId);
                BeanUtils.copyProperties(oldAnnualForecastSubmissionVO, annualForecastSubmissionDTO);
                annualForecastSubmissionDTO.setSaleOrgId(saleOrgId);
                annualForecastSubmissionDTO.setMarketType(rowData.get(1));
                annualForecastSubmissionDTO.setProductCode(productCode);
                annualForecastSubmissionDTO.setPartNumber(rowData.get(4));
                annualForecastSubmissionDTO.setVehicleModelCode(vehicleModelCode);
                annualForecastSubmissionDTO.setProductionDivision(rowData.get(6));
                annualForecastSubmissionDTO.setProductionOrgCode(productionOrgCode);
                annualForecastSubmissionDTO.setImportFlag(importFlag);
                annualForecastSubmissionDTO.setCustomerCode(customerCode);
                updateSubmissionDTOS.add(annualForecastSubmissionDTO);
                annualForecastSubmissionDetailService.deleteBySubmissionId(oldId, actualSubmissionYear);
            }
            AnnualForecastSubmissionDetailDTO annualForecastSubmissionDetailDTO = new AnnualForecastSubmissionDetailDTO();
            annualForecastSubmissionDetailDTO.setSubmissionId(annualForecastSubmissionDTO.getId());
            annualForecastSubmissionDetailDTO.setSubmissionYear(actualSubmissionYear);
            annualForecastSubmissionDetailDTO.setMonth1Quantity(StringUtils.isBlank(rowData.get(8)) ? null : new BigDecimal(rowData.get(8)));
            annualForecastSubmissionDetailDTO.setMonth2Quantity(StringUtils.isBlank(rowData.get(9)) ? null : new BigDecimal(rowData.get(9)));
            annualForecastSubmissionDetailDTO.setMonth3Quantity(StringUtils.isBlank(rowData.get(10)) ? null : new BigDecimal(rowData.get(10)));
            annualForecastSubmissionDetailDTO.setMonth4Quantity(StringUtils.isBlank(rowData.get(11)) ? null : new BigDecimal(rowData.get(11)));
            annualForecastSubmissionDetailDTO.setMonth5Quantity(StringUtils.isBlank(rowData.get(12)) ? null : new BigDecimal(rowData.get(12)));
            annualForecastSubmissionDetailDTO.setMonth6Quantity(StringUtils.isBlank(rowData.get(13)) ? null : new BigDecimal(rowData.get(13)));
            annualForecastSubmissionDetailDTO.setMonth7Quantity(StringUtils.isBlank(rowData.get(14)) ? null : new BigDecimal(rowData.get(14)));
            annualForecastSubmissionDetailDTO.setMonth8Quantity(StringUtils.isBlank(rowData.get(15)) ? null : new BigDecimal(rowData.get(15)));
            annualForecastSubmissionDetailDTO.setMonth9Quantity(StringUtils.isBlank(rowData.get(16)) ? null : new BigDecimal(rowData.get(16)));
            annualForecastSubmissionDetailDTO.setMonth10Quantity(StringUtils.isBlank(rowData.get(17)) ? null : new BigDecimal(rowData.get(17)));
            annualForecastSubmissionDetailDTO.setMonth11Quantity(StringUtils.isBlank(rowData.get(18)) ? null : new BigDecimal(rowData.get(18)));
            annualForecastSubmissionDetailDTO.setMonth12Quantity(StringUtils.isBlank(rowData.get(19)) ? null : new BigDecimal(rowData.get(19)));
            annualForecastSubmissionDetailDTO.setYear2Quantity(StringUtils.isBlank(rowData.get(20)) ? null : new BigDecimal(rowData.get(20)));
            annualForecastSubmissionDetailDTO.setYear3Quantity(StringUtils.isBlank(rowData.get(21)) ? null : new BigDecimal(rowData.get(21)));
            annualForecastSubmissionDetailDTO.setYear4Quantity(StringUtils.isBlank(rowData.get(21)) ? null : new BigDecimal(rowData.get(22)));
            annualForecastSubmissionDetailDTO.setYear5Quantity(StringUtils.isBlank(rowData.get(23)) ? null : new BigDecimal(rowData.get(23)));
            insertSubmissionDetailDTOS.add(annualForecastSubmissionDetailDTO);
        }
        if (CollectionUtils.isNotEmpty(insertSubmissionDTOS)) {
            List<AnnualForecastSubmissionPO> annualForecastSubmissionPOS = AnnualForecastSubmissionConvertor.INSTANCE.dto2Pos(insertSubmissionDTOS);
            BasePOUtils.insertBatchFiller(annualForecastSubmissionPOS);
            annualForecastSubmissionDao.insertBatchWithPrimaryKey(annualForecastSubmissionPOS);
        }
        if (CollectionUtils.isNotEmpty(updateSubmissionDTOS)) {
            this.doUpdateBatch(updateSubmissionDTOS);
        }
        if (CollectionUtils.isNotEmpty(insertSubmissionDetailDTOS)) {
            annualForecastSubmissionDetailService.doCreateBatch(insertSubmissionDetailDTOS);
        }
    }

    private void validateImportData(List<Map<Integer, String>> data, Map<String, String> saleOrganizeMap) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<String> productCodesList = new ArrayList<>();
        List<String> vehicleModelCodeList = new ArrayList<>();
        for (Map<Integer, String> map : data) {
            String productCode = map.get(3);
            if (!StringUtils.isBlank(productCode)) {
                productCodesList.add(productCode);
            }
            String vehicleModelCode = map.get(5);
            if (!StringUtils.isBlank(vehicleModelCode)) {
                vehicleModelCodeList.add(vehicleModelCode);
            }
        }
        log.info("Excel本厂编码集: {}", JSON.toJSONString(productCodesList));
        log.info("Excel车型编码集: {}", JSON.toJSONString(vehicleModelCodeList));
        // 查询主机厂数据
        List<OemVO> oemVOS = oemService.selectAll();
        List<String> customerCodes = oemVOS.stream().map(OemVO::getCustomerCode).distinct().collect(Collectors.toList());
        // 获取Excel本厂编码映射
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> newProductStockPointVOS1 = newMdsFeign.selectByProductCode(scenario.getData(), productCodesList);
        Map<String, List<NewProductStockPointVO>> productCodeMap = newProductStockPointVOS1.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        log.info("Excel本厂编码映射集Map: {}", productCodeMap.keySet());
        // 获取Excel车型编码映射
        List<NewProductStockPointVO> newProductStockPointVOS2 = newMdsFeign.selectByVehicleModelCode(scenario.getData(), vehicleModelCodeList);
        Map<String, List<NewProductStockPointVO>> vehicleCodeListMap = newProductStockPointVOS2.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));
        log.info("Excel车型编码映射集Map: {}", vehicleCodeListMap.keySet());
        List<NewProductionOrganizationVO> productionOrganizeVOS = newMdsFeign.selectNewProductionOrganizationVO(scenario.getData(), MapUtil.newHashMap());
        List<String> productionOrgCodes = productionOrganizeVOS.stream().map(NewProductionOrganizationVO::getOrganizationCode)
                .distinct().collect(Collectors.toList());
        log.info("生产工厂: {}", JSON.toJSONString(productionOrgCodes));
        int index = 0;
        for (Map<Integer, String> rowData : data) {
            index++;
            if (StringUtils.isBlank(rowData.get(0))) {
                throw new BusinessException("第" + index + "行必填字段子公司为空，请调整后再重新导入！");
            }
            if (!saleOrganizeMap.containsKey(rowData.get(0))) {
                throw new BusinessException("第" + index + "行子公司不存在，请调整后再重新导入！");
            }
            if (StringUtils.isBlank(rowData.get(2))) {
                throw new BusinessException("第" + index + "行必填字段客户编码为空，请调整后再重新导入！");
            }
            if (!customerCodes.contains(rowData.get(2))) {
                throw new BusinessException("第" + index + "行客户编码不存在，请调整后再重新导入！");
            }
            if (StringUtils.isBlank(rowData.get(3))) {
                throw new BusinessException("第" + index + "行必填字段产品编码为空，请调整后再重新导入！");
            }
            if (!productCodeMap.containsKey(rowData.get(3))) {
                throw new BusinessException("第" + index + "行产品编码不存在，请调整后再重新导入！");
            }
            if (StringUtils.isBlank(rowData.get(5))) {
                throw new BusinessException("第" + index + "行必填字段内部车型代号（车型编码）为空，请调整后再重新导入！");
            }
            if (!vehicleCodeListMap.containsKey(rowData.get(5))) {
                throw new BusinessException("第" + index + "行内部车型代号（车型编码）不存在，请调整后再重新导入！");
            }
            if (StringUtils.isBlank(rowData.get(7))) {
                throw new BusinessException("第" + index + "行必填字段生产工厂为空，请调整后再重新导入！");
            }
            if (!productionOrgCodes.contains(rowData.get(7))) {
                throw new BusinessException("第" + index + "行生产工厂不存在，请调整后再重新导入！");
            }
        }
    }

}
