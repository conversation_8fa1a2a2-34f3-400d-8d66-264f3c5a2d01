package com.yhl.scp.dfp.oem.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.oem.convertor.OemInventorySubmissionConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemInventorySubmissionDO;
import com.yhl.scp.dfp.oem.domain.service.OemInventorySubmissionDomainService;
import com.yhl.scp.dfp.oem.dto.OemInventorySubmissionDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemInventorySubmissionDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemInventorySubmissionPO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>OemInventorySubmissionServiceImpl</code>
 * <p>
 * 主机厂库存提报应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 10:17:10
 */
@Slf4j
@Service
public class OemInventorySubmissionServiceImpl extends AbstractService implements OemInventorySubmissionService {

    @Resource
    private OemInventorySubmissionDao oemInventorySubmissionDao;

    @Resource
    private OemInventorySubmissionDomainService oemInventorySubmissionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(OemInventorySubmissionDTO oemInventorySubmissionDTO) {
        // 0.数据转换
        OemInventorySubmissionDO oemInventorySubmissionDO = OemInventorySubmissionConvertor.INSTANCE.dto2Do(oemInventorySubmissionDTO);
        OemInventorySubmissionPO oemInventorySubmissionPO = OemInventorySubmissionConvertor.INSTANCE.dto2Po(oemInventorySubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemInventorySubmissionDomainService.validation(oemInventorySubmissionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemInventorySubmissionPO);
        oemInventorySubmissionDao.insert(oemInventorySubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(OemInventorySubmissionDTO oemInventorySubmissionDTO) {
        // 0.数据转换
        OemInventorySubmissionDO oemInventorySubmissionDO = OemInventorySubmissionConvertor.INSTANCE.dto2Do(oemInventorySubmissionDTO);
        OemInventorySubmissionPO oemInventorySubmissionPO = OemInventorySubmissionConvertor.INSTANCE.dto2Po(oemInventorySubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemInventorySubmissionDomainService.validation(oemInventorySubmissionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemInventorySubmissionPO);
        oemInventorySubmissionDao.updateSelective(oemInventorySubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemInventorySubmissionDTO> list) {
        List<OemInventorySubmissionPO> newList = OemInventorySubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemInventorySubmissionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OemInventorySubmissionDTO> list) {
        List<OemInventorySubmissionPO> newList = OemInventorySubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemInventorySubmissionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemInventorySubmissionDao.deleteBatch(idList);
        }
        return oemInventorySubmissionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemInventorySubmissionVO selectByPrimaryKey(String id) {
        OemInventorySubmissionPO po = oemInventorySubmissionDao.selectByPrimaryKey(id);
        return OemInventorySubmissionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "OEM_INVENTORY_SUBMISSION")
    public List<OemInventorySubmissionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "OEM_INVENTORY_SUBMISSION")
    public List<OemInventorySubmissionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemInventorySubmissionVO> dataList = oemInventorySubmissionDao.selectByCondition(sortParam, queryCriteriaParam);
        OemInventorySubmissionServiceImpl target = springBeanUtils.getBean(OemInventorySubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemInventorySubmissionVO> selectByParams(Map<String, Object> params) {
        List<OemInventorySubmissionPO> list = oemInventorySubmissionDao.selectByParams(params);
        return OemInventorySubmissionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemInventorySubmissionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM_INVENTORY_SUBMISSION.getCode();
    }

    @Override
    public List<OemInventorySubmissionVO> invocation(List<OemInventorySubmissionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
