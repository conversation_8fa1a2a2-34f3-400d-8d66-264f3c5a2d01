package com.yhl.scp.dfp.switchrelation.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.switchrelation.convertor.SwitchRelationBetweenProductConvertor;
import com.yhl.scp.dfp.switchrelation.domain.entity.SwitchRelationBetweenProductDO;
import com.yhl.scp.dfp.switchrelation.domain.service.SwitchRelationBetweenProductDomainService;
import com.yhl.scp.dfp.switchrelation.dto.SwitchRelationBetweenProductDTO;
import com.yhl.scp.dfp.switchrelation.infrastructure.dao.SwitchRelationBetweenProductDao;
import com.yhl.scp.dfp.switchrelation.infrastructure.po.SwitchRelationBetweenProductPO;
import com.yhl.scp.dfp.switchrelation.service.SwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationBetweenProductVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>SwitchRelationBetweenProductServiceImpl</code>
 * <p>
 * 新旧产品工程变更关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-19 16:35:31
 */
@Slf4j
@Service
public class SwitchRelationBetweenProductServiceImpl extends AbstractService implements SwitchRelationBetweenProductService {

    @Resource
    private SwitchRelationBetweenProductDao switchRelationBetweenProductDao;

    @Resource
    private SwitchRelationBetweenProductDomainService switchRelationBetweenProductDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Override
    public BaseResponse<Void> doCreate(SwitchRelationBetweenProductDTO switchRelationBetweenProductDTO) {
        // 0.数据转换
        SwitchRelationBetweenProductDO switchRelationBetweenProductDO =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Do(switchRelationBetweenProductDTO);
        SwitchRelationBetweenProductPO switchRelationBetweenProductPO =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Po(switchRelationBetweenProductDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        switchRelationBetweenProductDomainService.validation(switchRelationBetweenProductDO);
        switchRelationBetweenProductDomainService.updateQuantity(switchRelationBetweenProductPO);
        // 2.数据持久化
        BasePOUtils.insertFiller(switchRelationBetweenProductPO);
        switchRelationBetweenProductDao.insertWithPrimaryKey(switchRelationBetweenProductPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(SwitchRelationBetweenProductDTO switchRelationBetweenProductDTO) {
        // 0.数据转换
        SwitchRelationBetweenProductDO switchRelationBetweenProductDO =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Do(switchRelationBetweenProductDTO);
        SwitchRelationBetweenProductPO switchRelationBetweenProductPO =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Po(switchRelationBetweenProductDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        switchRelationBetweenProductDomainService.validation(switchRelationBetweenProductDO);
        switchRelationBetweenProductDomainService.updateQuantity(switchRelationBetweenProductPO);
        // 2.数据持久化
        BasePOUtils.updateFiller(switchRelationBetweenProductPO);
        switchRelationBetweenProductDao.update(switchRelationBetweenProductPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    public List<LabelValue<String>> getProductCodeByOemCode(String oemCode, String productCode) {
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Map<String, Object> params = new HashMap<>(2);
        params.put("oemCode", oemCode);
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(params);
        if (CollectionUtils.isEmpty(oemVehicleModelVOS)) {
            throw new BusinessException("主机厂车型信息无此主机厂：" + oemCode);
        }
        List<String> vehicleModelCodeList = oemVehicleModelVOS.stream()
                .map(OemVehicleModelVO::getOemVehicleModelCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
            throw new BusinessException("主机厂车型信息无此主机厂的车型：" + oemCode);
        }
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> newProductStockPointVOS =
                newMdsFeign.selectProductCodeLikeByVehicleByStock(scenario.getData(), productCode,
                        vehicleModelCodeList, rangeData);
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("此主机厂：" + oemCode + ",对应车型无本厂编码映射关系");
        }
        Map<String, NewProductStockPointVO> map = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(
                        NewProductStockPointVO::getProductCode, // 根据 productCode 去重
                        vo -> vo,
                        (existing, replacement) -> existing // 如果有重复的 productCode，保留第一个
                ));

        List<NewProductStockPointVO> list = new ArrayList<>(map.values());
        return list.stream()
                .map(newProductStockPointVO -> new LabelValue<>(newProductStockPointVO.getProductName() + "(" + newProductStockPointVO.getProductCode() + ")",
                        newProductStockPointVO.getProductCode()))
                .collect(Collectors.toList());
    }

    @Override
    public void doCreateBatch(List<SwitchRelationBetweenProductDTO> list) {
        List<SwitchRelationBetweenProductPO> newList =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        switchRelationBetweenProductDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<SwitchRelationBetweenProductDTO> list) {
        List<SwitchRelationBetweenProductPO> newList =
                SwitchRelationBetweenProductConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        switchRelationBetweenProductDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return switchRelationBetweenProductDao.deleteBatch(idList);
        }
        return switchRelationBetweenProductDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SwitchRelationBetweenProductVO selectByPrimaryKey(String id) {
        SwitchRelationBetweenProductPO po = switchRelationBetweenProductDao.selectByPrimaryKey(id);
        return SwitchRelationBetweenProductConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_dfp_switch_relation_between_product")
    public List<SwitchRelationBetweenProductVO> selectByPage(Pagination pagination, String sortParam,
                                                             String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_dfp_switch_relation_between_product")
    public List<SwitchRelationBetweenProductVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SwitchRelationBetweenProductVO> dataList =
                switchRelationBetweenProductDao.selectByCondition(sortParam, queryCriteriaParam);
        SwitchRelationBetweenProductServiceImpl target =
                springBeanUtils.getBean(SwitchRelationBetweenProductServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SwitchRelationBetweenProductVO> selectByParams(Map<String, Object> params) {
        List<SwitchRelationBetweenProductPO> list = switchRelationBetweenProductDao.selectByParams(params);
        return SwitchRelationBetweenProductConvertor.INSTANCE.po2Vos(list);
    }


    @Override
    public List<SwitchRelationBetweenProductVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SWITCH_RELATION_BETWEEN_PRODUCT.getCode();
    }

    @Override
    public List<SwitchRelationBetweenProductVO> invocation(List<SwitchRelationBetweenProductVO> dataList,
                                                           Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<SwitchRelationBetweenProductVO> selectByOemAndProduct(List<String> oemCodes,
                                                                      List<String> productCodes) {
        List<SwitchRelationBetweenProductPO> list = switchRelationBetweenProductDao.selectByOemAndProduct(oemCodes, productCodes);
        return SwitchRelationBetweenProductConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public SwitchRelationVO getSwitchRelation(List<String> oemCodes, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return SwitchRelationVO.builder().newOldMap(new HashMap<>()).oldNewMap(new HashMap<>()).allProductCodes(new ArrayList<>()).build();
        }
        Map<String, String> newOldMap = new HashMap<>();
        Map<String, String> oldNewMap = new HashMap<>();
        List<String> allProductCodes = new ArrayList<>(productCodes);
        List<SwitchRelationBetweenProductVO> switchRelations = this.selectByOemAndProduct(oemCodes, productCodes);
        if (CollectionUtils.isNotEmpty(switchRelations)) {
            newOldMap = switchRelations.stream().collect(Collectors
                    .toMap(SwitchRelationBetweenProductVO::getNewProductCode,
                            SwitchRelationBetweenProductVO::getOldProductCode, (t1, t2) -> t2));
            oldNewMap = switchRelations.stream().collect(Collectors
                    .toMap(SwitchRelationBetweenProductVO::getOldProductCode,
                            SwitchRelationBetweenProductVO::getNewProductCode, (t1, t2) -> t2));
            allProductCodes.addAll(newOldMap.keySet());
            allProductCodes.addAll(oldNewMap.keySet());
        }
        List<String> distinctList = allProductCodes.stream().distinct().collect(Collectors.toList());
        return SwitchRelationVO.builder().allProductCodes(distinctList).newOldMap(newOldMap).oldNewMap(oldNewMap).build();
    }

}