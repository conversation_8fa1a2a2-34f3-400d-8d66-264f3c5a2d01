package com.yhl.scp.dfp.massProduction.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.topic.MessageConstants;
import com.yhl.scp.biz.common.topic.RedissonPubSubUtil;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.massProduction.convertor.MassProductionHandoverConvertor;
import com.yhl.scp.dfp.massProduction.domain.entity.MassProductionHandoverDO;
import com.yhl.scp.dfp.massProduction.domain.service.MassProductionHandoverDomainService;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDTO;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDetailDTO;
import com.yhl.scp.dfp.massProduction.enums.ApprovalStatusEnum;
import com.yhl.scp.dfp.massProduction.infrastructure.dao.MassProductionHandoverDao;
import com.yhl.scp.dfp.massProduction.infrastructure.po.MassProductionHandoverPO;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverDetailService;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverService;
import com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverDetailVO;
import com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverVO;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.untils.UserMessageUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>MassProductionHandoverServiceImpl</code>
 * <p>
 * 量产移交信息主表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:14
 */
@Slf4j
@Service
public class MassProductionHandoverServiceImpl extends AbstractService implements MassProductionHandoverService {

    /**
     * 目标预测算法版本号前缀
     */
    public static final String DOCUMENT_CODE_PRE = "AXMLCYJQB";

    @Resource
    private MassProductionHandoverDao massProductionHandoverDao;

    @Resource
    private MassProductionHandoverDomainService massProductionHandoverDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MassProductionHandoverDetailService massProductionHandoverDetailService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private RedissonPubSubUtil redissonPubSubUtil;
    
    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Resource
    private UserMessageUtils userMessageUtils;

    @Override
    public BaseResponse<Void> doCreate(MassProductionHandoverDTO massProductionHandoverDTO) {
        // 1.数据转换
        MassProductionHandoverPO massProductionHandoverPO = MassProductionHandoverConvertor.INSTANCE.dto2Po(massProductionHandoverDTO);
        //维护单据编号
        String prefix = DOCUMENT_CODE_PRE + DateUtils.dateToString(new Date(), "yyyyMMdd");
        String maxDocumentCode = massProductionHandoverDao.selectMaxDocumentCode();
        String documentCode = prefix + getNewVersionCode(maxDocumentCode);
        massProductionHandoverPO.setDocumentCode(documentCode);
        // 2.数据持久化
        BasePOUtils.insertFiller(massProductionHandoverPO);
        massProductionHandoverDao.insert(massProductionHandoverPO);
        //新增明细
        List<MassProductionHandoverDetailDTO> detailList = massProductionHandoverDTO.getDetailList();
        detailList.forEach(e -> {
            e.setMassProductionHandoverId(massProductionHandoverPO.getId());
            e.setUpdateOrderPlannerFlag(YesOrNoEnum.NO.getCode());
        });
        massProductionHandoverDetailService.doCreateBatch(massProductionHandoverDTO.getDetailList());
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 最新版本号加一
     *
     * @param targetVersionCode 目标版本号
     * @return java.lang.String
     */
    protected String getNewVersionCode(String targetVersionCode) {
        String newVersionCode;
        int number = 1;
        if (StringUtils.isNotBlank(targetVersionCode)) {
            if (targetVersionCode.length() < 5) {
                throw new IllegalArgumentException("The version code must be at least 5 characters long.");
            }
            String lastFive = targetVersionCode.substring(targetVersionCode.length() - 5);
            number = Integer.parseInt(lastFive);
            // 数字加1
            number++;
        }
        // 格式化为五位数，不足五位补零
        newVersionCode = String.format("%05d", number);
        return newVersionCode;
    }

    @Override
    public BaseResponse<Void> doUpdate(MassProductionHandoverDTO massProductionHandoverDTO) {
        // 0.数据转换
        MassProductionHandoverDO massProductionHandoverDO = MassProductionHandoverConvertor.INSTANCE.dto2Do(massProductionHandoverDTO);
        MassProductionHandoverPO massProductionHandoverPO = MassProductionHandoverConvertor.INSTANCE.dto2Po(massProductionHandoverDTO);
        // 1.数据校验
        massProductionHandoverDomainService.validation(massProductionHandoverDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(massProductionHandoverPO);
        massProductionHandoverDao.update(massProductionHandoverPO);
        //先删除存量明细数据
        massProductionHandoverDetailService.deleteByMassProductionHandoverId(massProductionHandoverPO.getId());
        //在新增明细
        List<MassProductionHandoverDetailDTO> detailList = massProductionHandoverDTO.getDetailList();
        detailList.forEach(e -> {
            e.setMassProductionHandoverId(massProductionHandoverDTO.getId());
            if (StringUtils.isEmpty(e.getUpdateOrderPlannerFlag())) {
                e.setUpdateOrderPlannerFlag(YesOrNoEnum.NO.getCode());
            }
        });
        massProductionHandoverDetailService.doCreateBatch(massProductionHandoverDTO.getDetailList());
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MassProductionHandoverDTO> list) {
        List<MassProductionHandoverPO> newList = MassProductionHandoverConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        massProductionHandoverDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MassProductionHandoverDTO> list) {
        List<MassProductionHandoverPO> newList = MassProductionHandoverConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        massProductionHandoverDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        List<MassProductionHandoverPO> handoverList = massProductionHandoverDao.selectByPrimaryKeys(idList);
        for (MassProductionHandoverPO old : handoverList) {
            if (!ApprovalStatusEnum.NOT_SUBMITTED.getCode().equals(old.getApprovalStatus())) {
                throw new BusinessException("当前状态不支持删除操作");
            }
        }
        if (idList.size() > 1) {
            return massProductionHandoverDao.deleteBatch(idList);
        }
        int deleteNum = massProductionHandoverDao.deleteByPrimaryKey(idList.get(0));
        //删除明细数据
        massProductionHandoverDetailService.doDeleteByMassProductionHandoverIds(idList);
        return deleteNum;
    }

    @Override
    public MassProductionHandoverVO selectByPrimaryKey(String id) {
        MassProductionHandoverPO po = massProductionHandoverDao.selectByPrimaryKey(id);
        return MassProductionHandoverConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_mass_production_handover")
    public List<MassProductionHandoverVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_mass_production_handover")
    public List<MassProductionHandoverVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MassProductionHandoverVO> dataList = massProductionHandoverDao.selectByCondition(sortParam, queryCriteriaParam);
        MassProductionHandoverServiceImpl target = springBeanUtils.getBean(MassProductionHandoverServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MassProductionHandoverVO> selectByParams(Map<String, Object> params) {
        List<MassProductionHandoverPO> list = massProductionHandoverDao.selectByParams(params);
        return MassProductionHandoverConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MassProductionHandoverVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MASS_PRODUCTION_HANDOVER.getCode();
    }

    @Override
    public List<MassProductionHandoverVO> invocation(List<MassProductionHandoverVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public MassProductionHandoverVO selectDetail(String id) {
        MassProductionHandoverPO po = massProductionHandoverDao.selectByPrimaryKey(id);
        MassProductionHandoverVO massProductionHandoverVO = MassProductionHandoverConvertor.INSTANCE.po2Vo(po);
      //获取乘联品牌车型
        String vehicleModelBrand = queryVehicleModelBrand(massProductionHandoverVO.getOemCode(),
        		massProductionHandoverVO.getVehicleModelCode());
        massProductionHandoverVO.setVehicleModelBrand(vehicleModelBrand);
        List<MassProductionHandoverDetailVO> detailList = massProductionHandoverDetailService.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "massProductionHandoverId", id));
        massProductionHandoverVO.setDetailList(detailList);
        return massProductionHandoverVO;
    }

	private String queryVehicleModelBrand(String oemCode, String vehicleModelCode) {
		//获取乘联品牌车型
        List<String> vehicleModelBrands = oemVehicleModelMapService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"oemCode" , oemCode,
    			"vehicleModelCode" , vehicleModelCode)).stream()
        		.filter(e -> !StringUtils.isEmpty(e.getVehicleModelName()))
        		.map(OemVehicleModelMapVO::getVehicleModelName).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vehicleModelBrands)) {
        	throw new BusinessException(String.format("主机厂%s，内部车型代码%s，没有维护主机厂车型映射关系，请进行维护", 
        			oemCode, vehicleModelCode));
        }
		return String.join(",", vehicleModelBrands);
	}

    @Override
    public void doApproval(String id) {
        MassProductionHandoverPO massProductionHandover = massProductionHandoverDao.selectByPrimaryKey(id);
        //校验状态是否允许发起审批
        if (!ApprovalStatusEnum.NOT_SUBMITTED.getCode().equals(massProductionHandover.getApprovalStatus())) {
            throw new BusinessException("只有未下发状态支持发起审批");
        }
        //获取详情数据
        List<MassProductionHandoverDetailVO> detailList = massProductionHandoverDetailService.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "massProductionHandoverId", id));
        if (CollectionUtils.isEmpty(detailList)) {
            throw new BusinessException("请先维护产品信息!");
        }
        //组装量产移交xml数据
        String dataXml = initDataXml(id, massProductionHandover, detailList);
        Map<String, Object> map = MapUtil.newHashMap();
        String staffCode = SystemHolder.getUser().getStaffCode();
        if (StringUtils.isEmpty(staffCode)) {
            staffCode = SystemHolder.getUser().getUserName();
        }
        map.put("senderLoginName", SystemHolder.getUser().getStaffCode());
        map.put("subject", "量产移交OA审批创建-" + DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4));
        map.put("ids", id);
        map.put("data", dataXml);
        //发起审批
        BaseResponse<String> callExternalApi = null;
        try {
            callExternalApi = newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.OA.getCode(),
                    ApiCategoryEnum.CREATE_PRODUCTION_HANDOVER.getCode(), map);
        } catch (Exception e) {
            log.error("量产移交提交审批报错,{}", e.getMessage());
            throw new BusinessException("量产移交提交审批报错", e.getMessage());
        }
        if (!Boolean.TRUE.equals(callExternalApi.getSuccess())) {
            throw new BusinessException("提交审批失败：" + callExternalApi.getMsg());
        }
    }

    /**
     * 组装量产移交xml数据
     *
     * @param id
     * @param massProductionHandover
     * @return
     */
    private String initDataXml(String id, MassProductionHandoverPO massProductionHandover,
                               List<MassProductionHandoverDetailVO> detailList) {
        //获取开发类型字典
        StringBuffer sbValue = new StringBuffer();
        sbValue.append(getColumnData("内部车型代码", massProductionHandover.getVehicleModelCode(), null));
        for (int i = 1; i < 4; i++) {
            sbValue.append(getColumnData("主机厂编码", massProductionHandover.getOemCode(), i));
            sbValue.append(getColumnData("主机厂名称", massProductionHandover.getOemName(), i));
            sbValue.append(getColumnData("获取订单方式", massProductionHandover.getOrderAcquisitionMethod(), i));
            sbValue.append(getColumnData("发货地址", massProductionHandover.getShipAddress(), i));
            sbValue.append(getColumnData("开票客户信息库位代码", massProductionHandover.getLocationCode(), i));
            sbValue.append(getColumnData("客户编码", massProductionHandover.getCustomerCode(), i));
            sbValue.append(getColumnData("客户名称", massProductionHandover.getCustomerName(), i));
            sbValue.append(getColumnData("订单联系人", massProductionHandover.getOrderContactPerson(), i));
            sbValue.append(getColumnData("收货联系人", massProductionHandover.getReceivingContactPerson(), i));
            sbValue.append(getColumnData("特殊工艺说明", massProductionHandover.getSpecialProcessInstructions(), i));
            if (i == 1) {
                sbValue.append(getColumnData("车型企划量", massProductionHandover.getVehicleModelVolume(), i));
                sbValue.append(getColumnData("单据编号", massProductionHandover.getDocumentCode(), null));
            }
            sbValue.append(getColumnData("本厂编码", "", i));
            sbValue.append(getColumnData("产品名称", "", i));
            sbValue.append(getColumnData("零件号", "", i));
            sbValue.append(getColumnData("装车位置", "", i));
            sbValue.append(getColumnData("包装方式", "", i));
            sbValue.append(getColumnData("单片玻璃重量", "", i));
            sbValue.append(getColumnData("料箱立项数量", "", i));
            sbValue.append(getColumnData("标准装箱片数", "", i));
            sbValue.append(getColumnData("箱长", "", i));
            sbValue.append(getColumnData("箱宽", "", i));
            sbValue.append(getColumnData("箱高", "", i));
            sbValue.append(getColumnData("SOP时间", "", i));
            sbValue.append(getColumnData("EOP时间", "", i));
            sbValue.append(getColumnData("开发类型", "", i));
            sbValue.append(getColumnData("零件履历", "", i));
            sbValue.append(getColumnData("备注", "", i));
        }

        StringBuffer sbSubForms = new StringBuffer();
        for (int i = 0; i < 3; i++) {
            Integer definitionSize = 1;
            if (i == 0) {
                definitionSize = detailList.size();
            }
            String definitionColumn = getDefinitionColumn(definitionSize, i);
            String vauleRow = getValueRow(i, detailList);
            String subForm =
                    "<subForm>"
                            + "<definitions>" + definitionColumn + "</definitions>"
                            + "<values>" + vauleRow + "</values>"
                            + "</subForm>";

            sbSubForms.append(subForm);
        }


        String dataXml =
                "<formExport version='2.0'>" +
                        "<summary id='6422946221609074899' name='formmain_70001'></summary>" +
                        "<definitions></definitions>" +
                        "<values>" + sbValue + "</values>" +
                        "<subForms>" + sbSubForms + "</subForms>" +
                        "</formExport>";
        return dataXml;
    }

    private String getDefinitionColumn(Integer definitionSize, Integer definitionNum) {
        String definitionColumn = "";
        for (int i = 0; i < definitionSize; i++) {
            int fieldIdNum = 82;
            int fieldNameNum = 2;
            if (definitionNum == 1) {
                fieldIdNum = 98;
                fieldNameNum = 70;
            } else if (definitionNum == 2) {
                fieldIdNum = 114;
                fieldNameNum = 104;
            }
            for (int j = 0; j < 16; j++) {
                String fieldId = "field" + String.format("%04d", fieldIdNum);
                String fieldName = "field" + fieldNameNum;
                definitionColumn = definitionColumn
                        + "<column id='" + fieldId + "' type='0' name='" + fieldName + "' length='255' />";
                fieldNameNum++;
                fieldIdNum++;
            }
        }
        return definitionColumn;
    }

    private String getValueRow(Integer num, List<MassProductionHandoverDetailVO> detailList) {
        String definitionColumn = "";
        Integer eachNum = 1;
        if (num == 0) {
            eachNum = detailList.size();
        }
        for (int i = 0; i < eachNum; i++) {
            String productCode = "";
            String productName = "";
            String partNumber = "";
            String loadingPositionSub = "";
            String boxType = "";
            String pieceWeight = "";
            String boxWeight = "";
            String boxSpec = "";
            String productLength = "";
            String productWidth = "";
            String productThickness = "";
            String productSop = "";
            String productEop = "";
            String produceType = "";
            String partRecord = "";
            int fieldNameNum = 2;
            if (num == 0) {
                MassProductionHandoverDetailVO detailVO = detailList.get(i);
                productCode = detailVO.getProductCode();
                productName = detailVO.getProductName();
                partNumber = detailVO.getPartNumber();
                loadingPositionSub = detailVO.getLoadingPositionSub();
                boxType = detailVO.getBoxType();
                pieceWeight = bigDecimalToPlainString(detailVO.getPieceWeight());
                boxWeight = bigDecimalToPlainString(detailVO.getBoxWeight());
                boxSpec = detailVO.getBoxSpec() == null ? "" : detailVO.getBoxSpec().toString();
                productLength = bigDecimalToPlainString(detailVO.getProductLength());
                productWidth = bigDecimalToPlainString(detailVO.getProductWidth());
                productThickness = bigDecimalToPlainString(detailVO.getProductThickness());
                productSop = DateUtils.dateToString(detailVO.getProductSop(), DateUtils.COMMON_DATE_STR1);
                productEop = DateUtils.dateToString(detailVO.getProductEop(), DateUtils.COMMON_DATE_STR1);
                produceType = detailVO.getProduceType();
                partRecord = detailVO.getPartRecord();
            } else if (num == 1) {
                fieldNameNum = 70;
            } else if (num == 2) {
                fieldNameNum = 104;
            }
            definitionColumn = definitionColumn +
                    "<row>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productCode + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productName + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + partNumber + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + loadingPositionSub + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + boxType + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + pieceWeight + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + boxWeight + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + boxSpec + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productLength + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productWidth + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productThickness + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productSop + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + productEop + "</value></column>"
                    + "<column name='开发类型" + (i+1) + "-下拉框'><value>" + produceType + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value>" + partRecord + "</value></column>"
                    + "<column name='field" + fieldNameNum++ + "'><value></value></column>"
                    + "</row>";
        }
        return definitionColumn;
    }

    private String getColumnData(String columnName, String value, Integer index) {
        if (index != null) {
            columnName = columnName + index;
            if (index > 1) {
                value = "";
            }
        }
        String columnData = "<column name='" + columnName + "'>" +
                "<value>" + value + "</value>" +
                "</column>";
        return columnData;
    }

    private String bigDecimalToPlainString(BigDecimal value) {
        if (value == null) {
            return "";
        }
        return value.stripTrailingZeros().toPlainString();
    }

    @Override
    public BaseResponse<Void> syncCreateProductionHandover(String ids, String oaId) {
        log.info("量产移交更新状态：" + ids);
        try {
            String[] productionHandoverIds = ids.split(",");
            List<String> idList = Arrays.asList(productionHandoverIds);
            List<MassProductionHandoverPO> massProductionHandoverPOS = massProductionHandoverDao.selectByPrimaryKeys(idList);
            List<MassProductionHandoverDTO> productionHandoverDTOList = new ArrayList<>();
            massProductionHandoverPOS.forEach(item -> {
                //单据创建时赋值
                item.setApprovalStatus(ApprovalStatusEnum.SUBMITTED.getCode());
                item.setApprovalId(oaId);
                MassProductionHandoverDTO dto = new MassProductionHandoverDTO();
                BeanUtil.copyProperties(item, dto);
                productionHandoverDTOList.add(dto);
            });
            if (!productionHandoverDTOList.isEmpty()) {
                BasePOUtils.updateBatchFiller(productionHandoverDTOList);
                this.doUpdateBatch(productionHandoverDTOList);
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("量产移交创建表单默认塞值时报错:{},{}", e.getMessage(), e.getCause().toString());
            throw new BusinessException("量产移交创建表单默认塞值时报错:" + e.getMessage() + ",报错原因" + e.getCause().toString());
        }
    }

    @Override
    public BaseResponse<Void> syncStatus(String tenantCode, List<String> approvalIds) {
        try {
            log.info("开始同步量产移交状态");
            //同步OA量产移交状态
            String appIds = "";
            if (null == approvalIds) {
                //空值为定时 同步所谓未结束的单据
                List<MassProductionHandoverPO> massProductionHandoverPOS = massProductionHandoverDao.selectRunningData();
                appIds = massProductionHandoverPOS.stream()
                        .map(MassProductionHandoverPO::getApprovalId)
                        .filter(approvalId -> !StringUtils.isEmpty(approvalId))
                        .distinct()
                        .collect(Collectors.joining(","));
            } else {
                //不为空为手动
                if (approvalIds.isEmpty()) {
                    return BaseResponse.error("需要同步状态的单据不能为空");
                }
                tenantCode = SystemHolder.getTenantCode();
                appIds = String.join(",", approvalIds);
            }
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("appIds", appIds);
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.OA.getCode(),
                    ApiCategoryEnum.PRODUCTION_HANDOVER_STATUS.getCode(), map);

            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步OA量产移交状态报错,{},{}", e.getMessage(), e.getCause().toString());
            throw new BusinessException("同步OA量产移交状态报错", e.getMessage());
        }
    }

    @Override
    public void doUpdateApprovalStatus(String id) {
        MassProductionHandoverPO handoverPO = massProductionHandoverDao.selectByPrimaryKey(id);
        if (!ApprovalStatusEnum.SUBMITTED.getCode().equals(handoverPO.getApprovalStatus())
                && !ApprovalStatusEnum.IN_APPROVAL.getCode().equals(handoverPO.getApprovalStatus())) {
            throw new BusinessException("只有已提交和审批中状态支持更新流程状态");
        }
        String approvalId = handoverPO.getApprovalId();
        if (StringUtils.isEmpty(approvalId)) {
            throw new BusinessException("请先发起流程,再进行流程状态更新!");
        }
//        List<Object> appIds = new ArrayList<>();
//        appIds.add(handoverPO.getApprovalId());
        HashMap<String, Object> map = MapUtil.newHashMap();
        //appIds是指得流程Id通过逗号分割的字符串
        map.put("appIds", handoverPO.getApprovalId());
        BaseResponse<String> callExternalApi = null;
        try {
            callExternalApi = newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.OA.getCode(),
                    ApiCategoryEnum.PRODUCTION_HANDOVER_STATUS.getCode(), map);
        } catch (Exception e) {
            log.error("同步OA量产移交状态报错,{},{}", e.getMessage(), e.getCause().toString());
            throw new BusinessException("同步OA量产移交状态报错", e.getMessage());
        }
        if (!Boolean.TRUE.equals(callExternalApi.getSuccess())) {
            throw new BusinessException("同步OA量产移交状态失败：" + callExternalApi.getMsg());
        }
    }

    @Override
    public BaseResponse<Void> syncProductionHandoverStatus(List<MassProductionHandoverDTO> dtoS) {
        try {
            //获取以及同步的流程ID集合
            List<String> approvalIds = dtoS.stream().map(MassProductionHandoverDTO::getApprovalId).collect(Collectors.toList());
            //查询当前数据库种的数据
            Map<String, Object> approvalIdMap = ImmutableMap.of("approvalIds", approvalIds);
            List<MassProductionHandoverVO> massProductionHandoverVOS = this.selectByParams(approvalIdMap);
            //按照流程ID分类
            Map<String, List<MassProductionHandoverVO>> existMap = massProductionHandoverVOS.stream()
                    .collect(Collectors.groupingBy(MassProductionHandoverVO::getApprovalId));
            //创建集合对象
            List<MassProductionHandoverDTO> updateList = new ArrayList<>();
            List<String> needSendMessageCreatorList = new ArrayList<>();
            //给审批状态塞值
            dtoS.forEach(item -> {
                if (existMap.containsKey(item.getApprovalId())) {
                    List<MassProductionHandoverVO> massProductionHandoverVOS1 = existMap.get(item.getApprovalId());
                    String approveStatus = "";
                    if ("1".equals(item.getApprovalStatus()) || "3".equals(item.getApprovalStatus())) {
                        approveStatus = ApprovalStatusEnum.SUBMITTED.getCode();
                    } else if ("4".equals(item.getApprovalStatus()) || "6".equals(item.getApprovalStatus()) || "7".equals(item.getApprovalStatus())) {
                        approveStatus = ApprovalStatusEnum.IN_APPROVAL.getCode();
                    } else if ("5".equals(item.getApprovalStatus()) || "15".equals(item.getApprovalStatus())) {
                        approveStatus = ApprovalStatusEnum.CANCELED.getCode();
                    } else {
                        approveStatus = ApprovalStatusEnum.APPROVED.getCode();
                        String creator = item.getCreator();
                        // sendMessage(creator);
                        needSendMessageCreatorList.add(creator);
                    }
                    String finalApproveStatus = approveStatus;
                    massProductionHandoverVOS1.forEach(item2 -> {
                        item2.setApprovalStatus(finalApproveStatus);
                        MassProductionHandoverDTO dto = new MassProductionHandoverDTO();
                        BeanUtil.copyProperties(item2, dto);
                        updateList.add(dto);
                    });
                }
            });
            sendMessage(needSendMessageCreatorList);
            if (!updateList.isEmpty()) {
                this.doUpdateBatch(updateList);
            }
            return BaseResponse.success("更新成功");
        } catch (Exception e) {
            log.error("更新量产移交数据状态报错,{},{}", e.getMessage(), e.getCause().toString());
            throw new BusinessException("更新量产移交数据状态报错，" + e.getMessage());
        }
    }

    private void sendMessage(String creator) {
        try {
            redissonPubSubUtil.sendMessage(MessageConstants.APPROVAL_COMPLETED, creator);
        } catch (Exception e) {
            log.error("发送消息报错,{0}", e);
        }
    }

    private void sendMessage(List<String> creatorList) {
            if (CollectionUtils.isEmpty(creatorList)) {
                return;
            }
        List<UserMessageDTO> messageList = new ArrayList<>();
        for (String userId : creatorList) {
            // 量产移交审批完成提醒:OA流程审批完成后，给该物料的计划员发消息通知“您的量产移交流程已审批完成，请继续后面的移交流程”，跳转链接
            UserMessageDTO result = UserMessageDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .userId(userId)
                    .messageSource("系统消息")
                    .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                    .messageTitle("量产移交审批")
                    .messageContent("您的量产移交流程已审批完成，请继续后面的移交流程")
                    .messageEmergency("3")
                    .readStatus(YesOrNoEnum.NO.getCode())
                    .build();
            messageList.add(result);
        }
        userMessageUtils.sendMessage(messageList);

    }

	@Override
	public String getVehicleModelBrand(String oemCode, String vehicleModelCode) {
		return queryVehicleModelBrand(oemCode, vehicleModelCode);
	}

}
