<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.originDemand.infrastructure.dao.FdpOriginDemandForecastInterfaceLogDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO">
        <!--@Table fdp_origin_demand_forecast_interface_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="forecast_Set" jdbcType="VARCHAR" property="forecastSet"/>
        <result column="edi_location" jdbcType="VARCHAR" property="ediLocation"/>
        <result column="cust_Name" jdbcType="VARCHAR" property="custName"/>
        <result column="site_Address1" jdbcType="VARCHAR" property="siteAddress1"/>
        <result column="item_num" jdbcType="VARCHAR" property="itemNum"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="qty" jdbcType="VARCHAR" property="qty"/>
        <result column="schedule_type" jdbcType="VARCHAR" property="scheduleType"/>
        <result column="original_sch_st_date" jdbcType="TIMESTAMP" property="originalSchStDate"/>
        <result column="original_sch_end_date" jdbcType="TIMESTAMP" property="originalSchEndDate"/>
        <result column="original_ship_time" jdbcType="TIMESTAMP" property="originalShipTime"/>
        <result column="original_delivery_time" jdbcType="TIMESTAMP" property="originalDeliveryTime"/>
        <result column="forecast_date_start" jdbcType="TIMESTAMP" property="forecastDateStart"/>
        <result column="forecast_date_end" jdbcType="TIMESTAMP" property="forecastDateEnd"/>
        <result column="ebs_customer_id" jdbcType="VARCHAR" property="ebsCustomerId"/>
        <result column="ebs_site_id" jdbcType="VARCHAR" property="ebsSiteId"/>
        <result column="customer_item_num" jdbcType="VARCHAR" property="customerItemNum"/>
        <result column="materials_adv" jdbcType="VARCHAR" property="materialsAdv"/>
        <result column="forecast_creation_date" jdbcType="TIMESTAMP" property="forecastCreationDate"/>
        <result column="release_Num" jdbcType="VARCHAR" property="releaseNum"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="submission_type" jdbcType="VARCHAR" property="submissionType"/>
        <result column="import_type" jdbcType="VARCHAR" property="importType"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="serial_num" jdbcType="VARCHAR" property="serialNum"/>
        <result column="bu_id" jdbcType="VARCHAR" property="buId"/>
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="party_name" jdbcType="VARCHAR" property="partyName"/>
        <result column="site_name" jdbcType="VARCHAR" property="siteName"/>
        <result column="ordered_qty" jdbcType="VARCHAR" property="orderedQty"/>
        <result column="erp_party_site_id" jdbcType="VARCHAR" property="erpPartySiteId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO">
        <!-- TODO -->
        <result column="product_edi_flag" jdbcType="VARCHAR" property="productEdiFlag"/>

    </resultMap>
    <sql id="Base_Column_List">
        id
        ,forecast_Set,edi_location,cust_Name,site_Address1,item_num,org_id,qty,schedule_type,original_sch_st_date,original_sch_end_date,original_ship_time,original_delivery_time,forecast_date_start,forecast_date_end,ebs_customer_id,ebs_site_id,customer_item_num,materials_adv,forecast_creation_date,release_Num,last_update_date,submission_type,import_type,oem_code,enabled,creator,create_time,modifier,modify_time,version_value,serial_num,bu_id,organization_code,organization_name,party_name,site_name,ordered_qty,erp_party_site_id,business_type,kid,plant_code,customer_code
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_edi_flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastSet != null and params.forecastSet != ''">
                and forecast_Set = #{params.forecastSet,jdbcType=VARCHAR}
            </if>
            <if test="params.ediLocation != null and params.ediLocation != ''">
                and edi_location = #{params.ediLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.custName != null and params.custName != ''">
                and cust_Name = #{params.custName,jdbcType=VARCHAR}
            </if>
            <if test="params.siteAddress1 != null and params.siteAddress1 != ''">
                and site_Address1 = #{params.siteAddress1,jdbcType=VARCHAR}
            </if>
            <if test="params.itemNum != null and params.itemNum != ''">
                and item_num = #{params.itemNum,jdbcType=VARCHAR}
            </if>
            <if test="params.orgId != null and params.orgId != ''">
                and org_id = #{params.orgId,jdbcType=VARCHAR}
            </if>
            <if test="params.qty != null">
                and qty = #{params.qty,jdbcType=VARCHAR}
            </if>
            <if test="params.scheduleType != null and params.scheduleType != ''">
                and schedule_type = #{params.scheduleType,jdbcType=VARCHAR}
            </if>
            <if test="params.originalSchStDate != null">
                and original_sch_st_date = #{params.originalSchStDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.originalSchEndDate != null">
                and original_sch_end_date = #{params.originalSchEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.originalShipTime != null">
                and original_ship_time = #{params.originalShipTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.originalDeliveryTime != null">
                and original_delivery_time = #{params.originalDeliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.forecastDateStart != null">
                and forecast_date_start = #{params.forecastDateStart,jdbcType=TIMESTAMP}
            </if>
            <if test="params.forecastDateEnd != null">
                and forecast_date_end = #{params.forecastDateEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="params.ebsCustomerId != null and params.ebsCustomerId != ''">
                and ebs_customer_id = #{params.ebsCustomerId,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsSiteId != null and params.ebsSiteId != ''">
                and ebs_site_id = #{params.ebsSiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.customerItemNum != null and params.customerItemNum != ''">
                and customer_item_num = #{params.customerItemNum,jdbcType=VARCHAR}
            </if>
            <if test="params.materialsAdv != null and params.materialsAdv != ''">
                and materials_adv = #{params.materialsAdv,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastCreationDate != null">
                and forecast_creation_date = #{params.forecastCreationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.releaseNum != null and params.releaseNum != ''">
                and release_Num = #{params.releaseNum,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.submissionType != null and params.submissionType != ''">
                and submission_type = #{params.submissionType,jdbcType=VARCHAR}
            </if>
            <if test="params.importType != null and params.importType != ''">
                and import_type = #{params.importType,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.serialNum != null and params.serialNum != ''">
                and serial_num = #{params.serialNum,jdbcType=VARCHAR}
            </if>
            <if test="params.buId != null and params.buId != ''">
                and bu_id = #{params.buId,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationCode != null and params.organizationCode != ''">
                and organization_code = #{params.organizationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationName != null and params.organizationName != ''">
                and organization_name = #{params.organizationName,jdbcType=VARCHAR}
            </if>
            <if test="params.partyName != null and params.partyName != ''">
                and party_name = #{params.partyName,jdbcType=VARCHAR}
            </if>
            <if test="params.siteName != null and params.siteName != ''">
                and site_name = #{params.siteName,jdbcType=VARCHAR}
            </if>
            <if test="params.orderedQty != null">
                and ordered_qty = #{params.orderedQty,jdbcType=VARCHAR}
            </if>
            <if test="params.erpPartySiteId != null and params.erpPartySiteId != ''">
                and erp_party_site_id = #{params.erpPartySiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.customerCode != null and params.customerCode != ''">
                and customer_code = #{params.customerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productEdiFlag != null and params.productEdiFlag != ''">
                and product_edi_flag = #{params.product_edi_flag,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_forecast_interface_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_forecast_interface_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_origin_demand_forecast_interface_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_fdp_origin_demand_forecast_interface_log
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_forecast_interface_log
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByKids" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_forecast_interface_log
        where kid in
        <foreach collection="kids" item="kid" index="index" open="(" separator="," close=")">
            #{kid,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_origin_demand_forecast_interface_log(
        id,
        forecast_Set,
        edi_location,
        cust_Name,
        site_Address1,
        item_num,
        org_id,
        qty,
        schedule_type,
        original_sch_st_date,
        original_sch_end_date,
        original_ship_time,
        original_delivery_time,
        forecast_date_start,
        forecast_date_end,
        ebs_customer_id,
        ebs_site_id,
        customer_item_num,
        materials_adv,
        forecast_creation_date,
        release_Num,
        last_update_date,
        submission_type,
        import_type,
        oem_code,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        serial_num,
        bu_id,
        organization_code,
        organization_name,
        party_name,
        site_name,
        ordered_qty,
        erp_party_site_id,
        business_type,
        kid,
        plant_code,
        customer_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{forecastSet,jdbcType=VARCHAR},
        #{ediLocation,jdbcType=VARCHAR},
        #{custName,jdbcType=VARCHAR},
        #{siteAddress1,jdbcType=VARCHAR},
        #{itemNum,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{qty,jdbcType=VARCHAR},
        #{scheduleType,jdbcType=VARCHAR},
        #{originalSchStDate,jdbcType=TIMESTAMP},
        #{originalSchEndDate,jdbcType=TIMESTAMP},
        #{originalShipTime,jdbcType=TIMESTAMP},
        #{originalDeliveryTime,jdbcType=TIMESTAMP},
        #{forecastDateStart,jdbcType=TIMESTAMP},
        #{forecastDateEnd,jdbcType=TIMESTAMP},
        #{ebsCustomerId,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{customerItemNum,jdbcType=VARCHAR},
        #{materialsAdv,jdbcType=VARCHAR},
        #{forecastCreationDate,jdbcType=TIMESTAMP},
        #{releaseNum,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{submissionType,jdbcType=VARCHAR},
        #{importType,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{serialNum,jdbcType=VARCHAR},
        #{buId,jdbcType=VARCHAR},
        #{organizationCode,jdbcType=VARCHAR},
        #{organizationName,jdbcType=VARCHAR},
        #{partyName,jdbcType=VARCHAR},
        #{siteName,jdbcType=VARCHAR},
        #{orderedQty,jdbcType=VARCHAR},
        #{erpPartySiteId,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{customerCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO">
        insert into fdp_origin_demand_forecast_interface_log(id,
                                                             forecast_Set,
                                                             edi_location,
                                                             cust_Name,
                                                             site_Address1,
                                                             item_num,
                                                             org_id,
                                                             qty,
                                                             schedule_type,
                                                             original_sch_st_date,
                                                             original_sch_end_date,
                                                             original_ship_time,
                                                             original_delivery_time,
                                                             forecast_date_start,
                                                             forecast_date_end,
                                                             ebs_customer_id,
                                                             ebs_site_id,
                                                             customer_item_num,
                                                             materials_adv,
                                                             forecast_creation_date,
                                                             release_Num,
                                                             last_update_date,
                                                             submission_type,
                                                             import_type,
                                                             oem_code,
                                                             enabled,
                                                             creator,
                                                             create_time,
                                                             modifier,
                                                             modify_time,
                                                             version_value,
                                                             serial_num,
                                                             bu_id,
                                                             organization_code,
                                                             organization_name,
                                                             party_name,
                                                             site_name,
                                                             ordered_qty,
                                                             erp_party_site_id,
                                                             business_type,
                                                             kid,
                                                             plant_code,
                                                             customer_code)
        values (#{id,jdbcType=VARCHAR},
                #{forecastSet,jdbcType=VARCHAR},
                #{ediLocation,jdbcType=VARCHAR},
                #{custName,jdbcType=VARCHAR},
                #{siteAddress1,jdbcType=VARCHAR},
                #{itemNum,jdbcType=VARCHAR},
                #{orgId,jdbcType=VARCHAR},
                #{qty,jdbcType=VARCHAR},
                #{scheduleType,jdbcType=VARCHAR},
                #{originalSchStDate,jdbcType=TIMESTAMP},
                #{originalSchEndDate,jdbcType=TIMESTAMP},
                #{originalShipTime,jdbcType=TIMESTAMP},
                #{originalDeliveryTime,jdbcType=TIMESTAMP},
                #{forecastDateStart,jdbcType=TIMESTAMP},
                #{forecastDateEnd,jdbcType=TIMESTAMP},
                #{ebsCustomerId,jdbcType=VARCHAR},
                #{ebsSiteId,jdbcType=VARCHAR},
                #{customerItemNum,jdbcType=VARCHAR},
                #{materialsAdv,jdbcType=VARCHAR},
                #{forecastCreationDate,jdbcType=TIMESTAMP},
                #{releaseNum,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{submissionType,jdbcType=VARCHAR},
                #{importType,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{serialNum,jdbcType=VARCHAR},
                #{buId,jdbcType=VARCHAR},
                #{organizationCode,jdbcType=VARCHAR},
                #{organizationName,jdbcType=VARCHAR},
                #{partyName,jdbcType=VARCHAR},
                #{siteName,jdbcType=VARCHAR},
                #{orderedQty,jdbcType=VARCHAR},
                #{erpPartySiteId,jdbcType=VARCHAR},
                #{businessType,jdbcType=VARCHAR},
                #{kid,jdbcType=VARCHAR},
                #{plantCode,jdbcType=VARCHAR},
                #{customerCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_origin_demand_forecast_interface_log(
        id,
        forecast_Set,
        edi_location,
        cust_Name,
        site_Address1,
        item_num,
        org_id,
        qty,
        schedule_type,
        original_sch_st_date,
        original_sch_end_date,
        original_ship_time,
        original_delivery_time,
        forecast_date_start,
        forecast_date_end,
        ebs_customer_id,
        ebs_site_id,
        customer_item_num,
        materials_adv,
        forecast_creation_date,
        release_Num,
        last_update_date,
        submission_type,
        import_type,
        oem_code,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        serial_num,
        bu_id,
        organization_code,
        organization_name,
        party_name,
        site_name,
        ordered_qty,
        erp_party_site_id,
        business_type,
        kid,
        plant_code,
        customer_code)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.forecastSet,jdbcType=VARCHAR},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.custName,jdbcType=VARCHAR},
            #{entity.siteAddress1,jdbcType=VARCHAR},
            #{entity.itemNum,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.scheduleType,jdbcType=VARCHAR},
            #{entity.originalSchStDate,jdbcType=TIMESTAMP},
            #{entity.originalSchEndDate,jdbcType=TIMESTAMP},
            #{entity.originalShipTime,jdbcType=TIMESTAMP},
            #{entity.originalDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.forecastDateStart,jdbcType=TIMESTAMP},
            #{entity.forecastDateEnd,jdbcType=TIMESTAMP},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.ebsSiteId,jdbcType=VARCHAR},
            #{entity.customerItemNum,jdbcType=VARCHAR},
            #{entity.materialsAdv,jdbcType=VARCHAR},
            #{entity.forecastCreationDate,jdbcType=TIMESTAMP},
            #{entity.releaseNum,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.importType,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.serialNum,jdbcType=VARCHAR},
            #{entity.buId,jdbcType=VARCHAR},
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.partyName,jdbcType=VARCHAR},
            #{entity.siteName,jdbcType=VARCHAR},
            #{entity.orderedQty,jdbcType=VARCHAR},
            #{entity.erpPartySiteId,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.customerCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_origin_demand_forecast_interface_log(
        id,
        forecast_Set,
        edi_location,
        cust_Name,
        site_Address1,
        item_num,
        org_id,
        qty,
        schedule_type,
        original_sch_st_date,
        original_sch_end_date,
        original_ship_time,
        original_delivery_time,
        forecast_date_start,
        forecast_date_end,
        ebs_customer_id,
        ebs_site_id,
        customer_item_num,
        materials_adv,
        forecast_creation_date,
        release_Num,
        last_update_date,
        submission_type,
        import_type,
        oem_code,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        serial_num,
        bu_id,
        organization_code,
        organization_name,
        party_name,
        site_name,
        ordered_qty,
        erp_party_site_id,
        business_type,
        kid,
        plant_code,
        customer_code)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.forecastSet,jdbcType=VARCHAR},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.custName,jdbcType=VARCHAR},
            #{entity.siteAddress1,jdbcType=VARCHAR},
            #{entity.itemNum,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.scheduleType,jdbcType=VARCHAR},
            #{entity.originalSchStDate,jdbcType=TIMESTAMP},
            #{entity.originalSchEndDate,jdbcType=TIMESTAMP},
            #{entity.originalShipTime,jdbcType=TIMESTAMP},
            #{entity.originalDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.forecastDateStart,jdbcType=TIMESTAMP},
            #{entity.forecastDateEnd,jdbcType=TIMESTAMP},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.ebsSiteId,jdbcType=VARCHAR},
            #{entity.customerItemNum,jdbcType=VARCHAR},
            #{entity.materialsAdv,jdbcType=VARCHAR},
            #{entity.forecastCreationDate,jdbcType=TIMESTAMP},
            #{entity.releaseNum,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.importType,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.serialNum,jdbcType=VARCHAR},
            #{entity.buId,jdbcType=VARCHAR},
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.partyName,jdbcType=VARCHAR},
            #{entity.siteName,jdbcType=VARCHAR},
            #{entity.orderedQty,jdbcType=VARCHAR},
            #{entity.erpPartySiteId,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.plantCode,jdbcType=VARCHAR},
            #{entity.customerCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO">
        update fdp_origin_demand_forecast_interface_log
        set forecast_Set           = #{forecastSet,jdbcType=VARCHAR},
            edi_location           = #{ediLocation,jdbcType=VARCHAR},
            cust_Name              = #{custName,jdbcType=VARCHAR},
            site_Address1          = #{siteAddress1,jdbcType=VARCHAR},
            item_num               = #{itemNum,jdbcType=VARCHAR},
            org_id                 = #{orgId,jdbcType=VARCHAR},
            qty                    = #{qty,jdbcType=VARCHAR},
            schedule_type          = #{scheduleType,jdbcType=VARCHAR},
            original_sch_st_date   = #{originalSchStDate,jdbcType=TIMESTAMP},
            original_sch_end_date  = #{originalSchEndDate,jdbcType=TIMESTAMP},
            original_ship_time     = #{originalShipTime,jdbcType=TIMESTAMP},
            original_delivery_time = #{originalDeliveryTime,jdbcType=TIMESTAMP},
            forecast_date_start    = #{forecastDateStart,jdbcType=TIMESTAMP},
            forecast_date_end      = #{forecastDateEnd,jdbcType=TIMESTAMP},
            ebs_customer_id        = #{ebsCustomerId,jdbcType=VARCHAR},
            ebs_site_id            = #{ebsSiteId,jdbcType=VARCHAR},
            customer_item_num      = #{customerItemNum,jdbcType=VARCHAR},
            materials_adv          = #{materialsAdv,jdbcType=VARCHAR},
            forecast_creation_date = #{forecastCreationDate,jdbcType=TIMESTAMP},
            release_Num            = #{releaseNum,jdbcType=VARCHAR},
            last_update_date       = #{lastUpdateDate,jdbcType=TIMESTAMP},
            submission_type        = #{submissionType,jdbcType=VARCHAR},
            import_type            = #{importType,jdbcType=VARCHAR},
            oem_code               = #{oemCode,jdbcType=VARCHAR},
            enabled                = #{enabled,jdbcType=VARCHAR},
            modifier               = #{modifier,jdbcType=VARCHAR},
            modify_time            = #{modifyTime,jdbcType=TIMESTAMP},
            serial_num             = #{serialNum,jdbcType=VARCHAR},
            bu_id                  = #{buId,jdbcType=VARCHAR},
            organization_code      = #{organizationCode,jdbcType=VARCHAR},
            organization_name      = #{organizationName,jdbcType=VARCHAR},
            party_name             = #{partyName,jdbcType=VARCHAR},
            site_name              = #{siteName,jdbcType=VARCHAR},
            ordered_qty            = #{orderedQty,jdbcType=VARCHAR},
            erp_party_site_id      = #{erpPartySiteId,jdbcType=VARCHAR},
            business_type          = #{businessType,jdbcType=VARCHAR},
            kid                   = #{kid,jdbcType=VARCHAR},
            plant_code              = #{plantCode,jdbcType=VARCHAR},
            customer_code         = #{customerCode,jdbcType=VARCHAR},
            version_value              = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO">
        update fdp_origin_demand_forecast_interface_log
        <set>
            <if test="item.forecastSet != null and item.forecastSet != ''">
                forecast_Set = #{item.forecastSet,jdbcType=VARCHAR},
            </if>
            <if test="item.ediLocation != null and item.ediLocation != ''">
                edi_location = #{item.ediLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.custName != null and item.custName != ''">
                cust_Name = #{item.custName,jdbcType=VARCHAR},
            </if>
            <if test="item.siteAddress1 != null and item.siteAddress1 != ''">
                site_Address1 = #{item.siteAddress1,jdbcType=VARCHAR},
            </if>
            <if test="item.itemNum != null and item.itemNum != ''">
                item_num = #{item.itemNum,jdbcType=VARCHAR},
            </if>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.qty != null">
                qty = #{item.qty,jdbcType=VARCHAR},
            </if>
            <if test="item.scheduleType != null and item.scheduleType != ''">
                schedule_type = #{item.scheduleType,jdbcType=VARCHAR},
            </if>
            <if test="item.originalSchStDate != null">
                original_sch_st_date = #{item.originalSchStDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.originalSchEndDate != null">
                original_sch_end_date = #{item.originalSchEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.originalShipTime != null">
                original_ship_time = #{item.originalShipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.originalDeliveryTime != null">
                original_delivery_time = #{item.originalDeliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.forecastDateStart != null">
                forecast_date_start = #{item.forecastDateStart,jdbcType=TIMESTAMP},
            </if>
            <if test="item.forecastDateEnd != null">
                forecast_date_end = #{item.forecastDateEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.customerItemNum != null and item.customerItemNum != ''">
                customer_item_num = #{item.customerItemNum,jdbcType=VARCHAR},
            </if>
            <if test="item.materialsAdv != null and item.materialsAdv != ''">
                materials_adv = #{item.materialsAdv,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastCreationDate != null">
                forecast_creation_date = #{item.forecastCreationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.releaseNum != null and item.releaseNum != ''">
                release_Num = #{item.releaseNum,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.submissionType != null and item.submissionType != ''">
                submission_type = #{item.submissionType,jdbcType=VARCHAR},
            </if>
            <if test="item.importType != null and item.importType != ''">
                import_type = #{item.importType,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.serialNum != null and item.serialNum != ''">
                serial_num = #{item.serialNum,jdbcType=VARCHAR},
            </if>
            <if test="item.buId != null and item.buId != ''">
                bu_id = #{item.buId,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationCode != null and item.organizationCode != ''">
                organization_code = #{item.organizationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationName != null and item.organizationName != ''">
                organization_name = #{item.organizationName,jdbcType=VARCHAR},
            </if>
            <if test="item.partyName != null and item.partyName != ''">
                party_name = #{item.partyName,jdbcType=VARCHAR},
            </if>
            <if test="item.siteName != null and item.siteName != ''">
                site_name = #{item.siteName,jdbcType=VARCHAR},
            </if>
            <if test="item.orderedQty != null">
                ordered_qty = #{item.orderedQty,jdbcType=VARCHAR},
            </if>
            <if test="item.erpPartySiteId != null and item.erpPartySiteId != ''">
                erp_party_site_id = #{item.erpPartySiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.customerCode != null and item.customerCode != ''">
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_origin_demand_forecast_interface_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="forecast_Set = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastSet,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="edi_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ediLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_Name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="site_Address1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.siteAddress1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.qty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="schedule_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scheduleType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_sch_st_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalSchStDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="original_sch_end_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalSchEndDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="original_ship_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalShipTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="original_delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalDeliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="forecast_date_start = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastDateStart,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="forecast_date_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastDateEnd,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="ebs_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsCustomerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsSiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_item_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerItemNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="materials_adv = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialsAdv,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_creation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastCreationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="release_Num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.releaseNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="submission_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="import_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.importType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    version_value + 1
                </foreach>
            </trim>
            <trim prefix="serial_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.serialNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bu_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.buId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="party_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partyName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="site_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.siteName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ordered_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderedQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="erp_party_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.erpPartySiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_origin_demand_forecast_interface_log
            <set>
                <if test="item.forecastSet != null and item.forecastSet != ''">
                    forecast_Set = #{item.forecastSet,jdbcType=VARCHAR},
                </if>
                <if test="item.ediLocation != null and item.ediLocation != ''">
                    edi_location = #{item.ediLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.custName != null and item.custName != ''">
                    cust_Name = #{item.custName,jdbcType=VARCHAR},
                </if>
                <if test="item.siteAddress1 != null and item.siteAddress1 != ''">
                    site_Address1 = #{item.siteAddress1,jdbcType=VARCHAR},
                </if>
                <if test="item.itemNum != null and item.itemNum != ''">
                    item_num = #{item.itemNum,jdbcType=VARCHAR},
                </if>
                <if test="item.orgId != null and item.orgId != ''">
                    org_id = #{item.orgId,jdbcType=VARCHAR},
                </if>
                <if test="item.qty != null">
                    qty = #{item.qty,jdbcType=VARCHAR},
                </if>
                <if test="item.scheduleType != null and item.scheduleType != ''">
                    schedule_type = #{item.scheduleType,jdbcType=VARCHAR},
                </if>
                <if test="item.originalSchStDate != null">
                    original_sch_st_date = #{item.originalSchStDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.originalSchEndDate != null">
                    original_sch_end_date = #{item.originalSchEndDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.originalShipTime != null">
                    original_ship_time = #{item.originalShipTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.originalDeliveryTime != null">
                    original_delivery_time = #{item.originalDeliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.forecastDateStart != null">
                    forecast_date_start = #{item.forecastDateStart,jdbcType=TIMESTAMP},
                </if>
                <if test="item.forecastDateEnd != null">
                    forecast_date_end = #{item.forecastDateEnd,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                    ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
                </if>
                <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                    ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
                </if>
                <if test="item.customerItemNum != null and item.customerItemNum != ''">
                    customer_item_num = #{item.customerItemNum,jdbcType=VARCHAR},
                </if>
                <if test="item.materialsAdv != null and item.materialsAdv != ''">
                    materials_adv = #{item.materialsAdv,jdbcType=VARCHAR},
                </if>
                <if test="item.forecastCreationDate != null">
                    forecast_creation_date = #{item.forecastCreationDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.releaseNum != null and item.releaseNum != ''">
                    release_Num = #{item.releaseNum,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.submissionType != null and item.submissionType != ''">
                    submission_type = #{item.submissionType,jdbcType=VARCHAR},
                </if>
                <if test="item.importType != null and item.importType != ''">
                    import_type = #{item.importType,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.serialNum != null and item.serialNum != ''">
                    serial_num = #{item.serialNum,jdbcType=VARCHAR},
                </if>
                <if test="item.buId != null and item.buId != ''">
                    bu_id = #{item.buId,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationCode != null and item.organizationCode != ''">
                    organization_code = #{item.organizationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationName != null and item.organizationName != ''">
                    organization_name = #{item.organizationName,jdbcType=VARCHAR},
                </if>
                <if test="item.partyName != null and item.partyName != ''">
                    party_name = #{item.partyName,jdbcType=VARCHAR},
                </if>
                <if test="item.siteName != null and item.siteName != ''">
                    site_name = #{item.siteName,jdbcType=VARCHAR},
                </if>
                <if test="item.orderedQty != null">
                    ordered_qty = #{item.orderedQty,jdbcType=VARCHAR},
                </if>
                <if test="item.erpPartySiteId != null and item.erpPartySiteId != ''">
                    erp_party_site_id = #{item.erpPartySiteId,jdbcType=VARCHAR},
                </if>
                <if test="item.businessType != null and item.businessType != ''">
                    business_type = #{item.businessType,jdbcType=VARCHAR},
                </if>
                <if test="item.kid != null and item.kid != ''">
                    kid = #{item.kid,jdbcType=VARCHAR},
                </if>
                <if test="item.plantCode != null and item.plantCode != ''">
                    plant_code = #{item.plantCode,jdbcType=VARCHAR},
                </if>
                <if test="item.customerCode != null and item.customerCode != ''">
                    customer_code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                version_value = version_value + 1,
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_origin_demand_forecast_interface_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_origin_demand_forecast_interface_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteByType" parameterType="java.lang.String">
        delete from fdp_origin_demand_forecast_interface_log where import_type = #{importType,jdbcType=VARCHAR}
    </delete>
</mapper>
