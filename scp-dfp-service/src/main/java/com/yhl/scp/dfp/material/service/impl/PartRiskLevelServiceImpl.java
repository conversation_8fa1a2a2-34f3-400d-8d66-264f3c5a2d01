package com.yhl.scp.dfp.material.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseToWarehouseDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.forecast.service.HistoryForecastDataService;
import com.yhl.scp.dfp.forecast.vo.HistoryMonthForecastDataVO;
import com.yhl.scp.dfp.material.convertor.PartRiskLevelConvertor;
import com.yhl.scp.dfp.material.domain.entity.PartRiskLevelDO;
import com.yhl.scp.dfp.material.domain.service.PartRiskLevelDomainService;
import com.yhl.scp.dfp.material.dto.PartRiskLevelDTO;
import com.yhl.scp.dfp.material.dto.PartRiskLevelDetailDTO;
import com.yhl.scp.dfp.material.infrastructure.dao.PartRiskLevelDao;
import com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO;
import com.yhl.scp.dfp.material.service.PartRiskLevelDetailService;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.HeaderPartRiskLevelVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelDetailVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.service.OemRiskLevelService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.risk.service.RiskLevelRuleDetailService;
import com.yhl.scp.dfp.risk.service.RiskLevelRuleService;
import com.yhl.scp.dfp.risk.vo.RiskLevelRuleDetailVO;
import com.yhl.scp.dfp.risk.vo.RiskLevelRuleVO;
import com.yhl.scp.dfp.utils.DfpDateUtils;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.enums.ProductSaleTypeEnum;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>PartRiskLevelServiceImpl</code>
 * <p>
 * 零件风险等级应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Slf4j
@Service
public class PartRiskLevelServiceImpl extends AbstractService implements PartRiskLevelService {

    @Resource
    private PartRiskLevelDao partRiskLevelDao;

    @Resource
    private PartRiskLevelDomainService partRiskLevelDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private PartRiskLevelDetailService partRiskLevelDetailService;

    @Resource
    private RiskLevelRuleService riskLevelRuleService;

    @Resource
    private RiskLevelRuleDetailService riskLevelRuleDetailService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private OemRiskLevelService oemRiskLevelService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private PartRiskLevelService partRiskLevelService;
    
    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;

    @Resource
    private WarehouseReleaseToWarehouseDao warehouseReleaseToWarehouseDao;
    
    @Resource
    private HistoryForecastDataService historyForecastDataService;

    @Resource
    private OemService oemService;

    @Override
    public BaseResponse<Void> doCreate(PartRiskLevelDTO partRiskLevelDTO) {
        // 0.数据转换
        PartRiskLevelDO partRiskLevelDO = PartRiskLevelConvertor.INSTANCE.dto2Do(partRiskLevelDTO);
        PartRiskLevelPO partRiskLevelPO = PartRiskLevelConvertor.INSTANCE.dto2Po(partRiskLevelDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        partRiskLevelDomainService.validation(partRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(partRiskLevelPO);
        partRiskLevelDao.insertWithPrimaryKey(partRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PartRiskLevelDTO partRiskLevelDTO) {
        // 0.数据转换
        PartRiskLevelDO partRiskLevelDO = PartRiskLevelConvertor.INSTANCE.dto2Do(partRiskLevelDTO);
        PartRiskLevelPO partRiskLevelPO = PartRiskLevelConvertor.INSTANCE.dto2Po(partRiskLevelDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        partRiskLevelDomainService.validation(partRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(partRiskLevelPO);
        partRiskLevelDao.update(partRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PartRiskLevelDTO> list) {
        List<PartRiskLevelPO> newList = PartRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        partRiskLevelDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<PartRiskLevelDTO> list) {
        List<PartRiskLevelPO> newList = PartRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        partRiskLevelDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return partRiskLevelDao.deleteBatch(idList);
        }
        return partRiskLevelDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PartRiskLevelVO selectByPrimaryKey(String id) {
        PartRiskLevelPO po = partRiskLevelDao.selectByPrimaryKey(id);
        return PartRiskLevelConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_material_risk_level")
    public List<PartRiskLevelVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_material_risk_level")
    public List<PartRiskLevelVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PartRiskLevelVO> dataList = partRiskLevelDao.selectByCondition(sortParam, queryCriteriaParam);
        PartRiskLevelServiceImpl target = SpringBeanUtils.getBean(PartRiskLevelServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PartRiskLevelVO> selectByParams(Map<String, Object> params) {
        List<PartRiskLevelPO> list = partRiskLevelDao.selectByParams(params);
        return PartRiskLevelConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PartRiskLevelVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 详情查询
     *
     * @param materialRiskLevelId
     * @return
     */
    @Override
    public List<PartRiskLevelDetailVO> selectDetailByMaterialId(String materialRiskLevelId) {
        Map<String, Map<String, String>> result = MapUtil.newHashMap();
        Map<String, Object> detailParam = MapUtil.newHashMap();
        detailParam.put("enabled", YesOrNoEnum.YES.getCode());
        detailParam.put("materialRiskLevelId", materialRiskLevelId);
        List<PartRiskLevelDetailVO> detailVOS = partRiskLevelDetailService.selectVOByParams(detailParam);
        if (CollectionUtils.isEmpty(detailVOS)) {
            return Collections.emptyList();
        }
        return detailVOS;
    }

    @Override
    public BaseResponse<Void> reCalculate(String incrementCalculateFlag, String scenario) {
    	if(StringUtils.isEmpty(scenario)) {
    		scenario = SystemHolder.getScenario();
    	}
        // 获取评分规则配置
        Map<String, Object> riskLevelRuleParams = new HashMap<>();
        riskLevelRuleParams.put("enabled", YesOrNoEnum.YES.getCode());
        List<RiskLevelRuleVO> riskLevelRuleVOS = riskLevelRuleService.selectByParams(riskLevelRuleParams);
        if (CollectionUtils.isEmpty(riskLevelRuleVOS)) {
            log.warn("评分规则配置有效数据为空");
            return BaseResponse.success("计算成功");
        }
        riskLevelRuleParams.put("ruleIds", riskLevelRuleVOS.stream().map(RiskLevelRuleVO::getId).collect(Collectors.toList()));
        // 获取规则详情数据
        List<RiskLevelRuleDetailVO> riskLevelRuleDetailVOList = riskLevelRuleDetailService.selectByParams(riskLevelRuleParams);
        Map<String, List<RiskLevelRuleDetailVO>> riskLevelRuleDetailMapOfRuleId = riskLevelRuleDetailVOList.stream().collect(Collectors.groupingBy(RiskLevelRuleDetailVO::getRuleId));
        // 查询主机厂车型数据
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectAll();
        List<String> oemCodeList = oemVehicleModelVOS.stream()
                .map(OemVehicleModelVO::getOemCode).distinct().collect(Collectors.toList());
        List<String> vehicleModeCodeList = oemVehicleModelVOS.stream()
                .map(OemVehicleModelVO::getOemVehicleModelCode).distinct().collect(Collectors.toList());
        // 查询主机厂风险等级
        Map<String, Object> oemRiskLevelParams = new HashMap<>();
        oemRiskLevelParams.put("oemCodes", oemCodeList);
        List<OemRiskLevelVO> oemRiskLevelVOS = oemRiskLevelService.selectByParams(oemRiskLevelParams);
        Map<String, List<OemRiskLevelVO>> oemRiskLevelCollectOfOemCode = oemRiskLevelVOS.stream().collect(Collectors.groupingBy(OemRiskLevelVO::getOemCode));
        Map<String, Object> productParams = new HashMap<>();
        productParams.put("enabled", YesOrNoEnum.YES.getCode());
        productParams.put("vehicleCodeList" , vehicleModeCodeList);
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        if(StringUtils.isNotEmpty(rangeData)) {
        	List<String> stockPointCodes = Arrays.asList(rangeData.split(","));
        	productParams.put("stockPointCodes" , stockPointCodes);
        }
        List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductStockPointByParams(scenario, productParams);
        //查询存量数据的风险等级
        List<PartRiskLevelVO> currLevelList = partRiskLevelDao.selectLevelByOmeCodes(oemCodeList);
        Map<String, String> currLevelMap =  currLevelList.stream()
        		.filter( e-> StringUtils.isNotEmpty(e.getMaterialRiskLevel()))
        		.collect(Collectors.toMap(e -> String.join("&", e.getOemCode(), e.getVehicleModelCode(), e.getProductCode()),PartRiskLevelVO::getMaterialRiskLevel,(V1, V2) -> V1));
        //获取存量不需要重新计算的风险等级数据
        Map<String, String> unCalculateMap = currLevelList.stream()
        		.filter(e -> StringUtils.isNotEmpty(e.getUpdateRemark())
        				|| YesOrNoEnum.YES.getCode().equals(e.getChiefCheckFlag())
        				|| YesOrNoEnum.YES.getCode().equals(e.getPlannerCheckFlag()))
        		.collect(Collectors.toMap(e -> String.join("&", e.getOemCode(), e.getVehicleModelCode(), e.getProductCode()),
        				PartRiskLevelVO::getId,(V1, V2) -> V1));
        
        //获取数据字典中主机厂名称，产品编码的过滤条件
        List<OemVO> oemVOList = oemService.selectByParams(oemRiskLevelParams);
        Map<String, String> oemMap = oemVOList.stream().collect(Collectors.toMap(OemVO::getOemCode,OemVO::getOemName,(v1, v2) -> v1));
        
        List<CollectionValueVO> isbjEquesFilters = ipsFeign.getByCollectionCode("PART_RISK_LEVEL_RULE");
        List<CollectionValueVO> oemCodeContainsFilters = ipsFeign.getByCollectionCode("PART_RISK_LEVEL_RULE2");
        productStockPointVOList.removeIf( item ->{
        	if(CollectionUtils.isNotEmpty(isbjEquesFilters)) {
        		for (CollectionValueVO isbjEquesFilter : isbjEquesFilters) {
					if(StringUtils.isNotEmpty(item.getIsbj()) && item.getIsbj().equals(isbjEquesFilter.getCollectionValue())) {
						return true;
					}
				}
        	}
        	//订单计划员的字段为空，”重新计算“和”增量计算“的时候跳过不计算
        	if(StringUtils.isEmpty(item.getOrderPlanner())) {
        		return true;
        	}
        	//物料销售类型为代销的不计算
        	if(ProductSaleTypeEnum.DX.getCode().equals(item.getSaleType())) {
        		return true;
        	}
        	return false;
        });
        
        Map<String, List<NewProductStockPointVO>> newProductStockPointMapOfVehicleModelCode =
                productStockPointVOList.stream().collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));

        //查询历史三个月的出货数据
        List<String> productCodeList = productStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        //获取发货记录
        String startYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "yyyyMM");
        String endYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "yyyyMM");
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList =
                this.selectMonthData(oemCodeList, productCodeList, startYearMonth, endYearMonth);
        Map<String, BigDecimal> warehouseReleaseRecordMap = warehouseReleaseRecordMonthList.stream()
        		.collect(Collectors.toMap( e-> String.join("#", e.getOemCode(), e.getItemCode(), e.getYearMonth()), WarehouseReleaseRecordMonthVO::getSumQty,(v1, v2) -> v1.add(v2)));
        //获取历史需求预测数据
        List<HistoryMonthForecastDataVO> historyMonthForecastList = historyForecastDataService
        		.selectMonthVOByItemCodes(oemCodeList, productCodeList, startYearMonth, endYearMonth);
        Map<String, BigDecimal> historyMonthForecastMap = historyMonthForecastList.stream()
        		.collect(Collectors.toMap( e-> String.join("#", e.getOemCode(), e.getProductCode(), e.getForecastTime()), HistoryMonthForecastDataVO::getForecastQuantity,(v1, v2) -> v1));

        Date nowDate = new Date();
        Date startDate = DateUtils.moveMonth(nowDate, -6);
        Date endDate = DateUtils.moveMonth(nowDate, -1);
        startDate = DfpDateUtils.getFirstDayOfMonth(startDate);
        endDate = getLastMomentOfMonth(endDate);
        // 当前月份
        String nowMonth = DateUtils.dateToString(nowDate, "yyyyMM");
        // 查询仓库收发仓数据- 发货数量
        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = warehouseReleaseRecordService.actualDelivery(startDate, endDate, null);
        Map<String, List<WarehouseReleaseRecordVO>> warehouseReleaseRecordVOMapOfItemCode = warehouseReleaseRecordVOS.stream().collect(Collectors.groupingBy(WarehouseReleaseRecordVO::getItemCode));
        
        Date now = new Date();
        List<PartRiskLevelDTO> insertMaterialRiskLeveDtoList = new ArrayList<>();
        List<PartRiskLevelDetailDTO> insertMaterialRiskLeveDetailDtoList = new ArrayList<>();
        List<String> notDeleteIds = new ArrayList<>();
        for (OemVehicleModelVO oemVehicleModelVO : oemVehicleModelVOS) {
        	String oemCode = oemVehicleModelVO.getOemCode();
        	String oemName = oemMap.get(oemCode);
        	//过滤主机厂名称中包含指定字符串的数据
        	Boolean continueFlag = false;
        	if(CollectionUtils.isNotEmpty(oemCodeContainsFilters) && StringUtils.isNotEmpty(oemName)) {
        		for (CollectionValueVO oemCodeContainsFilter : oemCodeContainsFilters) {
					if(oemName.contains(oemCodeContainsFilter.getCollectionValue())) {
						continueFlag = true;
						break;
					}
				}
        	}
        	if(continueFlag) {
        		continue;
        	}
            // 获取车型的物料信息(需要拿到本厂编码)  +  过滤条件（只要库存点属于销售组织的）
            List<NewProductStockPointVO> newProductStockPointVOS =
                    Optional.ofNullable(newProductStockPointMapOfVehicleModelCode.get(oemVehicleModelVO.getOemVehicleModelCode()))
                            .orElse(Collections.emptyList())
                            .stream()
                            .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
                log.warn("车型匹配的物料信息为空");
                continue;
            }
            for (NewProductStockPointVO newProductStockPointVO : newProductStockPointVOS) {
            	// 获取物料信息的SOP,EOP时间
                Date productSop = newProductStockPointVO.getProductSop();
                Date productEop = newProductStockPointVO.getProductEop();
            	String productCode = newProductStockPointVO.getProductCode();
            	String vehicleModelCode = newProductStockPointVO.getVehicleModelCode();
            	String saleType = newProductStockPointVO.getSaleType();
            	
            	//如果该主机厂+编码已经存在且”修改原因“有值，则跳过不重新计算,销售类型为代销的也不进行计算
            	String unCalculateKey = String.join("&", oemCode, vehicleModelCode, productCode);
            	if(YesOrNoEnum.YES.getCode().equals(incrementCalculateFlag) && unCalculateMap.containsKey(unCalculateKey)) {
            		notDeleteIds.add(unCalculateMap.get(unCalculateKey));
            		continue;
            	}
            	
            	//如果连续三个月没有预测和出货数据，并且当前时间大于该编码EOP时间，就不计算该编码，如果没超过EOP时间，就记为大幅波动，且风险等级为高
            	Boolean checkEmptyWarehouseAndForecast = checkEmptyWarehouseAndForecast(warehouseReleaseRecordMap, historyMonthForecastMap, oemCode,
						productCode);
            	String materialRiskLevel = "";
            	if(checkEmptyWarehouseAndForecast && productEop != null && now.compareTo(productEop) > 0) {
            		continue;
            	}else if(checkEmptyWarehouseAndForecast && productEop != null && now.compareTo(productEop) <= 0){
            		materialRiskLevel = "高";
            	}
                PartRiskLevelDTO partRiskLevelDTO = new PartRiskLevelDTO();
                partRiskLevelDTO.setId(UUIDUtil.getUUID());
                partRiskLevelDTO.setOemCode(oemCode);
                partRiskLevelDTO.setVehicleModelCode(vehicleModelCode);
                partRiskLevelDTO.setProductCode(productCode);
                // 手动判断是否已存在该元素（每个风险等级只计算一次）
                boolean exists = insertMaterialRiskLeveDtoList.stream()
                        .anyMatch(existing -> existing.getOemCode().equals(partRiskLevelDTO.getOemCode()) &&
                                existing.getVehicleModelCode().equals(partRiskLevelDTO.getVehicleModelCode()) &&
                                existing.getProductCode().equals(partRiskLevelDTO.getProductCode()));
                //维护
                String oldMaterialRiskLevel = currLevelMap.get(String.join("&", oemCode, partRiskLevelDTO.getVehicleModelCode(), partRiskLevelDTO.getProductCode()));
                partRiskLevelDTO.setMaterialRiskLevelMonth(oldMaterialRiskLevel);
                if (!exists) {
                	partRiskLevelDTO.setChiefCheckFlag(YesOrNoEnum.NO.getCode());
                	partRiskLevelDTO.setPlannerCheckFlag(YesOrNoEnum.NO.getCode());;
                    insertMaterialRiskLeveDtoList.add(partRiskLevelDTO);
                } else {
                    continue;
                }

                int totalScore = 0;
                List<RiskLevelRuleDetailVO> totalScoreRuleDetailList = new ArrayList<>();
                for (RiskLevelRuleVO riskLevelRuleVO : riskLevelRuleVOS) {
                    // 获取规则详情
                    List<RiskLevelRuleDetailVO> ruleDetailVOList = riskLevelRuleDetailMapOfRuleId.get(riskLevelRuleVO.getId());
                    if (CollectionUtils.isEmpty(ruleDetailVOList)) {
                        log.warn("{}，该规则没有维护详情数据", riskLevelRuleVO.getRuleName());
                        continue;
                    }
                    Map<String, RiskLevelRuleDetailVO> riskLevelRuleDetailVOMapOfPerformanceSituation = ruleDetailVOList.stream().collect(Collectors.toMap(RiskLevelRuleDetailVO::getPerformanceSituation, Function.identity(), (k1, k2) -> k2));
                    if (StringUtils.equals("连续3个月预测准确率", riskLevelRuleVO.getRuleName())) {
                        //TODO 业务需求暂未确定
                        PartRiskLevelDetailDTO partRiskLevelDetailDTO = new PartRiskLevelDetailDTO();
                        partRiskLevelDetailDTO.setMaterialRiskLevelId(partRiskLevelDTO.getId());
                        partRiskLevelDetailDTO.setRiskLevelRuleId(riskLevelRuleVO.getId());
                        //获取连续3个月分数
                        int defaultScore = getThreeMonthScore(warehouseReleaseRecordMap, historyMonthForecastMap, 
                        		partRiskLevelDTO, partRiskLevelDetailDTO);
                        if(StringUtils.isNotEmpty(materialRiskLevel)) {
                        	partRiskLevelDetailDTO.setPerformanceSituation("大幅波动");
                        	defaultScore = 10;
                        }
                        partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(defaultScore));
                        insertMaterialRiskLeveDetailDtoList.add(partRiskLevelDetailDTO);
                        totalScore += defaultScore;
                    }

                    if (StringUtils.equals("主机厂风险等级", riskLevelRuleVO.getRuleName())) {
                        PartRiskLevelDetailDTO partRiskLevelDetailDTO = new PartRiskLevelDetailDTO();
                        partRiskLevelDetailDTO.setMaterialRiskLevelId(partRiskLevelDTO.getId());
                        partRiskLevelDetailDTO.setRiskLevelRuleId(riskLevelRuleVO.getId());
                        int riskScore = 2; // 默认分数
                        // 获取主机厂风险等级表
                        List<OemRiskLevelVO> oemRiskLevelVOList = oemRiskLevelCollectOfOemCode.get(oemVehicleModelVO.getOemCode());
                        if (CollectionUtils.isNotEmpty(oemRiskLevelVOList)) {
                            // 根据月份进行分组
                            Map<String, OemRiskLevelVO> oemRiskLevelVOMapOfEstimateTime = oemRiskLevelVOList.stream()
                                    .collect(Collectors.toMap(OemRiskLevelVO::getEstimateTime, Function.identity(), (k1, k2) -> k2));
                            // 获取当前月份的主机厂风险等级
                            OemRiskLevelVO oemRiskLevelVO = oemRiskLevelVOMapOfEstimateTime.get(nowMonth);
                            if (oemRiskLevelVO == null) {
                                // 如果当前月份没有，取最新的月份
                                oemRiskLevelVO = oemRiskLevelVOList.stream()
                                        .sorted(Comparator.comparing(OemRiskLevelVO::getEstimateTime).reversed())
                                        .findFirst()
                                        .orElse(null);
                            }
                            if (oemRiskLevelVO != null) {
                                // 主机厂风险等级
                                String riskLevel = oemRiskLevelVO.getRiskLevel();
                                RiskLevelRuleDetailVO riskLevelRuleDetailVO = riskLevelRuleDetailVOMapOfPerformanceSituation.get(riskLevel);
                                if (riskLevelRuleDetailVO != null) {
                                    riskScore = Integer.parseInt(riskLevelRuleDetailVO.getPerformanceScore());
                                }
                                partRiskLevelDetailDTO.setPerformanceSituation(oemRiskLevelVO.getRiskLevel());
                            }
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(riskScore));
                        } else {
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(riskScore));
                        }
                        insertMaterialRiskLeveDetailDtoList.add(partRiskLevelDetailDTO);
                        totalScore += riskScore;
                    }

                    if (StringUtils.equals("半年内月均消耗", riskLevelRuleVO.getRuleName())) {
                        PartRiskLevelDetailDTO partRiskLevelDetailDTO = new PartRiskLevelDetailDTO();
                        partRiskLevelDetailDTO.setMaterialRiskLevelId(partRiskLevelDTO.getId());
                        partRiskLevelDetailDTO.setRiskLevelRuleId(riskLevelRuleVO.getId());
                        int releaseRecordScore = 1; // 默认分数
                        // 获取发货数量
                        List<WarehouseReleaseRecordVO> releaseRecordVOList = warehouseReleaseRecordVOMapOfItemCode.get(newProductStockPointVO.getProductCode());
                        if (CollectionUtils.isNotEmpty(releaseRecordVOList)) {
                            BigDecimal totalSum = releaseRecordVOList.stream().map(WarehouseReleaseRecordVO::getSumQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                            // 计算逻辑为半年内月均消耗= avg（实际发货值）
                            BigDecimal qtyAvg = totalSum.divide(new BigDecimal(6), 2, RoundingMode.HALF_UP);
                            for (RiskLevelRuleDetailVO riskLevelRuleDetailVO : ruleDetailVOList) {
                                if (riskLevelRuleDetailVO.getPerformanceSituation().contains("<")) {
                                    // 小于
                                    String value = riskLevelRuleDetailVO.getPerformanceSituation().split("<")[1];
                                    if (qtyAvg.compareTo(new BigDecimal(value)) <= 0) {
                                        releaseRecordScore = Integer.parseInt(riskLevelRuleDetailVO.getPerformanceScore());
                                        break;
                                    }
                                }
                                if (riskLevelRuleDetailVO.getPerformanceSituation().contains(">")) {
                                    // 大于
                                    String value = riskLevelRuleDetailVO.getPerformanceSituation().split(">")[1];
                                    if (qtyAvg.compareTo(new BigDecimal(value)) >= 0) {
                                        releaseRecordScore = Integer.parseInt(riskLevelRuleDetailVO.getPerformanceScore());
                                        break;
                                    }
                                }
                            }
                            partRiskLevelDetailDTO.setPerformanceSituation(qtyAvg.toString());
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(releaseRecordScore));
                        } else {
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(releaseRecordScore));
                        }
                        insertMaterialRiskLeveDetailDtoList.add(partRiskLevelDetailDTO);
                        totalScore += releaseRecordScore;
                    }

                    if (StringUtils.equals("零件生命周期", riskLevelRuleVO.getRuleName())) {
                        PartRiskLevelDetailDTO partRiskLevelDetailDTO = new PartRiskLevelDetailDTO();
                        partRiskLevelDetailDTO.setMaterialRiskLevelId(partRiskLevelDTO.getId());
                        partRiskLevelDetailDTO.setRiskLevelRuleId(riskLevelRuleVO.getId());
                        int partLifeCycleScore = 1; // 默认分数
                        if (null != productSop && null != productEop) {
                            // int partLifeCycleScore = 0;
                            String partLifeCycleStatus = "";
                            if (nowDate.before(productSop)) {
                                // 新项目阶段
                                partLifeCycleStatus = "新项目阶段";
                            } else if (isInRange(nowDate, productSop, DateUtils.moveDay(productSop, 90))) {
                                // 爬坡阶段
                                partLifeCycleStatus = "爬坡阶段";
                            } else if (nowDate.after(DateUtils.moveDay(productSop, 90)) 
                            		&& nowDate.before(DateUtils.moveDay(productEop, -90))) {
                            	//(sop时间+90天) < 当前时间 < (EPO时间 - 90)
                                partLifeCycleStatus = "量产阶段";
                            } else if (DateUtils.moveDay(productEop, -90).compareTo(nowDate) <= 0 ) {
                            	//(EPO时间 - 90) <= 当前时间
                                partLifeCycleStatus = "EOP阶段";
                            }
                            RiskLevelRuleDetailVO riskLevelRuleDetailVO = riskLevelRuleDetailVOMapOfPerformanceSituation.get(partLifeCycleStatus);
                            if (null != riskLevelRuleDetailVO) {
                                partLifeCycleScore = Integer.parseInt(riskLevelRuleDetailVO.getPerformanceScore());
                            }
                            // totalScore += partLifeCycleScore;
                            partRiskLevelDetailDTO.setPerformanceSituation(partLifeCycleStatus);
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(partLifeCycleScore));
                        } else {
                            partRiskLevelDetailDTO.setPerformanceScore(String.valueOf(partLifeCycleScore));
                        }
                        insertMaterialRiskLeveDetailDtoList.add(partRiskLevelDetailDTO);
                        totalScore += partLifeCycleScore;
                    }

                    if (StringUtils.equals("总评分", riskLevelRuleVO.getRuleName())) {
                        totalScoreRuleDetailList = ruleDetailVOList;
                    }
                }
                if (CollectionUtils.isNotEmpty(totalScoreRuleDetailList) && StringUtils.isEmpty(materialRiskLevel)) {
                    for (RiskLevelRuleDetailVO riskLevelRuleDetailVO : totalScoreRuleDetailList) {
                        String performanceSituation = riskLevelRuleDetailVO.getPerformanceSituation();
                        String[] split = performanceSituation.split(",");
                        if (StringUtils.equals("-∞", split[0])) {
                            if (totalScore <= Integer.parseInt(split[1])) {
                                materialRiskLevel = riskLevelRuleDetailVO.getPerformanceScore();
                                break;
                            }
                        }
                        if (StringUtils.equals("∞", split[1])) {
                            if (totalScore >= Integer.parseInt(split[0])) {
                                materialRiskLevel = riskLevelRuleDetailVO.getPerformanceScore();
                                break;
                            }
                        }
                        if (!StringUtils.equals("-∞", split[0]) && !StringUtils.equals("∞", split[1])) {
                            if (totalScore >= Integer.parseInt(split[0]) && totalScore <= Integer.parseInt(split[1])) {
                                materialRiskLevel = riskLevelRuleDetailVO.getPerformanceScore();
                                break;
                            }
                        }
                    }
                }
                partRiskLevelDTO.setTotalScore(BigDecimal.valueOf(totalScore));
                partRiskLevelDTO.setMaterialRiskLevel(materialRiskLevel);
                partRiskLevelDTO.setMaterialRiskLevelCalculate(materialRiskLevel);
            }
        }

        if (CollectionUtils.isNotEmpty(insertMaterialRiskLeveDtoList)) {
            // 清除上次计算的数据
            this.doDeleteAll(notDeleteIds);
            BulkOperationUtils.bulkUpdateOrCreate(insertMaterialRiskLeveDtoList, 
            		addList -> this.doCreateBatch(addList), 2000);
            
        }
        if (CollectionUtils.isNotEmpty(insertMaterialRiskLeveDetailDtoList)) {
            // 清除上次计算的数据
            partRiskLevelDetailService.doDeleteAll(notDeleteIds);
            partRiskLevelDetailService.doCreateBatch(insertMaterialRiskLeveDetailDtoList);
            BulkOperationUtils.bulkUpdateOrCreate(insertMaterialRiskLeveDetailDtoList, 
            		addList -> partRiskLevelDetailService.doCreateBatch(addList), 2000);
        }
        return BaseResponse.success("计算成功");
    }

    /**
     * 校验连续三个月没有预测和出货数据
     * @param warehouseReleaseRecordMap
     * @param historyMonthForecastMap
     * @param oemCode
     * @param productCode
     */
	private Boolean checkEmptyWarehouseAndForecast(Map<String, BigDecimal> warehouseReleaseRecordMap,
			Map<String, BigDecimal> historyMonthForecastMap, String oemCode, String productCode) {
		String startMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "yyyyMM");
		String centerMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -2), "yyyyMM");
		String endMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "yyyyMM");
		//分别获取前三个月的发货数量，预测值
		String yearMonthKey = String.join("#", oemCode, productCode, startMonth);
		BigDecimal startDeliveryQty = warehouseReleaseRecordMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
		BigDecimal startForecastQty = historyMonthForecastMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
		String centerMonthKey = String.join("#", oemCode, productCode, centerMonth);
		BigDecimal centerDeliveryQty = warehouseReleaseRecordMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
		BigDecimal centerForecastQty = historyMonthForecastMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
		String endMonthKey = String.join("#", oemCode, productCode, endMonth);
		BigDecimal endDeliveryQty = warehouseReleaseRecordMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
		BigDecimal endForecastQty = historyMonthForecastMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
		if(startDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && startForecastQty.compareTo(BigDecimal.ZERO) == 0
				&& centerDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && centerForecastQty.compareTo(BigDecimal.ZERO) == 0
				&& endDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && endForecastQty.compareTo(BigDecimal.ZERO) == 0) {
			return true;
		}
		return false;
	}

    private List<WarehouseReleaseRecordMonthVO> selectMonthData(List<String> oemCodeList, List<String> productCodeList, String startYearMonth, String endYearMonth) {
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
                .selectMonthVOByItemCodes(oemCodeList, productCodeList, startYearMonth, endYearMonth);
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList2 = warehouseReleaseToWarehouseDao
                .selectMonthVOByItemCodes(oemCodeList, productCodeList, startYearMonth, endYearMonth);
        warehouseReleaseRecordMonthList.addAll(warehouseReleaseRecordMonthList2);
        return warehouseReleaseRecordMonthList;
    }

	private int getThreeMonthScore(Map<String, BigDecimal> warehouseReleaseRecordMap, Map<String, BigDecimal> historyMonthForecastMap,
			PartRiskLevelDTO partRiskLevelDTO, PartRiskLevelDetailDTO partRiskLevelDetailDTO) {
		String startYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "yyyyMM");
        String centerYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -2), "yyyyMM");
        String endYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "yyyyMM");
        int defaultScore = 1; // 默认分数
		String oemCode = partRiskLevelDTO.getOemCode();
		String productCode = partRiskLevelDTO.getProductCode();
		//分别获取前三个月的发货数量，预测值,偏差率 = (实际发货量-业务预测量)/业务预测量*100
		String yearMonthKey = String.join("#", oemCode, productCode, startYearMonth);
		BigDecimal startDeliveryQty = warehouseReleaseRecordMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
		BigDecimal startForecastQty = historyMonthForecastMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
		BigDecimal startDeviationRatio = BigDecimal.valueOf(100);
		if(startForecastQty.compareTo(BigDecimal.ZERO) != 0) {
			startDeviationRatio = (startDeliveryQty.subtract(startForecastQty))
					.multiply(BigDecimal.valueOf(100)).divide(startForecastQty, 0, BigDecimal.ROUND_HALF_UP);
		}else if(startDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && startForecastQty.compareTo(BigDecimal.ZERO) == 0) {
			startDeviationRatio = BigDecimal.valueOf(0);
		}
		
		String centerMonthKey = String.join("#", oemCode, productCode, centerYearMonth);
		BigDecimal centerDeliveryQty = warehouseReleaseRecordMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
		BigDecimal centerForecastQty = historyMonthForecastMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
		BigDecimal centerDeviationRatio = BigDecimal.valueOf(100);
		if(centerForecastQty.compareTo(BigDecimal.ZERO) != 0) {
			centerDeviationRatio = (centerDeliveryQty.subtract(centerForecastQty))
					.multiply(BigDecimal.valueOf(100)).divide(centerForecastQty, 0, BigDecimal.ROUND_HALF_UP);
		}else if(centerDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && centerForecastQty.compareTo(BigDecimal.ZERO) == 0) {
			centerDeviationRatio = BigDecimal.valueOf(0);
		}
			
		String endMonthKey = String.join("#", oemCode, productCode, endYearMonth);
		BigDecimal endDeliveryQty = warehouseReleaseRecordMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
		BigDecimal endForecastQty = historyMonthForecastMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
		BigDecimal endDeviationRatio = BigDecimal.valueOf(100);
		if(endForecastQty.compareTo(BigDecimal.ZERO) != 0) {
			endDeviationRatio = (endDeliveryQty.subtract(endForecastQty))
					.multiply(BigDecimal.valueOf(100)).divide(endForecastQty, 0, BigDecimal.ROUND_HALF_UP);
		}else if(endDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && endForecastQty.compareTo(BigDecimal.ZERO) == 0) {
			endDeviationRatio = BigDecimal.valueOf(0);
		}
		
		BigDecimal forty = BigDecimal.valueOf(40);
		BigDecimal fifteen = BigDecimal.valueOf(15);
		BigDecimal negativeFifteen = BigDecimal.valueOf(-15);
		if(startDeviationRatio.abs().compareTo(forty) >= 0 || centerDeviationRatio.abs().compareTo(forty) >= 0
				|| endDeviationRatio.abs().compareTo(forty) >= 0) {
			//至少一个月的绝对值>=40
			defaultScore = 10;
			partRiskLevelDetailDTO.setPerformanceSituation("大幅波动");
		}else if(startDeviationRatio.compareTo(fifteen) > 0 && centerDeviationRatio.compareTo(fifteen) > 0
				&& endDeviationRatio.compareTo(fifteen) > 0) {
			//每个月 > 15
			defaultScore = 10;
			partRiskLevelDetailDTO.setPerformanceSituation("连续增长");
		}else if(startDeviationRatio.compareTo(negativeFifteen) < 0 && centerDeviationRatio.compareTo(negativeFifteen) < 0
				&& endDeviationRatio.compareTo(negativeFifteen) < 0) {
			//每个月 < -15
			defaultScore = 10;
			partRiskLevelDetailDTO.setPerformanceSituation("连续下降");
		}else if(startDeviationRatio.compareTo(fifteen) <= 0 && startDeviationRatio.compareTo(negativeFifteen) >= 0
				&& centerDeviationRatio.compareTo(fifteen) <= 0 && centerDeviationRatio.compareTo(negativeFifteen) >= 0
				&& endDeviationRatio.compareTo(fifteen) <= 0 && endDeviationRatio.compareTo(negativeFifteen) >= 0) {
			//每个月在-15——15之间
			defaultScore = 1;
			partRiskLevelDetailDTO.setPerformanceSituation("平稳变化");
		}else {
			defaultScore = 3;
			partRiskLevelDetailDTO.setPerformanceSituation("小幅波动");
		}
		return defaultScore;
	}

    private String getMdsScenario() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        return defaultScenario.getData();
    }

    @Override
    public BaseResponse<Void> updateRiskLevelDetail(String materialRiskLevelDetailId, String materialRiskLevel) {
        return partRiskLevelDetailService.updateRiskLevelDetail(materialRiskLevelDetailId, materialRiskLevel);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PART_RISK_LEVEL.getCode();
    }
    
    @Override
    public List<PartRiskLevelVO> invocation(List<PartRiskLevelVO> dataList, Map<String, Object> params, String invocation) {
    	List<String> productCodes = dataList.stream().map(PartRiskLevelVO::getProductCode).distinct().collect(Collectors.toList());
    	List<String> oemCodeList = dataList.stream().map(PartRiskLevelVO::getOemCode).distinct().collect(Collectors.toList());
    	if(CollectionUtils.isNotEmpty(productCodes)) {
    		BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                    "SALE_ORGANIZATION",
                    "INTERNAL", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            if(StringUtils.isNotEmpty(rangeData)) {
            	List<String> stockPointCodes = Arrays.asList(rangeData.split(","));
                List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByParams(defaultScenario.getData(), ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), 
            			"productCodes" , productCodes,
                		"stockPointCodes" , stockPointCodes));
                Map<String,String> productMap = productList.stream()
                		.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getProductName,(V1, V2) -> V1));
                dataList.forEach( e -> {
                	e.setProductName(productMap.get(e.getProductCode()));
                });
            }
    	}
    	
    	//处理零件风险评估明细及月预测，出货，偏差率数据
    	List<String> materialRiskLevelIds = dataList.stream().map(PartRiskLevelVO::getId).collect(Collectors.toList());    	
    	//查询对应风险评估明细
    	Map<String, Object> detailParam = MapUtil.newHashMap();
        detailParam.put("enabled", YesOrNoEnum.YES.getCode());
        detailParam.put("materialRiskLevelIds", materialRiskLevelIds);
        List<PartRiskLevelDetailVO> levelDetails = partRiskLevelDetailService.selectVOByParams(detailParam);
//        List<String> ruleNames = Arrays.asList("零件生命周期","半年内月均消耗","主机厂风险等级");
        Map<String, List<PartRiskLevelDetailVO>> levelDetailMap = levelDetails.stream()
//        		.filter( e-> ruleNames.contains(e.getRuleName()))
        		.collect(Collectors.groupingBy(PartRiskLevelDetailVO::getMaterialRiskLevelId));
        //预测，出货，偏差率数据
        String startYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "yyyyMM");
        String endYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "yyyyMM");
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList =
                this.selectMonthData(oemCodeList, productCodes, startYearMonth, endYearMonth);
        Map<String, BigDecimal> warehouseReleaseRecordMap = warehouseReleaseRecordMonthList.stream()
        		.collect(Collectors.toMap( e-> String.join("#", e.getOemCode(), e.getItemCode(), e.getYearMonth()), WarehouseReleaseRecordMonthVO::getSumQty,(v1, v2) -> v1.add(v2)));
        //获取历史需求预测数据
        List<HistoryMonthForecastDataVO> historyMonthForecastList = historyForecastDataService
        		.selectMonthVOByItemCodes(oemCodeList, productCodes, startYearMonth, endYearMonth);
        Map<String, BigDecimal> historyMonthForecastMap = historyMonthForecastList.stream()
        		.collect(Collectors.toMap( e-> String.join("#", e.getOemCode(), e.getProductCode(), e.getForecastTime()), HistoryMonthForecastDataVO::getForecastQuantity,(v1, v2) -> v1));
    	//表头
        String oneYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "yyyyMM");
        String twoYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -2), "yyyyMM");
        String threeYearMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "yyyyMM");
        String oneMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -3), "MM");
        String twoMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -2), "MM");
        String threeMonth = DateUtils.dateToString(DateUtils.moveMonth(new Date(), -1), "MM");
        List<String> headerList = Arrays.asList(oneMonth + "月预测", twoMonth + "月预测", threeMonth+ "月预测", 
        		oneMonth + "月实际出货", twoMonth + "月实际出货", threeMonth+ "月实际出货", 
        		oneMonth + "月偏差率", twoMonth + "月偏差率", threeMonth+ "月偏差率");
        for (int i = 0; i < dataList.size(); i++) {
        	PartRiskLevelVO partRiskLevelVO = dataList.get(i);
        	if(i == 0) {
        		partRiskLevelVO.setHeaderList(headerList);
        	}
        	List<PartRiskLevelDetailVO> detailVOlist = levelDetailMap.getOrDefault(partRiskLevelVO.getId(), new ArrayList<>());
        	for (PartRiskLevelDetailVO detailVO : detailVOlist) {
				if("零件生命周期".equals(detailVO.getRuleName())) {
					partRiskLevelVO.setPartPerformanceScore(detailVO.getPerformanceScore());
					partRiskLevelVO.setPartPerformanceSituation(detailVO.getPerformanceSituation());
				}else if("半年内月均消耗".equals(detailVO.getRuleName())) {
					partRiskLevelVO.setYearPerformanceScore(detailVO.getPerformanceScore());
					partRiskLevelVO.setYearPerformanceSituation(detailVO.getPerformanceSituation());
				}else if("主机厂风险等级".equals(detailVO.getRuleName())) {
					partRiskLevelVO.setOemPerformanceScore(detailVO.getPerformanceScore());
					partRiskLevelVO.setOemPerformanceSituation(detailVO.getPerformanceSituation());
				}else if("连续3个月预测准确率".equals(detailVO.getRuleName())) {
					partRiskLevelVO.setMonthPerformanceScore(detailVO.getPerformanceScore());
					partRiskLevelVO.setMonthPerformanceSituation(detailVO.getPerformanceSituation());
				}
			}
        	//预测，出货，偏差率
        	List<HeaderPartRiskLevelVO> headerDetailList = new ArrayList<>();
        	String oemCode = partRiskLevelVO.getOemCode();
        	String productCode = partRiskLevelVO.getProductCode();
        	//分别获取前三个月的发货数量，预测值,偏差率 = (实际发货量-业务预测量)/业务预测量*100
    		String yearMonthKey = String.join("#", oemCode, productCode, oneYearMonth);
    		BigDecimal startDeliveryQty = warehouseReleaseRecordMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
    		BigDecimal startForecastQty = historyMonthForecastMap.getOrDefault(yearMonthKey, BigDecimal.ZERO);
    		BigDecimal startDeviationRatio = BigDecimal.valueOf(100);
    		if(startForecastQty.compareTo(BigDecimal.ZERO) != 0) {
    			startDeviationRatio = (startDeliveryQty.subtract(startForecastQty))
    					.multiply(BigDecimal.valueOf(100)).divide(startForecastQty, 0, BigDecimal.ROUND_HALF_UP);
    		}else if(startDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && startForecastQty.compareTo(BigDecimal.ZERO) == 0) {
    			startDeviationRatio = BigDecimal.valueOf(0);
    		}
    		String centerMonthKey = String.join("#", oemCode, productCode, twoYearMonth);
    		BigDecimal centerDeliveryQty = warehouseReleaseRecordMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
    		BigDecimal centerForecastQty = historyMonthForecastMap.getOrDefault(centerMonthKey, BigDecimal.ZERO);
    		BigDecimal centerDeviationRatio = BigDecimal.valueOf(100);
    		if(centerForecastQty.compareTo(BigDecimal.ZERO) != 0) {
    			centerDeviationRatio = (centerDeliveryQty.subtract(centerForecastQty))
    					.multiply(BigDecimal.valueOf(100)).divide(centerForecastQty, 0, BigDecimal.ROUND_HALF_UP);
    		}else if(centerDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && centerForecastQty.compareTo(BigDecimal.ZERO) == 0) {
    			centerDeviationRatio = BigDecimal.valueOf(0);
    		}
    		String endMonthKey = String.join("#", oemCode, productCode, threeYearMonth);
    		BigDecimal endDeliveryQty = warehouseReleaseRecordMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
    		BigDecimal endForecastQty = historyMonthForecastMap.getOrDefault(endMonthKey, BigDecimal.ZERO);
    		BigDecimal endDeviationRatio = BigDecimal.valueOf(100);
    		if(endForecastQty.compareTo(BigDecimal.ZERO) != 0) {
    			endDeviationRatio = (endDeliveryQty.subtract(endForecastQty))
    					.multiply(BigDecimal.valueOf(100)).divide(endForecastQty, 0, BigDecimal.ROUND_HALF_UP);
    		}else if(endDeliveryQty.compareTo(BigDecimal.ZERO) == 0 && endForecastQty.compareTo(BigDecimal.ZERO) == 0) {
    			endDeviationRatio = BigDecimal.valueOf(0);
    		}
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(0), startForecastQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(1), centerForecastQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(2), endForecastQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(3), startDeliveryQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(4), centerDeliveryQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(5), endDeliveryQty.stripTrailingZeros().toPlainString()));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(6), startDeviationRatio.stripTrailingZeros().toPlainString()+ "%"));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(7), centerDeviationRatio.stripTrailingZeros().toPlainString()+ "%"));
    		headerDetailList.add(new HeaderPartRiskLevelVO(headerList.get(8), endDeviationRatio.stripTrailingZeros().toPlainString()+ "%"));
        	partRiskLevelVO.setHeaderDetailList(headerDetailList);
		}
        return dataList;
    }

    private boolean isInRange(Date nowDate, Date startDate, Date endDate) {
        return !nowDate.before(startDate) && !nowDate.after(endDate);
    }

    /**
     * 获取date月的最后一天
     *
     * @param date
     * @return
     */
    private Date getLastMomentOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置为本月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        // 将时间部分设置为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date lastMomentOfMonth = calendar.getTime();
        return lastMomentOfMonth;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return partRiskLevelDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<PartRiskLevelVO> selectByProductCodeList(List<String> productCodeList) {
        return partRiskLevelDao.selectByProductCodeList(productCodeList, null);
    }

    @Override
    public void doDeleteAll(List<String> notDeleteIds) {
        partRiskLevelDao.deleteAll(notDeleteIds);
    }

    @Override
    public List<LabelValue<String>> getPartRiskLevel(String oemCode, String productCode) {
        HashMap<String, Object> param = new HashMap<>(2);
        param.put("oemCode", oemCode);
        param.put("productCode", productCode);
        List<PartRiskLevelVO> partRiskLevelVOS = partRiskLevelService.selectByParams(param);
        if (CollectionUtils.isNotEmpty(partRiskLevelVOS)) {
            List<LabelValue<String>> list = partRiskLevelVOS.stream()
                    .map(x -> new LabelValue<>(x.getMaterialRiskLevel(), x.getMaterialRiskLevel()))
                    .collect(Collectors.toList());
            return list.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                    lv -> lv,
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
        }
        return Collections.emptyList();
    }

	@Override
	public BaseResponse<Void> batchUpdateLevel(PartRiskLevelDTO dto) {
		partRiskLevelDao.batchUpdateLevel(dto);
		return BaseResponse.success(BaseResponse.OP_SUCCESS);
	}

	@Override
	public BaseResponse<Void> doPlannerOrChiefCheck(String plannerCheckFlag, String chiefCheckFlag) {
		//获取当前用户权限下销售组织的物料
		BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
		if(YesOrNoEnum.YES.getCode().equals(plannerCheckFlag)) {
			String rangeData = scenarioBusinessRange.getData().getRangeData();
	        List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of(
	    			"enabled", YesOrNoEnum.YES.getCode(), 
	    			"stockPointCode", rangeData,
	    			"orderPlanner", SystemHolder.getUserId()));
	        if (CollectionUtils.isEmpty(productInfoList)) {
	            throw new BusinessException("当前用户没有配置物料数据权限");
	        }
	        List<String> productCodes = productInfoList.stream().map(NewProductStockPointVO::getProductCode)
	        		.collect(Collectors.toList());
	        partRiskLevelDao.updatePlannerOrChiefCheck(productCodes, plannerCheckFlag, null);
		}else {
			partRiskLevelDao.updatePlannerOrChiefCheck(null, null, chiefCheckFlag);
		}
		return BaseResponse.success(BaseResponse.OP_SUCCESS);
	}

	@Override
	public List<LabelValue<String>> getOemCodeDropDown() {
		return partRiskLevelDao.queryOemCodeDropDown();
	}

	@Override
	public List<LabelValue<String>> getVehicleModelCodeDropDown() {
		return partRiskLevelDao.queryVehicleModelCodeDropDown();
	}

}
