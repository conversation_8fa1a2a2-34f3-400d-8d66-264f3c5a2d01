package com.yhl.scp.dfp.oem.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.oem.convertor.OemAddressInventoryLogConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemAddressInventoryLogDO;
import com.yhl.scp.dfp.oem.domain.service.OemAddressInventoryLogDomainService;
import com.yhl.scp.dfp.oem.dto.OemAddressInventoryLogDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemAddressInventoryLogDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemAddressInventoryLogPO;
import com.yhl.scp.dfp.oem.service.OemAddressInventoryLogService;
import com.yhl.scp.dfp.oem.vo.OemAddressInventoryLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemAddressInventoryLogServiceImpl</code>
 * <p>
 * 主机厂地址_中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-13 21:08:29
 */
@Slf4j
@Service
public class OemAddressInventoryLogServiceImpl extends AbstractService implements OemAddressInventoryLogService {

    @Resource
    private OemAddressInventoryLogDao oemAddressInventoryLogDao;

    @Resource
    private OemAddressInventoryLogDomainService oemAddressInventoryLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(OemAddressInventoryLogDTO oemAddressInventoryLogDTO) {
        // 0.数据转换
        OemAddressInventoryLogDO oemAddressInventoryLogDO =
                OemAddressInventoryLogConvertor.INSTANCE.dto2Do(oemAddressInventoryLogDTO);
        OemAddressInventoryLogPO oemAddressInventoryLogPO =
                OemAddressInventoryLogConvertor.INSTANCE.dto2Po(oemAddressInventoryLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemAddressInventoryLogDomainService.validation(oemAddressInventoryLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemAddressInventoryLogPO);
        oemAddressInventoryLogDao.insert(oemAddressInventoryLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(OemAddressInventoryLogDTO oemAddressInventoryLogDTO) {
        // 0.数据转换
        OemAddressInventoryLogDO oemAddressInventoryLogDO =
                OemAddressInventoryLogConvertor.INSTANCE.dto2Do(oemAddressInventoryLogDTO);
        OemAddressInventoryLogPO oemAddressInventoryLogPO =
                OemAddressInventoryLogConvertor.INSTANCE.dto2Po(oemAddressInventoryLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemAddressInventoryLogDomainService.validation(oemAddressInventoryLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemAddressInventoryLogPO);
        oemAddressInventoryLogDao.update(oemAddressInventoryLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemAddressInventoryLogDTO> list) {
        List<OemAddressInventoryLogPO> newList = OemAddressInventoryLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemAddressInventoryLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OemAddressInventoryLogDTO> list) {
        List<OemAddressInventoryLogPO> newList = OemAddressInventoryLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemAddressInventoryLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemAddressInventoryLogDao.deleteBatch(idList);
        }
        return oemAddressInventoryLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemAddressInventoryLogVO selectByPrimaryKey(String id) {
        OemAddressInventoryLogPO po = oemAddressInventoryLogDao.selectByPrimaryKey(id);
        return OemAddressInventoryLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "OEM_ADDRESS_INVENTORY_LOG")
    public List<OemAddressInventoryLogVO> selectByPage(Pagination pagination, String sortParam,
                                                       String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "OEM_ADDRESS_INVENTORY_LOG")
    public List<OemAddressInventoryLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemAddressInventoryLogVO> dataList = oemAddressInventoryLogDao.selectByCondition(sortParam,
                queryCriteriaParam);
        OemAddressInventoryLogServiceImpl target = springBeanUtils.getBean(OemAddressInventoryLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemAddressInventoryLogVO> selectByParams(Map<String, Object> params) {
        List<OemAddressInventoryLogPO> list = oemAddressInventoryLogDao.selectByParams(params);
        return OemAddressInventoryLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemAddressInventoryLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> syncOemAddressLog(List<OemAddressInventoryLogDTO> list) {
        List<OemAddressInventoryLogDTO> insertDTOS = new ArrayList<>();
        List<OemAddressInventoryLogDTO> updateDTOS = new ArrayList<>();
        Set<String> kids =
                list.stream().map(OemAddressInventoryLogDTO::getKid).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("kids", kids);
        List<OemAddressInventoryLogPO> oldPos = oemAddressInventoryLogDao.selectByParams(map);
        Map<String, OemAddressInventoryLogPO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(
                        Collectors.toMap(OemAddressInventoryLogPO::getKid, Function.identity(), (v1, v2) -> v1));
        for (OemAddressInventoryLogDTO dto : list) {
            if (oldPosMap.containsKey(dto.getKid())) {
                OemAddressInventoryLogPO oldPo = oldPosMap.get(dto.getKid());
                dto.setId(oldPo.getId());
                dto.setCreator(oldPo.getCreator());
                dto.setCreateTime(oldPo.getCreateTime());
                dto.setModifier(oldPo.getModifier());
                dto.setModifyTime(oldPo.getModifyTime());
                dto.setVersionValue(oldPo.getVersionValue());
                dto.setEnabled(oldPo.getEnabled());
                updateDTOS.add(dto);
            } else {
                dto.setEnabled(YesOrNoEnum.YES.getCode());
                insertDTOS.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDTOS)) {
            doCreateBatch(insertDTOS);
        }
        if (CollectionUtils.isNotEmpty(updateDTOS)) {
            doUpdateBatch(updateDTOS);
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncOemAddress(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_CUSTOMER_ADDRESS_FOR_BPIM");
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.OEM_ADDRESS.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM_ADDRESS_INVENTORY_LOG.getCode();
    }

    @Override
    public List<OemAddressInventoryLogVO> invocation(List<OemAddressInventoryLogVO> dataList,
                                                     Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<LabelValue<String>> selectTransitStockDropdown(String transitStockLike, String transitStockCode) {
        return oemAddressInventoryLogDao.selectTransitStockDropdown(StringConvertUtils.convertToLike(transitStockLike), transitStockCode);
    }

    @Override
    public List<LabelValue<String>> selectStockLocationDropdown(String transitStockCode, String stockLocationLike) {
        if (StringUtils.isNotBlank(transitStockCode)) {
            return oemAddressInventoryLogDao.selectStockLocationDropdown(transitStockCode,
                    StringConvertUtils.convertToLike(stockLocationLike));
        }
        return new ArrayList<>();
    }

    @Override
    public List<LabelValue<String>> selectOemAddressDropdown(String transitStockCode, String stockLocationCode,
                                                             String oemAddressLike) {
        if (StringUtils.isNotBlank(transitStockCode) && StringUtils.isNotBlank(stockLocationCode)) {
            return oemAddressInventoryLogDao.selectOemAddressDropdown(transitStockCode, stockLocationCode,
                    StringConvertUtils.convertToLike(oemAddressLike));
        }
        return new ArrayList<>();
    }

	@Override
	public List<LabelValue<String>> selectCustomerAddressDropdown(String customerCode) {
		List<OemAddressInventoryLogPO> vos = oemAddressInventoryLogDao.selectCustomerAddressDropdown(customerCode);
		return vos.stream()
                .map(x -> new LabelValue<>(x.getCustomerAddress() + "(" + x.getCustomerCode() + ")", x.getCustomerAddress()))
                .collect(Collectors.toList());
	}

}