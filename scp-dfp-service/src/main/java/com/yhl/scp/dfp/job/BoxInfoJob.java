package com.yhl.scp.dfp.job;

import cn.hutool.core.map.MapUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.box.service.BoxInfoService;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <code>MesBoxInfoJob</code>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 16:22:11
 */
@Component
@Slf4j
public class BoxInfoJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign mdsFeign;

    @XxlJob("boxInfoJob")
    public ReturnT<String> boxInfoJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步箱体信息job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("ebsOuId", "324");
            params.put("tenantId", scenario.getTenantId());
            mdsFeign.synBoxInfo(scenario.getDataBaseName(), params);

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步箱体信息job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("testXxlJobEmail")
    public ReturnT<String> testXxlJobEmail() {
        int result = 1 / 0;
        System.out.println(result);
        return ReturnT.SUCCESS;
    }

}
