package com.yhl.scp.dfp.delivery.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedParamsDTO;
import com.yhl.scp.dfp.delivery.dto.MesInterfaceDTO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.sale.service.SaleOrganizeService;
import com.yhl.scp.dfp.sale.vo.SaleOrganizeVO;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanPublishedConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanPublishedDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanPublishedDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanPublishedServiceImpl</code>
 * <p>
 * 发货计划发布表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-17 16:00:40
 */
@Slf4j
@Service
public class DeliveryPlanPublishedServiceImpl extends AbstractService implements DeliveryPlanPublishedService {

    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;

    @Resource
    private DeliveryPlanPublishedDomainService deliveryPlanPublishedDomainService;

    @Resource
    private OemService oemService;

    @Resource
    private SaleOrganizeService saleOrganizeService;

    @Resource
    private InventoryShiftService inventoryShiftService;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedDTO deliveryPlanPublishedDTO) {
        // 0.数据转换
        DeliveryPlanPublishedDO deliveryPlanPublishedDO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Do(deliveryPlanPublishedDTO);
        DeliveryPlanPublishedPO deliveryPlanPublishedPO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Po(deliveryPlanPublishedDTO);
        // 1.数据校验
        deliveryPlanPublishedDomainService.validation(deliveryPlanPublishedDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedPO);
        deliveryPlanPublishedDao.insertWithPrimaryKey(deliveryPlanPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedDTO deliveryPlanPublishedDTO) {
        // 0.数据转换
        DeliveryPlanPublishedDO deliveryPlanPublishedDO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Do(deliveryPlanPublishedDTO);
        DeliveryPlanPublishedPO deliveryPlanPublishedPO = DeliveryPlanPublishedConvertor.INSTANCE.dto2Po(deliveryPlanPublishedDTO);
        // 1.数据校验
        deliveryPlanPublishedDomainService.validation(deliveryPlanPublishedDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedPO);
        deliveryPlanPublishedDao.update(deliveryPlanPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedDTO> list) {
        List<DeliveryPlanPublishedPO> newList = DeliveryPlanPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedDTO> list) {
        List<DeliveryPlanPublishedPO> newList = DeliveryPlanPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedPO po = deliveryPlanPublishedDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_delivery_plan_published")
    public List<DeliveryPlanPublishedVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_delivery_plan_published")
    public List<DeliveryPlanPublishedVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedVO> dataList = deliveryPlanPublishedDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanPublishedServiceImpl target = SpringBeanUtils.getBean(DeliveryPlanPublishedServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectByParams(Map<String, Object> params) {
        return deliveryPlanPublishedDao.selectVOByParams(params);
    }

    @Override
    public List<DeliveryPlanPublishedVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<DeliveryPlanPublishedVO> invocation(List<DeliveryPlanPublishedVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    /**
     * 同步发货计划数据到MES
     */
    @Override
    public BaseResponse<String> publish() {
        // 获取所有发货计划单数据
        List<DeliveryPlanPublishedVO> allDeliveryPlans = this.selectAll();

        if (allDeliveryPlans.isEmpty()) {
            log.info("没有找到任何发货计划单数据");
            return BaseResponse.success("没有需要发布的发货计划单数据");
        }

        List<MesInterfaceDTO> syncList = new ArrayList<>();

        for (DeliveryPlanPublishedVO mainVo : allDeliveryPlans) {
            try {
                // 判断 KID 是否为空
                if (ObjectUtils.isEmpty(mainVo.getKID())) {
                    log.error("KID 为空，跳过该条数据，kid: {}", mainVo.getKID());
                    continue;
                }

                // 获取主机厂编码并处理相关逻辑，现在基于每个发货计划单实例
                String oemCode = mainVo.getOemCode();
                if (StringUtils.isEmpty(oemCode)) {
                    log.error("缺少主机厂编码数据，无法关联到主机厂表，oemCode: {}", oemCode);
                    continue;
                }

                // 查找 Oem 表的数据
                Map<String, Object> oemMap = new HashMap<>();
                oemMap.put("oemCode", oemCode);
                List<OemVO> oemVOS = oemService.selectByParams(oemMap);
                if (oemVOS.isEmpty()) {
                    log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCode);
                    continue;
                }
                OemVO matchedOem = oemVOS.get(0);

                MesInterfaceDTO mesInterfaceDTO = new MesInterfaceDTO();
                mesInterfaceDTO.setSalePlantId(matchedOem.getSaleOrgId());
                mesInterfaceDTO.setCustomerCode(matchedOem.getCustomerCode());

                //  根据 saleOrgId 查找销售组织表里的 saleOrgCode
                String saleOrgId = matchedOem.getSaleOrgId();
                if (StringUtils.isEmpty(saleOrgId)) {
                    log.error("缺少销售组织ID数据，无法关联到销售组织表");
                    continue;
                }
                Map<String, Object> saleMap = new HashMap<>();
                saleMap.put("saleOrgId", saleOrgId);
                List<SaleOrganizeVO> saleOrganizeVOS = saleOrganizeService.selectByParams(saleMap);
                if (saleOrganizeVOS.isEmpty()) {
                    log.error("该数据的销售组织ID是{}，但是在销售组织表中匹配不到数据。", saleOrgId);
                    continue;
                }
                SaleOrganizeVO matchedSaleOrganize = saleOrganizeVOS.get(0);
                mesInterfaceDTO.setSalePlantCode(matchedSaleOrganize.getSaleOrgCode());

                //  根据 customerCode 查找客户表里的 customerId
                String customerCode = matchedOem.getCustomerCode();
                if (StringUtils.isEmpty(customerCode)) {
                    log.error("缺少客户编码数据，无法关联到客户表");
                    continue;
                }
                HashMap<String, Object> customerMap = new HashMap<>();
                customerMap.put("customerCode", customerCode);
                List<CustomerVO> customerVOS = newMdsFeign.selectCustomerByParams(customerMap);
                if (customerVOS.isEmpty()) {
                    log.error("该数据的客户编码是{}，但是在客户表中匹配不到数据。", customerCode);
                    continue;
                }
                CustomerVO matchedCustomer = customerVOS.get(0);
                mesInterfaceDTO.setCustomerId(matchedCustomer.getCustomerId());

                // 设置 itemNum 字段, 拼接而成
                String itemNum = mesInterfaceDTO.getSalePlantCode() + "PC" + mesInterfaceDTO.getCustomerCode();
                mesInterfaceDTO.setLineNum(itemNum);

                // 在库存推移表里查找对应的在途发运，中转库库存
                Map<String, Object> uniteMap = new HashMap<>();
                uniteMap.put("oemCode", oemCode);
                uniteMap.put("productCode", mainVo.getProductCode());
                uniteMap.put("versionId", mainVo.getDeliveryVersionId());

                List<InventoryShiftVO> inventoryShiftVOS = inventoryShiftService.selectByParams(uniteMap);

                if (!inventoryShiftVOS.isEmpty()) {
                    boolean foundMatchingRecord = false;
                    Date demandTime = mainVo.getDemandTime();
                    for (InventoryShiftVO inventoryShift : inventoryShiftVOS) {
                        Date plannedDate = inventoryShift.getPlannedDate();
                        // 如果需求时间和计划日期相同，则使用该条记录的数据
                        if (demandTime.equals(plannedDate)) {
                            mesInterfaceDTO.setOnWayDelivery(inventoryShift.getArrivalPlan());
                            mesInterfaceDTO.setWarehouseStock(inventoryShift.getOpeningInventory());
                            foundMatchingRecord = true;
                            break; // 一旦找到匹配项，即停止循环
                        }
                    }
                    if (!foundMatchingRecord) {
                        log.warn("该数据的主机厂编码，物料编码，版本id是{}, 但是在库存推移表中未找到与需求时间匹配的数据。", oemCode);
                    }
                } else {
                    log.warn("该数据的主机厂编码，物料编码，版本id是{}，但是在库存推移表中匹配不到数据。", oemCode);
                }

                //  在发货计划表里查找对应的每箱片数
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanService.selectByParams(uniteMap);
                if (!deliveryPlanVOS.isEmpty()) {
                    DeliveryPlanVO matchedDeliveryPlan = deliveryPlanVOS.get(0);
                    mesInterfaceDTO.setPerBoxQTY(matchedDeliveryPlan.getPiecePerBox());
                } else {
                    log.warn("该数据的主机厂编码，物料编码，版本id是{}，但是在发货计划表中匹配不到数据。", oemCode);
                }

                // 获取本厂编码，在物料表里查找对应数据
                String productCode = mainVo.getProductCode();
                if (StringUtils.isEmpty(productCode)) {
                    log.error("缺少本厂编码数据，无法关联到物料表");
                    continue;
                }

                List<String> productCodes = Collections.singletonList(productCode);
                List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByProducts(productCodes);

                Optional<NewProductStockPointVO> matchedProductOpt = Optional.empty();
                if (!newProductStockPointVOS.isEmpty()) {
                    for (NewProductStockPointVO product : newProductStockPointVOS) {
                        // 检查每个 stock_point_code 是否在 mds_stock_point 表中有 organize_type 为 SALE_ORGANIZATION 的记录
                        String stockPointCode = product.getStockPointCode();
                        HashMap<String, Object> queryMap = new HashMap<>();
                        queryMap.put("stockPointCode", stockPointCode);
                        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointBySubCategory(queryMap);

                        if (!newStockPointVOS.isEmpty() && "SALE_ORGANIZATION".equals(newStockPointVOS.get(0).getOrganizeType())) {
                            // 处理 PoCategory，仅保留 . 后面的数据
                            String poCategory = product.getPoCategory();
                            String[] poCategoryParts = poCategory.split("\\.", 2);
                            String subCategory = poCategoryParts.length > 1 ? poCategoryParts[1] : poCategory;

                            mesInterfaceDTO.setPlantCode(subCategory); // 设置处理后的子类别

                            // 设置最终匹配的产品信息
                            matchedProductOpt = Optional.of(product);
                            break;
                        }
                    }

                    if (!matchedProductOpt.isPresent()) {
                        log.warn("没有找到 organize_type 为 SALE_ORGANIZATION 的 stock_point_code 数据对于物料编码 {}", productCode);
                    }
                } else {
                    log.error("该数据的本厂编码是{}, 但是在物料表中匹配不到数据。", productCode);
                    continue;
                }

                if (matchedProductOpt.isPresent()) {
                    NewProductStockPointVO matchedProduct = matchedProductOpt.get();
                    mesInterfaceDTO.setItemId(matchedProduct.getInventoryItemId());
                    mesInterfaceDTO.setStatus(matchedProduct.getMaterialDemandStatus());

                    // 根据处理后的销售组织Code去库存表里查找对应的id
                    HashMap<String, Object> subCategoryMap = new HashMap<>();
                    subCategoryMap.put("stockPointCode", mesInterfaceDTO.getPlantCode());
                    List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointBySubCategory(subCategoryMap);
                    if (!newStockPointVOS.isEmpty()) {
                        mesInterfaceDTO.setPlantId(newStockPointVOS.get(0).getId());
                    } else {
                        log.warn("未能在库存表中找到与组织Code {} 对应的 stock_point_code 数据", mesInterfaceDTO.getPlantCode());
                    }
                }

                // 用户工号 根据创建人判断工号
                if (StringUtils.isNotEmpty(mainVo.getCreator())) {
                    User user = ipsNewFeign.userNameById(mainVo.getCreator());
                    if (user != null) {
                        mesInterfaceDTO.setCreatedBy(user.getUserName());
                        mesInterfaceDTO.setSaler(user.getUserName());
                    } else {
                        log.error("用户表中匹配不到该数据的创建人{}", mainVo.getCreator());
                        continue;
                    }
                } else {
                    log.error("缺少数据创建人，请先补充数据并保存。");
                    continue;
                }

                if (StringUtils.isNotEmpty(mainVo.getModifier())) {
                    User user1 = ipsNewFeign.userNameById(mainVo.getModifier());
                    if (user1 != null) {
                        mesInterfaceDTO.setLastUpdatedBy(user1.getUserName());
                    } else {
                        log.error("用户表中匹配不到该数据{}", mainVo.getModifier());
                        continue;
                    }
                } else {
                    log.error("缺少数据更新人，请先补充数据并保存");
                    continue;
                }

                // 发货日期，去掉后面的时分秒
                Date demandTime = mainVo.getDemandTime();
                LocalDate localDate = demandTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                Date dateWithoutTime = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                mesInterfaceDTO.setDemandDate(DateUtils.dateToString(dateWithoutTime, DateUtils.COMMON_DATE_STR3));

                // 设置其他字段
                mesInterfaceDTO.setBillNo(mainVo.getKID() + ".99"); // 单号
                mesInterfaceDTO.setVersion(mainVo.getKID() + ".99"); // 批次ID
                mesInterfaceDTO.setCreationDate(DateUtils.dateToString(mainVo.getCreateTime(), DateUtils.COMMON_DATE_STR1)); // 创建时间
                mesInterfaceDTO.setDemandQTY(mainVo.getDemandQuantity()); // 需求数量
                mesInterfaceDTO.setItemCode(mainVo.getProductCode()); // 本厂编码
                mesInterfaceDTO.setKid(mainVo.getKID() + ".99"); // kid
                mesInterfaceDTO.setLastUpdateDate(DateUtils.dateToString(mainVo.getModifyTime(), DateUtils.COMMON_DATE_STR1)); // 修改时间
                mesInterfaceDTO.setRemark(mainVo.getRemark()); // 备注
                mesInterfaceDTO.setSafeStock(0); // 厂内安全库存，默认为0

                syncList.add(mesInterfaceDTO);

            } catch (Exception e) {
                log.error("处理 KID {} 发货计划单时发生错误: {}", mainVo.getKID(), e.getMessage());
            }
        }

        if (syncList.isEmpty()) {
            log.warn("所有发货计划单处理后没有生成有效的同步数据");
            return BaseResponse.success("所有发货计划单已处理，但无有效数据发布");
        }

        try {
            // 准备传输数据对象
            for (MesInterfaceDTO mesInterfaceDTO : syncList) {
                // 构建 apiParams
                String apiParams = mesInterfaceDTO.getKid() + "@" + mesInterfaceDTO.getKid();

                // 传输数据对象
                Map<String, Object> dcpMap = new HashMap<>();
                dcpMap.put("data", mesInterfaceDTO);
                dcpMap.put("apiParams", apiParams);

                // 调用外部API，每个MES接口调用都包含一个对应的apiParams
                newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                        ApiCategoryEnum.DELIVERY_PLAN_MES.getCode(), dcpMap);
            }

            return BaseResponse.success("数据发布成功");
        } catch (Exception e) {
            log.error("批量发布发货计划单报错，{}", e.getMessage());
            return BaseResponse.error("批量发布发货计划单报错：" + e.getMessage());
        }
    }

	@Override
	public List<DeliveryPlanPublishedVO> selectSumDeliveryPlanPublished(Map<String, Object> params) {
		return deliveryPlanPublishedDao.selectSumDeliveryPlanPublished(params);
	}

    @Override
    public List<DeliveryPlanPublishedVO> selectDeliveryPlanPublishedByParamOnDynamicColumns(List<String> dynamicColumns, Map<String, Object> params) {
        String dynamicColumn = getDynamicColumn(dynamicColumns);
        log.info("已发布发货计划数据查询列：{}", dynamicColumn);
        return deliveryPlanPublishedDao.selectProductListByParamOnDynamicColumns(dynamicColumn, params);
    }

    private String getDynamicColumn(List<String> columns) {
        // 获取
        List<String> allowedColumns = deliveryPlanPublishedDao.getAllColumns();
        if (columns == null || columns.isEmpty()) {
            return "*";
        }
        StringBuilder safeColumns = new StringBuilder();
        for (String col : columns) {
            if (col == null || col.trim().isEmpty()) continue;
            for (String allowedCol : allowedColumns) {
                if (allowedCol.equalsIgnoreCase(col.trim())) {
                    if (safeColumns.length() > 0) {
                        safeColumns.append(", ");
                    }
                    safeColumns.append(allowedCol);
                    break;
                }
            }
        }
        return safeColumns.toString();
    }


    @Override
    public PageInfo<DeliveryPlanPublishedVO> selectDateByParams(DeliveryPlanPublishedParamsDTO paramsDTO) {
        List<DeliveryPlanPublishedVO> list = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(2);
        params.put("demandCategory", paramsDTO.getDemandCategory());
        params.put("oemCode", paramsDTO.getOemCode());
        params.put("productCode", paramsDTO.getProductCode());
        params.put("demandStartTime", paramsDTO.getStartTime());
        params.put("demandEndTime", paramsDTO.getEndTime());
        params.put("publisher", paramsDTO.getPublisher());
        if (StringUtils.isNotBlank(paramsDTO.getProductCode())) {
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = this.selectByParams(ImmutableMap.of("productCode", paramsDTO.getProductCode()));
            if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) {
                return new PageInfo<>();
            }
        }
        PageMethod.startPage(paramsDTO.getPageNum(), paramsDTO.getPageSize());
        List<String> productCodes = deliveryPlanPublishedDao.selectProducts(params);
        if (CollectionUtils.isEmpty(productCodes)) {
            return new PageInfo<>();
        }
        PageInfo<String> pageInfo = new PageInfo<>(productCodes);
        Map<String, Object> params2 = new HashMap<>(2);
        params2.put("productCodes", productCodes);
        List<DeliveryPlanPublishedVO> dataList = this.selectVOByParams(params2);
        Map<String, List<DeliveryPlanPublishedVO>> collect = dataList.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            List<DeliveryPlanPublishedDetailVO> detailList = new ArrayList<>();
            for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : value) {
                DeliveryPlanPublishedDetailVO deliveryPlanPublishedDetailVO = new DeliveryPlanPublishedDetailVO();
                deliveryPlanPublishedDetailVO.setDemandTime(deliveryPlanPublishedVO.getDemandTime());
                deliveryPlanPublishedDetailVO.setDemandQuantity(deliveryPlanPublishedVO.getDemandQuantity());
                detailList.add(deliveryPlanPublishedDetailVO);
            }
            List<Date> dateList;
            if ((paramsDTO.getStartTime() != null && paramsDTO.getEndTime() != null)) {
                dateList = value.stream()
                        .filter(Objects::nonNull)
                        .map(DeliveryPlanPublishedVO::getDemandTime)
                        .filter(Objects::nonNull)
                        .filter(demandTime -> !demandTime.before(paramsDTO.getStartTime()) && !demandTime.after(paramsDTO.getEndTime()))
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
            } else {
                dateList = value.stream().map(DeliveryPlanPublishedVO::getDemandTime).distinct().sorted().collect(Collectors.toList());
            }

            DeliveryPlanPublishedVO deliveryPlanPublishedVO = value.get(0);
            DeliveryPlanPublishedVO maxDeliveryPlanPublishedVO = value.stream()
                    .max(Comparator.comparing(DeliveryPlanPublishedVO::getPublishTime))
                    .orElse(null); // 如果没有数据，返回 null
            deliveryPlanPublishedVO.setPublishTime(maxDeliveryPlanPublishedVO.getPublishTime());
            deliveryPlanPublishedVO.setDateList(dateList);
            deliveryPlanPublishedVO.setDetaDetailList(detailList);
            list.add(deliveryPlanPublishedVO);
        }
        PageInfo<DeliveryPlanPublishedVO> pageInfoResult = new PageInfo<>(list);
        pageInfoResult.setTotal(pageInfo.getTotal());
        pageInfoResult.setPages(pageInfo.getPages());
        return pageInfoResult;
    }

    @Override
    public List<LabelValue<String>> getOem() {
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = this.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(deliveryPlanPublishedVOS)) {
            List<LabelValue<String>> list = deliveryPlanPublishedVOS.stream()
                    .map(deliveryPlanPublishedVO -> new LabelValue<>(deliveryPlanPublishedVO.getOemName(), deliveryPlanPublishedVO.getOemCode()))
                    .distinct()
                    .collect(Collectors.toList());
            return removeDuplicates(list);
        }
        return Collections.emptyList();
    }

    private List<DeliveryPlanPublishedVO> selectVOByParams(Map<String, Object> params) {
        return deliveryPlanPublishedDao.selectVOByParams(params);
    }

    private List<LabelValue<String>> removeDuplicates(List<LabelValue<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                lv -> lv,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    @Override
    public void doUpdateCoordinationQuantity2Null(List<String> idList) {
        String userId = SystemHolder.getUserId();
        if(CollectionUtils.isEmpty(idList)) {
            return;
        }
        deliveryPlanPublishedDao.updateCoordinationQuantity2Null(idList, userId);
    }

    @Override
    public void doUpdateCoordinationQuantity(List<DeliveryPlanPublishedDTO> list) {
        String userId = SystemHolder.getUserId();
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        deliveryPlanPublishedDao.updateCoordinationQuantity(list, userId);
    }

}