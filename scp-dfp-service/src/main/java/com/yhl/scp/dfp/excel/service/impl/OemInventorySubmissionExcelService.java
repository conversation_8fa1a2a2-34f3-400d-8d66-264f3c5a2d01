package com.yhl.scp.dfp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.oem.convertor.OemInventorySubmissionConvertor;
import com.yhl.scp.dfp.oem.dto.OemInventorySubmissionDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemInventorySubmissionDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemInventorySubmissionPO;
import com.yhl.scp.dfp.oem.service.*;
import com.yhl.scp.dfp.oem.vo.*;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.yhl.scp.common.excel.service.AbstractExcelService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OemInventorySubmissionExcelService</code>
 * <p>
 * 主机厂库存提报Excel导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 10:35:41
 */
@Service
@Slf4j
public class OemInventorySubmissionExcelService extends AbstractExcelService<OemInventorySubmissionDTO, OemInventorySubmissionPO, OemInventorySubmissionVO> {

    @Resource
    private OemInventorySubmissionDao oemInventorySubmissionDao;

    @Resource
    private OemInventorySubmissionService oemInventorySubmissionService;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Resource
    private OemService oemService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Override
    public BaseDao<OemInventorySubmissionPO, OemInventorySubmissionVO> getBaseDao() {
        return oemInventorySubmissionDao;
    }

    @Override
    public Function<OemInventorySubmissionDTO, OemInventorySubmissionPO> getDTO2POConvertor() {
        return OemInventorySubmissionConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<OemInventorySubmissionDTO> getDTOClass() {
        return OemInventorySubmissionDTO.class;
    }

    @Override
    public BaseService<OemInventorySubmissionDTO, OemInventorySubmissionVO> getBaseService() {
        return oemInventorySubmissionService;
    }


    @Override
    protected void fillIdForUpdateData(List<OemInventorySubmissionDTO> updateList, Map<String, OemInventorySubmissionPO> existingDataMap) {
        for (OemInventorySubmissionDTO oemInventorySubmissionDTO : updateList) {
            OemInventorySubmissionPO oemInventorySubmissionPO = existingDataMap.get(oemInventorySubmissionDTO.getOemCode() + "&"
                    + oemInventorySubmissionDTO.getStockPointCode() + "&" + oemInventorySubmissionDTO.getProductCode() + "&" + oemInventorySubmissionDTO.getSubmissionDate());
            if (Objects.isNull(oemInventorySubmissionPO)) {
                continue;
            }
            oemInventorySubmissionDTO.setId(oemInventorySubmissionPO.getId());
        }
    }

    @Override
    protected ImportRelatedDataHolder<OemInventorySubmissionPO> prepareData(List<OemInventorySubmissionDTO> oemInventorySubmissionDTOS) {
        // 找到数据库现在所有的数据
        List<OemInventorySubmissionPO> alreadyExitData = oemInventorySubmissionDao.selectByParams(new HashMap<>(2));
        Map<String, OemInventorySubmissionPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getOemCode() + "&"
                + x.getStockPointCode() + "&" + x.getProductCode() + "&" + x.getSubmissionDate(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("oemCode", "stockPointCode", "productCode", "submissionDate");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<OemInventorySubmissionPO>builder()
                .existingData(alreadyExitData)
                .existingDataMap(codeToPOMap)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .build();
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<OemInventorySubmissionDTO, OemInventorySubmissionPO> resultHolder, ImportContext importContext) {
        List<OemInventorySubmissionDTO> insertList = resultHolder.getInsertList();
        List<OemInventorySubmissionDTO> updateList = resultHolder.getUpdateList();
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.verifyPaternity(insertList, resultHolder.getImportLogList());
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.verifyPaternity(updateList, resultHolder.getImportLogList());
        }
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<OemInventorySubmissionDTO> checkList, List<DataImportInfo> importLogList) {
        List<String> productCodeList = checkList.stream()
                .map(OemInventorySubmissionDTO::getProductCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<String> stockPointCodes = checkList.stream()
                .map(OemInventorySubmissionDTO::getStockPointCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<String> oemCodeList = checkList.stream()
                .map(OemInventorySubmissionDTO::getOemCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        Map<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        // 主机厂库存点编码Map
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectByParams(params);
        Map<String, OemStockPointMapVO> oemStockPointMapMap = oemStockPointMapVOS.stream()
                .collect(Collectors.toMap(data -> String.join("&", data.getOemCode(), data.getStockPointCode()),
                        Function.identity(), (v1, v2) -> v1));
        // 库存点编码Map
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario.getData(), params);
        Map<String, List<NewStockPointVO>> newStockPointMap = Optional.ofNullable(newStockPointVOS)
                .orElse(Collections.emptyList())
                .stream()
                .filter(NewStockPointVO -> NewStockPointVO.getStockPointCode() != null)
                .collect(Collectors.groupingBy(NewStockPointVO::getStockPointCode));
        // 主机厂编码Map
        List<OemVO> oemVOS = oemService.selectByParams(params);
        Map<String, List<OemVO>> oemMap = Optional.ofNullable(oemVOS)
                .orElse(Collections.emptyList())
                .stream()
                .filter(oemVO -> oemVO.getOemCode() != null)
                .collect(Collectors.groupingBy(OemVO::getOemCode));
        // 产品编码Map
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(scenario.getData(), productCodeList);
        Map<String, List<NewProductStockPointVO>> productStockPointMap = Optional.ofNullable(newProductStockPointVOS)
                .orElse(Collections.emptyList())
                .stream()
                .filter(NewProductStockPointVO -> NewProductStockPointVO.getProductCode() != null)
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        // 产品编码和中转库Map
        List<NewProductStockPointVO> newProductStockPointVOS2 = newMdsFeign.selectProductStockPointByParams(scenario.getData(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "stockPointCodes", stockPointCodes,
                        "productCodes", productCodeList));
        Map<String, NewProductStockPointVO> productStockPointCodeMap = newProductStockPointVOS2.stream()
                .collect(Collectors.toMap(data -> String.join("&", data.getProductCode(), data.getStockPointCode()),
                        Function.identity(), (v1, v2) -> v1));
        // 查询主机厂车型信息数据
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(params);
        Map<String, Set<String>> oemVehicleModelMap = oemVehicleModelVOS.stream()
                .distinct()
                .collect(Collectors.groupingBy(OemVehicleModelVO::getOemCode,
                        Collectors.mapping(OemVehicleModelVO::getOemVehicleModelCode, Collectors.toSet())));
        // 构建产品编码到vehicleModelCode的映射
        assert newProductStockPointVOS != null;
        Map<String, Set<String>> productToVehicleModelMap = newProductStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode,
                        Collectors.mapping(NewProductStockPointVO::getVehicleModelCode, Collectors.toSet())));
        Iterator<OemInventorySubmissionDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            OemInventorySubmissionDTO inventorySubmissionDTO = iterator.next();
            if (isFieldEmpty(inventorySubmissionDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[中转库编码]未填写");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[中转库编码]未填写");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(inventorySubmissionDTO.getOemCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[主机厂编码]未填写");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[主机厂编码]未填写");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(inventorySubmissionDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[产品编码]未填写");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[产品编码]未填写");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(String.valueOf(inventorySubmissionDTO.getSubmissionDate()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[日期]未填写");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[日期]未填写");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!newStockPointMap.containsKey(inventorySubmissionDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[中转库编码]不存在");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[中转库编码]不存在");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!productStockPointMap.containsKey(inventorySubmissionDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[产品编码]不存在");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[产品编码]不存在");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            Set<String> vehicleModelCodes = productToVehicleModelMap.get(inventorySubmissionDTO.getProductCode());
            if (vehicleModelCodes == null || vehicleModelCodes.isEmpty()) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[产品编码无对应的车型]");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[产品编码无对应的车型]");
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (oemProductValidator(importLogList, vehicleModelCodes, oemVehicleModelMap, inventorySubmissionDTO, iterator))
                continue;
            OemStockPointMapVO oemStockPointVO = oemStockPointMapMap.get(String.join("&", inventorySubmissionDTO.getOemCode()
                    , inventorySubmissionDTO.getStockPointCode()));
            if (Objects.isNull(oemStockPointVO)) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[主机厂编码]与[中转库]不匹配");
                dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
                dataImportInfo.setErrorDetail("[主机厂编码]与[中转库]不匹配");
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

    private static boolean oemProductValidator(List<DataImportInfo> importLogList, Set<String> vehicleModelCodes,
                                               Map<String, Set<String>> oemVehicleModelMap,
                                               OemInventorySubmissionDTO inventorySubmissionDTO,
                                               Iterator<OemInventorySubmissionDTO> iterator) {
        boolean isValid = false;
        for (String vehicleModelCode : vehicleModelCodes) {
            Set<String> oemVehicleModelCodes = oemVehicleModelMap.get(inventorySubmissionDTO.getOemCode());
            if (oemVehicleModelCodes != null && oemVehicleModelCodes.contains(vehicleModelCode)) {
                isValid = true;
                break;
            }
        }
        if (!isValid) {
            DataImportInfo dataImportInfo = new DataImportInfo();
            dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
            dataImportInfo.setRemark("行数：" + inventorySubmissionDTO.getRowIndex() + ";[主机厂编码]与[产品编码]不匹配");
            log.error("行数：{}，[主机厂编码]与[产品编码]不匹配", inventorySubmissionDTO.getOemCode());
            dataImportInfo.setDisplayIndex(inventorySubmissionDTO.getRowIndex());
            dataImportInfo.setErrorDetail("[主机厂编码]与[产品编码]不匹配");
            importLogList.add(dataImportInfo);
            iterator.remove();
            return true;
        }
        return false;
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

}
