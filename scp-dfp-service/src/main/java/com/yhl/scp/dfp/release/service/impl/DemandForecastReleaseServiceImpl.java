package com.yhl.scp.dfp.release.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.utils.MiscellaneousUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.release.dto.*;
import com.yhl.scp.dfp.release.enums.ReleaseDataTypeEnum;
import com.yhl.scp.dfp.release.enums.ReleaseDemandTypeEnum;
import com.yhl.scp.dfp.release.service.DemandForecastReleaseService;
import com.yhl.scp.dfp.release.vo.*;
import com.yhl.scp.dfp.switchrelation.service.SwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DemandForecastReleaseServiceImpl</code>
 * <p>
 * DemandForecastReleaseServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-29 18:08:54
 */
@Service
@Slf4j
public class DemandForecastReleaseServiceImpl implements DemandForecastReleaseService {

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private OemService oemService;

    @Resource
    private PartRelationMapService partRelationMapService;

    public static final String YYYY_MM = "yyyyMM";

    @Resource
    private SwitchRelationBetweenProductService switchRelationBetweenProductService;

    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Override
    public ReleaseOemConditionVO queryOemCondition(ReleaseDTO releaseDTO) {
        ReleaseOemConditionVO conditionVO = new ReleaseOemConditionVO();
        List<LabelValue<String>> oemCodes = Lists.newArrayList();
        List<LabelValue<String>> oemNames = Lists.newArrayList();
        ReleaseOemDTO releaseOemDTO = new ReleaseOemDTO();
        releaseOemDTO.setVersionId(releaseDTO.getVersionId());
        releaseOemDTO.setDemandType(releaseDTO.getDemandType());
        List<ReleaseOemVO> releaseOemVOS = consistenceDemandForecastDataService.selectOemInfoByVersionId(releaseOemDTO);
        if (CollectionUtils.isNotEmpty(releaseOemVOS)) {
            releaseOemVOS.forEach(releaseOemVO -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setValue(releaseOemVO.getOemCode());
                labelValue.setLabel(releaseOemVO.getOemCode());
                oemCodes.add(labelValue);
                labelValue = new LabelValue<>();
                labelValue.setValue(releaseOemVO.getOemName());
                labelValue.setLabel(releaseOemVO.getOemName());
                oemNames.add(labelValue);
            });
        }
        oemCodes.sort(Comparator.comparing(LabelValue::getLabel));
        conditionVO.setOemCodes(oemCodes);
        oemNames.sort(Comparator.comparing(LabelValue::getLabel));
        conditionVO.setOemNames(oemNames);
        return conditionVO;
    }

    @Override
    public ReleaseCoreProcessConditionVO queryCoreProcessCondition(ReleaseCoreProcessConditionDTO releaseCoreProcessConditionDTO) {
        ReleaseCoreProcessConditionVO coreProcessConditionVO = new ReleaseCoreProcessConditionVO();
        List<LabelValue<String>> coreProcesses = Lists.newArrayList();
        List<LabelValue<String>> productSpecials = Lists.newArrayList();
        List<LabelValue<String>> vehicleModelCodes = Lists.newArrayList();
        Set<String> exitsCoreProcesses = Sets.newHashSet();
        Set<String> exitsProductSpecials = Sets.newHashSet();
        Set<String> exitsVehicleModelCodes = Sets.newHashSet();
        List<ReleaseVO> releaseVOS = this.selectReleaseVOs(releaseCoreProcessConditionDTO);
        if (CollectionUtils.isNotEmpty(releaseVOS)) {
            releaseVOS.forEach(releaseVO -> {
                if (StringUtils.isNotBlank(releaseVO.getCoreProcess())
                        && !exitsCoreProcesses.contains(releaseVO.getCoreProcess())) {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setValue(releaseVO.getCoreProcess());
                    labelValue.setLabel(releaseVO.getCoreProcess());
                    coreProcesses.add(labelValue);
                    exitsCoreProcesses.add(releaseVO.getCoreProcess());
                }

                if (StringUtils.isNotBlank(releaseVO.getProductSpecial())
                        && !exitsProductSpecials.contains(releaseVO.getProductSpecial())) {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setValue(releaseVO.getProductSpecial());
                    labelValue.setLabel(releaseVO.getProductSpecial());
                    productSpecials.add(labelValue);
                    exitsProductSpecials.add(releaseVO.getProductSpecial());
                }

                if (StringUtils.isNotBlank(releaseVO.getVehicleModelCode())
                        && !exitsVehicleModelCodes.contains(releaseVO.getVehicleModelCode())) {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setValue(releaseVO.getVehicleModelCode());
                    labelValue.setLabel(releaseVO.getVehicleModelCode());
                    vehicleModelCodes.add(labelValue);
                    exitsVehicleModelCodes.add(releaseVO.getVehicleModelCode());
                }
            });
        }
        coreProcessConditionVO.setCoreProcesses(coreProcesses);
        coreProcessConditionVO.setProductSpecials(productSpecials);
        coreProcessConditionVO.setVehicleModelCodes(vehicleModelCodes);
        return coreProcessConditionVO;
    }

    @Override
    public ReleaseProductConditionVO queryProductCondition(ReleaseProductConditionDTO releaseProductConditionDTO) {
        String demandType = releaseProductConditionDTO.getDemandType();
        String versionId = releaseProductConditionDTO.getVersionId();
        ReleaseProductConditionVO coreProcessConditionVO = new ReleaseProductConditionVO();
        List<LabelValue<String>> demandTypes = Lists.newArrayList();
        demandTypes.add(new LabelValue<>(demandType, demandType));
        List<LabelValue<String>> productCodes = Lists.newArrayList();
        ReleaseCoreProcessConditionDTO conditionDTO = new ReleaseCoreProcessConditionDTO();
        conditionDTO.setDemandType(demandType);
        conditionDTO.setVersionId(versionId);
        conditionDTO.setOemCode(releaseProductConditionDTO.getOemCode());
        List<ReleaseVO> releaseVOS = this.selectReleaseVOs(conditionDTO);
        if (CollectionUtils.isNotEmpty(releaseVOS)) {
            for (ReleaseVO releaseVO : releaseVOS) {
                if (StringUtils.isEmpty(releaseVO.getCoreProcess())) {
                    throw new BusinessException("本厂编码:" + releaseVO.getProductCode() + "未维护核心工序");
                }
                if (StringUtils.isEmpty(releaseVO.getProductSpecial())) {
                    throw new BusinessException("本厂编码:" + releaseVO.getProductCode() + "未维护产品特性");
                }
            }
            releaseVOS.stream().filter(releaseVO -> StringUtils.equals(releaseVO.getCoreProcess(),
                            releaseProductConditionDTO.getCoreProcess()) &&
                            StringUtils.equals(releaseVO.getProductSpecial(),
                                    releaseProductConditionDTO.getProductSpecial()) &&
                            StringUtils.equals(releaseVO.getVehicleModelCode(),
                                    releaseProductConditionDTO.getVehicleModelCode()))
                    .map(ReleaseVO::getProductCode).distinct().forEach(x -> {
                        LabelValue<String> labelValue = new LabelValue<>();
                        labelValue.setValue(x);
                        labelValue.setLabel(x);
                        productCodes.add(labelValue);
                    });
        }
        coreProcessConditionVO.setDemandTypes(demandTypes);
        coreProcessConditionVO.setProductCodes(productCodes);
        return coreProcessConditionVO;
    }

    @Override
    public List<ReleaseOemVO> selectOemVOsByCondition(int pageNum, int pageSize, ReleaseOemDTO releaseOemDTO) {
        PageHelper.startPage(pageNum, pageSize);
        return consistenceDemandForecastDataService.selectOemInfoByVersionId(releaseOemDTO);
    }

    @Override
    public ReleaseCoreProcessSummaryVO selectCoreProcessSummary(ReleaseCoreProcessDTO releaseCoreProcessDTO) {
        ReleaseCoreProcessSummaryVO summaryVO = new ReleaseCoreProcessSummaryVO();
        List<ReleaseCoreProcessVO> coreProcessVOS = Lists.newArrayList();
        List<String> titles = Lists.newArrayList();
        // 核心工序
        List<String> coreProcesses = CollectionUtils.isEmpty(releaseCoreProcessDTO.getCoreProcesses()) ?
                Lists.newArrayList() : releaseCoreProcessDTO.getCoreProcesses();
        // 产品特性
        List<String> productSpecials = CollectionUtils.isEmpty(releaseCoreProcessDTO.getProductSpecials()) ?
                Lists.newArrayList() : releaseCoreProcessDTO.getProductSpecials();
        // 车型编码
        List<String> vehicleModelCodes = CollectionUtils.isEmpty(releaseCoreProcessDTO.getVehicleModelCodes()) ?
                Lists.newArrayList() : releaseCoreProcessDTO.getVehicleModelCodes();
        ReleaseCoreProcessConditionDTO conditionDTO = new ReleaseCoreProcessConditionDTO();
        conditionDTO.setDemandType(releaseCoreProcessDTO.getDemandType());
        conditionDTO.setVersionId(releaseCoreProcessDTO.getVersionId());
        conditionDTO.setOemCode(releaseCoreProcessDTO.getOemCode());
        List<ReleaseVO> releaseVOS = this.selectReleaseVOs(conditionDTO);
        // 查询上个版本的数据
        String lastVersionId =
                consistenceDemandForecastVersionService.getLastVersionId(releaseCoreProcessDTO.getVersionId());
        List<ReleaseVO> lastReleaseVOS;
        Map<String, List<ReleaseVO>> lastReleaseMapOfJoinKey = new HashMap<>();
        if (StringUtils.isNotBlank(lastVersionId)) {
            conditionDTO.setVersionId(lastVersionId);
            lastReleaseVOS = this.selectReleaseVOs(conditionDTO);
            List<ReleaseVO> filterReleaseList = lastReleaseVOS.stream().filter(releaseVO ->
                    (CollectionUtils.isEmpty(coreProcesses)
                            || coreProcesses.contains(releaseVO.getCoreProcess()))
                            && (CollectionUtils.isEmpty(productSpecials)
                            || productSpecials.contains(releaseVO.getProductSpecial()))
                            && (CollectionUtils.isEmpty(vehicleModelCodes)
                            || vehicleModelCodes.contains(releaseVO.getVehicleModelCode()))
                            && releaseVO.getQuantity() != null).collect(Collectors.toList());

            lastReleaseMapOfJoinKey = filterReleaseList.stream().collect(Collectors.groupingBy(x ->
                    String.join("_",
                            StringUtils.isBlank(x.getCoreProcess()) ? "null" : x.getCoreProcess(),
                            StringUtils.isBlank(x.getProductSpecial()) ? "null" : x.getProductSpecial(),
                            StringUtils.isBlank(x.getVehicleModelCode()) ? "null" :
                                    x.getVehicleModelCode()), Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(releaseVOS)) {
            // 动态表头
            titles.addAll(releaseVOS.stream().map(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM))
                    .distinct().collect(Collectors.toList()));
            // 行数据
            List<ReleaseVO> filterReleaseList = releaseVOS.stream().filter(releaseVO ->
                    (CollectionUtils.isEmpty(coreProcesses)
                            || coreProcesses.contains(releaseVO.getCoreProcess()))
                            && (CollectionUtils.isEmpty(productSpecials)
                            || productSpecials.contains(releaseVO.getProductSpecial()))
                            && (CollectionUtils.isEmpty(vehicleModelCodes)
                            || vehicleModelCodes.contains(releaseVO.getVehicleModelCode()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterReleaseList)) {
                Map<String, List<ReleaseVO>> finalLastReleaseMapOfJoinKey = lastReleaseMapOfJoinKey;
                filterReleaseList.stream().collect(Collectors.groupingBy(x ->
                                String.join("_",
                                        StringUtils.isBlank(x.getCoreProcess()) ? "null" : x.getCoreProcess(),
                                        StringUtils.isBlank(x.getProductSpecial()) ? "null" : x.getProductSpecial(),
                                        StringUtils.isBlank(x.getVehicleModelCode()) ? "null" :
                                                x.getVehicleModelCode()), Collectors.toList()))
                        .forEach((x, y) -> {
                            ReleaseCoreProcessVO releaseCoreProcessVO = new ReleaseCoreProcessVO();
                            releaseCoreProcessVO.setDataType(ReleaseDataTypeEnum.DETAIL.getCode());
                            String[] split = x.split("_");
                            releaseCoreProcessVO.setCoreProcess("null".equals(split[0]) ? "" : split[0]);
                            releaseCoreProcessVO.setProductSpecial("null".equals(split[1]) ? "" : split[1]);
                            releaseCoreProcessVO.setVehicleModelCode("null".equals(split[2]) ? "" : split[2]);
                            List<ReleaseCoreProcessDetailVO> detailVOS = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(y)) {
                                Map<String, Integer> detailMap = y.stream()
                                        .filter(t -> t.getForecastTime() != null && t.getQuantity() != null)
                                        .collect(Collectors.groupingBy(z ->
                                                        DateUtils.dateToString(z.getForecastTime(), YYYY_MM),
                                                Collectors.summingInt(ReleaseVO::getQuantity)));

                                Map<String, Integer> lastDetailMap = new HashMap<>();
                                if (finalLastReleaseMapOfJoinKey.containsKey(x)) {
                                    lastDetailMap = finalLastReleaseMapOfJoinKey.get(x).stream().collect(Collectors
                                            .groupingBy(z -> DateUtils.dateToString(z.getForecastTime(), YYYY_MM),
                                                    Collectors.summingInt(ReleaseVO::getQuantity)));
                                }

                                for (String title : titles) {
                                    Integer quantity = detailMap.getOrDefault(title, 0);
                                    Integer oldQuantity = lastDetailMap.getOrDefault(title, 0);
                                    ReleaseCoreProcessDetailVO processDetailVO = new ReleaseCoreProcessDetailVO();
                                    processDetailVO.setTitle(title);
                                    processDetailVO.setQuantity(quantity);
                                    processDetailVO.setOldQuantity(oldQuantity);
                                    processDetailVO.setAdjustType(processDetailVO.getAdjustType());
                                    detailVOS.add(processDetailVO);
                                }
                            }
                            releaseCoreProcessVO.setDetailVOS(detailVOS);
                            coreProcessVOS.add(releaseCoreProcessVO);
                        });
                // 汇总数据
                ReleaseCoreProcessVO releaseCoreProcessVO = new ReleaseCoreProcessVO();
                releaseCoreProcessVO.setDataType(ReleaseDataTypeEnum.SUMMARY.getCode());
                List<ReleaseCoreProcessDetailVO> detailVOS = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(coreProcessVOS)) {
                    List<ReleaseCoreProcessDetailVO> totalDetails = Lists.newArrayList();
                    for (ReleaseCoreProcessVO coreProcessVO : coreProcessVOS) {
                        List<ReleaseCoreProcessDetailVO> detailVOList = coreProcessVO.getDetailVOS();
                        if (CollectionUtils.isEmpty(detailVOList)) {
                            continue;
                        }
                        totalDetails.addAll(detailVOList);
                    }
                    Map<String, Integer> summaryMap =
                            totalDetails.stream().collect(Collectors.groupingBy(ReleaseCoreProcessDetailVO::getTitle,
                                    Collectors.summingInt(ReleaseCoreProcessDetailVO::getQuantity)));
                    for (String title : titles) {
                        Integer quantity = summaryMap.getOrDefault(title, 0);
                        ReleaseCoreProcessDetailVO processDetailVO = new ReleaseCoreProcessDetailVO();
                        processDetailVO.setTitle(title);
                        processDetailVO.setQuantity(quantity);
                        detailVOS.add(processDetailVO);
                    }
                }
                releaseCoreProcessVO.setDetailVOS(detailVOS);
                coreProcessVOS.add(releaseCoreProcessVO);
            }
        }
        summaryVO.setCoreProcessVOS(coreProcessVOS);
        summaryVO.setTitles(titles);
        return summaryVO;
    }

    @Override
    public ReleaseProductSummaryVO selectProductSummary(ReleaseProductDTO releaseProductDTO) {
        ReleaseProductSummaryVO summaryVO = new ReleaseProductSummaryVO();
        List<ReleaseProductVO> productVOS = Lists.newArrayList();
        List<String> titles = Lists.newArrayList();
        // 本厂编码
        List<String> productCodes = CollectionUtils.isEmpty(releaseProductDTO.getProductCodes()) ?
                Lists.newArrayList() : releaseProductDTO.getProductCodes();
        // 零件风险等级
        List<String> materialRiskLevels = CollectionUtils.isEmpty(releaseProductDTO.getMaterialRiskLevels()) ?
                Lists.newArrayList() : releaseProductDTO.getMaterialRiskLevels();
        ReleaseCoreProcessConditionDTO conditionDTO = new ReleaseCoreProcessConditionDTO();
        conditionDTO.setDemandType(releaseProductDTO.getDemandType());
        conditionDTO.setVersionId(releaseProductDTO.getVersionId());
        conditionDTO.setOemCode(releaseProductDTO.getOemCode());
        List<ReleaseVO> releaseVOS = this.selectReleaseVOs(conditionDTO);
        Map<String, Integer> oldQuantityMap = new HashMap<>();
        boolean compareLastVersion = releaseProductDTO.getCompareLastVersion();
        if (compareLastVersion) {
            List<DemandForecastEstablishmentVO> demandForecastEstablishmentVOS =
                    consistenceDemandForecastVersionService.selectMaxVersionCodeData(releaseProductDTO.getDemandType(),
                            releaseProductDTO.getOemCode(), productCodes);
            if (CollectionUtils.isNotEmpty(demandForecastEstablishmentVOS)) {
                demandForecastEstablishmentVOS.stream().collect(Collectors
                                .groupingBy(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM)))
                        .forEach((key, value) -> {
                            if (CollectionUtils.isEmpty(value)) {
                                return;
                            }
                            AtomicReference<Integer> quantity = new AtomicReference<>(0);
                            value.forEach(a -> {
                                if (Objects.isNull(a.getDemandForecast())) {
                                    return;
                                }
                                quantity.updateAndGet(v -> v + a.getDemandForecast().intValue());
                            });
                            oldQuantityMap.put(key, quantity.get());
                        });
            }
        }
        if (CollectionUtils.isNotEmpty(releaseVOS)) {
            // 头信息
            titles.addAll(releaseVOS.stream().map(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM))
                    .distinct().collect(Collectors.toList()));
            // 行明细数据
            filterData(releaseVOS, productCodes, materialRiskLevels, releaseProductDTO);

            releaseVOS.stream().collect(Collectors.groupingBy(x ->
                                    String.join("_",
                                            StringUtils.isBlank(x.getProductCode()) ? "null" : x.getProductCode(),
                                            StringUtils.isBlank(x.getMaterialRiskLevel()) ? "null" :
                                                    x.getMaterialRiskLevel()),
                            Collectors.toList()))
                    .forEach((k, v) -> {
                        ReleaseProductVO releaseProductVO = new ReleaseProductVO();
                        releaseProductVO.setDataType(ReleaseDataTypeEnum.DETAIL.getCode());
                        releaseProductVO.setDemandType(releaseProductDTO.getDemandType());
                        String[] split = k.split("_");
                        releaseProductVO.setProductCode("null".equals(split[0]) ? "" : split[0]);
                        releaseProductVO.setMaterialRiskLevel("null".equals(split[1]) ? "" : split[1]);
                        List<ReleaseProductDetailVO> detailVOS = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(v)) {
                            Map<String, ReleaseVO> detailMap = v.stream().collect(Collectors
                                    .toMap(z -> DateUtils.dateToString(z.getForecastTime(), YYYY_MM),
                                            Function.identity(), (v1, v2) -> v1));
                            for (String title : titles) {
                                ReleaseVO releaseVO = detailMap.get(title);
                                ReleaseProductDetailVO processDetailVO = new ReleaseProductDetailVO();
                                processDetailVO.setTitle(title);
                                if (Objects.nonNull(releaseVO)) {
                                    processDetailVO.setDataId(releaseVO.getId());
                                    processDetailVO.setVersionValue(releaseVO.getVersionValue());
                                    processDetailVO.setQuantity(releaseVO.getQuantity());
                                } else {
                                    processDetailVO.setQuantity(0);
                                }
                                processDetailVO.setOldQuantity(oldQuantityMap.get(title));
                                detailVOS.add(processDetailVO);
                            }
                        }
                        releaseProductVO.setDetailVOS(detailVOS);
                        productVOS.add(releaseProductVO);
                    });
            // 汇总数据
            ReleaseProductVO releaseProductVO = new ReleaseProductVO();
            releaseProductVO.setDataType(ReleaseDataTypeEnum.SUMMARY.getCode());
            releaseProductVO.setDemandType(releaseProductDTO.getDemandType());
            List<ReleaseProductDetailVO> detailVOS = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(productVOS)) {
                List<ReleaseProductDetailVO> totalDetails = Lists.newArrayList();
                for (ReleaseProductVO productVO : productVOS) {
                    List<ReleaseProductDetailVO> detailVOList = productVO.getDetailVOS();
                    if (CollectionUtils.isEmpty(detailVOList)) {
                        continue;
                    }
                    totalDetails.addAll(detailVOList);
                }
                Map<String, Integer> summaryMap = totalDetails.stream()
                        .filter(t -> null != t.getTitle() && null != t.getQuantity())
                        .collect(Collectors.groupingBy(ReleaseProductDetailVO::getTitle,
                                Collectors.summingInt(ReleaseProductDetailVO::getQuantity)));
                for (String title : titles) {
                    Integer quantity = summaryMap.getOrDefault(title, 0);
                    ReleaseProductDetailVO processDetailVO = new ReleaseProductDetailVO();
                    processDetailVO.setTitle(title);
                    processDetailVO.setQuantity(quantity);
                    detailVOS.add(processDetailVO);
                }
            }
            releaseProductVO.setDetailVOS(detailVOS);
            productVOS.add(releaseProductVO);
        }
        summaryVO.setProductVOS(productVOS);
        List<LabelValue<String>> collect = productVOS.stream().map(ReleaseProductVO::getProductCode)
                .filter(Objects::nonNull).sorted().map(x -> new LabelValue<>(x, x)).collect(Collectors.toList());
        summaryVO.setProductDropdown(collect);
        summaryVO.setTitles(titles);
        return summaryVO;
    }

    private void filterData(List<ReleaseVO> releaseVOS, List<String> productCodes, List<String> materialRiskLevels,
                            ReleaseProductDTO releaseProductDTO) {
        releaseVOS.removeIf(item -> {
            if (CollectionUtils.isNotEmpty(productCodes)) {
                return !productCodes.contains(item.getProductCode());
            }
            if (CollectionUtils.isNotEmpty(materialRiskLevels)) {
                return !materialRiskLevels.contains(item.getMaterialRiskLevel());
            }
            if (StringUtils.isNotEmpty(releaseProductDTO.getCoreProcess())) {
                return !StringUtils.equals(item.getCoreProcess(), releaseProductDTO.getCoreProcess());
            }
            if (StringUtils.isNotEmpty(releaseProductDTO.getProductSpecial())) {
                return !StringUtils.equals(item.getProductSpecial(), releaseProductDTO.getProductSpecial());
            }
            if (StringUtils.isNotEmpty(releaseProductDTO.getVehicleModelCode())) {
                return !StringUtils.equals(item.getVehicleModelCode(), releaseProductDTO.getVehicleModelCode());
            }
            return false;
        });
    }

    @Override
    public ReleaseLineChartVO selectLineChartData(ReleaseCoreProcessConditionDTO releaseCoreProcessConditionDTO) {
        String demandType = releaseCoreProcessConditionDTO.getDemandType();
        String oemCode = releaseCoreProcessConditionDTO.getOemCode();
        int currentYear = DateUtils.getYearByDate(new Date());
        int lastYear = currentYear - 1;
        List<ReleaseLineChartMonthVO> currentYearMonths = consistenceDemandForecastDataService
                .selectLineChartByYearAndOem(currentYear, demandType, oemCode);
        List<ReleaseLineChartMonthVO> lastYearMonths = consistenceDemandForecastDataService
                .selectLineChartByYearAndOem(lastYear, demandType, oemCode);
        Map<String, ReleaseLineChartMonthVO> curretMonthMap = CollectionUtils.isEmpty(currentYearMonths) ?
                new HashMap<>() : currentYearMonths.stream().collect(
                Collectors.toMap(e -> e.getColumn().substring(0, 7),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, ReleaseLineChartMonthVO> lastMonthMap = CollectionUtils.isEmpty(lastYearMonths) ?
                new HashMap<>() : lastYearMonths.stream().collect(
                Collectors.toMap(e -> e.getColumn().substring(0, 7),
                        Function.identity(), (v1, v2) -> v1));

        ReleaseLineChartVO releaseLineChartVO = new ReleaseLineChartVO();
        List<ReleaseLineChartYearVO> years = Lists.newArrayList();
        ReleaseLineChartYearVO releaseLineChartYearVO = new ReleaseLineChartYearVO();
        releaseLineChartYearVO.setYear(currentYear);
        List<ReleaseLineChartMonthVO> months = Lists.newArrayList();
        for (int i = 0; i < 12; i++) {
            String currentMonthKey = currentYear + "-" + (i + 1);
            if (i + 1 < 10) {
                currentMonthKey = currentYear + "-0" + (i + 1);
            }
            ReleaseLineChartMonthVO releaseLineChartMonthVO = curretMonthMap.get(currentMonthKey);
            if (Objects.isNull(releaseLineChartMonthVO)) {
                releaseLineChartMonthVO = new ReleaseLineChartMonthVO();
                releaseLineChartMonthVO.setQuantity(0);
            }
            releaseLineChartMonthVO.setColumn(String.valueOf(i + 1));
            months.add(releaseLineChartMonthVO);
        }
        releaseLineChartYearVO.setMonths(months);
        ReleaseLineChartYearVO lastReleaseLineChartYearVO = new ReleaseLineChartYearVO();
        lastReleaseLineChartYearVO.setYear(lastYear);
        List<ReleaseLineChartMonthVO> lastMonths = Lists.newArrayList();
        for (int i = 0; i < 12; i++) {
            String lastMonthKey = lastYear + "-" + (i + 1);
            if (i + 1 < 10) {
                lastMonthKey = lastYear + "-0" + (i + 1);
            }
            ReleaseLineChartMonthVO releaseLineChartMonthVO = lastMonthMap.get(lastMonthKey);
            if (Objects.isNull(releaseLineChartMonthVO)) {
                releaseLineChartMonthVO = new ReleaseLineChartMonthVO();
                releaseLineChartMonthVO.setQuantity(0);
            }
            releaseLineChartMonthVO.setColumn(String.valueOf(i + 1));
            lastMonths.add(releaseLineChartMonthVO);
        }
        lastReleaseLineChartYearVO.setMonths(lastMonths);
        years.add(releaseLineChartYearVO);
        years.add(lastReleaseLineChartYearVO);
        releaseLineChartVO.setYears(years);
        return releaseLineChartVO;
    }

    @Override
    public int updateQuantityByDataId(ReleaseProductUpdateDTO updateDTO) {
        List<ReleaseProductItemDTO> items = updateDTO.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return 0;
        }
        return consistenceDemandForecastDataService.updateForecastValueByIds(items);
    }

    @Override
    public List<ReleaseVO> selectReleaseVOs(ReleaseCoreProcessConditionDTO releaseCoreProcessConditionDTO) {
        List<ReleaseVO> result = Lists.newArrayList();
        String demandType = releaseCoreProcessConditionDTO.getDemandType();
        String versionId = releaseCoreProcessConditionDTO.getVersionId();
        String oemCode = releaseCoreProcessConditionDTO.getOemCode();
//        if (StringUtils.isBlank(oemCode)) {
//            return result;
//        }
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<ReleaseVO> releaseVOS = consistenceDemandForecastDataService.selectByVersionAndOem(versionId, demandType
                , oemCode, rangeData);
        if (CollectionUtils.isNotEmpty(releaseVOS)) {
            releaseVOS.removeIf(Objects::isNull);
            result.addAll(releaseVOS);
        }
        return result;
    }

    @Override
    public List<LabelValue<String>> versionDropdown() {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<Map<String, String>> versionCodeMap = Optional.ofNullable(consistenceDemandForecastDataService
                .selectDistinctNewVersionCode()).orElse(Lists.newArrayList());
        versionCodeMap.forEach(versionCode -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setValue(versionCode.get("id"));
            labelValue.setLabel(versionCode.get("versionCode"));
            result.add(labelValue);
        });
        return result;
    }

    @Override
    public String queryVersionStatus(ReleaseDTO releaseDTO) {
        String versionStatus = null;
        if (ReleaseDemandTypeEnum.LOADING_DEMAND.getCode().equals(releaseDTO.getDemandType())) {
            ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO =
                    consistenceDemandForecastVersionService
                            .selectByPrimaryKey(releaseDTO.getVersionId());
            versionStatus = consistenceDemandForecastVersionVO.getVersionStatus();
        }
        return PublishStatusEnum.PUBLISHED.getCode().equals(versionStatus) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, String versionId) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "需求预测发布");

        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        if (Objects.isNull(version) ) {
            throw new BusinessException("一致性需求预测版本不存在");
        }

        Date planPeriod = DateUtils.stringToDate(version.getPlanPeriod() + "01", "yyyyMMdd");
        // 当年一月~当前月实际发货 （示例：24-4月实际发货）
        Map<String, String> historicalMonthMap = getHistoricalMonthMap(planPeriod);
        // 当前月，及其后11月（实例：24-12月综评）
        Map<String, String> forecastMonthMap = getForecastMonthMap(planPeriod);

        List<List<String>> headers = initHeaders(historicalMonthMap, forecastMonthMap);

        Map<String, Object> params = new HashMap<>();
        params.put("versionId", versionId);
        List<ReleaseExportVO> releaseExportList = consistenceDemandForecastDataService.selectReleaseExportVO(params);
        List<String> productCodes =
                releaseExportList.stream().map(ReleaseExportVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> oemCodes =
                releaseExportList.stream().map(ReleaseExportVO::getOemCode).distinct().collect(Collectors.toList());
        // 查询销售组织的数据
        Map<String, Object> saleOrganizeParams = new HashMap<>();
        saleOrganizeParams.put("organizeType", "SALE_ORGANIZATION");
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(),
                saleOrganizeParams);
        List<String> stockPointCodes =
                newStockPointVOS.stream().map(NewStockPointVO::getStockPointCode).distinct().collect(Collectors.toList());
        // 查询物品数据
        Map<String, Object> productQueryParams = new HashMap<>();
        productQueryParams.put("productCodes", productCodes);
        productQueryParams.put("stockPointCodes", stockPointCodes);
        // productQueryParams.put("orderPlanner", SystemHolder.getUserId());
        List<String> fields = Lists.newArrayList("product_code", "product_name", "vehicle_model_code",
                "core_process", "loading_position", "loading_position_sub", "sale_type", "po_category",
                "order_planner");
        List<NewProductStockPointVO> productList =
                newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), FeignDynamicParam.builder()
                                .queryParam(productQueryParams).dynamicColumnParam(fields).build())
                        .stream().filter(x -> StringUtils.isNotBlank(x.getProductCode()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productList)) {
            log.info("当前用户未分配物品订单计划员权限");
            EasyExcel.write(out)
                    .sheet("需求预测发布")
                    .head(headers)
                    .registerWriteHandler(new CustomColumnWidthHandler())
                    .doWrite(Collections.emptyList());
            return;
        }

        Map<String, NewProductStockPointVO> productCode2ObjMap = productList.stream().collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                        (t1, t2) -> t2));
        productCodes = Lists.newArrayList(productCode2ObjMap.keySet());
        Map<String, String> code2VehicleCodeMap = productList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductCode())
                        && StringUtils.isNotBlank(x.getVehicleModelCode())).collect(Collectors
                        .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode,
                                (t1, t2) -> t2));
        // 数据过滤
        releaseExportList = releaseExportList.stream().filter(x -> productCode2ObjMap
                .containsKey(x.getProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(releaseExportList)) {
            EasyExcel.write(out)
                    .sheet("需求预测发布")
                    .head(headers)
                    .registerWriteHandler(new CustomColumnWidthHandler())
                    .doWrite(Collections.emptyList());
            return;
        }

        // 查询历史发货数据
        Map<String, Object> releaseQueryParams = new HashMap<>();
        List<String> planPeriods = historicalMonthMap.keySet().stream().sorted().collect(Collectors.toList());

        SwitchRelationVO switchRelation = switchRelationBetweenProductService.getSwitchRelation(oemCodes,
                productCodes);
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
        Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        List<String> allProductCodes = switchRelation.getAllProductCodes();

        releaseQueryParams.put("oemCodes", oemCodes);
        releaseQueryParams.put("productCodes", allProductCodes);
        releaseQueryParams.put("beginDate", planPeriods.get(0) + "-01");
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(DateUtils
                .stringToDate(planPeriods.get(planPeriods.size() - 1) + "-01"), Calendar.MONTH, 1));
        releaseQueryParams.put("endDate", endDate);
        List<WarehouseReleaseRecordMonthVO> releaseMonthList =
                warehouseReleaseRecordService.selectMonthVOByParams(releaseQueryParams);
        //获取中转库发货记录(只需要查当月数据，主机厂业务类型为MTS取中转库，否则取仓库收发货记录)
        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of("oemCodes" , oemCodes));
        List<String> mtsOemCodes = oemList.stream()
        		.filter( e-> OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode).collect(Collectors.toList());
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = new ArrayList<>();
        if(version.getPlanPeriod().equals(DateUtils.dateToString(new Date(), "yyyyMM")) && CollectionUtils.isNotEmpty(mtsOemCodes)) {
        	//如果导出版本计划周期是当前月，那么当月中转库数据只差当月的数据即可
        	releaseQueryParams.put("oemCodes", mtsOemCodes);
        	releaseQueryParams.put("beginDate", DateUtils.dateToString(DateUtils.getMonthFirstDay(new Date())));
        	warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
            		.selectMonthVOByParams(releaseQueryParams);
        }
        //仓库收发货记录剔除当月主机厂为MTS的数据当月的数据
        releaseMonthList = releaseMonthList.stream()
        		.filter(e -> !(mtsOemCodes.contains(e.getOemCode())
        				&& Objects.equals(e.getYearMonth(), DateUtils.dateToString(planPeriod, DateUtils.YEAR_MONTH))))
        		.collect(Collectors.toList());
        // 组装历史发货数据
        Map<String, List<ReleaseExportVO>> historicalQtyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(releaseMonthList) || CollectionUtils.isNotEmpty(warehouseReleaseToWarehouses)) {
            assembleHistoricalQtyMap(historicalQtyMap, releaseMonthList, warehouseReleaseToWarehouses, code2VehicleCodeMap);
        }

        // 查询 零件映射
        Map<String, String> productCode2PartNumberMap = partRelationMapService.selectByParams(ImmutableMap
                        .of("productCodeList", productCodes)).stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductCode())
                        && StringUtils.isNotBlank(x.getPartNumber())).collect(Collectors
                        .toMap(PartRelationMapVO::getProductCode, PartRelationMapVO::getPartNumber, (t1, t2) -> t2));
        // 查询 主机厂
        List<OemVO> oemVOList = oemService.selectByParams(new HashMap<>()).stream()
        		.filter(x -> StringUtils.isNotBlank(x.getOemCode()))
        		.collect(Collectors.toList());
        Map<String, OemVO> oemVOMap = oemVOList.stream().collect(Collectors.toMap(OemVO::getOemCode,e->e,(v1, v2) -> v2));
        Map<String, String> oemCode2NameMap = oemVOList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode())
                        && StringUtils.isNotBlank(x.getOemName())).collect(Collectors
                        .toMap(OemVO::getOemCode, OemVO::getOemName, (t1, t2) -> t2));
        // 查询 用户
        Map<String, String> userId2NameMap = ipsNewFeign.userList().stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        for (ReleaseExportVO releaseExportVO : releaseExportList) {
            String oemName = oemCode2NameMap.get(releaseExportVO.getOemCode());
            releaseExportVO.setOemName(oemName);
        }
        List<String> finalProductCodes = productCodes;
        Map<String, List<ReleaseExportVO>> forecastQtyMap = releaseExportList.stream()
                .filter(x -> finalProductCodes.contains(x.getProductCode())).collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER, x.getDemandCategory(), x.getOemCode(),
                                x.getVehicleModelCode(), x.getProductCode())));
        // 查询 是否镀膜/夹丝/调光
        Set<String> productCodeSet = forecastQtyMap.values().stream()
                .flatMap(List::stream)
                .map(ReleaseExportVO::getProductCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        List<MdsProductStockPointBaseVO> baseInfoList = newMdsFeign.selectMdsProductStockPointBaseInfo(productCodes);
        Map<String, MdsProductStockPointBaseVO> productCode2BaseInfoMap = Optional.ofNullable(baseInfoList)
                .orElse(Collections.emptyList()).stream()
                .filter(vo -> vo != null && vo.getProductCode() != null)
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        // 获取主工序
        List<String> productCodesForQuery = new ArrayList<>(productCodeSet);
        Map<String, String> productCode2MainProcessMap = combinedMainProcess(productCodesForQuery);
        // 遍历组装数据体
        List<List<Object>> dataList = assembleDataList(forecastQtyMap, historicalQtyMap, oemCode2NameMap,
                productCode2PartNumberMap, productCode2ObjMap, userId2NameMap, historicalMonthMap, forecastMonthMap,
                newOldMap, oldNewMap, oemVOMap, productCode2BaseInfoMap, productCode2MainProcessMap);
        EasyExcel.write(out)
                .sheet("需求预测发布")
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(dataList);
    }

    /**
     * 通过成品编码获取对应主工序（递归半成品编码）
     * @param productCodesForQuery
     * @return
     */
    private Map<String, String> combinedMainProcess(List<String> productCodesForQuery) {
        // 1：获取成品编码的主工序
        Map<String, String> productCode2MainProcessMap = newMdsFeign.selectMainProcessByProductCodes(productCodesForQuery);
        // 2：获取成品编码对应的半成品编码列表
        List<BomRoutingStepInputVO> bomRoutingStepInputVOs = newMdsFeign
                .selectBomRoutingStepInputByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodeList", productCodesForQuery,"productType", ProductTypeEnum.SA.getCode()));
        // 构建成品编码 -> 半成品编码的映射
        Map<String, List<String>> productCode2HalfProductsMap = bomRoutingStepInputVOs.stream()
                .collect(Collectors.groupingBy(
                        BomRoutingStepInputVO::getSourceProductCode,
                        Collectors.mapping(BomRoutingStepInputVO::getProductCode, Collectors.toList())
                ));
        // 提取所有半成品编码
        List<String> halfProductCodesList = bomRoutingStepInputVOs.stream()
                .map(BomRoutingStepInputVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());
        // 3：获取半成品编码的主工序
        Map<String, String> halfProductCode2MainProcessMap = newMdsFeign.selectMainProcessByProductCodes(halfProductCodesList);
        // 4：构建最终结果
        Map<String, String> resultMap = new HashMap<>();
        for (String productCode : productCodesForQuery) {
            // 优先使用成品本身的主工序
            if (productCode2MainProcessMap.containsKey(productCode)) {
                resultMap.put(productCode, productCode2MainProcessMap.get(productCode));
                continue;
            }
            // 没有则查找成品对应的半成品
            List<String> halfProductCodes = productCode2HalfProductsMap.getOrDefault(productCode, Collections.emptyList());
            if (halfProductCodes.isEmpty()) {
                continue;
            }
            // 查找半成品的主工序
            List<String> mainProcesses = halfProductCodes.stream()
                    .map(halfProductCode2MainProcessMap::get)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (!mainProcesses.isEmpty()) {
                resultMap.put(productCode, String.join(",", mainProcesses));
            }
        }
        return resultMap;
    }

    private static List<List<Object>> assembleDataList(Map<String, List<ReleaseExportVO>> forecastQtyMap,
                                                       Map<String, List<ReleaseExportVO>> historicalQtyMap,
                                                       Map<String, String> oemCode2NameMap,
                                                       Map<String, String> productCode2PartNumberMap,
                                                       Map<String, NewProductStockPointVO> productCode2ObjMap,
                                                       Map<String, String> userId2NameMap,
                                                       Map<String, String> historicalMonthMap,
                                                       Map<String, String> forecastMonthMap,
                                                       Map<String, String> newOldMap,
                                                       Map<String, String> oldNewMap, Map<String, OemVO> oemVOMap,
                                                       Map<String, MdsProductStockPointBaseVO> productCode2BaseInfoMap,
                                                       Map<String, String> productCode2MainProcessMap) {
        List<List<Object>> dataList = new ArrayList<>();
        for (Map.Entry<String, List<ReleaseExportVO>> entry : forecastQtyMap.entrySet()) {
            String key = entry.getKey();
            List<ReleaseExportVO> value = entry.getValue();
            String[] split = key.split(Constants.DELIMITER);
            String demandCategory;
            try {
                demandCategory = ProductionDemandTypeEnum.valueOf(split[0]).getDesc();
            } catch (Exception e) {
                throw new BusinessException("不可识别的需求类型");
            }
            String oemCode = split[1];
            String vehicleModelCode = split[2];
            String productCode = split[3];
            String joinKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, productCode);
            String xItemCode = newOldMap.get(productCode);
            String xKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, xItemCode);
            String yItemCode = oldNewMap.get(productCode);
            String yKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, yItemCode);
            if (StringUtils.isBlank(vehicleModelCode)) {
                vehicleModelCode = "";
            }
            Map<String, BigDecimal> historicalPlanPeriodQtyMap = new HashMap<>();
            List<ReleaseExportVO> tempList = Lists.newArrayList();
            if (MapUtils.isNotEmpty(historicalQtyMap)) {
                if (historicalQtyMap.containsKey(joinKey)) {
                    tempList.addAll(historicalQtyMap.get(joinKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(xItemCode) && historicalQtyMap.containsKey(xKey)) {
                    tempList.addAll(historicalQtyMap.get(xKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(yItemCode) && historicalQtyMap.containsKey(yKey)) {
                    tempList.addAll(historicalQtyMap.get(yKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(tempList)) {
                    historicalPlanPeriodQtyMap =
                            tempList.stream().collect(Collectors.groupingBy(ReleaseExportVO::getPlanPeriod,
                                    Collectors.reducing(BigDecimal.ZERO, ReleaseExportVO::getSumQty, BigDecimal::add)));
                }
            }
            Map<String, BigDecimal> forecastPlanPeriodQtyMap = value.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                    .collect(Collectors.toMap(ReleaseExportVO::getPlanPeriod, ReleaseExportVO::getSumQty,
                            (t1, t2) -> t2));
            String oemName = oemCode2NameMap.get(oemCode);
            String partNumber = productCode2PartNumberMap.get(productCode);
            NewProductStockPointVO productItem = productCode2ObjMap.get(productCode);
            String productName = productItem == null ? "" : productItem.getProductName();
            String loadingPositionSub = productItem == null ? "" : productItem.getLoadingPositionSub();
            String coreProcess = productItem == null ? "" : productItem.getCoreProcess();
            String salesType = productItem == null ? "" : productItem.getSaleType();
            String poCategory = productItem == null ? "" : productItem.getPoCategory();
            if (poCategory != null && poCategory.contains(".")) {
                poCategory = poCategory.substring(poCategory.lastIndexOf('.') + 1);
            }
            String orderPlannerName = "";
            if (productItem != null && StringUtils.isNotBlank(productItem.getOrderPlanner())) {
                orderPlannerName = Arrays.stream(productItem.getOrderPlanner().split(",")).distinct()
                        .map(userId2NameMap::get).collect(Collectors.joining(","));
            }
            String customerCode = "";
            String customerName = "";
            OemVO oemVO = oemVOMap.get(oemCode);
            if(oemVO != null) {
            	customerCode = oemVO.getCustomerCode();
            	customerName = oemVO.getCustomerName();
            }
            MdsProductStockPointBaseVO baseInfo = productCode2BaseInfoMap.get(productCode);
            String isMembrane = "否";
            String isClamp = "否";
            String isDimming = "否";

            if (baseInfo != null) {
                isMembrane = "/".equals(baseInfo.getMembraneSystem()) ? "否" : "是";
                isClamp = ("无".equals(baseInfo.getClampType()) || "/".equals(baseInfo.getClampType())) ? "否" : "是";
                isDimming = "调光".equals(baseInfo.getItemFlag()) ? "是" : "否";
            }

            String mainProcess = productCode2MainProcessMap.getOrDefault(productCode, "");

            List<Object> subList = Lists.newArrayList(demandCategory, oemCode, oemName, customerCode, customerName,
            		productCode, partNumber,
                    vehicleModelCode, productName, loadingPositionSub, isMembrane, isClamp, isDimming, coreProcess, "");

            Map<String, BigDecimal> finalHistoricalPlanPeriodQtyMap = historicalPlanPeriodQtyMap;
            historicalMonthMap.keySet().stream().sorted()
                    .forEach(x -> subList.add(finalHistoricalPlanPeriodQtyMap.getOrDefault(x, BigDecimal.ZERO)));
            forecastMonthMap.keySet().stream().sorted()
                    .forEach(x -> subList.add(forecastPlanPeriodQtyMap.getOrDefault(x, BigDecimal.ZERO)));

            List<Object> strings = Lists.newArrayList(poCategory);
            subList.addAll(strings);
            subList.add(mainProcess);
            subList.add(orderPlannerName);
            subList.add(salesType);
            List<Object> strings1 = Lists.newArrayList("", "");
            subList.addAll(strings1);
            dataList.add(subList);
        }
        dataList.sort(Comparator.comparing(o -> String.join("#", (String) o.get(0), (String) o.get(1),
                (String) o.get(2))));
        return dataList;
    }

    private static List<List<String>> initHeaders(Map<String, String> historicalMonthMap,
                                                  Map<String, String> forecastMonthMap) {
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("需求类型"));
        headers.add(Collections.singletonList("主机厂编码"));
        headers.add(Collections.singletonList("主机厂名称"));
        headers.add(Collections.singletonList("客户编码"));
        headers.add(Collections.singletonList("客户名称"));
        headers.add(Collections.singletonList("本厂编码"));
        headers.add(Collections.singletonList("零件号"));
        headers.add(Collections.singletonList("车型"));
        headers.add(Collections.singletonList("产品名称"));

        headers.add(Collections.singletonList("零件位置"));// 装车位置小类
        headers.add(Collections.singletonList("是否镀膜"));// 核心工序
        headers.add(Collections.singletonList("是否夹丝"));//
        headers.add(Collections.singletonList("是否调光"));//
        headers.add(Collections.singletonList("是否有90天以上材料"));//
        headers.add(Collections.singletonList("新产品分类"));//

        historicalMonthMap.values().forEach(item -> headers.add(Collections.singletonList(item)));

        forecastMonthMap.values().forEach(item -> headers.add(Collections.singletonList(item)));

        headers.add(Collections.singletonList("工厂"));
        headers.add(Collections.singletonList("主工序"));//
        headers.add(Collections.singletonList("业务员"));// 人员
        headers.add(Collections.singletonList("分类"));// 销售类型
        headers.add(Collections.singletonList("备注"));//
        headers.add(Collections.singletonList(""));//
        return headers;
    }

    private void assembleHistoricalQtyMap(Map<String, List<ReleaseExportVO>> historicalQtyMap,
                                          List<WarehouseReleaseRecordMonthVO> releaseMonthList,
                                          List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses,
                                          Map<String, String> code2VehicleCodeMap) {
        if (MapUtils.isEmpty(code2VehicleCodeMap)) {
            return;
        }
        //仓库
        List<ReleaseExportVO> historicalReleaseExportList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(releaseMonthList)) {
        	historicalReleaseExportList = releaseMonthList.stream()
        	        .map(x -> ReleaseExportVO.builder().oemCode(x.getOemCode())
        	                .vehicleModelCode(code2VehicleCodeMap.getOrDefault(x.getItemCode(), null)).productCode(x.getItemCode())
        	                .planPeriod(x.getYearMonth()).sumQty(x.getSumQty()).build()).collect(Collectors.toList());
        }
        //中转库
        if(CollectionUtils.isNotEmpty(warehouseReleaseToWarehouses)) {
        	List<ReleaseExportVO> historicalToReleaseExportList = warehouseReleaseToWarehouses.stream()
                    .map(x -> ReleaseExportVO.builder().oemCode(x.getOemCode())
                            .vehicleModelCode(code2VehicleCodeMap.getOrDefault(x.getItemCode(), null)).productCode(x.getItemCode())
                            .planPeriod(x.getYearMonth()).sumQty(x.getSumQty()).build()).collect(Collectors.toList());
            historicalReleaseExportList.addAll(historicalToReleaseExportList);
        }
        Map<String, List<ReleaseExportVO>> dataMap = historicalReleaseExportList.stream()
        		.collect(Collectors.groupingBy(x -> String
                        .join(Constants.DELIMITER, x.getOemCode(), x.getVehicleModelCode(), x.getProductCode())));
        historicalQtyMap.putAll(dataMap);
    }

    public static Map<String, String> getForecastMonthMap(Date planPeriod) {
        Map<String, String> result = new TreeMap<>();
        Calendar instance = Calendar.getInstance();
        for (int i = 0; i < 12; i++) {
            Date date = DateUtils.moveCalendar(planPeriod, Calendar.MONTH, i);
            instance.setTime(date);
            int month = instance.get(Calendar.MONTH) + 1;
            String yearMonth = DateUtils.dateToString(date, DateUtils.YEAR_MONTH);
            result.put(yearMonth, yearMonth.substring(2, 4) + "-" + month + "月综评");
        }
        return result;
    }

    public static Map<String, String> getHistoricalMonthMap(Date planPeriod) {
        Map<String, String> result = new TreeMap<>();
        LocalDate now = MiscellaneousUtils.date2LocalDate(planPeriod);
        for (int i = 0; i < 12; i++) {
            LocalDate date = now.minusMonths(i);
            String yearMonth = date.format(DateTimeFormatter.ofPattern(DateUtils.YEAR_MONTH));
            result.put(yearMonth, yearMonth + "月实际发货");
        }
        return result;
    }

	@Override
	public List<LabelValue<String>> getVersionDownByType() {
		List<LabelValue<String>> result = Lists.newArrayList();
        List<Map<String, String>> versionCodeMap = Optional.ofNullable(consistenceDemandForecastDataService
                .selectDistinctNewVersionCode()).orElse(Lists.newArrayList());
        versionCodeMap.forEach(versionCode -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setValue(versionCode.get("id"));
            String versionStatus = EnumUtils.getDescByCode(PublishStatusEnum.class, versionCode.get("versionStatus"));
            labelValue.setLabel(versionCode.get("versionCode") + "(" + versionStatus +")");
            result.add(labelValue);
        });
        return result;
	}

    @Override
    public BaseResponse selectData(QueryBodyVO queryBodyVO) {
        BaseResponse<List<ReleaseReportVO>> baseResponse = getReportData(queryBodyVO);
        if (!baseResponse.getSuccess()){
            return baseResponse;
        }
        List<ReleaseReportVO> combinedData = baseResponse.getData();
        if (StringUtils.isNotBlank(queryBodyVO.getDimension())){
            combinedData = groupByDimension(combinedData, queryBodyVO.getDimension());
        }
        //返回数据
        Integer currentPage = queryBodyVO.getCurrentPage();
        Integer pageSize = queryBodyVO.getPageSize();
        Map<String, Object> map = new HashMap<>();
        map.put("total", combinedData.size());
        if (combinedData.isEmpty()) {
            map.put("data", combinedData);
        } else {
            int beginIndex = (currentPage - 1) * pageSize;
            if (beginIndex >= combinedData.size()) {
                map.put("data", new ArrayList<>());
            }
            int endIndex = Math.min(beginIndex + pageSize, combinedData.size());
            map.put("data", combinedData.subList(beginIndex, endIndex));
        }
        return BaseResponse.success(map);
    }

    public List<String> getThreeMonthList(String planPeriod) {
        List<String> result = new ArrayList<>();
        int year = Integer.parseInt(planPeriod.substring(0, 4));
        int month = Integer.parseInt(planPeriod.substring(4, 6));
        LocalDate localDate = LocalDate.of(year, month, 1);
        for (int i = 0; i < 3; i++) {
            LocalDate date = localDate.plusMonths(i);
            String yearMonth = date.format(DateTimeFormatter.ofPattern(DateUtils.YEAR_MONTH));
            result.add(yearMonth);
        }
        return result;
    }

    private List<ReleaseReportVO> assembleReportDataList(Map<String, List<ReleaseExportVO>> forecastQtyMap,
                                                         Map<String, List<ReleaseExportVO>> historicalQtyMap,
                                                         Map<String, String> oemCode2NameMap,
                                                         Map<String, String> productCode2PartNumberMap,
                                                         Map<String, NewProductStockPointVO> productCode2ObjMap,
                                                         Map<String, String> userId2NameMap,
                                                         Map<String, String> newOldMap,
                                                         Map<String, String> oldNewMap, Map<String, OemVO> oemVOMap,
                                                         List<String> planPeriods,
                                                         Map<String, String> productMainProcessMap) {
        List<ReleaseReportVO> dataList = new ArrayList<>();
        for (Map.Entry<String, List<ReleaseExportVO>> entry : forecastQtyMap.entrySet()) {
            String key = entry.getKey();
            List<ReleaseExportVO> value = entry.getValue();
            String[] split = key.split(Constants.DELIMITER);
            String demandCategory;
            try {
                demandCategory = ProductionDemandTypeEnum.valueOf(split[0]).getDesc();
            } catch (Exception e) {
                throw new BusinessException("不可识别的需求类型");
            }
            String oemCode = split[1]; //主机厂
            String vehicleModelCode = split[2];//车型
            String productCode = split[3];//物料
            String joinKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, productCode);
            String xItemCode = newOldMap.get(productCode);
            String xKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, xItemCode);
            String yItemCode = oldNewMap.get(productCode);
            String yKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, yItemCode);
            if (StringUtils.isBlank(vehicleModelCode)) {
                vehicleModelCode = "";
            }
            Map<String, BigDecimal> historicalPlanPeriodQtyMap = new HashMap<>();
            List<ReleaseExportVO> tempList = Lists.newArrayList();
            //实际发货相同的数据相加
            if (MapUtils.isNotEmpty(historicalQtyMap)) {
                if (historicalQtyMap.containsKey(joinKey)) {
                    tempList.addAll(historicalQtyMap.get(joinKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(xItemCode) && historicalQtyMap.containsKey(xKey)) {
                    tempList.addAll(historicalQtyMap.get(xKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(yItemCode) && historicalQtyMap.containsKey(yKey)) {
                    tempList.addAll(historicalQtyMap.get(yKey).stream()
                            .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                            .collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(tempList)) {
                    historicalPlanPeriodQtyMap =
                            tempList.stream().collect(Collectors.groupingBy(ReleaseExportVO::getPlanPeriod,
                                    Collectors.reducing(BigDecimal.ZERO, ReleaseExportVO::getSumQty, BigDecimal::add)));
                }
            }
            //相同的数据按照计划周期分类
            Map<String, BigDecimal> forecastPlanPeriodQtyMap = value.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getPlanPeriod()) && null != item.getSumQty())
                    .collect(Collectors.toMap(ReleaseExportVO::getPlanPeriod, ReleaseExportVO::getSumQty,
                            (t1, t2) -> t2));
            String oemName = oemCode2NameMap.get(oemCode);//主机厂名称
            String partNumber = productCode2PartNumberMap.get(productCode);//零件号
            NewProductStockPointVO productItem = productCode2ObjMap.get(productCode);
            String productName = productItem == null ? "" : productItem.getProductName();//本厂名称
            String loadingPositionSub = productItem == null ? "" : productItem.getLoadingPositionSub();//装车位置
            String salesType = productItem == null ? "" : productItem.getSaleType();//销售类型  分类
            String poCategory = productItem == null ? "" : productItem.getPoCategory();//采购计划类别
            //主工序
            String coreProcess = productMainProcessMap.getOrDefault(productCode, "");
            if (poCategory != null && poCategory.contains(".")) {
                poCategory = poCategory.substring(poCategory.lastIndexOf('.') + 1);
            }
            String orderPlannerName = "";//订单计划员名称
            if (productItem != null && StringUtils.isNotBlank(productItem.getOrderPlanner())) {
                orderPlannerName = Arrays.stream(productItem.getOrderPlanner().split(",")).distinct()
                        .map(userId2NameMap::get).collect(Collectors.joining(","));
            }
            String customerCode = "";//客户编码
            String customerName = "";//客户名称
            OemVO oemVO = oemVOMap.get(oemCode);
            if(oemVO != null) {
                customerCode = oemVO.getCustomerCode();
                customerName = oemVO.getCustomerName();
            }
            //创建该行对象
            ReleaseReportVO releaseReportVO = ReleaseReportVO.builder().demandCategory(demandCategory).oemCode(oemCode).oemName(oemName).customerCode(customerCode)
                    .customerName(customerName).productCode(productCode).productName(productName).partCode(partNumber)
                    .vehicleModelCode(vehicleModelCode).loadingPositionSub(loadingPositionSub).plate(poCategory)
                    .mainProcess(coreProcess).planner(orderPlannerName).type(salesType).build();
            //实际发货
            Map<String, BigDecimal> historicalMonthMap = new TreeMap<>();
            //综合预测
            Map<String, BigDecimal> forecastMonthMap = new TreeMap<>();
            //准确性
            Map<String, String> accuracyMonthMap = new TreeMap<>();
            //预测数据不为空的数量
            int isHaveAccuracy = 0;
            //预测数据之和
            BigDecimal sum = new BigDecimal(0);
            //实际发货和预测是否不为空
            boolean isNull = true;
            for (String planPeriod : planPeriods) {
                BigDecimal actualData = historicalPlanPeriodQtyMap.getOrDefault(planPeriod, BigDecimal.ZERO);
                BigDecimal forecastData = forecastPlanPeriodQtyMap.getOrDefault(planPeriod, BigDecimal.ZERO);
                historicalMonthMap.put(planPeriod + "月实际发货", actualData);
                forecastMonthMap.put(planPeriod + "月综评", forecastData);
                if (forecastData.compareTo(BigDecimal.ZERO) != 0) {
                    isNull = false;
                    isHaveAccuracy++;
                    sum = sum.add(actualData.divide(forecastData, 2, RoundingMode.HALF_UP));
                    BigDecimal bigDecimal = actualData.divide(forecastData, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).stripTrailingZeros();
                    accuracyMonthMap.put(planPeriod + "月准确率", bigDecimal.toPlainString() + "%");
                } else {
                    if (actualData.compareTo(BigDecimal.ZERO) != 0){
                        isNull = false;
                    }
                    accuracyMonthMap.put(planPeriod + "月准确率", "/");
                }
            }
            if (isNull){
                continue;
            }
            //平均准确性
            if (isHaveAccuracy>0) {
                String avg = sum.divide(new BigDecimal(isHaveAccuracy), 2,
                        RoundingMode.HALF_UP).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
                releaseReportVO.setAvgAccuracy(avg);
            }
            //赋值
            releaseReportVO.setHistoricalMonthMap(historicalMonthMap);
            releaseReportVO.setForecastMonthMap(forecastMonthMap);
            releaseReportVO.setAccuracyMonthMap(accuracyMonthMap);
            dataList.add(releaseReportVO);
        }
        dataList.sort(Comparator.comparing(o -> String.join("#", o.getDemandCategory(), o.getOemCode(), o.getProductCode(), o.getVehicleModelCode())));
        return dataList;
    }

    @SneakyThrows
    @Override
    public void exportReportData(HttpServletResponse response, QueryBodyVO queryBodyVO) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "需求预测准确率");
        //获取数据
        BaseResponse<List<ReleaseReportVO>> baseResponse = getReportData(queryBodyVO);
        //获取时间
        List<String> planPeriods = this.getThreeMonthList(queryBodyVO.getPlanPeriod());
        //初始化表头和数据
        List<List<String>> headers = initHeaders(planPeriods);
        List<List<String>> dataList = new ArrayList<>();
        if (baseResponse.getSuccess()){
            List<ReleaseReportVO> reportData = baseResponse.getData();
            if (!reportData.isEmpty()){
                if (StringUtils.isNotBlank(queryBodyVO.getDimension())){
                    reportData = groupByDimension(reportData, queryBodyVO.getDimension());
                }
                dataList = initData(reportData);
            }
        } else {
            throw new BusinessException(baseResponse.getMsg());
        }
        // 设置单元格合并
        EasyExcel.write(out)
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .sheet("Sheet1")
                .doWrite(dataList);
    }

    private List<List<String>> initData(List<ReleaseReportVO> reportData) {
        List<List<String>> excelData = new ArrayList<>();
        reportData.forEach(item->{
            List<String> itemList = Lists.newArrayList(
                    item.getDemandCategory(),
                    item.getOemCode(),
                    item.getOemName(),
                    item.getCustomerCode(),
                    item.getCustomerName(),
                    item.getProductCode(),
                    item.getProductName(),
                    item.getPartCode(),
                    item.getVehicleModelCode(),
                    item.getLoadingPositionSub()
            );
            Collection<BigDecimal> historyValues = item.getHistoricalMonthMap().values();
            historyValues.forEach(value->{
                itemList.add(value.stripTrailingZeros().toPlainString());
            });
            Collection<BigDecimal> forecastValues = item.getForecastMonthMap().values();
            forecastValues.forEach(value->{
                itemList.add(value.stripTrailingZeros().toPlainString());
            });
            itemList.addAll(item.getAccuracyMonthMap().values());
            itemList.add(item.getAvgAccuracy());
            itemList.add(item.getPlate());
            itemList.add(item.getMainProcess());
            itemList.add(item.getPlanner());
            excelData.add(itemList);
        });
        return excelData;
    }

    /**
     * 需求预测准确率excel表头部
     * @param planPeriods 三个月时间
     */
    private List<List<String>> initHeaders(List<String> planPeriods) {
        List<List<String>> headers = new ArrayList<>();
        String mainTitle = "需求预测准确率";
        headers.add(Lists.newArrayList(mainTitle, "量产需求"));
        headers.add(Lists.newArrayList(mainTitle, "主机厂编码"));
        headers.add(Lists.newArrayList(mainTitle, "主机厂名称"));
        headers.add(Lists.newArrayList(mainTitle, "客户编码"));
        headers.add(Lists.newArrayList(mainTitle, "客户名称"));
        headers.add(Lists.newArrayList(mainTitle, "本厂编码"));
        headers.add(Lists.newArrayList(mainTitle, "产品名称"));
        headers.add(Lists.newArrayList(mainTitle, "零件号"));
        headers.add(Lists.newArrayList(mainTitle, "车型编码"));
        headers.add(Lists.newArrayList(mainTitle, "装车位置"));
        planPeriods.forEach(item->{
            headers.add(Lists.newArrayList(mainTitle, item+"月实际发货"));
        });
        planPeriods.forEach(item->{
            headers.add(Lists.newArrayList(mainTitle, item+"月综评"));
        });
        planPeriods.forEach(item->{
            headers.add(Lists.newArrayList(mainTitle, item+"月准确率"));
        });
        headers.add(Lists.newArrayList(mainTitle, "准确率平均值"));
        headers.add(Lists.newArrayList(mainTitle, "工厂"));
        headers.add(Lists.newArrayList(mainTitle, "主工序"));
        headers.add(Lists.newArrayList(mainTitle, "计划员"));
        return headers;
    }

    private BaseResponse<List<ReleaseReportVO>> getReportData(QueryBodyVO queryBodyVO) {
        log.info("进入需求准确性报表查询。");
        //通过计划周期获取该月最新版本的一致性需求预测版本
        String planPeriod = queryBodyVO.getPlanPeriod();
        List<ConsistenceDemandForecastVersionVO> consistenceDemandForecastVersionVOS =
                consistenceDemandForecastVersionService.selectByPlanPeriodList(ListUtils.newArrayList(planPeriod), PublishStatusEnum.PUBLISHED.getCode());
        if (consistenceDemandForecastVersionVOS.isEmpty()) {
            log.error("没有发现计划周期为" + planPeriod + "的一致性需求预测版本数据。");
            return BaseResponse.error("没有发现计划周期为" + planPeriod + "的一致性需求预测版本数据。");
        }
        ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO = consistenceDemandForecastVersionVOS.stream()
                .max(Comparator.comparing(BaseVO::getCreateTime)).get();
        //计划周期的最新版本
        String versionId = consistenceDemandForecastVersionVO.getId();
        String versionCode = consistenceDemandForecastVersionVO.getVersionCode();

        //获取该版本的数据(以及按照时间分类并且携带预测数量)
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", versionId);
        List<ReleaseExportVO> releaseExportList = consistenceDemandForecastDataService.selectReleaseExportVO(params);
        //过滤计划周期以及后面未来两个月数据
        int year = Integer.parseInt(planPeriod.substring(0, 4));
        int month = Integer.parseInt(planPeriod.substring(4, 6));
        LocalDate localDate = LocalDate.of(year, month, 1).plusMonths(3);
        String beginDateStr = year+ "-" + String.format("%02d", month);
        String endDateStr = localDate.getYear() + "-" + String.format("%02d", localDate.getMonthValue());
        releaseExportList = releaseExportList.stream()
                .filter(item ->beginDateStr.compareTo(item.getPlanPeriod()) <=0 && endDateStr.compareTo(item.getPlanPeriod())>0)
                .collect(Collectors.toList());

        //获取本厂编码集合
        List<String> productCodes =
                releaseExportList.stream().map(ReleaseExportVO::getProductCode).distinct().collect(Collectors.toList());
        //获取主机厂集合
        List<String> oemCodes =
                releaseExportList.stream().map(ReleaseExportVO::getOemCode).distinct().collect(Collectors.toList());
        // 查询销售组织的数据
        Map<String, Object> saleOrganizeParams = new HashMap<>();
        saleOrganizeParams.put("organizeType", "SALE_ORGANIZATION");
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(),
                saleOrganizeParams);
        List<String> stockPointCodes =
                newStockPointVOS.stream().map(NewStockPointVO::getStockPointCode).distinct().collect(Collectors.toList());
        // 查询物料数据
        Map<String, Object> productQueryParams = new HashMap<>();
        productQueryParams.put("productCodes", productCodes);
        productQueryParams.put("stockPointCodes", stockPointCodes);
        List<String> fields = Lists.newArrayList("product_code", "product_name", "vehicle_model_code",
                "core_process", "loading_position", "loading_position_sub", "sale_type", "po_category",
                "order_planner");
        List<NewProductStockPointVO> productList =
                newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), FeignDynamicParam.builder()
                                .queryParam(productQueryParams).dynamicColumnParam(fields).build())
                        .stream().filter(x -> StringUtils.isNotBlank(x.getProductCode()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productList)) {
            log.info("通过{}物料集合以及{}销售组织未匹配到物料数据",productCodes,stockPointCodes);
            return BaseResponse.error("通过" + productCodes + "物料集合以及" + stockPointCodes + "销售组织未匹配到物料数据。");
        }
        //按照物料编码分类
        Map<String, NewProductStockPointVO> productCode2ObjMap = productList.stream().collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                        (t1, t2) -> t2));
        productCodes = Lists.newArrayList(productCode2ObjMap.keySet());
        //将物料按照编码分类
        Map<String, String> code2VehicleCodeMap = productList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductCode()) && StringUtils.isNotBlank(x.getVehicleModelCode()))
                .collect(Collectors
                        .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode,
                                (t1, t2) -> t2));
        // 数据过滤
        releaseExportList = releaseExportList.stream().filter(x -> productCode2ObjMap
                .containsKey(x.getProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(releaseExportList)) {
            log.info("该版本{}的一致性需求预测数据中匹配不到物料数据。",versionCode);
            return BaseResponse.error("该版本" + versionCode + "的一致性需求预测数据中匹配不到物料数据。");
        }

        // 查询历史发货数据
        Map<String, Object> releaseQueryParams = new HashMap<>();
        List<String> planPeriods = this.getThreeMonthList(planPeriod);
        //新旧产品工程变更关系
        SwitchRelationVO switchRelation = switchRelationBetweenProductService.getSwitchRelation(oemCodes,
                productCodes);
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
        Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        List<String> allProductCodes = switchRelation.getAllProductCodes();

        releaseQueryParams.put("oemCodes", oemCodes);
        releaseQueryParams.put("productCodes", allProductCodes);
        releaseQueryParams.put("beginDate", planPeriods.get(0) + "-01");
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(DateUtils
                .stringToDate(planPeriods.get(planPeriods.size() - 1) + "-01"), Calendar.MONTH, 1));
        releaseQueryParams.put("endDate", endDate);
        //发货记录至主机厂
        List<WarehouseReleaseRecordMonthVO> releaseMonthList =
                warehouseReleaseRecordService.selectMonthVOByParams(releaseQueryParams);
        //发货记录至中转库
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouseMonthVOS =
                warehouseReleaseToWarehouseService.selectMonthVOByParams(releaseQueryParams);
        // 组装历史发货数据
        Map<String, List<ReleaseExportVO>> historicalQtyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(releaseMonthList)) {
            assembleHistoricalQtyMap(historicalQtyMap, releaseMonthList, warehouseReleaseToWarehouseMonthVOS, code2VehicleCodeMap);
        }
        // 查询 零件映射
        Map<String, String> productCode2PartNumberMap = partRelationMapService.selectByParams(ImmutableMap
                        .of("productCodeList", productCodes)).stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductCode())
                        && StringUtils.isNotBlank(x.getPartNumber()))
                .collect(Collectors
                        .toMap(PartRelationMapVO::getProductCode, PartRelationMapVO::getPartNumber, (t1, t2) -> t2));
        // 查询 主机厂
        List<OemVO> oemVOList = oemService.selectByParams(new HashMap<>()).stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode()))
                .collect(Collectors.toList());
        Map<String, OemVO> oemVOMap = oemVOList.stream().collect(Collectors.toMap(OemVO::getOemCode,e->e,(v1, v2) -> v2));
        Map<String, String> oemCode2NameMap = oemVOList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode()) && StringUtils.isNotBlank(x.getOemName()))
                .collect(Collectors
                        .toMap(OemVO::getOemCode, OemVO::getOemName, (t1, t2) -> t2));
        // 查询 用户
        Map<String, String> userId2NameMap = ipsNewFeign.userList().stream().collect(Collectors
                .toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        for (ReleaseExportVO releaseExportVO : releaseExportList) {
            String oemName = oemCode2NameMap.get(releaseExportVO.getOemCode());
            releaseExportVO.setOemName(oemName);
        }
        List<String> finalProductCodes = productCodes;
        Map<String, List<ReleaseExportVO>> forecastQtyMap = releaseExportList.stream()
                .filter(x -> finalProductCodes.contains(x.getProductCode()))
                .collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER, x.getDemandCategory(), x.getOemCode(),
                                x.getVehicleModelCode(), x.getProductCode())));
        //获取主工序
        Map<String, String> productMainProcessMap = newMdsFeign.selectMainProcessByProductCodes(productCodes);
        // 遍历组装数据体
        List<ReleaseReportVO> combinedData= assembleReportDataList(forecastQtyMap, historicalQtyMap, oemCode2NameMap,
                productCode2PartNumberMap, productCode2ObjMap, userId2NameMap, newOldMap, oldNewMap, oemVOMap, planPeriods,productMainProcessMap);
        if (StringUtils.isNotEmpty(queryBodyVO.getOemCode())){
            combinedData = combinedData.stream()
                    .filter(item -> queryBodyVO.getOemCode().equals(item.getOemCode()))
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(queryBodyVO.getProductCode())){
            combinedData = combinedData.stream()
                    .filter(item -> queryBodyVO.getProductCode().equals(item.getProductCode()))
                    .collect(Collectors.toList());
        }
        return BaseResponse.success(combinedData);
    }

    /**
     * 按维度分类获取合计数据
     * @param combinedData 原始数据
     * @param dimension 维度
     */
    private List<ReleaseReportVO> groupByDimension(List<ReleaseReportVO> combinedData, String dimension) {
        List<ReleaseReportVO> endData = new ArrayList<>();
        if ("0".equals(dimension)){// 0:按客户维度
            combinedData.sort(Comparator.comparing(ReleaseReportVO::getCustomerCode));
            Map<String, List<ReleaseReportVO>> dataMap = combinedData.stream().collect(Collectors.groupingBy(ReleaseReportVO::getCustomerCode));
            dataMap.forEach((itemKey, values) -> {
                endData.addAll(values);
                ReleaseReportVO releaseReportVO = new ReleaseReportVO();
                releaseReportVO.setCustomerCode(itemKey);
                releaseReportVO.setCustomerName(values.get(0).getCustomerName());
                getThreeMapData(releaseReportVO, values);
                endData.add(releaseReportVO);
            });
        } else if ("1".equals(dimension)){// 1:按主机厂维度
            combinedData.sort(Comparator.comparing(ReleaseReportVO::getOemCode));
            Map<String, List<ReleaseReportVO>> dataMap = combinedData.stream().collect(Collectors.groupingBy(ReleaseReportVO::getOemCode));
            dataMap.forEach((itemKey, values) -> {
                endData.addAll(values);
                ReleaseReportVO releaseReportVO = new ReleaseReportVO();
                releaseReportVO.setOemCode(itemKey);
                releaseReportVO.setOemName(values.get(0).getOemName());
                getThreeMapData(releaseReportVO, values);
                endData.add(releaseReportVO);
            });
        } else if ("2".equals(dimension)){// 2:按业务员维度
            combinedData.sort(Comparator.comparing(ReleaseReportVO::getPlanner));
            Map<String, List<ReleaseReportVO>> dataMap = combinedData.stream().collect(Collectors.groupingBy(ReleaseReportVO::getPlanner));
            dataMap.forEach((itemKey, values) -> {
                endData.addAll(values);
                ReleaseReportVO releaseReportVO = new ReleaseReportVO();
                releaseReportVO.setPlanner(itemKey);
                getThreeMapData(releaseReportVO, values);
                endData.add(releaseReportVO);
            });
        } else {
            return combinedData;
        }
        return endData;
    }

    /**
     * 组装数据
     */
    private void getThreeMapData(ReleaseReportVO releaseReportVO, List<ReleaseReportVO> values) {
        //整合实际发货
        Map<String, BigDecimal> mergedHistoricalMap = values.stream()
                .flatMap(r -> r.getHistoricalMonthMap().entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                Map.Entry::getValue,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        releaseReportVO.setHistoricalMonthMap(mergedHistoricalMap);
        //整合综评
        Map<String, BigDecimal> mergedForecastMap = values.stream()
                .flatMap(r -> r.getForecastMonthMap().entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                Map.Entry::getValue,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        releaseReportVO.setForecastMonthMap(mergedForecastMap);
        //整合准确度
        getAccuracy(values, releaseReportVO);
    }

    /**
     * 获取准确率
     */
    private void getAccuracy(List<ReleaseReportVO> values, ReleaseReportVO releaseReportVO) {
        // 获取所有 accuracyMonthMap 的 key
        ReleaseReportVO first = values.get(0);
        Set<String> keys = first.getAccuracyMonthMap().keySet();
        // 存储每个 key 的合格率（字符串百分比）
        Map<String, String> mergedAccuracyMap = new LinkedHashMap<>();
        // 存储每个 key 的合格率数值，用于计算 avgAccuracy
        List<Double> accuracyValues = new ArrayList<>();
        for (String key : keys) {
            int total = 0;
            int pass = 0;
            // 判断该 key 使用哪个合格区间
            BigDecimal min, max;
            if (key.equals(keys.iterator().next())) { // 第一个 key
                min = new BigDecimal("95");
                max = new BigDecimal("105");
            } else { // 第二、三个 key
                min = new BigDecimal("90");
                max = new BigDecimal("110");
            }
            // 遍历所有 report，检查该 key 的值
            for (ReleaseReportVO report : values) {
                String valueStr = report.getAccuracyMonthMap().get(key);
                BigDecimal value = parsePercentage(valueStr);
                total++;
                if (value != null && value.compareTo(min) >= 0 && value.compareTo(max) <= 0) {
                    pass++;
                }
            }
            // 计算合格率：pass / total
            double passRate = total > 0 ? (pass * 100.0 / total) : 0;
            String passRateStr = new BigDecimal(passRate).setScale(0, RoundingMode.HALF_UP).toString() + "%";
            mergedAccuracyMap.put(key, passRateStr);
            accuracyValues.add(passRate);
        }
        // 计算 avgAccuracy：所有合格率的平均值
        double avg = accuracyValues.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        String avgAccuracy = new BigDecimal(avg).setScale(0, RoundingMode.HALF_UP).toString() + "%";

        // 设置到合并对象
        releaseReportVO.setAccuracyMonthMap(mergedAccuracyMap);
        releaseReportVO.setAvgAccuracy(avgAccuracy);
    }

    /**
     * 解析百分比字符串，如 "95%" -> 95，"/" -> null
      */
    private static BigDecimal parsePercentage(String str) {
        if (str == null || str.trim().equals("/") || str.trim().isEmpty()) {
            return null;
        }
        // 移除 % 符号并解析
        try {
            return new BigDecimal(str.replaceAll("%", "").trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}