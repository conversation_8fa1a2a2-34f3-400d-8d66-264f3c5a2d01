package com.yhl.scp.dfp.excel.service.impl;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.ImportTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.service.OemRiskLevelService;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.safety.convertor.SafetyStockLevelConvertor;
import com.yhl.scp.dfp.safety.dto.SafetyStockLevelDTO;
import com.yhl.scp.dfp.safety.infrastructure.dao.SafetyStockLevelDao;
import com.yhl.scp.dfp.safety.infrastructure.po.SafetyStockLevelPO;
import com.yhl.scp.dfp.safety.service.SafetyStockLevelService;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import cn.hutool.core.collection.ListUtil;

/**
 * <code>SafetyStockLevelExcelService</code>
 * <p>
 * 安全库存管理导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-09 19:30:41
 */
@Service
public class SafetyStockLevelExcelService extends AbstractExcelService<SafetyStockLevelDTO, SafetyStockLevelPO, SafetyStockLevelVO> {

    @Resource
    private SafetyStockLevelService safetyStockLevelService;

    @Resource
    private SafetyStockLevelDao safetyStockLevelDao;
    
    @Resource
    private OemRiskLevelService oemRiskLevelService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private PartRelationMapService partRelationMapService;
    
    @Resource
    private PartRiskLevelService partRiskLevelService;

    @Override
    public BaseDao<SafetyStockLevelPO, SafetyStockLevelVO> getBaseDao() {
        return safetyStockLevelDao;
    }

    @Override
    public Function<SafetyStockLevelDTO, SafetyStockLevelPO> getDTO2POConvertor() {
        return SafetyStockLevelConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<SafetyStockLevelDTO> getDTOClass() {
        return SafetyStockLevelDTO.class;
    }

    @Override
    public BaseService<SafetyStockLevelDTO, SafetyStockLevelVO> getBaseService() {
        return safetyStockLevelService;
    }

    @Override
    protected void fillIdForUpdateData(List<SafetyStockLevelDTO> list, Map<String, SafetyStockLevelPO> map) {
        for (SafetyStockLevelDTO safetyInventoryDTO : list) {
        	SafetyStockLevelPO safetyStockLevelPO = map.get(safetyInventoryDTO.getStockCode() 
            		+ "&" + safetyInventoryDTO.getOemCode()
            		+ "&" + safetyInventoryDTO.getProductCode());
            if (safetyStockLevelPO != null) {
                safetyInventoryDTO.setId(safetyStockLevelPO.getId());
                safetyInventoryDTO.setVersionValue(safetyStockLevelPO.getVersionValue());
                safetyInventoryDTO.setEnabled(safetyStockLevelPO.getEnabled());
                if(StringUtils.isEmpty(safetyInventoryDTO.getRemark())) {
                	safetyInventoryDTO.setRemark(safetyStockLevelPO.getRemark());
                }
                safetyInventoryDTO.setCreateTime(safetyStockLevelPO.getCreateTime());
                safetyInventoryDTO.setCreator(safetyStockLevelPO.getCreator());
                safetyInventoryDTO.setMaterialRiskLevel(safetyStockLevelPO.getMaterialRiskLevel());
            }
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<SafetyStockLevelDTO, SafetyStockLevelPO> resultHolder, ImportContext importContext) {
    	if(ImportTypeEnum.FULL_IMPORT.getCode().equals(importContext.getImportType().getCode())) {
        	throw new BusinessException("安全库存管理仅支持文件增量导入");
        }
    	BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
    	
    	List<SafetyStockLevelDTO> insertList = resultHolder.getInsertList();
        List<SafetyStockLevelDTO> updateList = resultHolder.getUpdateList();
        List<String> stockPointCodes = insertList.stream().map(SafetyStockLevelDTO::getStockCode).collect(Collectors.toList());
        stockPointCodes.addAll(updateList.stream().map(SafetyStockLevelDTO::getStockCode).collect(Collectors.toList()));
        stockPointCodes = stockPointCodes.stream().distinct().collect(Collectors.toList());
        
        //获取库存点数据
        List<NewStockPointVO> stockPointList = newMdsFeign.selectStockPointByParams(scenario.getData(), 
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
    			"stockPointCodes" , stockPointCodes));
        Map<String, NewStockPointVO> stockPointMap = stockPointList.stream()
        		.collect(Collectors.toMap(NewStockPointVO::getStockPointCode, e -> e,(v1, v2) -> v1));
        
        //获取SJ下的物料
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> productCodes = insertList.stream().map(SafetyStockLevelDTO::getProductCode).collect(Collectors.toList());
        productCodes.addAll(updateList.stream().map(SafetyStockLevelDTO::getProductCode).collect(Collectors.toList()));
        productCodes = productCodes.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByParams(scenario.getData(), 
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
    			"stockPointCode" , rangeData,
    			"productCodes", productCodes));
        Map<String, NewProductStockPointVO> productInfoMap = productList.stream()
        		.collect(Collectors.toMap(NewProductStockPointVO::getProductCode, e -> e,(v1, v2) -> v1));
        
        //获取主机厂风险等级数据信息
        List<String> oemCodes = insertList.stream().map(SafetyStockLevelDTO::getOemCode).collect(Collectors.toList());
        oemCodes.addAll(updateList.stream().map(SafetyStockLevelDTO::getOemCode).collect(Collectors.toList()));
        oemCodes = oemCodes.stream().distinct().collect(Collectors.toList());
        List<OemRiskLevelVO> oemRiskLevelList = oemRiskLevelService.selectByOemCodes(oemCodes);
        Map<String, List<OemRiskLevelVO>> oemRiskLevelMap = oemRiskLevelList.stream()
        		.collect(Collectors.groupingBy(OemRiskLevelVO::getOemCode));
        Map<String, String> levelMap = new HashMap<>();
        Map<String, String> oemNameMap = new HashMap<>();
        for (Entry<String, List<OemRiskLevelVO>> oemRiskLevelEntry : oemRiskLevelMap.entrySet()) {
        	String oemCode = oemRiskLevelEntry.getKey();
        	List<OemRiskLevelVO> levelList = oemRiskLevelEntry.getValue();
        	levelList.sort(Comparator.comparing(OemRiskLevelVO::getEstimateTime).reversed());
        	levelMap.put(oemCode, levelList.get(0).getRiskLevel());
        	oemNameMap.put(oemCode, levelList.get(0).getOemName());
		}
        
        //获取零件映射关系
        List<PartRelationMapVO> partRelationMapList = partRelationMapService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
    			"productCodeList", productCodes));
        Map<String, List<PartRelationMapVO>> partRelationMap = partRelationMapList.stream()
        		.collect(Collectors.groupingBy(PartRelationMapVO::getProductCode));
        Map<String, String> materialMap = new HashMap<>();
        for (Entry<String, List<PartRelationMapVO>> partRelationEntry : partRelationMap.entrySet()) {
        	String productCode = partRelationEntry.getKey();
        	List<PartRelationMapVO> partRelationList = partRelationEntry.getValue();
        	List<PartRelationMapVO> filterPartRelationList = partRelationList.stream()
        			.filter( e -> StringUtils.isNotEmpty(e.getRelationVersion())).collect(Collectors.toList());
        	if(CollectionUtils.isNotEmpty(filterPartRelationList)) {
        		filterPartRelationList.forEach( e -> {
            		if(StringUtils.isNotEmpty(e.getRelationVersion())) {
            			e.setRelationVersion(e.getRelationVersion().replace("v", "V"));
            		}
            	});
        		filterPartRelationList.sort(Comparator.comparing(PartRelationMapVO::getRelationVersion).reversed());
            	materialMap.put(productCode, filterPartRelationList.get(0).getPartNumber());
        	}else {
        		materialMap.put(productCode, partRelationList.get(0).getPartNumber());
        	}
		}
        
        //获取零件风险等级
        List<PartRiskLevelVO> partRiskLevelList = partRiskLevelService.selectByParams(
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
    			"productCodes", productCodes));
        Map<String,String> partRiskLevelMap = partRiskLevelList.stream()
        		.collect(Collectors.toMap(e -> e.getOemName() + "&" + e.getProductCode(), 
        				PartRiskLevelVO::getMaterialRiskLevel,(v1, v2) -> v1));
        
        if(CollectionUtils.isNotEmpty(insertList)) {
        	this.verifyPaternity(insertList, resultHolder.getImportLogList(), 
        			stockPointMap, productInfoMap, levelMap, materialMap, oemNameMap, partRiskLevelMap);
        }
        if(CollectionUtils.isNotEmpty(updateList)) {
        	this.verifyPaternity(updateList, resultHolder.getImportLogList(),
        			stockPointMap, productInfoMap, levelMap, materialMap, oemNameMap, partRiskLevelMap);
        }
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }
    
    private void verifyPaternity(List<SafetyStockLevelDTO> checkList, List<DataImportInfo> importLogList, 
    		Map<String, NewStockPointVO> stockPointMap, Map<String, NewProductStockPointVO> productInfoMap, 
    		Map<String, String> levelMap, Map<String, String> materialMap, Map<String, String> oemNameMap, 
    		Map<String, String> partRiskLevelMap) {
        Iterator<SafetyStockLevelDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
        	SafetyStockLevelDTO safetyStockLevelDTO = iterator.next();
        	//校验库存点代码
            if (!stockPointMap.containsKey(safetyStockLevelDTO.getStockCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyStockLevelDTO.getRowIndex() + ";[库存点代码]不存在");
                dataImportInfo.setDisplayIndex(safetyStockLevelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            NewStockPointVO newStockPointVO = stockPointMap.get(safetyStockLevelDTO.getStockCode());
            safetyStockLevelDTO.setStockPointId(newStockPointVO.getId());
            //校验产品编码
            if (!productInfoMap.containsKey(safetyStockLevelDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyStockLevelDTO.getRowIndex() + ";[产品编码]不存在");
                dataImportInfo.setDisplayIndex(safetyStockLevelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            NewProductStockPointVO productInfo = productInfoMap.get(safetyStockLevelDTO.getProductCode());
            safetyStockLevelDTO.setSupplyType(productInfo.getSupplyType());
            safetyStockLevelDTO.setVehicleModelCode(productInfo.getVehicleModelCode());
            //校验主机厂编码
            if (!oemNameMap.containsKey(safetyStockLevelDTO.getOemCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyStockLevelDTO.getRowIndex() + ";[主机厂编码]不存在");
                dataImportInfo.setDisplayIndex(safetyStockLevelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            safetyStockLevelDTO.setOemName(oemNameMap.get(safetyStockLevelDTO.getOemCode()));
            
            //校验零件映射关系
            if (!materialMap.containsKey(safetyStockLevelDTO.getProductCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyStockLevelDTO.getRowIndex() + ";[零件映射关系]不存在");
                dataImportInfo.setDisplayIndex(safetyStockLevelDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            safetyStockLevelDTO.setMaterialName(materialMap.get(safetyStockLevelDTO.getProductCode()));
            
            //维护主机厂风险等级
            safetyStockLevelDTO.setOemRiskLevel(levelMap.get(safetyStockLevelDTO.getOemCode()));
            
            //维护零件风险等级
            safetyStockLevelDTO.setMaterialRiskLevel(
            		partRiskLevelMap.get(safetyStockLevelDTO.getOemCode() + "&" + safetyStockLevelDTO.getProductCode()));
        }
    }

    @Override
    protected ImportRelatedDataHolder<SafetyStockLevelPO> prepareData(List<SafetyStockLevelDTO> safetyStockLevelDTOS) {
        // 找到数据库现在所有的数据
        List<SafetyStockLevelPO> existingData = safetyStockLevelDao.selectByParams(new HashMap<>(2));
        Map<String, SafetyStockLevelPO> codeToPOMap = existingData.stream()
        		.collect(Collectors.toMap(x -> x.getStockCode() + "&" + x.getOemCode() + "&" + x.getProductCode(), 
        				Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("stockCode", "oemCode", "productCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();

        return ImportRelatedDataHolder.<SafetyStockLevelPO>builder()
                .existingData(existingData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

}
