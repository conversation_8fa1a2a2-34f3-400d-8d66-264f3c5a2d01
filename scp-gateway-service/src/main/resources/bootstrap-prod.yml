nacos-server-addr: nacos-headless.nacos.svc.cluster.local:8848
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos-server-addr}
        namespace: bpim
      config:
        server-addr: ${nacos-server-addr}
        namespace: bpim
        group: DEFAULT_GROUP
        prefix: scp-gateway-service
        file-extension: yaml
        shared-configs:
          - dataId: redis.yaml
            group: DEFAULT_GROUP
            refresh: true
  codec:
    max-in-memory-size: 10MB