package com.yhl.scp.gateway.filters;

import cn.hutool.core.util.ObjectUtil;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.auth.AuthenticationException;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.ConnectException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
public class AccessLogFilter implements GlobalFilter, Ordered {

    private final String url = "http://scp-ips-service/requestLog/create";
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();
    @Resource
    private RestTemplate restTemplate;
    private static final int MAX_RECORD_BYTES = 10 * 1024 * 1024;

    @Override
    @SuppressWarnings("unchecked")
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpRequest request = exchange.getRequest();
        // 获取请求基本信息
        String requestUri = request.getURI().getPath();
        HttpHeaders headers = request.getHeaders();
        // 获取用户相关信息
        String userId = getUserIdByRequest(request);
        String token = getTokenByRequest(request);
        String userAgent = getUserAgentByRequest(request);
        RequestLogDTO requestLogDTO = RequestLogDTO.builder()
                .id(UUIDUtil.getUUID())
                .requestTime(new Date())
                .userId(userId)
                .token(token)
                .requestUri(requestUri)
                .userAgent(userAgent)
                .requestHeaders(headers.toString())
                .build();
        MediaType mediaType = request.getHeaders().getContentType();
        if (MediaType.APPLICATION_FORM_URLENCODED.isCompatibleWith(mediaType) || MediaType.APPLICATION_JSON.isCompatibleWith(mediaType)) {
            return writeBodyLog(exchange, chain, requestLogDTO).onErrorResume(throwable -> {
                Throwable replaceThrowable;
                if (throwable instanceof Throwable) {
                    replaceThrowable = (Throwable) throwable;
                } else {
                    replaceThrowable = new RuntimeException(throwable.toString());
                }
                // 即使发生异常也记录日志
                writeRequestLog(requestLogDTO, replaceThrowable);
                return Mono.error(replaceThrowable);
            });
        } else {
            return writeBasicLog(exchange, chain, requestLogDTO).onErrorResume(throwable -> {
                // 即使发生异常也记录日志
                writeRequestLog(requestLogDTO, throwable);
                return Mono.error(throwable);
            });
        }
    }

    private Mono<Void> writeBasicLog(ServerWebExchange exchange, GatewayFilterChain chain, RequestLogDTO requestLogDTO) {
        StringBuilder builder = new StringBuilder();
        MultiValueMap<String, String> queryParams = exchange.getRequest().getQueryParams();
        for (Map.Entry<String, List<String>> entry : queryParams.entrySet()) {
            builder.append(entry.getKey()).append("=").append(StringUtils.join(entry.getValue(), ",")).append("&");
        }
        requestLogDTO.setRequestPayLoad(builder.toString());
        //获取响应体
        ServerHttpResponseDecorator decoratedResponse = recordResponseLog(exchange, requestLogDTO);
        return chain.filter(exchange.mutate().response(decoratedResponse).build())
                .then(Mono.fromRunnable(() -> {
                    // 打印日志
                    writeRequestLog(requestLogDTO, null);
                }));
    }


    /**
     * 写入响应日志
     *
     * @param exchange
     * @param chain
     * @param requestLogDTO
     * @return
     */
    @SuppressWarnings("unchecked")
    private Mono<Void> writeBodyLog(ServerWebExchange exchange, GatewayFilterChain chain, RequestLogDTO requestLogDTO) {
        ServerHttpRequest request = exchange.getRequest();

        // 1、获取 Content-Length，判断是否过大（如 > 10MB）
        Long contentLength = request.getHeaders().getContentLength();
        if (contentLength != null && contentLength > MAX_RECORD_BYTES) {
            requestLogDTO.setRequestPayLoad("BODY_TOO_LARGE:" + contentLength + " bytes");
            // 不读 body，直接放行
            return chain.filter(exchange)
                    .then(Mono.fromRunnable(() -> writeRequestLog(requestLogDTO, null)));
        }

        // 2、安全读取 body
        return DataBufferUtils.join(request.getBody(), MAX_RECORD_BYTES) // 最多读 64KB
                .flatMap(dataBuffer -> {
                    try {
                        int length = dataBuffer.readableByteCount();
                        byte[] bytes = new byte[length];
                        dataBuffer.read(bytes);
                        String body = new String(bytes, StandardCharsets.UTF_8);
                        requestLogDTO.setRequestPayLoad(body);
                        return Mono.just(body);
                    } finally {
                        DataBufferUtils.release(dataBuffer); // 释放 buffer
                    }
                })
                .onErrorReturn("READ_ERROR")
                .flatMap(body -> {
                    // 3. 正确创建 Flux<DataBuffer>
                    DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
                    Flux<DataBuffer> bodyFlux = body.equals("READ_ERROR") ? Flux.empty() : Flux.just(buffer);

                    //4. 装饰 request，只重写 getBody()，不重写 getHeaders() —— 保持原始 header，包括 Content-Length
                    ServerHttpRequest decoratedRequest = new ServerHttpRequestDecorator(request) {
                        @Override
                        public Flux<DataBuffer> getBody() {
                            return bodyFlux;
                        }
                    };

                    return chain.filter(exchange.mutate().request(decoratedRequest).build());
                })
                .then(Mono.fromRunnable(() -> writeRequestLog(requestLogDTO, null)));
    }

    /**
     * 记录响应日志
     * 通过 DataBufferFactory 解决响应体分段传输问题。
     */
    private ServerHttpResponseDecorator recordResponseLog(ServerWebExchange exchange, RequestLogDTO requestLogDTO) {
        ServerHttpResponse response = exchange.getResponse();
        DataBufferFactory bufferFactory = response.bufferFactory();

        return new ServerHttpResponseDecorator(response) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    HttpStatus responseStatusCode = response.getStatusCode();
                    if (Objects.nonNull(responseStatusCode)) {
                        requestLogDTO.setResponseStatus(responseStatusCode.value());
                    }
                    Date responseTime = new Date();
                    // 计算执行时间
                    long executeTime = (responseTime.getTime() - requestLogDTO.getRequestTime().getTime());
                    requestLogDTO.setCostTime(executeTime);
                    // 获取响应类型，如果是 json 就打印
                    String originalResponseContentType = exchange.getAttribute(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR);
                    if (ObjectUtil.equal(this.getStatusCode(), HttpStatus.OK)
                            && StringUtils.isNotBlank(originalResponseContentType)
                            && originalResponseContentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
                        Flux<? extends DataBuffer> fluxBody = Flux.from(body);
                        return super.writeWith(fluxBody.buffer().map(dataBuffers -> {
                            // 合并多个流集合，解决返回体分段传输
                            DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
                            DataBuffer join = dataBufferFactory.join(dataBuffers);
                            byte[] content = new byte[join.readableByteCount()];
                            join.read(content);
                            byte[] unGzipContent = isGzip(response) ? this.decodeContent(content) : content;
                            // 释放掉内存
                            DataBufferUtils.release(join);
                            String responseResult = new String(unGzipContent, StandardCharsets.UTF_8);
                            requestLogDTO.setResponsePayLoad(responseResult);
                            return bufferFactory.wrap(content);
                        }));
                    }
                }
                // if body is not a flux. never got there.
                return super.writeWith(body);
            }

            private byte[] decodeContent(byte[] content) {
                try (ByteArrayInputStream bis = new ByteArrayInputStream(content);
                     GZIPInputStream gzipInputStream = new GZIPInputStream(bis);
                     ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = gzipInputStream.read(buffer)) > 0) {
                        bos.write(buffer, 0, len);
                    }
                    return bos.toByteArray();
                } catch (IOException e) {
                    log.warn("Gzip解压失败，返回原始内容", e);
                    return content;
                }
            }
        };
    }

    /**
     * 判断响应是否是 gzip
     *
     * @param serverHttpResponse
     * @return
     */
    private boolean isGzip(ServerHttpResponse serverHttpResponse) {
        HttpHeaders headers = serverHttpResponse.getHeaders();
        if (headers.containsKey(HttpHeaders.CONTENT_ENCODING)) {
            List<String> encodingList = headers.get(HttpHeaders.CONTENT_ENCODING);
            return CollectionUtils.isNotEmpty(encodingList) && encodingList.contains("gzip");
        }
        return false;
    }

    /**
     * 保存日志
     *
     * @param logDTO 网关日志
     */
    private void writeRequestLog(RequestLogDTO logDTO, Throwable throwable) {
        if (Objects.nonNull(throwable)) {
            if (throwable instanceof ConnectException) {
                logDTO.setResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            } else if (throwable instanceof AuthenticationException) {
                logDTO.setResponseStatus(HttpStatus.UNAUTHORIZED.value());
            } else if (throwable instanceof AccessDeniedException) {
                logDTO.setResponseStatus(HttpStatus.FORBIDDEN.value());
            } else if (throwable instanceof HttpClientErrorException) {
                logDTO.setResponseStatus(((HttpClientErrorException) throwable).getRawStatusCode());
            } else if (throwable instanceof HttpServerErrorException) {
                logDTO.setResponseStatus(((HttpServerErrorException) throwable).getRawStatusCode());
            } else {
                logDTO.setResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            }
            long currentTime = System.currentTimeMillis();
            long executeTime = currentTime - logDTO.getRequestTime().getTime();
            logDTO.setCostTime(executeTime);
        }
        logDTO.setCreateTime(new Date());
        try {
            executorService.submit(() -> {
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<RequestLogDTO> entity = new HttpEntity<>(logDTO, httpHeaders);
                restTemplate.postForLocation(url, entity);
            });
        } catch (Exception e) {
            log.error("保存日志失败，日志内容：{},{}", logDTO, e.getMessage());
        }
    }

    @Override
    public int getOrder() {
        return -100;
    }

    /**
     * 获取userId
     */
    private String getUserIdByRequest(ServerHttpRequest request) {
        String user = request.getHeaders().getFirst("X-User-Id");
        if (StringUtils.isBlank(user)) {
            user = request.getHeaders().getFirst("userid");
        }
        if (StringUtils.isBlank(user)) {
            user = request.getHeaders().getFirst("Userid");
        }
        return user;
    }

    /**
     * 获取token
     */
    private String getTokenByRequest(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst("Authorization");
        if (StringUtils.isBlank(token)) {
            token = request.getHeaders().getFirst("token");
        }
        if (StringUtils.isBlank(token)) {
            token = request.getHeaders().getFirst("Token");
        }
        return token;
    }

    /**
     * 获取user-agent
     */
    private String getUserAgentByRequest(ServerHttpRequest request) {
        return request.getHeaders().getFirst("User-Agent");
    }
}
