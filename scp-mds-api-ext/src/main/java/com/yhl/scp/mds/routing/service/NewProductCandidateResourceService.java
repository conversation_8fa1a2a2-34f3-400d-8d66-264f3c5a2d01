package com.yhl.scp.mds.routing.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldTooling;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;

import java.util.List;
import java.util.Map;

/**
 * <code>NewProductCandidateResourceService</code>
 * <p>
 * 物品候选资源应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 11:43:23
 */
public interface NewProductCandidateResourceService extends BaseService<ProductCandidateResourceDTO, ProductCandidateResourceVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductCandidateResourceVO}
     */
    List<ProductCandidateResourceVO> selectAll();

    /**
     * 定时任务同步模具工装
     *
     * @return
     */
    BaseResponse<Void> synMoldToolingGroupDir(String tenantId);

    /**
     * 同步模具工装
     *
     * @return
     */
    BaseResponse<Void> handleMoldToolingGroupDir(List<MesMoldToolingGroupDir> list);

    List<ProductCandidateResourceVO> selectVOByParams(Map<String, Object> params);

	void doLogicDelete(List<String> ids);

}
