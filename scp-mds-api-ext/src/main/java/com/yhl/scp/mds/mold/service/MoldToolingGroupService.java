package com.yhl.scp.mds.mold.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroup;
import com.yhl.scp.mds.mold.dto.MoldToolingGroupDTO;
import com.yhl.scp.mds.mold.vo.MoldToolingGroupVO;

import java.util.List;

/**
 * <code>MoldToolingGroupService</code>
 * <p>
 * 模具工装族应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:27:01
 */
public interface MoldToolingGroupService extends BaseService<MoldToolingGroupDTO, MoldToolingGroupVO> {

    /**
     * 查询所有
     *
     * @return list {@link MoldToolingGroupVO}
     */
    List<MoldToolingGroupVO> selectAll();

    /**
     * 定时任务同步模具工装
     *
     * @return
     */
    BaseResponse<Void> synMoldToolingGroup(String tenantId);

    /**
     * 同步模具工装
     *
     * @return
     */
    BaseResponse<Void> handleMoldToolingGroup(List<MesMoldToolingGroup> list);

}
