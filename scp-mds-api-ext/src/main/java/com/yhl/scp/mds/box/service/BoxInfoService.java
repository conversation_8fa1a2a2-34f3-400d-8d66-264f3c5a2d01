package com.yhl.scp.mds.box.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesBoxInfo;
import com.yhl.scp.mds.box.dto.BoxInfoDTO;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;

import java.util.List;

/**
 * <code>BoxInfoService</code>
 * <p>
 * 箱体信息应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:13:43
 */
public interface BoxInfoService extends BaseService<BoxInfoDTO, BoxInfoVO> {

    /**
     * 查询所有
     *
     * @return list {@link BoxInfoVO}
     */
    List<BoxInfoVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int deleteBatchVersion(List<RemoveVersionDTO> versionDTOList);

    List<LabelValue<String>> listBoxTypeDropDown();

    List<LabelValue<String>> listBoxCodeDropDown(String boxType);

    List<BoxInfoVO> selectByPrimaryKeys(List<String> ids);

    BaseResponse<Void> syncStockPoints(String tenantId,String organizeId);

    BaseResponse<Void> handleFoundationBoxInfo(List<MesBoxInfo> o, String ebsOuId);

    List<BoxInfoVO> boxCodeDropDown();
}
