package com.yhl.scp.mds.productBox.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductBoxRelation;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.productBox.dto.ProductBoxRelationDTO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;

import java.util.List;

/**
 * <code>ProductBoxRelationService</code>
 * <p>
 * 产品与成品箱关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 21:03:25
 */
public interface ProductBoxRelationService extends BaseService<ProductBoxRelationDTO, ProductBoxRelationVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductBoxRelationVO}
     */
    List<ProductBoxRelationVO> selectAll();

    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    List<ProductBoxRelationVO> selectVOByProductCodeList(List<String> productCodeList);

    /**
     * 查询箱型下类型
     * @return
     */
    List<LabelValue<String>> queryBoxType();

    BaseResponse<Void> syncProductBoxRelation(String tenantId);

    BaseResponse<Void> handleProductBoxRelation(List<MesProductBoxRelation> mesProductBoxRelationList);

    List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointId(List<String> productStockPointIdList);
}
