package com.yhl.scp.mds.productroutestepbase.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>MdsProductStockPointBaseVO</code>
 * <p>
 * 产品工艺基础数据VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:28:43
 */
@ApiModel(value = "产品工艺基础数据VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdsProductStockPointBaseVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 900972383863741677L;

    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    @FieldInterpretation(value = "库存点ID")
    private String stockPointId;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 编码名称
     */
    @ApiModelProperty(value = "编码名称")
    @FieldInterpretation(value = "编码名称")
    private String productName;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚
     */
    @ApiModelProperty(value = "厚")
    @FieldInterpretation(value = "厚")
    private BigDecimal productThickness;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @FieldInterpretation(value = "装车位置")
    private String loadPosition;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 玻璃颜色
     */
    @ApiModelProperty(value = "玻璃颜色")
    @FieldInterpretation(value = "玻璃颜色")
    private String glassColor;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @FieldInterpretation(value = "产品类型")
    private String productType;
    /**
     * 难度等级
     */
    @ApiModelProperty(value = "难度等级")
    @FieldInterpretation(value = "难度等级")
    private String difficultyLevel;
    /**
     * 风栅类型
     */
    @ApiModelProperty(value = "风栅类型")
    @FieldInterpretation(value = "风栅类型")
    private String gridType;
    /**
     * 生产模式
     */
    @ApiModelProperty(value = "生产模式")
    @FieldInterpretation(value = "生产模式")
    private String productionModel;
    /**
     * 钢化类型
     */
    @ApiModelProperty(value = "钢化类型")
    @FieldInterpretation(value = "钢化类型")
    private String tougheningType;
    /**
     * 膜系
     */
    @ApiModelProperty(value = "膜系")
    @FieldInterpretation(value = "膜系")
    private String membraneSystem;
    /**
     * HUD
     */
    @ApiModelProperty(value = "HUD")
    @FieldInterpretation(value = "HUD")
    private String hud;
    /**
     * 夹丝类型
     */
    @ApiModelProperty(value = "夹丝类型")
    @FieldInterpretation(value = "夹丝类型")
    private String clampType;
    /**
     * 印边
     */
    @ApiModelProperty(value = "印边")
    @FieldInterpretation(value = "印边")
    private String sealEdge;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @FieldInterpretation(value = "面积")
    private String productArea;
    /**
     * 曲率
     */
    @ApiModelProperty(value = "曲率")
    @FieldInterpretation(value = "曲率")
    private String curvature;
    /**
     * 编码目录号
     */
    @ApiModelProperty(value = "编码目录号")
    @FieldInterpretation(value = "编码目录号")
    private String dirNum;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    @FieldInterpretation(value = "工艺类型")
    private String itemType;
    /**
     * 除膜工艺
     */
    @ApiModelProperty(value = "除膜工艺")
    @FieldInterpretation(value = "除膜工艺")
    private String attr1;
    /**
     * 物料标识
     */
    @ApiModelProperty(value = "物料标识")
    @FieldInterpretation(value = "物料标识")
    private String itemFlag;
    /**
     * 生产线组
     */
    @ApiModelProperty(value = "生产线组")
    @FieldInterpretation(value = "生产线组")
    private String lineGroup;

    @ApiModelProperty(value = "资源id")
    @FieldInterpretation(value = "资源id")
    private String standardResourceId;
    
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @FieldInterpretation(value = "物料id")
    private String productId;

    @Override
    public void clean() {

    }
    /**
     * 资源代码
     */
    @ApiModelProperty(value = "资源代码")
    @FieldInterpretation(value = "资源代码")
    private String resourceCode;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    @FieldInterpretation(value = "资源名称")
    private String resourceName;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @FieldInterpretation(value = "零件号")
    private String partNum;
    /**
     * 钢化淋子方向
     */
    @ApiModelProperty(value = "钢化淋子方向")
    @FieldInterpretation(value = "钢化淋子方向")
    private String sprinkleDirection;
    /**
     * 夹层第一片淋子方向
     */
    @ApiModelProperty(value = "夹层第一片淋子方向")
    @FieldInterpretation(value = "夹层第一片淋子方向")
    private String oneSprinkleDirection;
    /**
     * 夹层第二片淋子方向
     */
    @ApiModelProperty(value = "夹层第二片淋子方向")
    @FieldInterpretation(value = "夹层第二片淋子方向")
    private String twoSprinkleDirection;
    /**
     * 夹层第三片淋子方向
     */
    @ApiModelProperty(value = "夹层第三片淋子方向")
    @FieldInterpretation(value = "夹层第三片淋子方向")
    private String threeSprinkleDirection;
    /**
     * 夹层第四片淋子方向
     */
    @ApiModelProperty(value = "夹层第四片淋子方向")
    @FieldInterpretation(value = "夹层第四片淋子方向")
    private String fourSprinkleDirection;
    /**
     * PLM账号
     */
    @ApiModelProperty(value = "PLM账号")
    @FieldInterpretation(value = "PLM账号")
    private String userId;
}
