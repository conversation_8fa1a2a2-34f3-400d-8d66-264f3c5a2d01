package com.yhl.scp.mds.supplier.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.mds.extension.supplier.dto.SupplierDTO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>SupplierService</code>
 * <p>
 * 供应商应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-21 18:37:13
 */
public interface SupplierDataService extends BaseService<SupplierDTO, SupplierVO> {

    /**
     * 查询所有
     *
     * @return list {@link SupplierVO}
     */
    List<SupplierVO> selectAll();

    /**
     * 冗余供应商信息
     *
     * @param dataList             VO数据
     * @param invocation           invocation
     * @param fieldName            指定冗余字段名
     * @param paramName            参数
     * @param relationKey          关联外键
     * @param relationObjectColumn 关联主键
     */
    void addSupplierColumn(List<? extends BaseVO> dataList, String invocation, String fieldName, String paramName, String relationKey, String relationObjectColumn);


    /**
     * 导出
     *
     * @param response
     */
    void export(HttpServletResponse response);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncSupplier(String tenantId);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleSupplier(List<ErpSupplier> erpSuppliers);

    List<LabelValue<String>> supplyDropdown();

    /**
     * 供应商（模糊搜索）
     *
     * @param supplierName 名称
     * @return 物品信息
     */
    List<SupplierVO> selectSupplierLike(String supplierName);
}