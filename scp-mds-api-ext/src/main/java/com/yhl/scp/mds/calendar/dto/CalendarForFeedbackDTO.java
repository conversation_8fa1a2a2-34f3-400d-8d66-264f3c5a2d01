package com.yhl.scp.mds.calendar.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <code>CalendarForFeedbackDTO</code>
 * <p>
 * 异常报工反馈维护异常日历请求参数实体
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-08 17:33:32
 */
@ApiModel(value = "异常报工反馈维护异常日历请求参数实体DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CalendarForFeedbackDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -4412308482540237219L;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "库存点代码")
    private Date endTime;
    
    /**
     * 资源编码
     */
    @ApiModelProperty(value = "资源编码")
    private String resourceCode;
    
    /**
     * 日历规则ids（已绑定，需要删除的）
     */
    @ApiModelProperty(value = "日历规则ids（已绑定，需要删除的）")
    private String calendarRuleIds;
    
    /**
     * 生产组织编码
     */
    @ApiModelProperty(value = "生产组织编码")
    private String organizationCode;
    
    /**
     * 日历规则ids（已绑定，需要删除的）
     */
    @ApiModelProperty(value = "日历规则ids（已绑定，需要删除的）")
    private String onlyDeleteCalendarRuleIds;
  
}
