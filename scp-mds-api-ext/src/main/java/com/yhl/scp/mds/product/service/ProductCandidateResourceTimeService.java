package com.yhl.scp.mds.product.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.mds.product.dto.ProductCandidateResourceTimeDTO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>ProductCandidateResourceTimeService</code>
 * <p>
 * 产品资源生产关系时段优先级表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 10:02:54
 */
public interface ProductCandidateResourceTimeService extends BaseService<ProductCandidateResourceTimeDTO, ProductCandidateResourceTimeVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductCandidateResourceTimeVO}
     */
    List<ProductCandidateResourceTimeVO> selectAll();

    void export(HttpServletResponse response);

    /**
     * 更新优先级
     * @param vo
     */
    void updateData(ProductCandidateResourceTimeVO vo);

    /**
     * 同步生产节拍
     * @param
     * @return
     */
    BaseResponse<Void> syncRoutingStepResourceBase(List<MesMoldChangeTime> list);


    BaseResponse<Void> syncMoldChangeTime(String tenantId);

    List<ProductCandidateResourceTimeVO> selectByProductCode(List<String> productCodeList);

    void afterCandidateResourceOnProduct();
}
