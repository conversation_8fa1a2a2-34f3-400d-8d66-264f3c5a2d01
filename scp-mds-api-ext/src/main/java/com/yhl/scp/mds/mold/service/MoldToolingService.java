package com.yhl.scp.mds.mold.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldTooling;
import com.yhl.scp.mds.mold.dto.MoldToolingDTO;
import com.yhl.scp.mds.mold.vo.MoldToolingVO;

import java.util.List;

/**
 * <code>MoldToolingService</code>
 * <p>
 * 模具工装族与工装编号关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:28:45
 */
public interface MoldToolingService extends BaseService<MoldToolingDTO, MoldToolingVO> {

    /**
     * 查询所有
     *
     * @return list {@link MoldToolingVO}
     */
    List<MoldToolingVO> selectAll();

    /**
     * 定时任务同步模具工装
     *
     * @return
     */
    BaseResponse<Void> synMoldTooling(String tenantId);

    /**
     * 同步模具工装
     *
     * @return
     */
    BaseResponse<Void> handleMoldTooling(List<MesMoldTooling> list);

    List<MoldToolingVO> selectById(List<String> idList);
}
