package com.yhl.scp.mds.customer.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpCustomer;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.mds.customer.dto.CustomerDTO;
import com.yhl.scp.mds.customer.vo.CustomerVO;

import java.util.List;

/**
 * <code>CustomerService</code>
 * <p>
 * erp客户应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-02 19:12:46
 */
public interface CustomerService extends BaseService<CustomerDTO, CustomerVO> {

    /**
     * 查询所有
     *
     * @return list {@link CustomerVO}
     */
    List<CustomerVO> selectAll();
    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncCustomer(String tenantId);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleCustomer(List<ErpCustomer> o);
}
