package com.yhl.scp.mds.newproduct.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "新-物品VO")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewProductStockPointVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -26603356772205250L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @FieldInterpretation(value = "主键ID")
    private String id;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    @FieldInterpretation(value = "物品代码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;
    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    @FieldInterpretation(value = "物品类型")
    private String productType;
    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @FieldInterpretation(value = "物品分类")
    private String productClassify;
    /**
     * 分类说明
     */
    @ApiModelProperty(value = "分类说明")
    @FieldInterpretation(value = "分类说明")
    private String classifyDesc;

    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productFactoryCode;

    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    @FieldInterpretation(value = "供应类型")
    private String supplyType;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @FieldInterpretation(value = "计量单位")
    private String measurementUnit;
    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    @FieldInterpretation(value = "销售类型")
    private String saleType;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @FieldInterpretation(value = "装车位置")
    private String loadingPosition;
    /**
     * 装车位置小类
     */
    @ApiModelProperty(value = "装车位置小类")
    @FieldInterpretation(value = "装车位置小类")
    private String loadingPositionSub;
    /**
     * 车型类型
     */
    @ApiModelProperty(value = "车型类型")
    @FieldInterpretation(value = "车型类型")
    private String vehicleModelType;
    /**
     * 业务特性
     */
    @ApiModelProperty(value = "业务特性")
    @FieldInterpretation(value = "业务特性")
    private String businessSpecial;
    /**
     * 核心工序
     */
    @ApiModelProperty(value = "核心工序")
    @FieldInterpretation(value = "核心工序")
    private String coreProcess;
    /**
     * 产品特性
     */
    @ApiModelProperty(value = "产品特性")
    @FieldInterpretation(value = "产品特性")
    private String productSpecial;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @FieldInterpretation(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @FieldInterpretation(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 面积M2
     */
    @ApiModelProperty(value = "面积M2")
    @FieldInterpretation(value = "面积M2")
    private BigDecimal productArea;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @FieldInterpretation(value = "重量")
    private BigDecimal productWeight;
    /**
     * 重量单位
     */
    @ApiModelProperty(value = "重量单位")
    @FieldInterpretation(value = "重量单位")
    private String weightUnit;
    /**
     * 每卷/每箱数量
     */
    @ApiModelProperty(value = "每卷/每箱数量")
    @FieldInterpretation(value = "每卷/每箱数量")
    private BigDecimal productQuantity;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private Integer expireDate;
    /**
     * 特性说明
     */
    @ApiModelProperty(value = "特性说明")
    @FieldInterpretation(value = "特性说明")
    private String specialDesc;
    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
    @FieldInterpretation(value = "最小起订量")
    private BigDecimal minOrderQuantity;
    /**
     * 运输周期
     */
    @ApiModelProperty(value = "运输周期")
    @FieldInterpretation(value = "运输周期")
    private BigDecimal transportCycle;
    /**
     * 采购周期预加工
     */
    @ApiModelProperty(value = "采购周期预加工")
    @FieldInterpretation(value = "采购周期预加工")
    private String purchaseProcessPre;
    /**
     * 采购周期加工中
     */
    @ApiModelProperty(value = "采购周期加工中")
    @FieldInterpretation(value = "采购周期加工中")
    private String purchaseProcessIng;
    /**
     * 采购周期加工后
     */
    @ApiModelProperty(value = "采购周期加工后")
    @FieldInterpretation(value = "采购周期加工后")
    private String purchaseProcessAfter;
    /**
     * 物料采购锁定期
     */
    @ApiModelProperty(value = "物料采购锁定期")
    @FieldInterpretation(value = "物料采购锁定期")
    private BigDecimal purchaseLockPeriod;
    /**
     * 物料计划员
     */
    @ApiModelProperty(value = "物料计划员")
    @FieldInterpretation(value = "物料计划员")
    private String productPlanUser;
    /**
     * 人员
     */
    @ApiModelProperty(value = "人员")
    @FieldInterpretation(value = "人员")
    private String productUser;
    /**
     * 订单计划员
     */
    @ApiModelProperty(value = "订单计划员")
    @FieldInterpretation(value = "订单计划员")
    private String orderPlanner;
    /**
     * 生产计划员
     */
    @ApiModelProperty(value = "生产计划员")
    @FieldInterpretation(value = "生产计划员")
    private String productionPlanner;
    /**
     * 材料计划员
     */
    @ApiModelProperty(value = "材料计划员")
    @FieldInterpretation(value = "材料计划员")
    private String materialPlanner;
    /**
     * 采购计划员
     */
    @ApiModelProperty(value = "采购计划员")
    @FieldInterpretation(value = "采购计划员")
    private String purchasePlanner;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    @FieldInterpretation(value = "SOP")
    private Date productSop;
    /**
     * EOP
     */
    @ApiModelProperty(value = "EOP")
    @FieldInterpretation(value = "EOP")
    private Date productEop;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    @FieldInterpretation(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @FieldInterpretation(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @FieldInterpretation(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @FieldInterpretation(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @FieldInterpretation(value = "修改时间")
    private Date modifyTime;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @FieldInterpretation(value = "计划员")
    private String plannerCode;
    /**
     * 采购计划类别
     */
    @ApiModelProperty(value = "采购计划类别")
    @FieldInterpretation(value = "采购计划类别")
    private String poCategory;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    @FieldInterpretation(value = "组织ID")
    private String organizationId;
    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    @FieldInterpretation(value = "物料ID")
    private String inventoryItemId;
    /**
     * 预测销量
     */
    @ApiModelProperty(value = "预测销量")
    @FieldInterpretation(value = "预测销量")
    private BigDecimal forecastNumber;

    /**
     * 历史销量
     */
    @ApiModelProperty(value = "历史销量")
    @FieldInterpretation(value = "历史销量")
    private BigDecimal historyNumber;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    @FieldInterpretation(value = "物料状态")
    private String inventoryItemStatusCode;

    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    @FieldInterpretation(value = "物料大类")
    private String productCategory;
    /**
     * 物料大类
     */
    @ApiModelProperty(value = "最近更新时间")
    @FieldInterpretation(value = "最近更新时间")
    private Date lastUpdateDate;
    /**
     * 库存点类型
     */
    @ApiModelProperty(value = "库存点类型")
    @FieldInterpretation(value = "库存点类型")
    private String stockPointType;


    @Override
    public void clean() {

    }

    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @FieldInterpretation(value = "零件号")
    private String partNumber;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    @FieldInterpretation(value = "零件名称")
    private String partName;
    /**
     * 订单计划员名
     */
    @ApiModelProperty(value = "订单计划员名")
    @FieldInterpretation(value = "订单计划员名")
    private String orderPlannerName;
    /**
     * 生产计划员名
     */
    @ApiModelProperty(value = "生产计划员名")
    @FieldInterpretation(value = "生产计划员名")
    private String productionPlannerName;
    /**
     * 材料计划员名
     */
    @ApiModelProperty(value = "材料计划员名")
    @FieldInterpretation(value = "材料计划员名")
    private String materialPlannerName;
    /**
     * edi标志
     */
    @ApiModelProperty(value = "edi标志")
    @FieldInterpretation(value = "edi标志")
    private String ediFlag;

    /**
     * 材料状态
     */
    @ApiModelProperty(value = "材料状态")
    @FieldInterpretation(value = "材料状态")
    private String materialDemandStatus;
    /**
     * 物料标识
     */
    @ApiModelProperty(value = "物料标识")
    @FieldInterpretation(value = "物料标识")
    private String itemFlag;
    /**
     * 编码类型标识
     */
    @ApiModelProperty(value = "编码类型标识")
    @FieldInterpretation(value = "编码类型标识")
    private String isbj;

    @ApiModelProperty(value = "模数量限制")
    @FieldInterpretation(value = "模数量限制")
    private Integer moldQuantityLimit;

    /**
     * 自提类型：自提，非自提
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    @FieldInterpretation(value = "自提类型：自提，非自提")
    private String pickUpType;

    /**
     * 理货单模式，MES，GRP
     */
    @ApiModelProperty(value = "理货单模式，MES，GRP")
    @FieldInterpretation(value = "理货单模式，MES，GRP")
    private String tallyOrderMode;

    /**
     * 是否整箱
     */
    @ApiModelProperty(value = "是否整箱")
    @FieldInterpretation(value = "是否整箱")
    private String fullBoxFlag;

    /**
     * 单位输入输出量
     */
    @ApiModelProperty(value = "单位输入输出量")
    @FieldInterpretation(value = "单位输入输出量")
    private BigDecimal ioFactor;
    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    @FieldInterpretation(value = "风险等级")
    private String riskLevel;
    /**
     * 物料成本
     */
    @ApiModelProperty(value = "物料成本")
    @FieldInterpretation(value = "物料成本")
    private String itemCost;

    public String getItemCost() {
        return null;
    }
}
