package com.yhl.scp.biz.common.excel;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.yhl.scp.common.constants.Constants;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <code>SheetStyleHandler</code>
 * <p>
 * SheetStyleHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19 08:52:54
 */
@Slf4j
@Getter
@Setter
public class SheetStyleHandler implements CellWriteHandler {

    /**
     * 行索引->颜色索引映射
     */
    private Map<Integer, Short> rowBackColorMap;
    /**
     * 列索引->颜色索引映射
     */
    private Map<Integer, Short> columnBackColorMap;
    /**
     * 单元格（行索引#列索引）索引->颜色索引映射
     */
    private Map<String, Short> cellBackColorMap;
    /**
     * 总行数
     */
    private Integer rowCount;

    /**
     * 条件判断接口
     */
    @FunctionalInterface
    public interface CellColorCondition {
        boolean shouldApply(Cell cell, Integer rowIndex, Integer columnIndex);
    }

    /**
     * 条件样式判断接口数据
     */
    private List<CellColorCondition> cellColorConditions = new ArrayList<>();
    
    public SheetStyleHandler(Map<Integer, Short> rowBackColorMap, Integer rowCount) {
        this(rowBackColorMap, null, null, rowCount);
    }
    
    public SheetStyleHandler(Map<Integer, Short> rowBackColorMap, Map<Integer, Short> columnBackColorMap,
                             Map<String, Short> cellBackColorMap, Integer rowCount) {
        this.rowBackColorMap = rowBackColorMap;
        this.columnBackColorMap = columnBackColorMap;
        this.cellBackColorMap = cellBackColorMap;
        this.rowCount = rowCount;
    }

    /**
     * 添加条件样式
     */
    public void addCellColorCondition(CellColorCondition condition) {
        this.cellColorConditions.add(condition);
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        Cell cell = context.getCell();
        Boolean isHead = context.getHead();
        // 当前事件会在 数据设置到poi的cell里面才会回调
        // 判断不是头的情况 如果是fill 的情况 这里会==null 所以用not true
        sheet.setDisplayGridlines(true);
        // 行号-重要***
        Integer rowIndex = context.getRowIndex();
        // 列号-重要***
        int colIndex = cell.getColumnIndex();
        if (Boolean.TRUE.equals(isHead)) {
            if (context.getRelativeRowIndex() == 0) {
                // 设置列宽，单位是1/256个字符宽度
                sheet.setColumnWidth(colIndex, 15 * 256);
            }
            // 表头
            Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // 颜色
            // 设置rgb颜色
            XSSFCellStyle xssfCellColorStyle = (XSSFCellStyle) cellStyle;
            xssfCellColorStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
            xssfCellColorStyle.setAlignment(HorizontalAlignment.CENTER);
            xssfCellColorStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 宽度
            // 边框
            xssfCellColorStyle.setBorderBottom(BorderStyle.THIN);
            xssfCellColorStyle.setBorderLeft(BorderStyle.THIN);
            xssfCellColorStyle.setBorderRight(BorderStyle.THIN);
            xssfCellColorStyle.setBorderTop(BorderStyle.THIN);

            // 字体
            Font font = workbook.createFont();
            font.setBold(true);
            font.setFontHeightInPoints((short) 11);
            font.setFontName("微软雅黑");
            font.setColor(IndexedColors.BLACK.getIndex());
            xssfCellColorStyle.setFont(font);

            cell.setCellStyle(xssfCellColorStyle);
            context.getFirstCellData().setWriteCellStyle(null);
        } else {
            // 单元格
            Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
            // 不同单元格尽量传同一个 cellStyle
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 如果设置了每行的样式，动态设置；没有设置则统一设置为白色背景
            // 1. 先应用行背景色
            Short rowColor = rowBackColorMap != null ? rowBackColorMap.get(rowIndex) : null;
            if (rowColor != null) {
                cellStyle.setFillForegroundColor(rowColor);
            }

            // 2. 应用列背景色（会覆盖行背景色）
            if (columnBackColorMap != null) {
                Short columnColor = columnBackColorMap.get(colIndex);
                if (columnColor != null) {
                    cellStyle.setFillForegroundColor(columnColor);
                }
            }

            // 3. 应用条件背景色（优先级最高）
            for (CellColorCondition condition : cellColorConditions) {
                if (condition.shouldApply(cell, rowIndex, colIndex)) {
                    // 示例：数值大于100的单元格设置为红色
                    String coordination = rowIndex + Constants.DELIMITER + colIndex;
                    if (cellBackColorMap.containsKey(coordination)) {
                        cellStyle.setFillForegroundColor(cellBackColorMap.get(coordination));
                    } else {
                        cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                    }
                    // 可以添加更多条件判断
                    break;
                }
            }

            // 4. 默认背景色（如果没有设置任何背景色）
            if (cellStyle.getFillForegroundColor() == 0) {
                cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            }
            // 水平居中
            cellStyle.setAlignment(HorizontalAlignment.CENTER);

            // 垂直居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 边框
            if (rowCount != null) {
                if (rowIndex < rowCount - 3) {
                    cellStyle.setBorderBottom(BorderStyle.THIN);
                    cellStyle.setBorderLeft(BorderStyle.THIN);
                    cellStyle.setBorderRight(BorderStyle.THIN);
                    cellStyle.setBorderTop(BorderStyle.THIN);
                } else {
                    cellStyle.setBorderBottom(BorderStyle.NONE);
                    cellStyle.setBorderLeft(BorderStyle.NONE);
                    cellStyle.setBorderRight(BorderStyle.NONE);
                    cellStyle.setBorderTop(BorderStyle.NONE);
                }
            } else {
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
            }

            // 字体
            Font font = workbook.createFont();
            font.setFontHeightInPoints((short) 8);
            font.setFontName("微软雅黑");
            cellStyle.setFont(font);

            cell.setCellStyle(cellStyle);
            /*
             * 由于这里没有指定dataformat 最后展示的数据 格式可能会不太正确
             * 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler
             * 默认会将 WriteCellStyle 设置到 cell里面去 会导致自己设置的不一样（很关键）
             */
            context.getFirstCellData().setWriteCellStyle(null);
        }
    }

}
