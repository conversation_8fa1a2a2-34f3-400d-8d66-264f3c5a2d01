package com.yhl.scp.biz.common.webSocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.common.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <code>WebSocketHandler</code>
 * <p>
 * 公共WebSocket处理类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-18 17:14:26
 */
@Component
@Slf4j
public abstract class CommonWebSocketNewHandler extends TextWebSocketHandler {

    // 过期时间常量
    private static final int REDIS_EXPIRE_DAYS = 1;
    // 消息体
    private static final String MESSAGE = "message";
    // 发送方Pod
    private static final String FROM_POD = "fromPod";
    // 接受session_id
    private static final String SESSION_ID = "sessionId";

    // 本地存储：仅保存当前Pod的WebSocket会话
    private final Map<String, WebSocketSession> localSessionMap = new ConcurrentHashMap<>();

    private final Map<String, List<String>> dataRange2SessionIdMap = new ConcurrentHashMap<>();

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private SetOperations<String, String> setOperations;

    // Redis消息监听器容器
    @Resource
    private RedisMessageListenerContainer redisMessageListenerContainer;

    // 消息监听适配器
    private MessageListenerAdapter broadcastMessageListenerAdapter;
    private MessageListenerAdapter podMessageListenerAdapter;

    // 缓存Pod ID，确保在整个应用生命周期内保持一致
    private String podId;

    @PostConstruct
    public void init() {
        if (redisTemplate != null) {
            setOperations = redisTemplate.opsForSet();
            // 初始化Pod ID
            this.podId = generatePodId();
            log.info("当前Pod ID: {}", this.podId);
            // 注册消息监听器
            registerRedisMessageListeners();
        }
    }

    @PreDestroy
    public void destroy() {
        // 销毁时取消注册消息监听器
        if (redisMessageListenerContainer != null) {
            if (broadcastMessageListenerAdapter != null) {
                redisMessageListenerContainer.removeMessageListener(broadcastMessageListenerAdapter,
                        new ChannelTopic(RedisKeyManageEnum.WEBSOCKET_BROADCAST_CHANNEL.getKey()));
            }
            if (podMessageListenerAdapter != null) {
                redisMessageListenerContainer.removeMessageListener(podMessageListenerAdapter,
                        new ChannelTopic(redisKey(RedisKeyManageEnum.WEBSOCKET_POD_MESSAGE_CHANNEL_PREFIX.getKey(), getPodId())));
            }
        }
    }

    /**
     * 注册Redis消息监听器
     */
    private void registerRedisMessageListeners() {
        if (redisMessageListenerContainer != null) {
            // 注册广播消息监听器
            broadcastMessageListenerAdapter = new MessageListenerAdapter(this, "handleRedisBroadcastMessage");
            redisMessageListenerContainer.addMessageListener(broadcastMessageListenerAdapter,
                    new ChannelTopic(RedisKeyManageEnum.WEBSOCKET_BROADCAST_CHANNEL.getKey()));

            // 注册特定Pod消息监听器
            podMessageListenerAdapter = new MessageListenerAdapter(this, "handleRedisPodMessage");
            redisMessageListenerContainer.addMessageListener(podMessageListenerAdapter,
                    new ChannelTopic(redisKey(RedisKeyManageEnum.WEBSOCKET_POD_MESSAGE_CHANNEL_PREFIX.getKey(), getPodId())));
        }
    }

    /**
     * 处理来自Redis的广播消息
     *
     * @param message 消息内容
     * @param pattern 频道模式
     */
    public void handleRedisBroadcastMessage(String message, String pattern) {
        try {
            // 反序列化消息
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            Object data = messageMap.get(MESSAGE);
            // 检查是否是自己发送的消息
            String fromPod = (String) messageMap.get(FROM_POD);
            if (getPodId().equals(fromPod)) {
                // 自己发送的消息，不需要处理
                return;
            }
            // 向本地所有用户发送消息
            sendMessageToAllLocal(data);
        } catch (Exception e) {
            log.error("处理Redis广播消息异常", e);
        }
    }

    /**
     * 处理来自Redis的特定Pod消息
     *
     * @param message 消息内容
     * @param pattern 频道模式
     */
    public void handleRedisPodMessage(String message, String pattern) {
        try {
            // 反序列化消息
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);

            String sessionId = (String) messageMap.get(SESSION_ID);
            Object data = messageMap.get(MESSAGE);
            // 检查是否是自己发送的消息
            String fromPod = (String) messageMap.get(FROM_POD);
            if (getPodId().equals(fromPod)) {
                // 自己发送的消息，不需要处理
                return;
            }

            // 向本地特定session发送消息
            sendMessageToLocalSession(sessionId, data);
        } catch (Exception e) {
            log.error("处理Redis特定Pod消息异常", e);
        }
    }

    @Override
    public void afterConnectionEstablished(@NonNull WebSocketSession session) {
        String sessionId = session.getId();
        localSessionMap.put(sessionId, session);
        String dataRange = getDataRange(session);
        if (dataRange2SessionIdMap.containsKey(dataRange)
                && !dataRange2SessionIdMap.get(dataRange).contains(sessionId)) {
            dataRange2SessionIdMap.get(dataRange).add(sessionId);
        } else {
            dataRange2SessionIdMap.put(dataRange, Lists.newArrayList(sessionId));
        }

        // 将session信息存储到Redis
        storeSessionInfoToRedis(sessionId, dataRange);

        String userId = String.join(Constants.DELIMITER, dataRange, sessionId);
        log.info("用户 {} 已连接", userId);
    }

    @Override
    public void afterConnectionClosed(@NonNull WebSocketSession session, @NonNull CloseStatus status) {
        String sessionId = session.getId();
        localSessionMap.remove(sessionId);

        String dataRange = getDataRange(session);
        if (!dataRange2SessionIdMap.containsKey(dataRange)) {
            return;
        }
        if (!dataRange2SessionIdMap.get(dataRange).isEmpty()) {
            dataRange2SessionIdMap.get(dataRange).remove(sessionId);
        } else {
            dataRange2SessionIdMap.remove(dataRange);
        }

        // 从Redis中删除session信息
        removeSessionInfoFromRedis(sessionId, dataRange);

        String userId = String.join(Constants.DELIMITER, dataRange, sessionId);
        log.info("用户 {} 已断开连接", userId);
    }

    @Override
    protected void handleTextMessage(@NonNull WebSocketSession session, TextMessage message) {
        String userId = getUserId(session);
        String payload = message.getPayload();
        log.info("成功接收，客户端给用户 {} 的消息: {}", userId, payload);
        // 处理客户端消息（如报工请求）
        // 这里可以根据消息类型进行不同处理
    }

    /**
     * 将session信息存储到Redis
     */
    private void storeSessionInfoToRedis(String sessionId, String dataRange) {
        // 如果没有配置Redis，则跳过
        if (redisTemplate == null) {
            return;
        }

        try {
            // 存储session与Pod的映射关系
            String podId = getPodId(); // 获取当前Pod标识
            redisTemplate.opsForValue().set(redisKey(RedisKeyManageEnum.WEBSOCKET_SESSION_PREFIX.getKey(), sessionId), podId,
                                          REDIS_EXPIRE_DAYS, TimeUnit.DAYS);

            // 维护dataRange到session的映射
            String key = redisKey(RedisKeyManageEnum.WEBSOCKET_DATA_RANGE_PREFIX.getKey(), dataRange);

            if (setOperations != null) {
                setOperations.add(key, sessionId);
                redisTemplate.expire(key, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("存储session信息到Redis失败", e);
        }
    }

    /**
     * 从Redis中删除session信息
     */
    private void removeSessionInfoFromRedis(String sessionId, String dataRange) {
        // 如果没有配置Redis，则跳过
        if (redisTemplate == null) {
            return;
        }

        try {
            redisTemplate.delete(redisKey(RedisKeyManageEnum.WEBSOCKET_SESSION_PREFIX.getKey(), sessionId));

            String key = redisKey(RedisKeyManageEnum.WEBSOCKET_DATA_RANGE_PREFIX.getKey(), dataRange);
            if (setOperations != null) {
                setOperations.remove(key, sessionId);
            }
        } catch (Exception e) {
            log.error("从Redis删除session信息失败", e);
        }
    }

    /**
     * 向特定用户发送消息
     *
     * @param dataRange 数据范围
     * @param message   消息
     */
    public void sendMessageToUser(String dataRange, Object message) {
        // 如果配置了Redis，则使用Redis方案，否则使用本地方案
        if (redisTemplate != null) {
            sendMessageToUserWithRedis(dataRange, message);
        } else {
            sendMessageToUserLocal(dataRange, message);
        }
    }

    /**
     * 使用Redis向特定用户发送消息（支持跨Pod）
     */
    private void sendMessageToUserWithRedis(String dataRange, Object message) {
        try {
            String key = redisKey(RedisKeyManageEnum.WEBSOCKET_DATA_RANGE_PREFIX.getKey(), dataRange);

            // 安全地获取session IDs
            Set<String> sessionIds = new HashSet<>();
            if (setOperations != null) {
                sessionIds = setOperations.members(key);
            }

            if (sessionIds == null || sessionIds.isEmpty()) {
                return;
            }

            for (String sessionId : sessionIds) {
                // 获取session所在的Pod
                String podId = null;
                try {
                    podId = redisTemplate.opsForValue().get(redisKey(RedisKeyManageEnum.WEBSOCKET_SESSION_PREFIX.getKey(), sessionId));
                } catch (Exception e) {
                    log.warn("获取session {} 所在Pod失败", sessionId, e);
                    continue;
                }

                if (podId != null && podId.equals(getPodId())) {
                    // session在当前Pod，直接发送
                    sendMessageToLocalSession(sessionId, message);
                } else {
                    // session在其他Pod，通过消息队列发送
                    sendMessageToRemotePod(podId, sessionId, message);
                }
            }
        } catch (Exception e) {
            log.error("sendMessageToUserWithRedis异常", e);
            // 出错时回退到本地发送
            sendMessageToUserLocal(dataRange, message);
        }
    }

    /**
     * 本地发送消息给特定用户
     */
    private void sendMessageToUserLocal(String dataRange, Object message) {
        if (!dataRange2SessionIdMap.containsKey(dataRange)) {
            return;
        }
        List<String> sessionIds = dataRange2SessionIdMap.getOrDefault(dataRange, new ArrayList<>());
        for (String sessionId : sessionIds) {
            WebSocketSession session = localSessionMap.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    String jsonMessage = objectMapper.writeValueAsString(message);
                    session.sendMessage(new TextMessage(jsonMessage));
                    String userId = getUserId(session);
                    log.info("成功单发，服务端给用户 {} 的消息: {}", userId, jsonMessage);
                } catch (IOException e) {
                    log.error("WorkReportWebSocketHandler#sendMessageToUser", e);
                }
            }
        }
    }

    /**
     * 向所有用户发送消息
     *
     * @param message 消息
     */
    public void sendMessageToAll(Object message) {
        // 如果配置了Redis，则使用Redis方案，否则使用本地方案
        if (redisTemplate != null) {
            sendMessageToAllWithRedis(message);
        } else {
            sendMessageToAllLocal(message);
        }
    }

    /**
     * 使用Redis向所有用户发送消息
     */
    private void sendMessageToAllWithRedis(Object message) {
        try {
            // 向本地用户发送消息
            sendMessageToAllLocal(message);

            // 通过Redis Pub/Sub向其他Pod广播消息
            if (redisTemplate != null) {
                try {
                    // 在消息中添加发送标识，避免重复处理
                    Map<String, Object> broadcastMessage = new HashMap<>();
                    broadcastMessage.put(MESSAGE, message);
                    broadcastMessage.put(FROM_POD, getPodId());

                    String jsonMessage = objectMapper.writeValueAsString(broadcastMessage);
                    redisTemplate.convertAndSend(RedisKeyManageEnum.WEBSOCKET_BROADCAST_CHANNEL.getKey(), jsonMessage);
                    log.info("已通过Redis广播消息到所有Pod");
                } catch (Exception e) {
                    log.error("通过Redis广播消息失败", e);
                }
            }
        } catch (Exception e) {
            log.error("sendMessageToAllWithRedis异常", e);
            sendMessageToAllLocal(message);
        }
    }

    /**
     * 本地向所有用户发送消息
     */
    private void sendMessageToAllLocal(Object message) {
        localSessionMap.forEach((sessionId, session) -> {
            if (session.isOpen()) {
                try {
                    String jsonMessage = objectMapper.writeValueAsString(message);
                    session.sendMessage(new TextMessage(jsonMessage));
                    String userId = getUserId(session);
                    log.info("成功群发，服务端给用户 {} 的消息: {}", userId, jsonMessage);
                } catch (IOException e) {
                    log.error("WorkReportWebSocketHandler#sendMessageToAll", e);
                }
            }
        });
    }

    /**
     * 向本地session发送消息
     */
    private void sendMessageToLocalSession(String sessionId, Object message) {
        WebSocketSession session = localSessionMap.get(sessionId);
        if (session != null && session.isOpen()) {
            try {
                String jsonMessage = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(jsonMessage));
                String userId = getUserId(session);
                log.info("成功单发，服务端给用户 {} 的消息: {}", userId, jsonMessage);
            } catch (IOException e) {
                log.error("sendMessageToLocalSession", e);
            }
        }
    }

    /**
     * 通过消息队列向其他Pod发送消息
     */
    private void sendMessageToRemotePod(String podId, String sessionId, Object message) {
        // 如果没有配置Redis，则跳过
        if (redisTemplate == null) {
            return;
        }

        // 实现通过消息队列（如Redis Pub/Sub、RabbitMQ等）向指定Pod发送消息
        try {
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put(SESSION_ID, sessionId);
            messageMap.put(MESSAGE, message);
            messageMap.put(FROM_POD, getPodId());

            // 使用Redis Pub/Sub发送消息到特定Pod
            redisTemplate.convertAndSend(redisKey(RedisKeyManageEnum.WEBSOCKET_POD_MESSAGE_CHANNEL_PREFIX.getKey(), podId), messageMap);
        } catch (Exception e) {
            log.error("向远程Pod发送消息失败", e);
        }
    }

    /**
     * 获取用户ID
     *
     * @param session 会话
     * @return java.lang.String
     */
    private String getUserId(WebSocketSession session) {
        return getDataRange(session) + Constants.DELIMITER + session.getId();
    }

    /**
     * 获取数据发送范围
     * @param session
     * @return
     */
    protected abstract String getDataRange(WebSocketSession session);

    /**
     * 获取当前Pod标识
     */
    private String getPodId() {
        return this.podId;
    }

    private String generatePodId() {
        // 可以从环境变量、配置文件等获取Pod标识
        String podId = System.getenv("HOSTNAME");
        if (podId == null) {
            podId =  "pod-" + UUIDUtil.getUUID();
        }
        return podId;
    }

    private String redisKey(String keyPrefix, String data) {
        return String.format(keyPrefix, data);
    }
}
