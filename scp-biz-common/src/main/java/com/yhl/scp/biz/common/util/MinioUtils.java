package com.yhl.scp.biz.common.util;

import cn.hutool.core.io.FastByteArrayOutputStream;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import io.minio.*;
import io.minio.errors.MinioException;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <code>MinioUtils</code>
 * <p>
 * MinioUtils
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 16:07:13
 */
@Slf4j
@Component
public class MinioUtils {

    private final MinioClient minioClient;

    public MinioUtils(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    /**
     * 检查指定的存储桶是否存在的方法
     *
     * @param bucketName 存储桶名称，不能为空
     * @return 如果存储桶存在返回true，否则返回false
     */
    public Boolean bucketExists(String bucketName) {
        if (StringUtils.isBlank(bucketName)) {
            throw new BusinessException("上传文件桶名不能为空");
        }
        boolean found;
        try {
            found = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .build());
        } catch (Exception e) {
            log.error("MinIO OSS 查看bucket是否存在失败", e);
            return false;
        }
        return found;
    }

    /**
     * 创建一个新的存储桶
     * <p>
     * 此方法尝试创建一个由bucketName参数指定的新存储桶
     * 创建存储桶的操作对于存储桶的命名有一定的要求，名称需要全局唯一
     * 如果存储桶已经存在或者名称不符合规范，那么创建操作将会失败
     * 由于涉及网络操作，因此将其标记为可能抛出异常的操作
     *
     * @param bucketName 存储桶名称，不能为空
     */
    public void makeBucket(String bucketName) {
        if (StringUtils.isBlank(bucketName)) {
            throw new BusinessException("上传文件桶名不能为空");
        }
        try {
            minioClient.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .build());
        } catch (Exception e) {
            log.error("MinIO OSS 创建bucket失败", e);
        }
    }

    /**
     * 删除指定的存储桶
     *
     * @param bucketName 存储桶名称，不能为空
     * @return 返回一个Boolean值，表示删除操作的结果如果删除成功，返回true；否则，返回false
     */
    @SuppressWarnings("unused")
    public Boolean removeBucket(String bucketName) {
        if (StringUtils.isBlank(bucketName)) {
            throw new BusinessException("上传文件桶名不能为空");
        }
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .build());
        } catch (Exception e) {
            log.error("MinIO OSS 删除bucket失败", e);
            return false;
        }
        return true;
    }

    /**
     * 获取所有存储桶的列表
     * <p>
     * 此方法用于查询并返回用户账户下所有存储桶的信息它是了解一个账户所有存储空间的基础
     * 通过这个方法，可以快速获取到账户下所有存储桶的名称和其他元数据信息
     *
     * @return 包含所有存储桶信息的列表如果列表为空，则表示该账户下没有任何存储桶
     */
    @SuppressWarnings("unused")
    public List<Bucket> getAllBuckets() {
        try {
            return minioClient.listBuckets();
        } catch (Exception e) {
            log.error("MinIO OSS 获取全部bucket失败", e);
        }
        return Collections.emptyList();
    }

    /**
     * 上传文件到指定的存储桶
     * <p>
     * 此方法负责将传入的文件上传到对象存储服务中的指定存储桶
     * 它首先获取文件的原始名称，这是文件上传过程中的一个重要信息
     *
     * @param bucketName 存储桶名称，不能为空
     * @param file       待上传的文件，包含文件数据和元数据
     * @return 返回上传文件的原始名称，以便于后续可能需要的文件操作或记录
     */
    @Deprecated
    public String upload(String bucketName, MultipartFile file) {
        if (Objects.isNull(file)) {
            throw new BusinessException("上传文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new BusinessException("上传文件名不能为空");
        }
        // 判断桶是否存在，不存在则创建
        if (Boolean.FALSE.equals(bucketExists(bucketName.toLowerCase().replace("&", "")))) {
            makeBucket(bucketName.toLowerCase().replace("&", ""));
        }
        try {
            PutObjectArgs objectArgs = PutObjectArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .object(fileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            // 文件名称相同会覆盖
            minioClient.putObject(objectArgs);
        } catch (Exception e) {
            log.error("MinIO OSS 文件上传失败", e);
            return null;
        }
        return fileName;
    }

    /**
     * 上传文件到指定的存储桶
     * <p>
     * 此方法负责将传入的文件上传到对象存储服务中的指定存储桶
     * 它首先获取文件的原始名称，这是文件上传过程中的一个重要信息
     *
     * @param file         待上传的文件，包含文件数据和元数据
     * @param bucketName   存储桶名称，不能为空
     * @param scenario     场景名称，不能为空
     * @param businessCode 业务代码名称，不能为空
     * @param displayName  用户显示名
     * @param fileRename   是否重命名
     * @return 返回上传文件的原始名称，以便于后续可能需要的文件操作或记录
     */
    public String upload(MultipartFile file, String bucketName, String scenario, String businessCode,
                         String displayName, boolean fileRename) {
        parameterValidator(bucketName, scenario, businessCode);
        if (Objects.isNull(file)) {
            throw new BusinessException("上传文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new BusinessException("上传文件名不能为空");
        }
        if (fileRename) {
            String date = DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4);
            fileName = String.join("_", displayName, date, file.getOriginalFilename());
        }
        String filePath =  String.join("/", scenario, businessCode, fileName);
        // 判断桶是否存在，不存在则创建
        if (Boolean.FALSE.equals(bucketExists(bucketName.toLowerCase().replace("&", "")))) {
            makeBucket(bucketName.toLowerCase().replace("&", ""));
        }
        try (InputStream inputStream = file.getInputStream()) {
            PutObjectArgs objectArgs = PutObjectArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .object(filePath)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            // 文件名称相同会覆盖
            minioClient.putObject(objectArgs);
        } catch (IOException e) {
            log.error("MinIO OSS 文件上传失败: 无法读取文件流", e);
            return null;
        } catch (MinioException e) {
            log.error("MinIO OSS 文件上传失败: MinIO服务异常", e);
            return null;
        } catch (Exception e) {
            log.error("MinIO OSS 文件上传失败: 未知异常", e);
            return null;
        }
        return fileName;
    }

    /**
     * 通用参数校验
     *
     * @param bucketName   存储桶名称
     * @param scenario     场景名称
     * @param businessCode 业务代码名称
     */
    private static void parameterValidator(String bucketName, String scenario, String businessCode) {
        if (StringUtils.isBlank(bucketName)) {
            throw new BusinessException("上传文件桶名不能为空");
        }
        if (StringUtils.isBlank(scenario)) {
            throw new BusinessException("上传文件场景不能为空");
        }
        if (StringUtils.isBlank(businessCode)) {
            throw new BusinessException("上传文件业务编码不能为空");
        }
    }

    /**
     * 预览文件内容
     *
     * @param bucketName   存储桶名称，不能为空
     * @param scenario     场景名称，不能为空
     * @param businessCode 业务代码名称，不能为空
     * @param fileName     文件名称，唯一标识要预览的文件
     * @return 返回文件的内容字符串
     */
    @SuppressWarnings("unused")
    public String preview(String bucketName, String scenario, String businessCode, String fileName) throws MinioException, IOException,
            NoSuchAlgorithmException, InvalidKeyException {
        parameterValidator(bucketName, scenario, businessCode);
        String fullPath = String.join("/", scenario, businessCode, fileName);
        // 查看文件地址
        GetPresignedObjectUrlArgs build = GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName.toLowerCase().replace("&", ""))
                .object(fullPath)
                .method(Method.GET)
                .build();
        try {
            return minioClient.getPresignedObjectUrl(build);
        } catch (MinioException e) {
            log.error("MinIO OSS 获取文件预览URL失败", e);
            throw e;
        }
    }

    /**
     * 下载指定桶中的文件
     *
     * @param bucketName   存储桶名称，不能为空
     * @param scenario     场景名称，不能为空
     * @param businessCode 业务代码名称，不能为空
     * @param fileName     文件名称，不能为空
     * @param res          用于返回下载的文件流给客户端
     */
    public void download(String bucketName, String scenario, String businessCode, String fileName,
                         HttpServletResponse res) {
        parameterValidator(bucketName, scenario, businessCode);
        String filePath = String.join("/", scenario, businessCode, fileName);
        GetObjectArgs objectArgs = GetObjectArgs.builder()
                .bucket(bucketName.toLowerCase().replace("&", ""))
                .object(filePath)
                .build();
        try (GetObjectResponse response = minioClient.getObject(objectArgs)) {
            byte[] buffer = new byte[1024];
            int length;
            try (FastByteArrayOutputStream os = new FastByteArrayOutputStream()) {
                while ((length = response.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
                os.flush();
                byte[] bytes = os.toByteArray();
                res.setCharacterEncoding("utf-8");
                res.setContentType("application/octet-stream");
                // 设置强制下载不打开
                String fileNameCode = URLEncoder.encode(fileName.substring(fileName.lastIndexOf('/') + 1),
                        "utf-8").replace("\\+", "%20");
                res.addHeader("Content-Disposition", "attachment;filename=" + fileNameCode);
                try (ServletOutputStream stream = res.getOutputStream()) {
                    stream.write(bytes);
                    stream.flush();
                }
            }
        } catch (Exception e) {
            log.error("MinIO OSS 文件下载失败", e);
        }
    }

    /**
     * 列出指定存储桶中的所有对象
     *
     * @param bucketName 存储桶名称，不能为空
     * @return 返回一个包含存储桶中所有对象的列表
     */
    public List<Item> listObjects(String bucketName) {
        if (StringUtils.isBlank(bucketName)) {
            throw new BusinessException("上传文件桶名不能为空");
        }
        Iterable<Result<Item>> results = minioClient.listObjects(ListObjectsArgs.builder()
                .bucket(bucketName.toLowerCase().replace("&", ""))
                .build());
        List<Item> items = new ArrayList<>();
        try {
            for (Result<Item> result : results) {
                items.add(result.get());
            }
        } catch (Exception e) {
            log.error("MinIO OSS 查看文件列表失败", e);
            return Collections.emptyList();
        }
        return items;
    }

    /**
     * 删除指定存储桶中的文件
     * <p>
     * 注意：此方法仅删除指定存储桶中的一个文件如果需要删除整个存储桶或存储桶中的多个文件，请使用其他合适的方法或循环调用此方法
     *
     * @param bucketName   存储桶名称，不能为空
     * @param scenario     场景名称，不能为空
     * @param businessCode 业务代码名称，不能为空
     * @param fileName     要删除的文件的名称，不可以为null或空字符串
     * @return 返回true表示删除成功；返回false表示删除失败，可能因为文件不存在或网络错误等原因
     */
    public boolean remove(String bucketName, String scenario, String businessCode, String fileName) {
        parameterValidator(bucketName, scenario, businessCode);
        String filePath = String.join("/", scenario, businessCode, fileName);
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName.toLowerCase().replace("&", ""))
                    .object(filePath)
                    .build());
        } catch (Exception e) {
            log.error("MinIO OSS 移除文件失败", e);
            return false;
        }
        return true;
    }

}