package com.yhl.scp.biz.common.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>MinIOBusinessEnum</code>
 * <p>
 * 定义MinIO上传的业务类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23 11:13:36
 */
public enum MinIOBusinessEnum implements CommonEnum {

    DEMAND_FORECAST_ATTACHMENT("demandForecast_attachment", "装车需求提报附件"),

    BPM_ATTACHMENT("bpm_attachment", "BPM流程附件"),

    ;

    private String code;
    private String desc;

    MinIOBusinessEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }
}
