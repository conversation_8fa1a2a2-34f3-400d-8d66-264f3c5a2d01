package com.yhl.scp.dfp.stock.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.vo.RealTimeInventoryVO;
import com.yhl.scp.dfp.stock.dto.InventoryDataDTO;
import com.yhl.scp.dfp.stock.dto.InventoryRealTimeDataDTO;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryRealTimeDataService</code>
 * <p>
 * 库存实时数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:40:04
 */
public interface InventoryRealTimeDataService extends BaseService<InventoryRealTimeDataDTO, InventoryRealTimeDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryRealTimeDataVO}
     */
    List<InventoryRealTimeDataVO> selectAll();

    void doMergeStockBatchData();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据产品编码查询半品库存与成品库存
     * @param productCodes
     * @return
     */
    List<InventoryDataDTO> selectInventoryByProductCodes(List<String> productCodes);
}
