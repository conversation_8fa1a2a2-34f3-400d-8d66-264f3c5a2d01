package com.yhl.scp.dfp.calendar.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.scp.dfp.calendar.dto.CalendarRuleDTO;
import com.yhl.scp.dfp.calendar.vo.CalendarRuleVO;

import java.util.Date;
import java.util.List;

/**
 * <code>CalendarRuleService</code>
 * <p>
 * 日历规则应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:36
 */
public interface DfpCalendarRuleService extends BaseService<CalendarRuleDTO, CalendarRuleVO> {

    void doUpdateBatchSelective(List<CalendarRuleDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link CalendarRuleVO}
     */
    List<CalendarRuleVO> selectAll();

    /**
     * 搜索可用的日历规则
     *
     * @param organizationId 工厂id
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @return list {@link CalendarRuleVO}
     */
    List<CalendarRuleVO> searchAvailableRules(String organizationId,
                                              List<String> standardResourceIds,
                                              List<String> physicalResourceIds,
                                              Date startDate,
                                              Date endDate);

    /**
     * 冗余日历规则信息
     *
     * @param dataList             VO数据
     * @param invocation           invocation
     * @param fieldName            指定冗余字段名
     * @param paramName            参数
     * @param relationKey          关联外键
     * @param relationObjectColumn 关联主键
     */
    void addCalendarRuleColumn(List<? extends BaseVO> dataList, String invocation, String fieldName, String paramName, String relationKey, String relationObjectColumn);

}
