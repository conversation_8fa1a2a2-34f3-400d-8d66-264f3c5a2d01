package com.yhl.scp.dfp.clean.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDTO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;

import java.util.List;

/**
 * <code>CleanDemandDataService</code>
 * <p>
 * 日需求数据应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:16:21
 */
public interface CleanDemandDataService extends BaseService<CleanDemandDataDTO, CleanDemandDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link CleanDemandDataVO}
     */
    List<CleanDemandDataVO> selectAll();

    /**
     * 重新计算
     *
     * @param versionCode 版本代码
     * @return list {@link String} 校验结果
     */
    List<String> doRecalculate(String versionCode);

    /**
     * 自动计算
     */
    void autoCalculate();

    /**
     * 批量删除
     *
     * @param versionDTOList 版本列表
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    void doDeleteByVersionId(String versionId);

    List<CleanDemandDataVO> selectByPrimaryKeys(List<String> ids);

    List<LabelValue<String>> selectOemDropdown(String versionId);
}