package com.yhl.scp.dfp.warehouse.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.warehouse.dto.AbroadWarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.dto.EnRouteDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>WarehouseReleaseRecordService</code>
 * <p>
 * 仓库发货记录应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-31 10:17:12
 */
public interface WarehouseReleaseRecordService extends BaseService<WarehouseReleaseRecordDTO, WarehouseReleaseRecordVO> {

    /**
     * 查询所有
     *
     * @return list {@link WarehouseReleaseRecordVO}
     */
    List<WarehouseReleaseRecordVO> selectAll();

    /**
     * 手工同步mes发货记录
     */
    void syncMesData();

    /**
     * 批量删除
     *
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 获取指定范围内指定目标类型的仓库发货集合
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param type      类型
     * @return
     */
    List<WarehouseReleaseRecordVO> actualDelivery(Date beginTime, Date endTime, String type);

    BaseResponse<Void> syncWareHouseReleaseData(List<WarehouseReleaseRecordDTO> list, String code, String scenario);

    BaseResponse<Void> syncData(String beginTime, String endTime, String tenantId);

    List<WarehouseReleaseRecordVO> selectGroup();

    List<WarehouseReleaseRecordVO> getInRoad(List<String> demandProductCodeList,List<String> shipmentLocatorCodes);

    List<LabelValue<String>> selectTargetStockLocation();

    List<WarehouseReleaseRecordMonthVO> selectMonthVOByParams(Map<String, Object> params);

    List<WarehouseReleaseRecordMonthVO> selectMonthVOByParamsGroupOem(Map<String, Object> params);

    BaseResponse<Void> syncWareHouseReleaseFYSLData(List<WarehouseReleaseRecordDTO> arrayList);

    BaseResponse<Void> syncFYSLData(String tenantId, String id);

    BaseResponse<Void> syncEnRouteData(String tenantCode);

    BaseResponse<Void> syncFYDSData(List<EnRouteDTO> list);

	List<WarehouseReleaseRecordVO> selectGroupByItemCodes(List<String> itemCodes);

    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordSumQtyByDate(List<String> productCodes, String startDate, String endDate);

    BaseResponse<Void> syncFYEWarehouseData(String scenario, String plateCode, String tenantCode, String beginTime, String endTime);

    BaseResponse<Void> syncFYEWareHouseReleaseData(List<AbroadWarehouseReleaseRecordDTO> list);

    BaseResponse<Void> syncFYEData(String beginTime, String endTime);
}