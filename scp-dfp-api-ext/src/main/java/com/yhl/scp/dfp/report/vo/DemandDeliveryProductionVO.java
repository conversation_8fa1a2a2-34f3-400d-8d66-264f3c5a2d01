package com.yhl.scp.dfp.report.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>StockAlertVO</code>
 * <p>
 * DemandDeliveryProductionVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:40:27
 */
@Data
public class DemandDeliveryProductionVO implements Serializable {

    private static final long serialVersionUID = -8519374303480206685L;

    @ApiModelProperty("内部车型")
    private String vehicleModelCode;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("需求类型")
    private String demandCategory;

    @ApiModelProperty("主机厂编码")
    private String oemCode;


    @ApiModelProperty("中转库存")
    private BigDecimal bohStock;

    @ApiModelProperty("在途数量")
    private BigDecimal transportingQty;

    @ApiModelProperty("仓库成品")
    private BigDecimal fgStock;


    @ApiModelProperty("包装后")
    private BigDecimal afterPacking;

    @ApiModelProperty("包装")
    private BigDecimal packing;
    @ApiModelProperty("合片后")
    private BigDecimal afterLamination;
    @ApiModelProperty("合片")
    private BigDecimal lamination;
    @ApiModelProperty("成形后")
    private BigDecimal afterShape;
    @ApiModelProperty("成形")
    private BigDecimal shape;
    @ApiModelProperty("已排产量")
    private BigDecimal scheduleQty;

    @ApiModelProperty("分项")
    private String category;
    /**
     * 动态表头
     */
    private List<String> header;
    /**
     * 动态内容，用于覆盖发货计划
     */
    private List<Map<String, Object>> dynamicData;
    private List<Map<String, Object>> loadingDynamicData;
    /**
     * 详情数据
     */
    @ApiModelProperty(value = "详情数据")
    @FieldInterpretation(value = "详情数据")
    private List<DemandDeliveryProductionDetailVO> details;

}
