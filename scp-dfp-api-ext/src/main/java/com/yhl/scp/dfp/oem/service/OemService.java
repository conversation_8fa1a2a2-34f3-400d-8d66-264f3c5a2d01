package com.yhl.scp.dfp.oem.service;


import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.dto.OemDTO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.release.dto.ReleaseOemDTO;
import com.yhl.scp.dfp.release.vo.ReleaseOemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>OemService</code>
 * <p>
 * 主机厂档案应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:10:38
 */
public interface OemService extends BaseService<OemDTO, OemVO> {

    /**
     * 查询所有
     *
     * @return list {@link OemVO}
     */
    List<OemVO> selectAll();

    List<OemVO> getOemCodeByUserPermission();

    /**
     * 查询主机厂信息
     *
     * @return
     */
    List<LabelValue<String>> queryOemInfo();

    /**
     * 根据版本删除
     *
     * @param removeVersionDTOS
     * @return
     */
    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    List<ReleaseOemVO> selectPageByVersionId(ReleaseOemDTO releaseOemDTO);

    /**
     * 查询客户信息
     *
     * @return
     */
    List<LabelValue<String>> queryCustomerInfo();

    /**
     * 同步客户信息
     *
     * @return
     */
    BaseResponse<Void> syncCustomers(String organizeId,String tenantId);

    /**
     * 根据客户编码查询主机厂编码集合
     * @param customerCode
     * @return
     */
    List<LabelValue<String>> queryOemCodeByCustomerCode(String customerCode);


    /**
     * 组合查询-权限
     * @param params
     * @return
     */
    List<OemVO> selectVOByConditionParams(Map<String, Object> params);

    List<LabelValue<String>> queryNameAndCode();

    String getAddressByOemCode(String oemCode);

    List<LabelValue<String>> getCustomerByNoEdi();

    List<String> getAddressTwo(String locationArea2);

    List<OemVO> getAddressOneThreeFourByCustomer(String customerCode);

    String getOemCodeByCustomerCode(String customerCode);

    List<String> getPayment(String customerCode);

	String getTransitClauseByCodes(String oemCode, String productCode);
	List<OemVO> selectProductEdiFlag(String ediFlag, String productEdiFlag);
    List<OemVO> selectVOByParams(Map<String, Object> params);

	OemVO selectByOemCode(String oemCode);
}
