package com.yhl.scp.dfp.warehouse.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReceiveCheckDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;

/**
 * <code>WarehouseReleaseToWarehouseService</code>
 * <p>
 * 仓库发货至中转库应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 09:57:22
 */
public interface WarehouseReleaseToWarehouseService extends BaseService<WarehouseReleaseToWarehouseDTO, WarehouseReleaseToWarehouseVO> {

    /**
     * 查询所有
     *
     * @return list {@link WarehouseReleaseToWarehouseVO}
     */
    List<WarehouseReleaseToWarehouseVO> selectAll();

    List<WarehouseReleaseToWarehouseVO> getInRoad(List<String> demandProductCodeList,
                                                         List<String> shipmentLocatorCodes);

    List<String> selectTargetStockLocation();

	List<WarehouseReleaseToWarehouseMonthVO> selectMonthVOByParams(Map<String, Object> params);
	
	List<WarehouseReleaseToWarehouseDayVO> selectDayVOByParams(Map<String, Object> params);

	/**
	 * 查询未接收的中转库发货数据
	 * @param oemCodes
	 * @param productCodes
	 * @return
	 */
	List<WarehouseReleaseToWarehouseVO> selectNotReceiveInland(List<String> oemCodes,
			List<String> productCodes);

	PageInfo<WarehouseReceiveCheckVO> queryReceiveCheckPage(WarehouseReceiveCheckDTO warehouseReceiveCheckDTO);

	List<WarehouseReceiveCheckVO> selectNotReceiveInlandStatistics(Map<String, Object> queryMap);

}
