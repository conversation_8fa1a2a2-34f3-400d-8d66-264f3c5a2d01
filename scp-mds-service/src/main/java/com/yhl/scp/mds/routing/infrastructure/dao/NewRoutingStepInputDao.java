package com.yhl.scp.mds.routing.infrastructure.dao;

import java.util.List;
import java.util.Map;

import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import org.apache.ibatis.annotations.Param;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepInputPO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;

/**
 * <code>NewRoutingStepInputDao</code>
 * <p>
 * 新-生产路径步骤输入物品DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:57:56
 */
public interface NewRoutingStepInputDao extends BaseDao<NewRoutingStepInputPO, NewRoutingStepInputVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link NewRoutingStepInputVO}
     */
    List<NewRoutingStepInputVO> selectVOByParams(@Param("params") Map<String, Object> params);

	void doLogicDeleteBatchByRoutingIds(@Param("routingIds") List<String> routingIds);

	void doLogicDeleteBatchByRoutingStepIds(@Param("routingStepIds") List<String> routingStepIds);

	void doLogicDeleteBatchByIds(@Param("ids") List<String> ids);

	List<NewRoutingStepInputPO> selectMbplByStepIds(@Param("routingStepIds") List<String> routingStepIds);

	void deleteByCreator(@Param("creator") String creator);

	int updateEnableForExpiryTime();

	List<String> selectInputProductIdsByRoutingId(@Param("routingId") String routingId);
	List<NewRoutingStepInputVO> selectFormingProcess(@Param("inputProductIds") List<String> inputProductIds);
	List<StandardStepVO> selectDirectFormingProcess(@Param("inputProductId") String inputProductId);
}
