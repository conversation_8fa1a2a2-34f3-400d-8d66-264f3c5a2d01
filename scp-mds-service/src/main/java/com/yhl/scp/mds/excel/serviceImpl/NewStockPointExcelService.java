package com.yhl.scp.mds.excel.serviceImpl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.write.handler.WriteHandler;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.model.handler.DropDownHandler;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.excel.service.ProcessEnumsService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.enums.UnitTypeEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;

import com.yhl.scp.mds.overdeadlineday.dto.MdsOverDeadlineDaysDTO;
import com.yhl.scp.mds.stock.convertor.NewStockPointConvertor;
import com.yhl.scp.mds.stock.dto.NewStockPointDTO;
import com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao;
import com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>NewStockPointExcelService</code>
 * <p>
 * 组织管理导入
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-08 09:03:54
 */
@Service
public class NewStockPointExcelService extends AbstractExcelService<NewStockPointDTO, NewStockPointPO, NewStockPointVO> {
    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewStockPointDao newStockPointDao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    protected void fillIdForUpdateData(List<NewStockPointDTO> updateList, Map<String, NewStockPointPO> existingDataMap) {
        for (NewStockPointDTO newStockPointDTO : updateList) {
            NewStockPointPO newStockPointPO = existingDataMap.get(newStockPointDTO.getStockPointCode());
            if (Objects.nonNull(newStockPointPO)) {
                newStockPointDTO.setId(newStockPointPO.getId());
                newStockPointDTO.setVersionValue(1);
                newStockPointDTO.setEnabled(YesOrNoEnum.YES.getCode());
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<NewStockPointPO> prepareData(List<NewStockPointDTO> productStockPointDTOS) {
        // 1.配送提前期、制造变动提前期、制造固定提前期、采购变动提前期、采购固定提前期、库存有效期、订货间隔期
        // 找到数据库现在所有的数据
        List<NewStockPointPO> alreadyExitData = newStockPointDao.selectByParams(new HashMap<>(2));
        Map<String, NewStockPointPO> existingDataMap = alreadyExitData.stream().collect(Collectors.toMap(p -> p.getStockPointCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("stockPointCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        List<NewStockPointVO> stockPointVOS = newStockPointService.selectByParams(new HashMap<>(2));
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        // List<SimpleVO> stockVOS = stockPointVOS.stream().map(NewStockPointVO::toSimpleVO).collect(Collectors.toList());
        // foreignDataMap.put("stockPointCode", stockVOS);
        return ImportRelatedDataHolder.<NewStockPointPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(existingDataMap)
                .build();

    }

    @Override
    public void exportTemplate(HttpServletResponse response, String excelStrategy) {
        super.exportTemplate(response, excelStrategy);
    }

    @Override
    public BaseDao<NewStockPointPO, NewStockPointVO> getBaseDao() {
        return newStockPointDao;
    }

    @Override
    public Function<NewStockPointDTO, NewStockPointPO> getDTO2POConvertor() {
        return NewStockPointConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<NewStockPointDTO> getDTOClass() {
        return NewStockPointDTO.class;
    }

    @Override
    public BaseService<NewStockPointDTO, NewStockPointVO> getBaseService() {
        return newStockPointService;
    }


    @Override
    protected List<NewStockPointDTO> getCustomizedExampleData() {
        NewStockPointDTO build = NewStockPointDTO.builder()
                // .stockPointId("stock").productId("product").productType("成品")
                // .daysRetrieved("是")
                // .quantityCalculated("是")
                // .participationCalculated("是")
                // .salesOrderShortage("是")
                // .demandForecastShortage("是")
                // .consumptionShortage("是")
                // .outboundShortage("是")
                // .lowerThanMin("是")
                // .lowerThanTarget("是")
                // .lowerThanMax("是")
                // .keyMaterial("是")
                // .longPeriodMaterial("是")
                // .autoFixPegging("是")
                // .acrossStockPointAllowed("是")
                // .computationClass("是")
                .build();
        return Collections.singletonList(build);
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<NewStockPointDTO, NewStockPointPO> resultHolder, ImportContext importContext) {
        List<NewStockPointDTO> insertList = resultHolder.getInsertList();
        List<NewStockPointDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }
    private void verifyPaternity(List<NewStockPointDTO> checkList, List<DataImportInfo> importLogList) {
        // HashMap<String, Object> params = new HashMap<>();
        // params.put("enabled", YesOrNoEnum.YES.getCode());
        // List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(params);
        // Map<String, List<NewStockPointVO>> stockPointMap = newStockPointVOS.stream().collect(Collectors.groupingBy(NewStockPointVO::getStockPointCode));
        Iterator<NewStockPointDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            NewStockPointDTO newStockPointDTO = iterator.next();
            if (isFieldEmpty(newStockPointDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[库存点代码]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(newStockPointDTO.getStockPointName())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[库存点名称]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(newStockPointDTO.getStockPointType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[库存点类型]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(newStockPointDTO.getOrganizeId())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[组织Id]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(newStockPointDTO.getPlanArea())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[计划区域]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(newStockPointDTO.getInterfaceSource())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + newStockPointDTO.getRowIndex() + ";[数据来源]未填写");
                dataImportInfo.setDisplayIndex(newStockPointDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }

        }}

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    private String getMdsScenario() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        return defaultScenario.getData();
    }

    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        try {
            Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }
}
