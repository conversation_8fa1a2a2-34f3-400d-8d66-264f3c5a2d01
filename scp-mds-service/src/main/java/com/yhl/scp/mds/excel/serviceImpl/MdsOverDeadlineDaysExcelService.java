package com.yhl.scp.mds.excel.serviceImpl;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.overdeadlineday.convertor.MdsOverDeadlineDaysConvertor;
import com.yhl.scp.mds.overdeadlineday.dto.MdsOverDeadlineDaysDTO;
import com.yhl.scp.mds.overdeadlineday.infrastructure.dao.MdsOverDeadlineDaysDao;
import com.yhl.scp.mds.overdeadlineday.infrastructure.po.MdsOverDeadlineDaysPO;
import com.yhl.scp.mds.overdeadlineday.service.MdsOverDeadlineDaysService;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MdsOverDeadlineDaysExcelService</code>
 * <p>
 * 超期界定天数导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-07 09:03:54
 */
@Service
public class MdsOverDeadlineDaysExcelService extends AbstractExcelService<MdsOverDeadlineDaysDTO, MdsOverDeadlineDaysPO, MdsOverDeadlineDaysVO> {

    @Resource
    private MdsOverDeadlineDaysDao mdsOverDeadlineDaysDao;

    @Resource
    private MdsOverDeadlineDaysService mdsOverDeadlineDaysService;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseDao<MdsOverDeadlineDaysPO, MdsOverDeadlineDaysVO> getBaseDao() {
        return mdsOverDeadlineDaysDao;
    }

    @Override
    public Function<MdsOverDeadlineDaysDTO, MdsOverDeadlineDaysPO> getDTO2POConvertor() {
        return MdsOverDeadlineDaysConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<MdsOverDeadlineDaysDTO> getDTOClass() {
        return MdsOverDeadlineDaysDTO.class;
    }

    @Override
    public BaseService<MdsOverDeadlineDaysDTO, MdsOverDeadlineDaysVO> getBaseService() {
        return mdsOverDeadlineDaysService;
    }

    @Override
    protected void fillIdForUpdateData(List<MdsOverDeadlineDaysDTO> updateList, Map<String, MdsOverDeadlineDaysPO> existingDataMap) {

        for (MdsOverDeadlineDaysDTO mdsOverDeadlineDaysDTO : updateList) {
            MdsOverDeadlineDaysPO mdsOverDeadlineDaysPO = existingDataMap.get(mdsOverDeadlineDaysDTO.getStockPointCode() + "&"
                    + mdsOverDeadlineDaysDTO.getMaterialsType() + "&" + mdsOverDeadlineDaysDTO.getMaterialsMainClassification() + "&"
                    + mdsOverDeadlineDaysDTO.getMaterialsSecondClassification() + "&" + mdsOverDeadlineDaysDTO.getProductType() + "&"
                    + mdsOverDeadlineDaysDTO.getColorCode());
            if (Objects.isNull(mdsOverDeadlineDaysPO)) {
                continue;
            }
            mdsOverDeadlineDaysDTO.setId(mdsOverDeadlineDaysPO.getId());
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<MdsOverDeadlineDaysDTO, MdsOverDeadlineDaysPO> resultHolder, ImportContext importContext) {
        List<MdsOverDeadlineDaysDTO> insertList = resultHolder.getInsertList();
        List<MdsOverDeadlineDaysDTO> updateList = resultHolder.getUpdateList();
        verifyPaternity(insertList, resultHolder.getImportLogList());
        verifyPaternity(updateList, resultHolder.getImportLogList());
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<MdsOverDeadlineDaysDTO> checkList, List<DataImportInfo> importLogList) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(params);
        Map<String, List<NewStockPointVO>> stockPointMap = newStockPointVOS.stream().collect(Collectors.groupingBy(NewStockPointVO::getStockPointCode));
        Iterator<MdsOverDeadlineDaysDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            MdsOverDeadlineDaysDTO mdsOverDeadlineDaysDTO = iterator.next();
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[组织代码]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!stockPointMap.containsKey(mdsOverDeadlineDaysDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[组织代码]不存在");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getMaterialsType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[物料类型]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getMaterialsMainClassification())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[物料分类大类]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getMaterialsSecondClassification())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[物料分类小类]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getProductType())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[产品类型]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(mdsOverDeadlineDaysDTO.getColorCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[颜色代码]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (isFieldEmpty(String.valueOf(mdsOverDeadlineDaysDTO.getOverDeadlineDay()))) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[超期界定天数]未填写");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            } else if (!isNumeric(mdsOverDeadlineDaysDTO.getOverDeadlineDay().toString())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + mdsOverDeadlineDaysDTO.getRowIndex() + ";[超期界定天数]必须是数值类型");
                dataImportInfo.setDisplayIndex(mdsOverDeadlineDaysDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }

        }
    }

    @Override
    protected ImportRelatedDataHolder<MdsOverDeadlineDaysPO> prepareData(List<MdsOverDeadlineDaysDTO> mdsOverDeadlineDaysDTOS) {
        // 现有数据
        List<MdsOverDeadlineDaysPO> alreadyExitData = mdsOverDeadlineDaysDao.selectByParams(new HashMap<>(2));
        Map<String, MdsOverDeadlineDaysPO> codeToPOMap = alreadyExitData.stream().collect(Collectors.toMap(x -> x.getStockPointCode() + "&" + x.getMaterialsType() + "&" + x.getMaterialsMainClassification()
                + "&" + x.getMaterialsSecondClassification() + "&" + x.getProductType() + "&" + x.getColorCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("stockPointCode", "materialsType", "materialsMainClassification", "materialsSecondClassification", "productType", "colorCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<MdsOverDeadlineDaysPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

    private boolean isFieldEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    private String getMdsScenario() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        return defaultScenario.getData();
    }

    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        try {
            Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }


}
