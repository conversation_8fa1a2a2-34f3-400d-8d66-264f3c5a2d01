package com.yhl.scp.mds.stock.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.scp.ips.api.vo.ExtApiConfigVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.mds.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.stock.convertor.NewStockPointConvertor;
import com.yhl.scp.mds.stock.domain.entity.NewStockPointDO;
import com.yhl.scp.mds.stock.domain.service.NewStockPointDomainService;
import com.yhl.scp.mds.stock.dto.NewStockPointDTO;
import com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao;
import com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NewStockPointServiceImpl extends AbstractService implements NewStockPointService {

    @Resource
    private NewStockPointDao newStockPointDao;

    @Resource
    private NewStockPointDomainService newStockPointDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(NewStockPointDTO newStockPointDTO) {
        // 0.数据转换
        NewStockPointDO newStockPointDO = NewStockPointConvertor.INSTANCE.dto2Do(newStockPointDTO);
        NewStockPointPO newStockPointPO = NewStockPointConvertor.INSTANCE.dto2Po(newStockPointDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        newStockPointDomainService.validation(newStockPointDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(newStockPointPO);
        newStockPointDao.insert(newStockPointPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(NewStockPointDTO newStockPointDTO) {
        // 0.数据转换
        NewStockPointDO newStockPointDO = NewStockPointConvertor.INSTANCE.dto2Do(newStockPointDTO);
        NewStockPointPO newStockPointPO = NewStockPointConvertor.INSTANCE.dto2Po(newStockPointDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        newStockPointDomainService.validation(newStockPointDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(newStockPointPO);
        newStockPointDao.update(newStockPointPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewStockPointDTO> list) {
        List<NewStockPointPO> newList = NewStockPointConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        for (NewStockPointPO newStockPointPO : newList) {
            if (StringUtils.isNotBlank(newStockPointPO.getId())) {
                continue;
            }
            newStockPointPO.setId(UUIDUtil.getUUID());
        }
        newStockPointDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<NewStockPointDTO> list) {
        List<NewStockPointPO> newList = NewStockPointConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        newStockPointDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newStockPointDao.deleteBatch(idList);
        }
        return newStockPointDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewStockPointVO selectByPrimaryKey(String id) {
        NewStockPointPO po = newStockPointDao.selectByPrimaryKey(id);
        return NewStockPointConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "STOCK_POINT")
    public List<NewStockPointVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "STOCK_POINT")
    public List<NewStockPointVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewStockPointVO> dataList = newStockPointDao.selectByCondition(sortParam, queryCriteriaParam);
        NewStockPointServiceImpl target = springBeanUtils.getBean(NewStockPointServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewStockPointVO> selectByParams(Map<String, Object> params) {
        List<NewStockPointPO> list = newStockPointDao.selectByParams(params);
        return NewStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewStockPointVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        newStockPointDomainService.checkDelete(removeVersionDTOS);
        return newStockPointDao.deleteBatchVersion(removeVersionDTOS);
    }

    /**
     * 同步库存点
     *
     * @return
     */
    @Override
    public BaseResponse<Void> syncStockPoints(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_PLANT_FOR_BPIM");
        //调用远程的生产组织信息
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.STOCK_POINT.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    /**
     * 销售组织展示
     *
     * @return
     */
    @Override
    public List<NewStockPointVO> selectSaleOrgaByOrganizeType() {

        return newStockPointDao.selectSaleOrgaByOrganizeType();
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.STOCK_POINT.getCode();
    }

    @Override
    public List<NewStockPointVO> invocation(List<NewStockPointVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

	@Override
	public List<String> selectAllVehicleModel() {
		return newStockPointDao.selectAllVehicleModel();
	}

	@Override
	public List<NewStockPointVO> selectByOrganizeTypes(List<String> organizeTypes) {
		List<NewStockPointPO> poList = newStockPointDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"organizeTypeList" , CollUtil.isEmpty(organizeTypes) ? new ArrayList<>() : organizeTypes));
		return NewStockPointConvertor.INSTANCE.po2Vos(poList);
	}

    @Override
    public List<LabelValue<String>> pointDown(String stockPointCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("stockPointCode", stockPointCode);
        params.put("planArea", "FYSH");
        List<NewStockPointVO> newStockPointVOS = this.selectByParams(params);
        if (newStockPointVOS == null) {
            return Collections.emptyList();
        }
        return newStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getStockPointCode() + "（" + x.getStockPointName() + "）", x.getStockPointCode()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> getInterfaceFlag() {
        List<ExtApiConfigVO> apiConfigVOS = ipsNewFeign.selectALLApiConfig();
        apiConfigVOS = apiConfigVOS.stream()
                .filter(item->
                        !"AUTH".equals(item.getApiCategory()) &&
                                !item.getApiCategory().contains("TOKEN"))
                .collect(Collectors.toList());
        return apiConfigVOS.stream()
                .map(x-> new LabelValue<>(x.getApiName(),x.getApiCategory()))
                .distinct()
                .collect(Collectors.toList());
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
       EasyExcelUtil.initResponse(response, "组织管理模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("库存点代码*"));
        headers.add(Collections.singletonList("库存点名称*"));
        headers.add(Collections.singletonList("库存点类型*"));
        headers.add(Collections.singletonList("组织ID*"));
        headers.add(Collections.singletonList("计划区域*"));
        headers.add(Collections.singletonList("数据来源*"));
        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public List<LabelValue<String>> stockPointDownOnly() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("stockPointType", "GN");
        List<NewStockPointVO> newStockPointVOS = this.selectByParams(params);
        if (newStockPointVOS == null) {
            return Collections.emptyList();
        }
        List<LabelValue<String>> list = newStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getStockPointName(), x.getStockPointCode()))
                .distinct()
                .collect(Collectors.toList());
        return removeDuplicates(list);
    }

    @Override
    public List<LabelValue<String>> stockPointNameDropDown(String stockPointName) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("stockPointName", stockPointName);
        List<NewStockPointVO> newStockPointVOS = this.selectByParams(params);
        if (newStockPointVOS == null) {
            return Collections.emptyList();
        }
        List<LabelValue<String>> list = newStockPointVOS.stream()
                .map(x -> new LabelValue<>(x.getStockPointName(), x.getStockPointName()))
                .distinct()
                .collect(Collectors.toList());
        return removeDuplicates(list);
    }

    private List<LabelValue<String>> removeDuplicates(List<LabelValue<String>> list) {
        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                lv -> lv,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

}
