<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO">
        <!--@Table mds_rou_routing-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
        <result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="lead_time" jdbcType="INTEGER" property="leadTime"/>
        <result column="production_version" jdbcType="VARCHAR" property="productionVersion"/>
        <result column="production_cost" jdbcType="VARCHAR" property="productionCost"/>
        <result column="max_quantity" jdbcType="VARCHAR" property="maxQuantity"/>
        <result column="min_quantity" jdbcType="VARCHAR" property="minQuantity"/>
        <result column="lot_size" jdbcType="VARCHAR" property="lotSize"/>
        <result column="outsourcing_work_hours" jdbcType="INTEGER" property="outsourcingWorkHours"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="currency_unit_id" jdbcType="VARCHAR" property="currencyUnitId"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="expire_reason" jdbcType="VARCHAR" property="expireReason"/>
        <result column="product_status" jdbcType="VARCHAR" property="productStatus"/>
        <result column="material_status" jdbcType="VARCHAR" property="materialStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="routing_head_id" jdbcType="VARCHAR" property="routingHeadId"/>
        <result column="routing_version" jdbcType="VARCHAR" property="routingVersion"/>
        <result column="version_effective_time" jdbcType="TIMESTAMP" property="versionEffectiveTime"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="effective" jdbcType="VARCHAR" property="effective"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.NewRoutingVO">
    	<result column="product_name" jdbcType="VARCHAR" property="productName"/>
    </resultMap>
    <resultMap id="RoutingValidateVOResultMap"  type="com.yhl.scp.mds.routing.vo.NewRoutingValidateVO">
        <result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
        <result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="sequence_no" jdbcType="INTEGER" property="sequenceNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,product_id,stock_point_id,routing_code,routing_name,priority,lead_time,production_version,production_cost,max_quantity,min_quantity,lot_size,outsourcing_work_hours,effective_time,expiry_time,currency_unit_id,counting_unit_id,expire_reason,product_status,material_status,remark,enabled,creator,create_time,modifier,modify_time,version_value,routing_head_id,routing_version,version_effective_time,last_update_time,effective,product_code
    </sql>
    <sql id="VO_Column_List">
        id,
		stock_point_id,
		product_id,
		product_code,
		product_name,
		routing_code,
		routing_name,
		expiry_time,
		effective_time,
		priority,
		enabled,
		remark,
        product_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingCode != null and params.routingCode != ''">
                and routing_code = #{params.routingCode,jdbcType=VARCHAR}
            </if>
            <if test="params.routingName != null and params.routingName != ''">
                and routing_name = #{params.routingName,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.leadTime != null">
                and lead_time = #{params.leadTime,jdbcType=INTEGER}
            </if>
            <if test="params.productionVersion != null and params.productionVersion != ''">
                and production_version = #{params.productionVersion,jdbcType=VARCHAR}
            </if>
            <if test="params.productionCost != null">
                and production_cost = #{params.productionCost,jdbcType=VARCHAR}
            </if>
            <if test="params.maxQuantity != null">
                and max_quantity = #{params.maxQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.minQuantity != null">
                and min_quantity = #{params.minQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=VARCHAR}
            </if>
            <if test="params.outsourcingWorkHours != null">
                and outsourcing_work_hours = #{params.outsourcingWorkHours,jdbcType=INTEGER}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.currencyUnitId != null and params.currencyUnitId != ''">
                and currency_unit_id = #{params.currencyUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.productStatus != null and params.productStatus != ''">
                and product_status = #{params.productStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.materialStatus != null and params.materialStatus != ''">
                and material_status = #{params.materialStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.routingHeadId != null and params.routingHeadId != ''">
                and routing_head_id = #{params.routingHeadId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingVersion != null and params.routingVersion != ''">
                and routing_version = #{params.routingVersion,jdbcType=VARCHAR}
            </if>
            <if test="params.versionEffectiveTime != null">
                and version_effective_time = #{params.versionEffectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productIds != null and params.productIds.size() > 0">
                and product_id in
                <foreach collection="params.productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_new_mds_rou_routing
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_routing
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_routing(
        id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        expire_reason,
        product_status,
        material_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        routing_head_id,
        routing_version,
        version_effective_time,
        last_update_time,
        effective,
        product_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{routingCode,jdbcType=VARCHAR},
        #{routingName,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{leadTime,jdbcType=INTEGER},
        #{productionVersion,jdbcType=VARCHAR},
        #{productionCost,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{outsourcingWorkHours,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{productStatus,jdbcType=VARCHAR},
        #{materialStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{routingHeadId,jdbcType=VARCHAR},
        #{routingVersion,jdbcType=VARCHAR},
        #{versionEffectiveTime,jdbcType=TIMESTAMP},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO">
        insert into mds_rou_routing(
        id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        expire_reason,
        product_status,
        material_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        routing_head_id,
        routing_version,
        version_effective_time,
        last_update_time,
        effective,
        product_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{routingCode,jdbcType=VARCHAR},
        #{routingName,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{leadTime,jdbcType=INTEGER},
        #{productionVersion,jdbcType=VARCHAR},
        #{productionCost,jdbcType=VARCHAR},
        #{maxQuantity,jdbcType=VARCHAR},
        #{minQuantity,jdbcType=VARCHAR},
        #{lotSize,jdbcType=VARCHAR},
        #{outsourcingWorkHours,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{currencyUnitId,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{productStatus,jdbcType=VARCHAR},
        #{materialStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{routingHeadId,jdbcType=VARCHAR},
        #{routingVersion,jdbcType=VARCHAR},
        #{versionEffectiveTime,jdbcType=TIMESTAMP},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_routing(
        id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        expire_reason,
        product_status,
        material_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        routing_head_id,
        routing_version,
        version_effective_time,
        last_update_time,
        effective,
        product_code)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.routingCode,jdbcType=VARCHAR},
        #{entity.routingName,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.productionVersion,jdbcType=VARCHAR},
        #{entity.productionCost,jdbcType=VARCHAR},
        #{entity.maxQuantity,jdbcType=VARCHAR},
        #{entity.minQuantity,jdbcType=VARCHAR},
        #{entity.lotSize,jdbcType=VARCHAR},
        #{entity.outsourcingWorkHours,jdbcType=INTEGER},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.productStatus,jdbcType=VARCHAR},
        #{entity.materialStatus,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.routingHeadId,jdbcType=VARCHAR},
        #{entity.routingVersion,jdbcType=VARCHAR},
        #{entity.versionEffectiveTime,jdbcType=TIMESTAMP},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_routing(
        id,
        product_id,
        stock_point_id,
        routing_code,
        routing_name,
        priority,
        lead_time,
        production_version,
        production_cost,
        max_quantity,
        min_quantity,
        lot_size,
        outsourcing_work_hours,
        effective_time,
        expiry_time,
        currency_unit_id,
        counting_unit_id,
        expire_reason,
        product_status,
        material_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        routing_head_id,
        routing_version,
        version_effective_time,
        last_update_time,
        effective,
        product_code)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.routingCode,jdbcType=VARCHAR},
        #{entity.routingName,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.leadTime,jdbcType=INTEGER},
        #{entity.productionVersion,jdbcType=VARCHAR},
        #{entity.productionCost,jdbcType=VARCHAR},
        #{entity.maxQuantity,jdbcType=VARCHAR},
        #{entity.minQuantity,jdbcType=VARCHAR},
        #{entity.lotSize,jdbcType=VARCHAR},
        #{entity.outsourcingWorkHours,jdbcType=INTEGER},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.currencyUnitId,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.productStatus,jdbcType=VARCHAR},
        #{entity.materialStatus,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.routingHeadId,jdbcType=VARCHAR},
        #{entity.routingVersion,jdbcType=VARCHAR},
        #{entity.versionEffectiveTime,jdbcType=TIMESTAMP},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO">
        update mds_rou_routing set
        product_id = #{productId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        routing_code = #{routingCode,jdbcType=VARCHAR},
        routing_name = #{routingName,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=INTEGER},
        lead_time = #{leadTime,jdbcType=INTEGER},
        production_version = #{productionVersion,jdbcType=VARCHAR},
        production_cost = #{productionCost,jdbcType=VARCHAR},
        max_quantity = #{maxQuantity,jdbcType=VARCHAR},
        min_quantity = #{minQuantity,jdbcType=VARCHAR},
        lot_size = #{lotSize,jdbcType=VARCHAR},
        outsourcing_work_hours = #{outsourcingWorkHours,jdbcType=INTEGER},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        currency_unit_id = #{currencyUnitId,jdbcType=VARCHAR},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        expire_reason = #{expireReason,jdbcType=VARCHAR},
        product_status = #{productStatus,jdbcType=VARCHAR},
        material_status = #{materialStatus,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        routing_head_id = #{routingHeadId,jdbcType=VARCHAR},
        routing_version = #{routingVersion,jdbcType=VARCHAR},
        version_effective_time = #{versionEffectiveTime,jdbcType=TIMESTAMP},
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
        effective = #{effective,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO">
        update mds_rou_routing
        <set>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingCode != null and item.routingCode != ''">
                routing_code = #{item.routingCode,jdbcType=VARCHAR},
            </if>
            <if test="item.routingName != null and item.routingName != ''">
                routing_name = #{item.routingName,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.productionVersion != null and item.productionVersion != ''">
                production_version = #{item.productionVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCost != null">
                production_cost = #{item.productionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.outsourcingWorkHours != null">
                outsourcing_work_hours = #{item.outsourcingWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.productStatus != null and item.productStatus != ''">
                product_status = #{item.productStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.materialStatus != null and item.materialStatus != ''">
                material_status = #{item.materialStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.routingHeadId != null and item.routingHeadId != ''">
                routing_head_id = #{item.routingHeadId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingVersion != null and item.routingVersion != ''">
                routing_version = #{item.routingVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.versionEffectiveTime != null">
                version_effective_time = #{item.versionEffectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_routing
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="lead_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.leadTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="outsourcing_work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outsourcingWorkHours,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="currency_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currencyUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="routing_head_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingHeadId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionEffectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_routing
        <set>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingCode != null and item.routingCode != ''">
                routing_code = #{item.routingCode,jdbcType=VARCHAR},
            </if>
            <if test="item.routingName != null and item.routingName != ''">
                routing_name = #{item.routingName,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.leadTime != null">
                lead_time = #{item.leadTime,jdbcType=INTEGER},
            </if>
            <if test="item.productionVersion != null and item.productionVersion != ''">
                production_version = #{item.productionVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCost != null">
                production_cost = #{item.productionCost,jdbcType=VARCHAR},
            </if>
            <if test="item.maxQuantity != null">
                max_quantity = #{item.maxQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.minQuantity != null">
                min_quantity = #{item.minQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=VARCHAR},
            </if>
            <if test="item.outsourcingWorkHours != null">
                outsourcing_work_hours = #{item.outsourcingWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.currencyUnitId != null and item.currencyUnitId != ''">
                currency_unit_id = #{item.currencyUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.productStatus != null and item.productStatus != ''">
                product_status = #{item.productStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.materialStatus != null and item.materialStatus != ''">
                material_status = #{item.materialStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.routingHeadId != null and item.routingHeadId != ''">
                routing_head_id = #{item.routingHeadId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingVersion != null and item.routingVersion != ''">
                routing_version = #{item.routingVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.versionEffectiveTime != null">
                version_effective_time = #{item.versionEffectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_routing where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_routing where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

     <update id="doLogicDeleteBatchByIds">
        update
        	mds_rou_routing
        set
        	enabled = 'NO',
        	effective = 'NO'
        where
        	id in
        	<foreach collection="ids" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </update>

    <select id="selectProductRiskLevel" resultType="com.yhl.scp.mds.bom.vo.ProductRiskLevelVO">
        SELECT DISTINCT
            a.product_code as productCode,
            d.product_code as materialCode,
            d.product_name as materialName,
            e.material_risk_level as riskLevel
		FROM
			mds_rou_routing a
			LEFT JOIN mds_rou_routing_step_input c ON a.id = c.routing_id
			LEFT JOIN mds_product_stock_point d ON c.input_product_id = d.id
			LEFT JOIN mrp_material_risk_level e ON e.stock_point_code = d.stock_point_code
			AND e.material_code = d.product_code
		WHERE
			a.stock_point_id in
            <foreach collection="rangeList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
			AND a.product_code in
			<foreach collection="productCodeList" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
			AND e.material_risk_level IS NOT NULL
    </select>
    <select id="selectStockPointIdRoutingCode" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_rou_routing
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                stock_point_id = #{item.plantCode,jdbcType=VARCHAR} and routing_code = #{item.itemCode,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectStepSequenceInputNotExist"  resultMap="RoutingValidateVOResultMap">
        SELECT
        <include refid="VO_Column_List" />
        FROM
            v_mds_rou_routing
        WHERE
            id IN (
                SELECT
                    t.routing_id
                FROM
                    (
                        SELECT
                            t1.*,
                            t2.id AS input_id
                        FROM
                            mds_rou_routing_step t1
                                LEFT JOIN mds_rou_routing_step_input t2
                                          ON t1.id = t2.routing_step_id
                        WHERE t1.enabled = 'YES'
                    ) t
                        JOIN (
                        SELECT
                            t3.routing_id,
                            MIN(t3.sequence_no) AS min_sequence_no
                        FROM
                            mds_rou_routing_step t3
                        WHERE
                            t3.enabled = 'YES'
                        GROUP BY
                            t3.routing_id
                    ) first_steps
                             ON t.routing_id = first_steps.routing_id
                                 AND t.sequence_no = first_steps.min_sequence_no
                WHERE
                    t.input_id IS NULL
            )
          AND enabled = 'YES'
    </select>
    <select id="selectStepSequenceResourceNotExist"  resultMap="RoutingValidateVOResultMap">
        SELECT
        <include refid="VO_Column_List" />
        FROM v_mds_rou_routing r
        WHERE r.enabled = 'YES'
          AND EXISTS (
            SELECT 1
            FROM v_mds_rou_routing_step t1
                     LEFT JOIN mds_rou_routing_step_resource t2 ON t1.id = t2.routing_step_id AND t2.enabled = 'YES'
            WHERE t1.routing_id = r.id
              AND t1.enabled = 'YES'
              AND t2.routing_step_id IS NULL
        );

    </select>
    <select id="selectStepSequenceResourceWithoutBeat"  resultMap="RoutingValidateVOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_routing where id in(
            SELECT t1.routing_id
            FROM v_mds_rou_routing_step_resource t1
            WHERE t1.unit_production_time is null and t1.enabled='YES') and enabled = 'YES'
    </select>
    <select id="selectStepSequenceWithoutYield"  resultMap="RoutingValidateVOResultMap">
        select
        <include refid="VO_Column_List" /> from v_mds_rou_routing where id in(
        SELECT
        t.routing_id
        FROM
        v_mds_rou_routing_step t
        WHERE
        (t.yield IS NULL
        OR t.yield =0) and t.enabled = 'YES'
        )  and enabled = 'YES'
    </select>
    <select id="selectProductionLineResourceNotMatch"  resultMap="RoutingValidateVOResultMap">
        SELECT
        r.*
        FROM (
                 SELECT
                     t1.*,
                     t2.sequence_no,
                     t3.physical_resource_id
                 FROM v_mds_rou_routing t1
                          JOIN mds_rou_routing_step t2 ON t1.id = t2.routing_id
                          JOIN mds_rou_routing_step_resource t3 ON t2.id = t3.routing_step_id
                 WHERE t1.enabled = 'YES'
                   AND t2.enabled = 'YES'
                   AND t3.enabled = 'YES'
             ) r
                 LEFT JOIN (
            SELECT
                t1.id AS physical_resource_id,
                t1.sequence_code,
                t3.stock_point_code
            FROM mds_res_physical_resource t1
                     JOIN mds_res_standard_resource t2 ON t1.standard_resource_id = t2.id
                     JOIN mds_stock_point t3 ON t3.organize_id = t2.organization_id
            WHERE t1.enabled = 'YES'
              AND t2.enabled = 'YES'
        ) t ON r.physical_resource_id = t.physical_resource_id
            AND r.sequence_no = t.sequence_code
            AND r.stock_point_id = t.stock_point_code
        WHERE t.physical_resource_id IS NULL;

    </select>

    <select id="selectForCheckEnableFlag" resultMap="VOResultMap">
        SELECT
			id,
			stock_point_id,
			enabled,
			product_code
		FROM
			mds_rou_routing
    </select>

    <update id="updateEnableForExpiryTime">
        update
        	mds_rou_routing
        set
        	enabled = 'NO',
        	effective = 'NO'
        where expiry_time <![CDATA[ <= ]]> NOW()
        and enabled = 'YES' AND effective = 'YES'
    </update>

    <select id="selectRoutingIdsByProductCode" resultType="java.lang.String">
        select id
        from v_mds_rou_routing
        where product_code = #{productCode}
    </select>

    <select id="selectEnabledProductCodes" resultType="String">
        SELECT product_code
        FROM mds_rou_routing
        WHERE enabled = 'YES' AND product_code IN
        <foreach item="code" collection="productCodes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
