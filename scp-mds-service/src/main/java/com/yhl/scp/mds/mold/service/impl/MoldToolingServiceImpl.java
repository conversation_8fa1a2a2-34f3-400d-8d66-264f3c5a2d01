package com.yhl.scp.mds.mold.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldTooling;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.mold.convertor.MoldToolingConvertor;
import com.yhl.scp.mds.mold.domain.entity.MoldToolingDO;
import com.yhl.scp.mds.mold.domain.service.MoldToolingDomainService;
import com.yhl.scp.mds.mold.dto.MoldToolingDTO;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingDao;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingGroupDao;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingPO;
import com.yhl.scp.mds.mold.service.MoldToolingService;
import com.yhl.scp.mds.mold.vo.MoldToolingVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.routing.service.StandardStepService;
import com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao;
import com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MoldToolingServiceImpl</code>
 * <p>
 * 模具工装族与工装编号关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:27:53
 */
@Slf4j
@Service
public class MoldToolingServiceImpl extends AbstractService implements MoldToolingService {

    @Resource
    private MoldToolingDao moldToolingDao;

    @Resource
    private MoldToolingDomainService moldToolingDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MoldToolingGroupDao moldToolingGroupDao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewStockPointDao newStockPointDao;

    @Resource
    private StandardStepService standardStepService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MoldToolingDTO moldToolingDTO) {
        // 0.数据转换
        MoldToolingDO moldToolingDO = MoldToolingConvertor.INSTANCE.dto2Do(moldToolingDTO);
        MoldToolingPO moldToolingPO = MoldToolingConvertor.INSTANCE.dto2Po(moldToolingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldToolingDomainService.validation(moldToolingDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(moldToolingPO);
        moldToolingDao.insert(moldToolingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MoldToolingDTO moldToolingDTO) {
        // 0.数据转换
        MoldToolingDO moldToolingDO = MoldToolingConvertor.INSTANCE.dto2Do(moldToolingDTO);
        MoldToolingPO moldToolingPO = MoldToolingConvertor.INSTANCE.dto2Po(moldToolingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldToolingDomainService.validation(moldToolingDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(moldToolingPO);
        moldToolingDao.update(moldToolingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MoldToolingDTO> list) {
        List<MoldToolingPO> newList = MoldToolingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        moldToolingDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MoldToolingDTO> list) {
        List<MoldToolingPO> newList = MoldToolingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        moldToolingDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return moldToolingDao.deleteBatch(idList);
        }
        return moldToolingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MoldToolingVO selectByPrimaryKey(String id) {
        MoldToolingPO po = moldToolingDao.selectByPrimaryKey(id);
        return MoldToolingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MOLD_TOOLING")
    public List<MoldToolingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MOLD_TOOLING")
    public List<MoldToolingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MoldToolingVO> dataList = moldToolingDao.selectByCondition(sortParam, queryCriteriaParam);
        MoldToolingServiceImpl target = springBeanUtils.getBean(MoldToolingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MoldToolingVO> selectByParams(Map<String, Object> params) {
        List<MoldToolingPO> list = moldToolingDao.selectByParams(params);
        return MoldToolingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MoldToolingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MOLD_TOOLING.getCode();
    }

    @Override
    public List<MoldToolingVO> invocation(List<MoldToolingVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    @Override
    public BaseResponse<Void> synMoldTooling(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MOLD_TOOLING.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleMoldTooling(List<MesMoldTooling> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<MoldToolingDTO> insertDtoS = new ArrayList<>();
        List<MoldToolingDTO> updateDtoS = new ArrayList<>();
        List<MoldToolingGroupPO> updateStandardResourcePOS = new ArrayList<>();
        Set<String> kids = list.stream().map(MesMoldTooling::getToolingGroupId).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("kids", kids);
        List<MoldToolingGroupPO> standardResourcePOS = moldToolingGroupDao.selectByParams(map);
        Map<String, MoldToolingGroupPO> oldMap = CollectionUtils.isEmpty(standardResourcePOS) ?
                MapUtil.newHashMap() :
                standardResourcePOS.stream().collect(
                        Collectors.toMap(MoldToolingGroupPO::getKid, Function.identity(), (v1, v2) -> v1));
        Set<String> physicalResourceCodes =
                list.stream().map(MesMoldTooling::getToolingCode).collect(Collectors.toSet());
        HashMap<String, Object> mapStandard = MapUtil.newHashMap();
        mapStandard.put("physicalResourceCodes", physicalResourceCodes);
        List<MoldToolingPO> moldToolingPOS = moldToolingDao.selectByParams(mapStandard);
        Map<String, MoldToolingPO> oldMoldToolingMap = CollectionUtils.isEmpty(moldToolingPOS) ?
                MapUtil.newHashMap() :
                moldToolingPOS.stream().collect(
                        Collectors.toMap(MoldToolingPO::getPhysicalResourceCode, Function.identity(), (v1, v2) -> v1));
        Set<String> stockPointCodes = list.stream().map(MesMoldTooling::getPlantCode).collect(Collectors.toSet());
        HashMap<String, Object> mapstockPoint = MapUtil.newHashMap();
        mapstockPoint.put("stockPointCodes", stockPointCodes);
        List<NewStockPointPO> newStockPointPOS = newStockPointDao.selectByParams(mapstockPoint);
        Map<String, NewStockPointPO> newStockPointPOSMap = CollectionUtils.isEmpty(newStockPointPOS) ?
                MapUtil.newHashMap() :
                newStockPointPOS.stream().collect(
                        Collectors.toMap(NewStockPointPO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        //获取标准工序
        List<StandardStepVO> standardStepVOS = standardStepService.selectAll();
        Map<String, StandardStepVO> standardStepVOSMap = CollectionUtils.isEmpty(standardStepVOS) ?
                MapUtil.newHashMap() :
                standardStepVOS.stream().filter(item -> "FORMING_PROCESS".equals(item.getStandardStepType())).collect(
                        Collectors.toMap(StandardStepVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        for (MesMoldTooling mesMoldTooling : list) {

            String kid = mesMoldTooling.getToolingGroupId();
            if (!oldMap.containsKey(kid)) {
                continue;
            }
            MoldToolingGroupPO standardResourcePO = oldMap.get(kid);
            if (newStockPointPOSMap.containsKey(mesMoldTooling.getPlantCode())) {
                NewStockPointPO newStockPointPO = newStockPointPOSMap.get(mesMoldTooling.getPlantCode());
                standardResourcePO.setOrganizationId(newStockPointPO.getOrganizeId());
                updateStandardResourcePOS.add(standardResourcePO);
            }
            MoldToolingDTO dto = new MoldToolingDTO();
            if (oldMoldToolingMap.containsKey(mesMoldTooling.getToolingCode())) {
                MoldToolingPO oldPo = oldMoldToolingMap.get(mesMoldTooling.getToolingCode());
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(mesMoldTooling, dto,standardResourcePO,standardStepVOSMap);
                updateDtoS.add(dto);
            } else {
                generateDto(mesMoldTooling, dto,standardResourcePO,standardStepVOSMap);
                dto.setResourceQuantityCoefficient("1");
                dto.setResourceType("SINGLE");
                dto.setResourceCategory("TOOL");
                dto.setSubtaskType("WORK");
                insertDtoS.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateStandardResourcePOS)) {
            updateStandardResourcePOS = updateStandardResourcePOS.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    MoldToolingGroupPO::getId,
                                    sr -> sr,
                                    (existing, replacement) -> existing // 或者你可以选择其他合并逻辑
                            ),
                            maps -> new ArrayList<>(maps.values())
                    ));


            BasePOUtils.updateBatchFiller(updateStandardResourcePOS);
            moldToolingGroupDao.updateBatch(updateStandardResourcePOS);
        }
        return BaseResponse.success("同步成功");
    }

    private void generateDto(MesMoldTooling mesMoldTooling, MoldToolingDTO dto, MoldToolingGroupPO standardResourcePO, Map<String, StandardStepVO> standardStepVOSMap) {
        dto.setStandardResourceId(standardResourcePO.getId());
        String enabled = "是".equals(mesMoldTooling.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
        if(enabled.equals(YesOrNoEnum.YES.getCode())){
            if("入库".equals(mesMoldTooling.getToolingStatus())||"出库".equals(mesMoldTooling.getToolingStatus())||"激活".equals(mesMoldTooling.getToolingStatus())
                    ||"外借入库".equals(mesMoldTooling.getToolingStatus())||"外借出库".equals(mesMoldTooling.getToolingStatus())){
                enabled=YesOrNoEnum.YES.getCode();
            }else {
                enabled=YesOrNoEnum.NO.getCode();
            }
        }
        dto.setPhysicalResourceCode(mesMoldTooling.getToolingCode());
        dto.setPhysicalResourceName(mesMoldTooling.getToolingCode());
        if (standardStepVOSMap.containsKey(mesMoldTooling.getPlantCode())) {
            dto.setSequenceCode(standardStepVOSMap.get(mesMoldTooling.getPlantCode()).getStandardStepCode());
        } else {
            dto.setSequenceCode("");
        }
        dto.setEnabled(enabled);
    }

    @Override
    public List<MoldToolingVO> selectById(List<String> idList) {
        return moldToolingDao.selectById(idList);
    }
}
