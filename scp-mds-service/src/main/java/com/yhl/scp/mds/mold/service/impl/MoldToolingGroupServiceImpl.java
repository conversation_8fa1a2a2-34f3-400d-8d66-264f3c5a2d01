package com.yhl.scp.mds.mold.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroup;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.baseResource.dto.StandardResourceDTO;
import com.yhl.scp.mds.baseResource.infrastructure.dao.PhysicalResourceLogDao;
import com.yhl.scp.mds.baseResource.infrastructure.dao.StandardResource2Dao;
import com.yhl.scp.mds.baseResource.infrastructure.po.StandardResourcePO;
import com.yhl.scp.mds.mold.convertor.MoldToolingGroupConvertor;
import com.yhl.scp.mds.mold.domain.entity.MoldToolingGroupDO;
import com.yhl.scp.mds.mold.domain.service.MoldToolingGroupDomainService;
import com.yhl.scp.mds.mold.dto.MoldToolingGroupDTO;
import com.yhl.scp.mds.mold.infrastructure.dao.MoldToolingGroupDao;
import com.yhl.scp.mds.mold.infrastructure.po.MoldToolingGroupPO;
import com.yhl.scp.mds.mold.service.MoldToolingGroupService;
import com.yhl.scp.mds.mold.vo.MoldToolingGroupVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MoldToolingGroupServiceImpl</code>
 * <p>
 * 模具工装族应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:26:12
 */
@Slf4j
@Service
public class MoldToolingGroupServiceImpl extends AbstractService implements MoldToolingGroupService {

    @Resource
    private MoldToolingGroupDao moldToolingGroupDao;

    @Resource
    private MoldToolingGroupDomainService moldToolingGroupDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private StandardResource2Dao standardResource2Dao;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MoldToolingGroupDTO moldToolingGroupDTO) {
        // 0.数据转换
        MoldToolingGroupDO moldToolingGroupDO = MoldToolingGroupConvertor.INSTANCE.dto2Do(moldToolingGroupDTO);
        MoldToolingGroupPO moldToolingGroupPO = MoldToolingGroupConvertor.INSTANCE.dto2Po(moldToolingGroupDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldToolingGroupDomainService.validation(moldToolingGroupDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(moldToolingGroupPO);
        moldToolingGroupDao.insert(moldToolingGroupPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MoldToolingGroupDTO moldToolingGroupDTO) {
        // 0.数据转换
        MoldToolingGroupDO moldToolingGroupDO = MoldToolingGroupConvertor.INSTANCE.dto2Do(moldToolingGroupDTO);
        MoldToolingGroupPO moldToolingGroupPO = MoldToolingGroupConvertor.INSTANCE.dto2Po(moldToolingGroupDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldToolingGroupDomainService.validation(moldToolingGroupDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(moldToolingGroupPO);
        moldToolingGroupDao.update(moldToolingGroupPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MoldToolingGroupDTO> list) {
        List<MoldToolingGroupPO> newList = MoldToolingGroupConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        moldToolingGroupDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MoldToolingGroupDTO> list) {
        List<MoldToolingGroupPO> newList = MoldToolingGroupConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        moldToolingGroupDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return moldToolingGroupDao.deleteBatch(idList);
        }
        return moldToolingGroupDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MoldToolingGroupVO selectByPrimaryKey(String id) {
        MoldToolingGroupPO po = moldToolingGroupDao.selectByPrimaryKey(id);
        return MoldToolingGroupConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MOLD_TOOLING_GROUP")
    public List<MoldToolingGroupVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MOLD_TOOLING_GROUP")
    public List<MoldToolingGroupVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MoldToolingGroupVO> dataList = moldToolingGroupDao.selectByCondition(sortParam, queryCriteriaParam);
        MoldToolingGroupServiceImpl target = springBeanUtils.getBean(MoldToolingGroupServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MoldToolingGroupVO> selectByParams(Map<String, Object> params) {
        List<MoldToolingGroupPO> list = moldToolingGroupDao.selectByParams(params);
        return MoldToolingGroupConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MoldToolingGroupVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MOLD_TOOLING_GROUP.getCode();
    }

    @Override
    public List<MoldToolingGroupVO> invocation(List<MoldToolingGroupVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    @Override
    public BaseResponse<Void> synMoldToolingGroup(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MOLD_TOOLING_GROUP.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroup(List<MesMoldToolingGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<MoldToolingGroupDTO> insertDtoS = new ArrayList<>();
        List<MoldToolingGroupDTO> updateDtoS = new ArrayList<>();
        Set<String> kids = list.stream().map(MesMoldToolingGroup::getToolingGroupId).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("kids", kids);
        List<MoldToolingGroupPO> moldToolingGroupPOS = moldToolingGroupDao.selectByParams(map);
        Map<String, MoldToolingGroupPO> oldMap =  moldToolingGroupPOS.stream().collect(
                Collectors.toMap(MoldToolingGroupPO::getKid, Function.identity(), (v1, v2) -> v1));
        for (MesMoldToolingGroup mesMoldToolingGroup : list) {
            String enabled = "是".equals(mesMoldToolingGroup.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            String kid = mesMoldToolingGroup.getToolingGroupId();
            MoldToolingGroupDTO dto = new MoldToolingGroupDTO();
            if (oldMap.containsKey(kid)) {
                MoldToolingGroupPO oldPo = oldMap.get(kid);
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(mesMoldToolingGroup, enabled, dto);
                updateDtoS.add(dto);
            } else {
                generateDto(mesMoldToolingGroup, enabled, dto);
                dto.setKid(kid);
                dto.setResourceType("SINGLE");
                dto.setResourceCategory("TOOL");
                dto.setProductionEfficiency(new BigDecimal(1));
                insertDtoS.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }
        return BaseResponse.success("同步成功");
    }

    private void generateDto(MesMoldToolingGroup mesMoldToolingGroup, String enabled, MoldToolingGroupDTO dto) {
        dto.setStandardResourceName(mesMoldToolingGroup.getToolingGroupCode());
        dto.setStandardResourceCode(mesMoldToolingGroup.getToolingGroupCode());
        dto.setEnabled(enabled);
    }
}
