package com.yhl.scp.mds.job;

import java.util.List;

import javax.annotation.Resource;

import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepInputService;
import com.yhl.scp.mds.routing.service.NewRoutingStepService;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>RoutingJob</code>
 * <p>
 * 工艺路径候选资源失效处理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-07 20:33:57
 */
@Component
@Slf4j
public class RoutingStepResourceExpireJob {
	
    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private NewRoutingStepService newRoutingStepService;
    
    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;
    
    @Resource
    private NewRoutingService newRoutingService;
    @Resource
    private NewStockPointService newStockPointService;
    
    @XxlJob("routingStepResourceExpireJob")
    private ReturnT<String> routingStepResourceExpireJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的工艺路径候选资源失效处理job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            //1.处理已经过期失效的工艺路径步骤输入物品
            int updateEnableForExpiryTime = newRoutingStepInputService.updateEnableForExpiryTime();
            if(updateEnableForExpiryTime > 0) {
            	//2.维护第一道工序的输入物品，输出物品
                newRoutingStepService.doCreatFirstStepInputAndOut();
            }
            //工艺路径，工艺路径步骤处理过期数据
            newRoutingService.updateEnableForExpiryTime();
            newRoutingStepService.updateEnableForExpiryTime();
            
            //3.若存在S2的10工序没有候选资源则默认维护S2XL01资源
            newRoutingStepService.doAddDefaultResource();
            
            //4.根据采购类别判断工艺路径是否有效
            newRoutingService.checkEnableFlagByPoCategory(scenario.getDataBaseName());

            List<NewStockPointVO> stockPoints = newStockPointService.selectAll();
            newRoutingStepInputService.afterSetInputProduct(stockPoints,scenario.getDataBaseName());
            
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的工艺路径候选资源失效处理job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
