package com.yhl.scp.mds.common.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.calendar.infrastructure.dao.ResourceCalendarDao;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.mds.extension.calendar.infrastructure.po.ResourceCalendarPO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepInputPO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.infrastructure.dao.ProductStockPointDao;
import com.yhl.scp.mds.product.service.ProductStockPointService;
import com.yhl.scp.mds.resource.service.PhysicalResourceService;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepInputDao;
import com.yhl.scp.mds.routing.service.RoutingService;
import com.yhl.scp.mds.routing.service.RoutingStepInputService;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Api(tags = "A测试控制器")
@RestController
@RequestMapping("resourceCla")
public class TestController extends BaseController {

    @Resource
    private PhysicalResourceService physicalResourceService;

    @Resource
    private ResourceCalendarDao resourceCalendarDao;

    @Resource
    private NewStockPointService newStockPointService;
    @Autowired
    private RoutingStepInputService routingStepInputService;
    @Autowired
    private ProductStockPointDao productStockPointDao;
    @Autowired
    private ProductStockPointService productStockPointService;
    @Resource
    private NewProductStockPointService newProductStockPointService;
    @Autowired
    private RoutingService routingService;
    @Autowired
    private RoutingStepInputDao routingStepInputDao;

    @ApiOperation(value = "testProduct")
    @GetMapping(value = "/testProduct")
    public BaseResponse testProduct() {
        // 工艺路径
        List<RoutingVO> routingVOS = routingService.selectAll()
                .stream().filter(p -> StrUtil.isNotEmpty(p.getEnabled())
                        && YesOrNoEnum.YES.getCode().equals(p.getEnabled())).collect(Collectors.toList());
        Map<String, RoutingVO> routingVOMap = StreamUtils.mapByColumn(routingVOS, RoutingVO::getProductId);
        // 输入物品
        List<RoutingStepInputPO> routingStepInputPOS = routingStepInputDao.selectByParams(new HashMap<>())
                .stream().filter(p -> StrUtil.isNotEmpty(p.getEnabled())
                        && YesOrNoEnum.YES.getCode().equals(p.getEnabled())).collect(Collectors.toList());
        // 库存点
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectAll();
        Map<String, NewStockPointVO> stockPointVOMap = StreamUtils.mapByColumn(newStockPointVOS, NewStockPointVO::getStockPointCode);
        // 物料
        List<String> dynamicHeader = ListUtil.of("id", "stock_point_code", "product_code", "product_type");
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectProductListByParamOnDynamicColumns(dynamicHeader, new HashMap<>());
        Map<String, NewProductStockPointVO> productStockPointVOMap = StreamUtils.mapByColumn(newProductStockPointVOS, NewProductStockPointVO::getId);
        Map<String, List<NewProductStockPointVO>> productCodeMap = StreamUtils.mapListByColumn(newProductStockPointVOS, NewProductStockPointVO::getProductCode);

        List<RoutingStepInputPO> updateList = new ArrayList<>();
        for (RoutingStepInputPO routingStepInputVO : routingStepInputPOS) {
            String inputProductId = routingStepInputVO.getInputProductId();
            if (!productStockPointVOMap.containsKey(inputProductId)) {
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(inputProductId);
            String productType = newProductStockPointVO.getProductType();
            if (!productType.equals(ProductTypeEnum.SA.getCode())) {
                continue;
            }
            // 判读当前半品是否存在routing
            if (routingVOMap.containsKey(inputProductId)) {
                continue;
            }
            String productCode = newProductStockPointVO.getProductCode();
            if (!productCodeMap.containsKey(productCode)) {
                continue;
            }
            log.info("输入物料SA属性不存在工艺路径输入物品id：{}", routingStepInputVO.getId());
            List<NewProductStockPointVO> productList = productCodeMap.get(productCode);
            for (NewProductStockPointVO productStockPointVO : productList) {
                String productStockPointVOId = productStockPointVO.getId();
                if (!routingVOMap.containsKey(productStockPointVOId)) {
                    continue;
                }
                String stockPointCode = productStockPointVO.getStockPointCode();
                if (!stockPointVOMap.containsKey(stockPointCode)) {
                    continue;
                }
                String stockPointId = stockPointVOMap.get(stockPointCode).getId();
                routingStepInputVO.setInputProductId(productStockPointVOId);
                routingStepInputVO.setStockPointId(stockPointId);
                updateList.add(routingStepInputVO);
                break;
            }
        }
        log.info("修改路径不存在输入物品数量：{}", updateList.size());
        if(CollectionUtils.isNotEmpty(updateList)){
            BasePOUtils.insertBatchFiller(updateList);
            Lists.partition(updateList, 1000).forEach(list -> routingStepInputDao.updateBatch(list));
        }
        return BaseResponse.success();
    }

    @ApiOperation(value = "资源日历")
    @GetMapping(value = "/test")
    public BaseResponse test() {
        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectAll();
        List<Date> intervalDates = DateUtils.getIntervalDates(DateUtils.stringToDate("2023-01-01 00:00:00"),
                DateUtils.stringToDate("2024-12-31 00:00:00"));
        Date date = new Date();
        List<ResourceCalendarPO> resourceCalendarDOList = new ArrayList<>();
        for (PhysicalResourceVO physicalResourceVO : physicalResourceVOS) {
            for (Date intervalDate : intervalDates) {
                ResourceCalendarPO resourceCalendarDO = new ResourceCalendarPO();
                resourceCalendarDO.setId(UUIDUtil.getUUID());
                resourceCalendarDO.setOrganizationId("production000001");
                resourceCalendarDO.setPhysicalResourceId(physicalResourceVO.getId());
                resourceCalendarDO.setStandardResourceId(physicalResourceVO.getStandardResourceId());
                resourceCalendarDO.setShiftId("4a744f808cd3fe68e440a9bdcc072580");
                resourceCalendarDO.setShiftPattern("08:00-20:00");
                resourceCalendarDO.setWorkHours(BigDecimalUtils.toBigDecimal("12"));
                resourceCalendarDO.setWorkDay(intervalDate);
                resourceCalendarDO.setCalendarType("NORMAL");
                String s1 = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3) + " 08:00:00";
                String s2 = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3) + " 20:00:00";
                resourceCalendarDO.setStartTime(DateUtils.stringToDate(s1));
                resourceCalendarDO.setEndTime(DateUtils.stringToDate(s2));
                resourceCalendarDO.setEnabled("YES");
                resourceCalendarDO.setCreator("AOTO");
                resourceCalendarDO.setCreateTime(date);
                resourceCalendarDO.setModifier("AOTO");
                resourceCalendarDO.setModifyTime(date);
                resourceCalendarDOList.add(resourceCalendarDO);
            }
        }
        resourceCalendarDao.insertBatch(resourceCalendarDOList);
        log.info("添加日历行：{}", resourceCalendarDOList.size());
        return BaseResponse.success();
    }

}
