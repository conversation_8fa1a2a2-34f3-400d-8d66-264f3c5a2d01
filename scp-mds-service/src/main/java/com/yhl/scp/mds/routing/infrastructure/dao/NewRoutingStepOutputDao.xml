<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepOutputDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO">
        <!--@Table mds_rou_routing_step_output-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
        <result column="output_product_id" jdbcType="VARCHAR" property="outputProductId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="output_factor" jdbcType="VARCHAR" property="outputFactor"/>
        <result column="yield" jdbcType="VARCHAR" property="yield"/>
        <result column="scrap_strategy" jdbcType="VARCHAR" property="scrapStrategy"/>
        <result column="percentage_scrap_rate" jdbcType="VARCHAR" property="percentageScrapRate"/>
        <result column="scrap" jdbcType="VARCHAR" property="scrap"/>
        <result column="main_product" jdbcType="VARCHAR" property="mainProduct"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="effective" jdbcType="VARCHAR" property="effective"/>
        <result column="expire_reason" jdbcType="VARCHAR" property="expireReason"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.NewRoutingStepOutputVO">
		<result column="product_code" jdbcType="VARCHAR" property="productCode"/>
		<result column="product_name" jdbcType="VARCHAR" property="productName"/>
		<result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
		<result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
		<result column="sequence_no" jdbcType="VARCHAR" property="sequenceNo"/>
		<result column="routing_code" jdbcType="VARCHAR" property="routingCode"/>
		<result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
		<result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
		<result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,routing_id,routing_step_id,output_product_id,stock_point_id,output_factor,yield,scrap_strategy,percentage_scrap_rate,scrap,main_product,max_connection_duration,min_connection_duration,counting_unit_id,effective_time,effective,expire_reason,expiry_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        id,
		routing_id,
		routing_step_id,
		output_product_id,
		product_code,
		product_name,
		id stock_point_id,
		stock_point_code,
		stock_point_name,
		output_factor,
		yield,
		main_product,
		sequence_no,
		routing_code,
		routing_name,
		standard_step_code,
		standard_step_name,
		enabled,
		effective_time,
		expiry_time
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.outputProductId != null and params.outputProductId != ''">
                and output_product_id = #{params.outputProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.outputFactor != null">
                and output_factor = #{params.outputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.yield != null">
                and yield = #{params.yield,jdbcType=VARCHAR}
            </if>
            <if test="params.scrapStrategy != null and params.scrapStrategy != ''">
                and scrap_strategy = #{params.scrapStrategy,jdbcType=VARCHAR}
            </if>
            <if test="params.percentageScrapRate != null">
                and percentage_scrap_rate = #{params.percentageScrapRate,jdbcType=VARCHAR}
            </if>
            <if test="params.scrap != null">
                and scrap = #{params.scrap,jdbcType=VARCHAR}
            </if>
            <if test="params.mainProduct != null and params.mainProduct != ''">
                and main_product = #{params.mainProduct,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.effective != null and params.effective != ''">
                and effective = #{params.effective,jdbcType=VARCHAR}
            </if>
            <if test="params.expireReason != null and params.expireReason != ''">
                and expire_reason = #{params.expireReason,jdbcType=VARCHAR}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_output
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_output
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_new_mds_rou_routing_step_output
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_routing_step_output
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_routing_step_output
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_routing_step_output(
        id,
        routing_id,
        routing_step_id,
        output_product_id,
        stock_point_id,
        output_factor,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        main_product,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{outputProductId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{outputFactor,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{mainProduct,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO">
        insert into mds_rou_routing_step_output(
        id,
        routing_id,
        routing_step_id,
        output_product_id,
        stock_point_id,
        output_factor,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        main_product,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{outputProductId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{outputFactor,jdbcType=VARCHAR},
        #{yield,jdbcType=VARCHAR},
        #{scrapStrategy,jdbcType=VARCHAR},
        #{percentageScrapRate,jdbcType=VARCHAR},
        #{scrap,jdbcType=VARCHAR},
        #{mainProduct,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{countingUnitId,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{effective,jdbcType=VARCHAR},
        #{expireReason,jdbcType=VARCHAR},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_routing_step_output(
        id,
        routing_id,
        routing_step_id,
        output_product_id,
        stock_point_id,
        output_factor,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        main_product,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.outputProductId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.outputFactor,jdbcType=VARCHAR},
        #{entity.yield,jdbcType=VARCHAR},
        #{entity.scrapStrategy,jdbcType=VARCHAR},
        #{entity.percentageScrapRate,jdbcType=VARCHAR},
        #{entity.scrap,jdbcType=VARCHAR},
        #{entity.mainProduct,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_routing_step_output(
        id,
        routing_id,
        routing_step_id,
        output_product_id,
        stock_point_id,
        output_factor,
        yield,
        scrap_strategy,
        percentage_scrap_rate,
        scrap,
        main_product,
        max_connection_duration,
        min_connection_duration,
        counting_unit_id,
        effective_time,
        effective,
        expire_reason,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.outputProductId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.outputFactor,jdbcType=VARCHAR},
        #{entity.yield,jdbcType=VARCHAR},
        #{entity.scrapStrategy,jdbcType=VARCHAR},
        #{entity.percentageScrapRate,jdbcType=VARCHAR},
        #{entity.scrap,jdbcType=VARCHAR},
        #{entity.mainProduct,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.effective,jdbcType=VARCHAR},
        #{entity.expireReason,jdbcType=VARCHAR},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO">
        update mds_rou_routing_step_output set
        routing_id = #{routingId,jdbcType=VARCHAR},
        routing_step_id = #{routingStepId,jdbcType=VARCHAR},
        output_product_id = #{outputProductId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        output_factor = #{outputFactor,jdbcType=VARCHAR},
        yield = #{yield,jdbcType=VARCHAR},
        scrap_strategy = #{scrapStrategy,jdbcType=VARCHAR},
        percentage_scrap_rate = #{percentageScrapRate,jdbcType=VARCHAR},
        scrap = #{scrap,jdbcType=VARCHAR},
        main_product = #{mainProduct,jdbcType=VARCHAR},
        max_connection_duration = #{maxConnectionDuration,jdbcType=INTEGER},
        min_connection_duration = #{minConnectionDuration,jdbcType=INTEGER},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        effective = #{effective,jdbcType=VARCHAR},
        expire_reason = #{expireReason,jdbcType=VARCHAR},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO">
        update mds_rou_routing_step_output
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.outputProductId != null and item.outputProductId != ''">
                output_product_id = #{item.outputProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.outputFactor != null">
                output_factor = #{item.outputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.mainProduct != null and item.mainProduct != ''">
                main_product = #{item.mainProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_routing_step_output
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="output_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.outputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yield = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.yield,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap_strategy = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrapStrategy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="percentage_scrap_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.percentageScrapRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scrap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scrap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="main_product = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mainProduct,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="effective = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effective,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expireReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_routing_step_output 
        <set>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.outputProductId != null and item.outputProductId != ''">
                output_product_id = #{item.outputProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.outputFactor != null">
                output_factor = #{item.outputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.yield != null">
                yield = #{item.yield,jdbcType=VARCHAR},
            </if>
            <if test="item.scrapStrategy != null and item.scrapStrategy != ''">
                scrap_strategy = #{item.scrapStrategy,jdbcType=VARCHAR},
            </if>
            <if test="item.percentageScrapRate != null">
                percentage_scrap_rate = #{item.percentageScrapRate,jdbcType=VARCHAR},
            </if>
            <if test="item.scrap != null">
                scrap = #{item.scrap,jdbcType=VARCHAR},
            </if>
            <if test="item.mainProduct != null and item.mainProduct != ''">
                main_product = #{item.mainProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effective != null and item.effective != ''">
                effective = #{item.effective,jdbcType=VARCHAR},
            </if>
            <if test="item.expireReason != null and item.expireReason != ''">
                expire_reason = #{item.expireReason,jdbcType=VARCHAR},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_routing_step_output where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_routing_step_output where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <update id="doLogicDeleteBatchByRoutingIds">
        update 
        	mds_rou_routing_step_output
        set
        	enabled = 'NO',
        	effective = 'NO'
        where 
        	routing_id in
        	<foreach collection="routingIds" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </update>
    
    <update id="doLogicDeleteBatchByRoutingStepIds">
        update 
        	mds_rou_routing_step_output
        set
        	enabled = 'NO',
        	effective = 'NO'
        where 
        	routing_step_id in
        	<foreach collection="routingStepIds" item="item" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </update>
    
    <delete id="deleteByCreator">
        delete from mds_rou_routing_step_output where creator = #{creator,jdbcType=VARCHAR}
    </delete>
</mapper>
