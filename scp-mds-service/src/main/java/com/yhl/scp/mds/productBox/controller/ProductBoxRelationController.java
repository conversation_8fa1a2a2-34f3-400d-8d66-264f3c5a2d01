package com.yhl.scp.mds.productBox.controller;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.productBox.dto.ProductBoxRelationDTO;
import com.yhl.scp.mds.productBox.service.ProductBoxRelationService;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <code>ProductBoxRelationController</code>
 * <p>
 * 产品与成品箱关系控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 21:03:45
 */
@Slf4j
@Api(tags = "产品与成品箱关系控制器")
@RestController
@RequestMapping("productBoxRelation")
public class ProductBoxRelationController extends BaseController {
    @Resource
    private ProductBoxRelationService productBoxRelationService;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign mdsFeign;


    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ProductBoxRelationVO>> page() {
        List<ProductBoxRelationVO> productBoxRelationList = productBoxRelationService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductBoxRelationVO> pageInfo = new PageInfo<>(productBoxRelationList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductBoxRelationDTO productBoxRelationDTO) {
        return productBoxRelationService.doCreate(productBoxRelationDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductBoxRelationDTO productBoxRelationDTO) {
        return productBoxRelationService.doUpdate(productBoxRelationDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        productBoxRelationService.deleteBatchVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ProductBoxRelationVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productBoxRelationService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "产品与成品箱关系同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncProductBoxRelation() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步产品与成品箱关系job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            productBoxRelationService.syncProductBoxRelation(scenario.getTenantId());

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步产品与成品箱关系job结束", scenario);
        }
        return productBoxRelationService.syncProductBoxRelation(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "箱子类型下拉")
    @GetMapping(value = "boxTypeDropdown")
    public BaseResponse<List<LabelValue<String>>> queryBoxType() {
        return BaseResponse.success(productBoxRelationService.queryBoxType());
    }

}
