package com.yhl.scp.mds.routing.infrastructure.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepOutputVO;

/**
 * <code>NewRoutingStepOutputDao</code>
 * <p>
 * 新-生产路径步骤输出物品DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:58:33
 */
public interface NewRoutingStepOutputDao extends BaseDao<NewRoutingStepOutputPO, NewRoutingStepOutputVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link NewRoutingStepOutputVO}
     */
    List<NewRoutingStepOutputVO> selectVOByParams(@Param("params") Map<String, Object> params);

	void doLogicDeleteBatchByRoutingIds(@Param("routingIds") List<String> routingIds);

	void doLogicDeleteBatchByRoutingStepIds(@Param("routingStepIds") List<String> routingStepIds);

	void deleteByCreator(@Param("creator") String creator);

}
