package com.yhl.scp.mds.substitution.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.enums.ProductSubstitutionRelationshipSourceEnum;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.mds.extension.product.vo.StockPointVO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.StandardStepPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.service.StockPointService;
import com.yhl.scp.mds.routing.infrastructure.dao.StandardStepDao;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.convertor.ProductSubstitutionRelationshipConvertor;
import com.yhl.scp.mds.substitution.domain.entity.ProductSubstitutionRelationshipDO;
import com.yhl.scp.mds.substitution.domain.service.ProductSubstitutionRelationshipDomainService;
import com.yhl.scp.mds.substitution.dto.ProductSubstitutionRelationshipDTO;
import com.yhl.scp.mds.substitution.dto.ProductSubstitutionRelationshipExportDTO;
import com.yhl.scp.mds.substitution.infrastructure.dao.ProductSubstitutionRelationshipDao;
import com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO;
import com.yhl.scp.mds.substitution.service.ProductSubstitutionRelationshipService;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mds.utils.EasyExcelUtil;
import com.yhl.scp.mrp.substitutionRelationship.dto.GlassSubstitutionRelationshipDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductSubstitutionRelationshipServiceImpl</code>
 * <p>
 * 物料替代关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 11:32:33
 */
@Slf4j
@Service
public class ProductSubstitutionRelationshipServiceImpl extends AbstractService implements ProductSubstitutionRelationshipService {

    @Resource
    private ProductSubstitutionRelationshipDao productSubstitutionRelationshipDao;

    @Resource
    private ProductSubstitutionRelationshipDomainService productSubstitutionRelationshipDomainService;

    @Resource
    private StandardStepDao standardStepDao;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private StockPointService stockPointService;

    @Override
    public BaseResponse<Void> doCreate(ProductSubstitutionRelationshipDTO productSubstitutionRelationshipDTO) {
        // 0.数据转换
        ProductSubstitutionRelationshipDO productSubstitutionRelationshipDO = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Do(productSubstitutionRelationshipDTO);
        ProductSubstitutionRelationshipPO productSubstitutionRelationshipPO = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Po(productSubstitutionRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productSubstitutionRelationshipDomainService.validation(productSubstitutionRelationshipDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productSubstitutionRelationshipPO);
        productSubstitutionRelationshipDao.insert(productSubstitutionRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductSubstitutionRelationshipDTO productSubstitutionRelationshipDTO) {
        // 0.数据转换
        ProductSubstitutionRelationshipDO productSubstitutionRelationshipDO = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Do(productSubstitutionRelationshipDTO);
//        ProductSubstitutionRelationshipPO productSubstitutionRelationshipPO = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Po(productSubstitutionRelationshipDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        List<ProductSubstitutionRelationshipPO> productSubstitutionRelationshipPOS = productSubstitutionRelationshipDomainService.updatePriority(productSubstitutionRelationshipDO);
        if (productSubstitutionRelationshipPOS != null) {
            BasePOUtils.updateBatchFiller(productSubstitutionRelationshipPOS);
            productSubstitutionRelationshipDao.updateBatch(productSubstitutionRelationshipPOS);
        }
        // 2.数据持久化
/*        BasePOUtils.updateFiller(productSubstitutionRelationshipPO);
        productSubstitutionRelationshipDao.update(productSubstitutionRelationshipPO);*/
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductSubstitutionRelationshipDTO> list) {
        List<ProductSubstitutionRelationshipPO> newList = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productSubstitutionRelationshipDao.insertBatchWithPrimaryKey(newList);
    }

    private boolean doBatchUpdatePriority(List<ProductSubstitutionRelationshipPO> newList) {
        List<String> ids = newList.stream().map(ProductSubstitutionRelationshipPO::getId).collect(Collectors.toList());
        //修改的数据找到数据库中的数据
        List<ProductSubstitutionRelationshipPO> sourcePOs = productSubstitutionRelationshipDao.selectByPrimaryKeys(ids);
        Map<String, List<ProductSubstitutionRelationshipPO>> mapById =
                sourcePOs.stream().collect(Collectors.groupingBy(ProductSubstitutionRelationshipPO::getId));
        //传递给接口的参数
        List<Map<String,Object>> syncList = new ArrayList<>();
        //数据遍历
        newList.stream().forEach(item->{
            if (mapById.containsKey(item.getId())){
                ProductSubstitutionRelationshipPO oldData = mapById.get(item.getId()).get(0);
                if (oldData.getPriority() == null || (oldData.getPriority() != null && !oldData.getPriority().equals(item.getPriority()))) {
                    HashMap<String, Object> syncMap = new HashMap<>();
                    syncMap.put("componentSequenceId", oldData.getComponentSequenceId());
                    syncMap.put("segment1", oldData.getSubstituteProductCode());
                    syncMap.put("priority", item.getPriority());
                    syncList.add(syncMap);
                }
            }
        });

        if (!syncList.isEmpty()) {
            log.info("同步优先级给ERP参数：{}", syncList);
            try {
                newDcpFeign.callExternalApi(SystemHolder.getTenantId(), ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.PRODUCT_SUBSTITUTION_FEEDBACK.getCode(), ImmutableMap.of("syncList", syncList));
            } catch (Exception e) {
                log.error("同步优先级给ERP报错：{}", e.getMessage());
                throw new BusinessException("同步优先级给ERP报错" + e.getMessage());
            }
        }
        return true;
    }

    @Override
    public void doUpdateBatch(List<ProductSubstitutionRelationshipDTO> list) {
        List<ProductSubstitutionRelationshipPO> newList = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Pos(list);
        boolean isUpdatePriority = this.doBatchUpdatePriority(newList);
        if (isUpdatePriority) {
            BasePOUtils.updateBatchFiller(newList);
            productSubstitutionRelationshipDao.updateBatchSelective(newList);
        }
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productSubstitutionRelationshipDao.deleteBatch(idList);
        }
        return productSubstitutionRelationshipDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductSubstitutionRelationshipVO selectByPrimaryKey(String id) {
        ProductSubstitutionRelationshipPO po = productSubstitutionRelationshipDao.selectByPrimaryKey(id);
        return ProductSubstitutionRelationshipConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_SUBSTITUTION_RELATIONSHIP")
    public List<ProductSubstitutionRelationshipVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_SUBSTITUTION_RELATIONSHIP")
    public List<ProductSubstitutionRelationshipVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductSubstitutionRelationshipVO> dataList = productSubstitutionRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        ProductSubstitutionRelationshipServiceImpl target = SpringBeanUtils.getBean(ProductSubstitutionRelationshipServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductSubstitutionRelationshipVO> selectByParams(Map<String, Object> params) {
        List<ProductSubstitutionRelationshipVO> dataList = productSubstitutionRelationshipDao.selectVOByParams(params);
        if (CollectionUtils.isEmpty(dataList)){
            return new ArrayList<>();
        }
        assembleOtherAttributes(dataList);
        return dataList;
    }

    private void assembleOtherAttributes(List<ProductSubstitutionRelationshipVO> dataList){
        //匹配物料表
        Map<String, ProductSubstitutionRelationshipVO> collect = dataList.stream().collect(Collectors.toMap(
                x -> x.getStockPointCode() + "-" + x.getRawProductCode(),
                x -> x,
                (item1, item2) -> item1
        ));

        Collection<ProductSubstitutionRelationshipVO> values = collect.values();
        List<NewProductStockPointVO> lists = newProductStockPointService.selectByStockCodeAndProductCode(values);
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectByProductCode(dataList.stream().map(ProductSubstitutionRelationshipVO::getProductCode).distinct().collect(Collectors.toList()));
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream().collect(Collectors.toMap(item -> String.join("#", item.getStockPointCode(), item.getProductCode()), Function.identity(), (k1, k2) -> k2));


        // bomVersion
        Map<String, ProductBomVersionVO> productBomVersionVOMap = new HashMap<>();
        // bom
        Map<String, List<ProductBomVO>> productBomGroupOfVersionId = new HashMap<>();


        List<String> productIdList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
        List<ProductBomVersionVO> productBomVersionVOList = mdsProductBomVersionService.selectByParams(ImmutableMap.of("productIds", productIdList));
        if (CollectionUtils.isNotEmpty(productBomVersionVOList)){
            List<String> bomVersionIdList = productBomVersionVOList.stream().map(ProductBomVersionVO::getId).collect(Collectors.toList());
            productBomVersionVOMap = productBomVersionVOList.stream().collect(Collectors.toMap(ProductBomVersionVO::getProductId, Function.identity(), (k1, k2) -> k2));

            // 查询Bom
            List<ProductBomVO> productBomVOList = mdsProductBomService.selectVOByParams(ImmutableMap.of("bomVersionIds", bomVersionIdList));
            if (CollectionUtils.isNotEmpty(productBomVOList)){
                productBomGroupOfVersionId = productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
            }
        }

        Map<String, ProductBomVersionVO> finalProductBomVersionVOMap = productBomVersionVOMap;
        Map<String, List<ProductBomVO>> finalProductBomGroupOfVersionId = productBomGroupOfVersionId;
        dataList.forEach(item -> {
            Optional<NewProductStockPointVO> first = lists.stream()
                    .filter(x -> x.getStockPointCode().equals(item.getStockPointCode()) && x.getProductCode().equals(item.getRawProductCode())).findFirst();
            if (first.isPresent()) {
                NewProductStockPointVO newProductStockPointVO = first.get();
                item.setColor(newProductStockPointVO.getProductColor());
                item.setLength(newProductStockPointVO.getProductLength());
                item.setWide(newProductStockPointVO.getProductWidth());
                item.setThickness(newProductStockPointVO.getProductThickness());
            }

            // 维护用量
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(String.join("#", item.getStockPointCode(), item.getProductCode()));
            if (null != newProductStockPointVO){
                // 获取bomVersion
                ProductBomVersionVO productBomVersionVO = finalProductBomVersionVOMap.get(newProductStockPointVO.getId());
                if (null == productBomVersionVO){
                    return;
                }
                // 获取bom
                List<ProductBomVO> productBomVOS = finalProductBomGroupOfVersionId.get(productBomVersionVO.getId());
                if (CollectionUtils.isEmpty(productBomVOS)){
                    return;
                }
                Map<String, ProductBomVO> productBomVOMap = productBomVOS.stream()
                        .collect(Collectors.toMap(item2 -> String.join("#", item2.getIoProductCode(), item2.getStandardStepCode()), Function.identity(), (k1, k2) -> k2));

                ProductBomVO productBomVO = productBomVOMap.get(String.join("#", item.getRawProductCode(), item.getOperationCode()));
                if (null != productBomVO){
                    item.setInputFactor(productBomVO.getIoFactor());
                }
            }
        });
    }

    @Override
    public List<ProductSubstitutionRelationshipVO> selectAll() {
        return ProductSubstitutionRelationshipConvertor.INSTANCE.po2Vos(productSubstitutionRelationshipDao.selectAll());
    }


    @Override
    public void export(HttpServletResponse response) {
        List<ProductSubstitutionRelationshipPO> productSubstitutionRelationshipPOS = productSubstitutionRelationshipDao.selectByParams(new HashMap<>(2));
        List<ProductSubstitutionRelationshipVO> voList = ProductSubstitutionRelationshipConvertor.INSTANCE.po2Vos(productSubstitutionRelationshipPOS);
        this.invocation(voList, null, this.getInvocationName());
        List<ProductSubstitutionRelationshipExportDTO> productSubstitutionRelationshipExportDTOS = ProductSubstitutionRelationshipConvertor.INSTANCE.vo2ExportDtos(voList);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("物料替代关系.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), ProductSubstitutionRelationshipExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("物料替代关系")
                    .doWrite(productSubstitutionRelationshipExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_SUBSTITUTION_RELATIONSHIP.getCode();
    }

    @Override
    public List<ProductSubstitutionRelationshipVO> invocation(List<ProductSubstitutionRelationshipVO> dataList, Map<String, Object> params, String invocation) {
        //查询物料主数据相关信息
        List<String> codeList = dataList.stream().map(ProductSubstitutionRelationshipVO::getRawProductCode).distinct().collect(Collectors.toList());
        codeList.addAll(dataList.stream().map(ProductSubstitutionRelationshipVO::getSubstituteProductCode).distinct().collect(Collectors.toList()));
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectByProductCode(codeList);
        Map<String, List<NewProductStockPointVO>> porductCodeMap = newProductStockPointVOS.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "-" + t.getStockPointCode()));
        for (ProductSubstitutionRelationshipVO productSubstitutionRelationshipVO : dataList) {
            String rawKey = productSubstitutionRelationshipVO.getRawProductCode() + "-" + productSubstitutionRelationshipVO.getStockPointCode();
            String subKey = productSubstitutionRelationshipVO.getSubstituteProductCode() + "-" + productSubstitutionRelationshipVO.getStockPointCode();
            if (porductCodeMap.containsKey(rawKey)) {
                productSubstitutionRelationshipVO.setRawUnit(porductCodeMap.get(rawKey).get(0).getMeasurementUnit());
                productSubstitutionRelationshipVO.setLength(porductCodeMap.get(rawKey).get(0).getProductLength());
                productSubstitutionRelationshipVO.setWide(porductCodeMap.get(rawKey).get(0).getProductWidth());
                productSubstitutionRelationshipVO.setColor(porductCodeMap.get(rawKey).get(0).getProductColor());
                productSubstitutionRelationshipVO.setThickness(porductCodeMap.get(rawKey).get(0).getProductThickness());
            }
            if (porductCodeMap.containsKey(subKey)) {
                productSubstitutionRelationshipVO.setSubUnit(porductCodeMap.get(subKey).get(0).getMeasurementUnit());
            }
        }
        return dataList;
    }

    @Override
    public BaseResponse<Void> syncProductSubstitutionData(String scenario, List<ProductSubstitutionRelationshipDTO> list, String organizationCodes) {
        List<ProductSubstitutionRelationshipPO> pos = ProductSubstitutionRelationshipConvertor.INSTANCE.dto2Pos(list);
        //获取当前组织下数据库中的数据
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("stockPointCode", organizationCodes);
        List<ProductSubstitutionRelationshipPO> oldPos = productSubstitutionRelationshipDao.selectByParams(map);
        //对于bpim数据库中的数据 按照头id+行id 作为key
        Map<String, ProductSubstitutionRelationshipPO> oldMap = oldPos.stream().collect(Collectors.toMap(
                x -> x.getBillSequenceId() + "-" + x.getComponentSequenceId() + "-" + x.getSubstituteProductCode(),
                x -> x
        ));
        //获取上面数据的所有key
        Set<String> oldKeySet = oldMap.keySet();
        //新数据补充基本字段数据
        BasePOUtils.insertBatchFiller(pos);
        //获取bom的数据
        List<String> componentSequenceIds = pos.stream().map(ProductSubstitutionRelationshipPO::getComponentSequenceId).distinct().collect(Collectors.toList());
        List<ProductBomVO> productBomVOS = mdsProductBomService.selectByComponentSequenceIds(componentSequenceIds);
        //当前时间
        Date now = new Date();
        //时间极值
        Date earlyDate = new Date(Long.MIN_VALUE);
        Date fetureDate = new Date(Long.MAX_VALUE);
        //给bom数据的有效期时间填空
        productBomVOS.stream().forEach(x -> {
            if (Objects.isNull(x.getStartTime())) {
                x.setStartTime(earlyDate);
            }
            if (Objects.isNull(x.getEndTime())) {
                x.setEndTime(fetureDate);
            }
        });
        Map<String, Boolean> booleanBomMap = productBomVOS.stream().collect(Collectors.toMap(
                x -> x.getComponentSequenceId(),
                x -> YesOrNoEnum.YES.getCode().equals(x.getEnabled()) && !now.before(x.getStartTime()) && !now.after(x.getEndTime())
        ));
        //获取标准工艺路径步骤表数据
        List<StandardStepPO> standardStepPOS = standardStepDao.selectByParams(new HashMap<>());
        List<ProductSubstitutionRelationshipPO> insertList = new ArrayList<>();
        List<ProductSubstitutionRelationshipPO> updateList = new ArrayList<>();
        pos.stream().forEach(x -> {
            //设置工序名称
            List<StandardStepPO> collect = standardStepPOS.stream().filter(y ->
                    y.getStandardStepCode().equals(x.getOperationCode()) && y.getStockPointCode().equals(x.getStockPointCode())
            ).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                x.setOperationName(collect.get(0).getStandardStepName());
            }
            String key = x.getBillSequenceId() + "-" + x.getComponentSequenceId() + "-" + x.getSubstituteProductCode();
            //旧数A,B,C 同步的C,D
            if (oldKeySet.contains(key)) {
                ProductSubstitutionRelationshipPO oldData = oldMap.get(key);
                if (x.getLastUpdateDate().after(oldData.getLastUpdateDate()) ) {
                    if (booleanBomMap.containsKey(oldData.getComponentSequenceId())) {
                        x.setEnabled(booleanBomMap.get(oldData.getComponentSequenceId())
                                && YesOrNoEnum.NO.getCode().equals(x.getIsDeleted())
                                ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    } else {
                        x.setEnabled(YesOrNoEnum.NO.getCode().equals(x.getIsDeleted())
                                ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    }
                    x.setRule(oldData.getRule());
                    x.setId(oldData.getId());
                    // 替代类型
                    String substitutionType = x.getSubstitutionType();
                    x.setSubstitutionTypeDesc(substitutionType);
                    if (StringUtils.isNotEmpty(substitutionType)) {
                        if ("1对1替代".equals(substitutionType)) {
                            x.setSubstitutionType("1");
                        } else if ("混合替代".equals(substitutionType)) {
                            x.setSubstitutionType("2");
                        } else if ("主料且混合替代".equals(substitutionType)) {
                            x.setSubstitutionType("3");
                        } else {
                            x.setSubstitutionType(null);
                        }
                    } else {
                        x.setSubstitutionType(null);
                    }
                    updateList.add(x);
                } else {
                    if (booleanBomMap.containsKey(oldData.getComponentSequenceId())){
                        String status = booleanBomMap.get(oldData.getComponentSequenceId()) && YesOrNoEnum.NO.getCode().equals(x.getIsDeleted())
                                ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
                        if (!status.equals(oldData.getEnabled())){
                            x.setRule(oldData.getRule());
                            x.setEnabled(status);
                            x.setId(oldData.getId());
                            // 替代类型
                            String substitutionType = x.getSubstitutionType();
                            x.setSubstitutionTypeDesc(substitutionType);
                            if (StringUtils.isNotEmpty(substitutionType)) {
                                if ("1对1替代".equals(substitutionType)) {
                                    x.setSubstitutionType("1");
                                } else if ("混合替代".equals(substitutionType)) {
                                    x.setSubstitutionType("2");
                                } else if ("主料且混合替代".equals(substitutionType)) {
                                    x.setSubstitutionType("3");
                                } else {
                                    x.setSubstitutionType(null);
                                }
                            } else {
                                x.setSubstitutionType(null);
                            }
                            updateList.add(x);
                        }
                    }
                }
            } else {
                if (booleanBomMap.containsKey(x.getComponentPoCategory())) {
                    x.setEnabled(booleanBomMap.get(x.getComponentSequenceId()) && YesOrNoEnum.NO.getCode().equals(x.getIsDeleted())
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                }
                //默认 替代料优先
                x.setRule("B");
                //默认补充逻辑（优先级，不从接口取，改为在BPIM系统维护）
                x.setPriority(1);
                // 是/否禁用主料库存，默认为NO
                x.setIsDisableMasterProduct(YesOrNoEnum.NO.getCode());
                // 替代类型
                String substitutionType = x.getSubstitutionType();
                x.setSubstitutionTypeDesc(substitutionType);
                if (StringUtils.isNotEmpty(substitutionType)) {
                    if ("1对1替代".equals(substitutionType)) {
                        x.setSubstitutionType("1");
                    } else if ("混合替代".equals(substitutionType)) {
                        x.setSubstitutionType("2");
                    } else if ("主料且混合替代".equals(substitutionType)) {
                        x.setSubstitutionType("3");
                    } else {
                        x.setSubstitutionType(null);
                    }
                } else {
                    x.setSubstitutionType(null);
                }
                insertList.add(x);
            }
        });
        //数据新增
        if (!insertList.isEmpty()) {
            productSubstitutionRelationshipDao.insertBatchWithPrimaryKey(insertList);
        }
        //数据更新
        if (!updateList.isEmpty()) {
            productSubstitutionRelationshipDao.updateBatchSelective(updateList);
        }
        return BaseResponse.success("数据同步成功");
    }

    @Override
    public BaseResponse<Void> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步bom替代料关系");
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantId();
            }
            //获取对应库存点编码
            List<NewStockPointVO> newStockPointVOS = newStockPointService.selectAll();
            List<String> codes = new ArrayList<>();
            for (NewStockPointVO stockPointVO : newStockPointVOS) {
                String interfaceFlag = stockPointVO.getInterfaceFlag();
                if (StringUtils.isNotEmpty(interfaceFlag)) {
                    String[] split = interfaceFlag.split(",");
                    boolean b = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.PRODUCT_SUBSTITUTION.getCode().equals(x));
                    if (b) {
                        codes.add(stockPointVO.getStockPointCode());
                    }
                }
            }
            log.info("同步的库存点编码有{}", codes);
            if (!codes.isEmpty()) {
                String finalTenantCode = tenantCode;
                codes.forEach(item->{
                    map.put("organizationCode", item);
                    //获取ERP的BOM替代料关系数据
                    BaseResponse<String> stringBaseResponse = newDcpFeign.callExternalApi(finalTenantCode, ApiSourceEnum.ERP.getCode(),
                            ApiCategoryEnum.PRODUCT_SUBSTITUTION.getCode(), map);
                    if (!stringBaseResponse.getSuccess()){
                        log.error("同步组织{}的数据报错：{}",item,stringBaseResponse.getMsg());
                    }
                });

            }
            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步bom替代料关系报错,{}", e.getMessage());
            throw new BusinessException("同步bom替代料关系报错", e.getMessage());
        }
    }

    @Override
    public void doUpload(MultipartFile file) {
        List<ProductSubstitutionRelationshipDTO> fileList;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(GlassSubstitutionRelationshipDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 校验文件、数据
        checkFileData(fileList);

        List<ProductSubstitutionRelationshipDTO> insertList = new ArrayList<>();
        List<ProductSubstitutionRelationshipDTO> updateList = new ArrayList<>();
        // 按照产品编码+组件+替代组件做联合主键
        List<String> joinKeys = fileList.stream()
                .map(item -> String.join("#", item.getProductCode(), item.getRawProductCode(), item.getSubstituteProductCode()))
                .distinct().collect(Collectors.toList());

        // 查询表里的数据
        List<ProductSubstitutionRelationshipPO> relationshipPOList = productSubstitutionRelationshipDao
                .selectByParams(ImmutableMap.of("combineKeys", joinKeys));
        checkPriority(fileList, relationshipPOList);

        Map<String, ProductSubstitutionRelationshipPO> databaseDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relationshipPOList)){
            databaseDataMap = relationshipPOList.stream()
                    .collect(Collectors.toMap(item -> String.join("#",
                            item.getProductCode(),
                            item.getRawProductCode(),
                            item.getSubstituteProductCode()), item -> item));
        }


        // 查询组件、替代组件
        List<String> productCodeList = fileList.stream()
                .map(ProductSubstitutionRelationshipDTO::getRawProductCode).collect(Collectors.toList());

        List<String> substiteProductCodeList = fileList.stream()
                .map(ProductSubstitutionRelationshipDTO::getSubstituteProductCode)
                .collect(Collectors.toList());
        productCodeList.addAll(substiteProductCodeList);

        productCodeList = productCodeList.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectProductListByParamOnDynamicColumns(
                Lists.newArrayList("product_code", "product_name", "stock_point_code"),
                ImmutableMap.of("productCodeList", productCodeList));

        List<StockPointVO> stockPointVOList = stockPointService.selectAll();
        Map<String, StockPointVO> stockPointVOMap = stockPointVOList.stream().collect(Collectors.toMap(StockPointVO::getStockPointCode, item -> item, (k1, k2) -> k2));

        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, item -> item, (k1, k2) -> k2));

        Map<String, String> codeMapOfDesc = ProductSubstitutionRelationshipSourceEnum.getCodeMapOfDesc();
        for (ProductSubstitutionRelationshipDTO productSubstitutionRelationshipDTO : fileList) {
            // 转义需求规则
            productSubstitutionRelationshipDTO.setRule(codeMapOfDesc.get(productSubstitutionRelationshipDTO.getRule()));
            if (productStockPointVOMap.containsKey(productSubstitutionRelationshipDTO.getRawProductCode())){
                productSubstitutionRelationshipDTO.setRawProductName(productStockPointVOMap.get(productSubstitutionRelationshipDTO.getRawProductCode()).getProductName());
                productSubstitutionRelationshipDTO.setStockPointCode(productStockPointVOMap.get(productSubstitutionRelationshipDTO.getRawProductCode()).getStockPointCode());
                if (stockPointVOMap.containsKey(productSubstitutionRelationshipDTO.getStockPointCode())){
                    productSubstitutionRelationshipDTO.setStockPointName(productSubstitutionRelationshipDTO.getStockPointCode());
                }
            }

            if (productStockPointVOMap.containsKey(productSubstitutionRelationshipDTO.getSubstituteProductCode())){
                productSubstitutionRelationshipDTO.setSubstituteProductName(productStockPointVOMap.get(productSubstitutionRelationshipDTO.getSubstituteProductCode()).getProductName());
            }

            String joinKey = String.join("#", productSubstitutionRelationshipDTO.getProductCode(),
                    productSubstitutionRelationshipDTO.getRawProductCode(),
                    productSubstitutionRelationshipDTO.getSubstituteProductCode());
            if (databaseDataMap.containsKey(joinKey)){
                // 更新
                ProductSubstitutionRelationshipPO productSubstitutionRelationshipPO = databaseDataMap.get(joinKey);
                if (StringUtils.isBlank(productSubstitutionRelationshipPO.getDataSource()) ||
                        ProductSubstitutionRelationshipSourceEnum.ERP_SYNC.getCode().equals(productSubstitutionRelationshipPO.getDataSource())){
                    continue;
                }
                productSubstitutionRelationshipDTO.setId(productSubstitutionRelationshipPO.getId());
                updateList.add(productSubstitutionRelationshipDTO);
            }else {
                // 新增
                productSubstitutionRelationshipDTO.setId(UUID.randomUUID().toString());
                productSubstitutionRelationshipDTO.setDataSource(ProductSubstitutionRelationshipSourceEnum.MANUAL_ADDITION.getCode());
                insertList.add(productSubstitutionRelationshipDTO);
            }
        }

        // 持久化数据
        if (CollectionUtils.isNotEmpty(insertList)){
            this.doCreateBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            this.doUpdateBatch(updateList);
        }
    }

    private void checkPriority(List<ProductSubstitutionRelationshipDTO> fileList, List<ProductSubstitutionRelationshipPO> relationshipPOList) {
        // 需要校验优先级，每个rawProductCode的优先级必须有1,并且只能有一个1
        Map<String, List<ProductSubstitutionRelationshipDTO>> fileDataOfRawProductCode = fileList.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getRawProductCode(), item.getSubstituteProductCode())));

        Map<String, List<ProductSubstitutionRelationshipPO>> databaseDataMapOfRawProductCode = relationshipPOList.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getRawProductCode(), item.getSubstituteProductCode())));

        for (Map.Entry<String, List<ProductSubstitutionRelationshipDTO>> entry : fileDataOfRawProductCode.entrySet()) {
            // 文件
            List<ProductSubstitutionRelationshipDTO> list = entry.getValue();
            // 过滤出优先级为1的
            List<ProductSubstitutionRelationshipDTO> dtoList = list.stream()
                    .filter(item -> item.getPriority().equals(1))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dtoList)){
                throw new BusinessException(entry.getKey() + "优先级必须要有1");
            }
            if (dtoList.size() > 1){
                throw new BusinessException(entry.getKey() + "优先级必须只有一个为1");
            }
        }

        for (Map.Entry<String, List<ProductSubstitutionRelationshipDTO>> entry : fileDataOfRawProductCode.entrySet()) {
            List<ProductSubstitutionRelationshipPO> list = databaseDataMapOfRawProductCode.get(entry.getKey());
            if (CollectionUtils.isEmpty(list)){
                continue;
            }
            // 过滤出优先级为1的
            List<ProductSubstitutionRelationshipDTO> dtoList = entry.getValue().stream()
                    .filter(item -> item.getPriority().equals(1))
                    .collect(Collectors.toList());
            List<ProductSubstitutionRelationshipPO> databaseList = list.stream()
                    .filter(item -> item.getPriority().equals(1))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dtoList) && CollectionUtils.isNotEmpty(databaseList)){
                // 判断数据库里为1的，是否是在文件中存在
                List<String> fileJoinKeys = dtoList.stream()
                        .map(item -> String.join("#", item.getProductCode(), item.getRawProductCode(), item.getSubstituteProductCode()))
                        .collect(Collectors.toList());

                List<String> databaseJoinKeys = databaseList.stream()
                        .map(item -> String.join("#", item.getProductCode(), item.getRawProductCode(), item.getSubstituteProductCode()))
                        .collect(Collectors.toList());
                if (!fileJoinKeys.get(0).equals(databaseJoinKeys.get(0))){
                    // 数据不一致
                    throw new BusinessException(entry.getKey() + "优先级必须只有一个为1");
                }
            }
        }
    }

    private void checkFileData(List<ProductSubstitutionRelationshipDTO> fileList) {
        if (CollectionUtils.isEmpty(fileList)){
            throw new BusinessException("文件数据为空");
        }

        for (ProductSubstitutionRelationshipDTO excelDTO : fileList) {
            if (StringUtils.isBlank(excelDTO.getProductCode())){
                throw new BusinessException("本厂编码*不能为空");
            }
            if (StringUtils.isBlank(excelDTO.getRawProductCode())){
                throw new BusinessException("ERP-BOM*不能为空");
            }
            if (StringUtils.isBlank(excelDTO.getSubstituteProductCode())){
                throw new BusinessException("生产BOM*不能为空");
            }
            if (null == excelDTO.getPriority()){
                throw new BusinessException("优先级*不能为空");
            }
        }

    }

    private List<ProductSubstitutionRelationshipDTO> getProductByFileData(List<ProductSubstitutionRelationshipDTO> fileList,
                                                                          List<ProductBomVO> productBomVOList,
                                                                          List<ProductBomVersionVO> productBomVersionVOList) {
        List<ProductSubstitutionRelationshipDTO> result = new ArrayList<>();
        Map<String, List<ProductBomVO>> productBomMapOfIoProductCode = productBomVOList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getIoProductCode()))
                .collect(Collectors.groupingBy(ProductBomVO::getIoProductCode));

        Map<String, ProductBomVersionVO> productBomVersionVOMapOfId = productBomVersionVOList.stream()
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));
        for (ProductSubstitutionRelationshipDTO dto : fileList) {
            // 获取所有的BOM
            List<ProductBomVO> bomVOList = productBomMapOfIoProductCode.get(dto.getRawProductCode());
            if (CollectionUtils.isEmpty(bomVOList)) {
                result.add(dto);
                continue;
            }
            List<String> bomVersionIdList = bomVOList.stream().map(ProductBomVO::getBomVersionId).distinct().collect(Collectors.toList());
            List<ProductBomVersionVO> bomVersionVOList = new ArrayList<>();
            for (String bomVersionId : bomVersionIdList) {
                if (productBomVersionVOMapOfId.containsKey(bomVersionId)) {
                    bomVersionVOList.add(productBomVersionVOMapOfId.get(bomVersionId));
                }
            }
            if (CollectionUtils.isEmpty(bomVersionVOList)) {
                result.add(dto);
                continue;
            }
            for (ProductBomVersionVO productBomVersionVO : bomVersionVOList) {
                ProductSubstitutionRelationshipDTO relationshipDTO = ProductSubstitutionRelationshipDTO.builder()
                        .id(UUID.randomUUID().toString())
                        .stockPointCode(dto.getStockPointCode())
                        .rawProductCode(dto.getRawProductCode())
                        .substituteProductCode(dto.getSubstituteProductCode())
                        .productCode(productBomVersionVO.getProductCode())
                        .productName(productBomVersionVO.getProductName())
                        .priority(dto.getPriority())
                        .rule(dto.getRule())
                        .build();
                result.add(relationshipDTO);
            }
        }
        return result;
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "物料替代关系导入模板");
        List<List<String>> headers = com.google.common.collect.Lists.newArrayList();
        headers.add(Collections.singletonList("本厂编码*"));
        headers.add(Collections.singletonList("ERP-BOM*"));
        headers.add(Collections.singletonList("生产BOM*"));
        headers.add(Collections.singletonList("优先级*"));
        headers.add(Collections.singletonList("生效时间"));
        headers.add(Collections.singletonList("失效时间"));

        EasyExcel.write(out)
                .sheet("物料替代关系")
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

}