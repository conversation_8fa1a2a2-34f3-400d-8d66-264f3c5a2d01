package com.yhl.scp.mds.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.bom.service.ProductAboutBomService;
import com.yhl.scp.mds.deleteGroup.service.MdsDeleteGroupsService;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.routing.service.ProductRoutingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MultiProductJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-21 16:39:22
 */
@Component
@Slf4j
public class MultiProductJob {

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MdsDeleteGroupsService mdsDeleteGroupsService;
    @Resource
    private ProductRoutingService productRoutingService;
    @Resource
    private ProductAboutBomService productAboutBomService;
    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;
    @XxlJob("multiProductJob")
    private ReturnT<String> multiProductJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的多个生产模块任务job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            productRoutingService.syncProductRoutings(scenario.getTenantId());
            mdsDeleteGroupsService.syncDeleteGroups(scenario.getTenantId());
            productAboutBomService.syncData(scenario.getTenantId(), null, null, null, scenario.getDataBaseName());
            mdsDeleteGroupsService.syncDeleteGroups(scenario.getTenantId());
            productCandidateResourceTimeService.syncMoldChangeTime(scenario.getTenantId());
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步多个生产模块任务job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
