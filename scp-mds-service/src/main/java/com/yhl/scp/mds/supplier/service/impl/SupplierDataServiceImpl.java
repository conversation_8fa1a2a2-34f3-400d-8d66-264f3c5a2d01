package com.yhl.scp.mds.supplier.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.supplier.domain.entity.SupplierDO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierDTO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.convertor.NewProductStockPointConvertor;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.supplier.convertor.SupplierConvertor;
import com.yhl.scp.mds.supplier.convertor.SupplierDataConvertor;
import com.yhl.scp.mds.supplier.domain.service.SupplierDataDomainService;
import com.yhl.scp.mds.supplier.dto.SupplierAddressDTO;
import com.yhl.scp.mds.supplier.dto.SupplierExportDTO;
import com.yhl.scp.mds.supplier.infrastructure.dao.SupplierAddressDao;
import com.yhl.scp.mds.supplier.infrastructure.dao.SupplierDataDao;
import com.yhl.scp.mds.supplier.infrastructure.po.SupplierAddressPO;
import com.yhl.scp.mds.supplier.service.SupplierAddressService;
import com.yhl.scp.mds.supplier.service.SupplierDataService;
import com.yhl.scp.mds.supplier.service.SupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>SupplierServiceImpl</code>
 * <p>
 * 供应商主数据及地址状态
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-09 16:19:48
 */
@Slf4j
@Service
public class SupplierDataServiceImpl extends AbstractService implements SupplierDataService {

    @Resource
    private SupplierDataDao supplierDataDao;

    @Resource
    private SupplierDataDomainService supplierDataDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private SupplierService supplierService;

    @Resource
    private SupplierAddressService supplierAddressService;

    @Resource
    private SupplierAddressDao supplierAddressDao;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private IpsNewFeign ipsNewFeign;

/*    @Resource
    private ProductionOrganizationService productionOrganizationService;*/

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SupplierDTO supplierDTO) {
        // 0.数据转换
        SupplierDO supplierDO = SupplierDataConvertor.INSTANCE.dto2Do(supplierDTO);
        SupplierPO supplierPO = SupplierDataConvertor.INSTANCE.dto2Po(supplierDTO);
        // 1.数据校验
        supplierDataDomainService.validation(supplierDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(supplierPO);
        supplierDataDao.insert(supplierPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SupplierDTO supplierDTO) {
        // 0.数据转换
        SupplierDO supplierDO = SupplierDataConvertor.INSTANCE.dto2Do(supplierDTO);
        SupplierPO supplierPO = SupplierDataConvertor.INSTANCE.dto2Po(supplierDTO);
        // 1.数据校验
        supplierDataDomainService.validation(supplierDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(supplierPO);
        supplierDataDao.update(supplierPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SupplierDTO> list) {
        List<SupplierPO> newList = SupplierDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        supplierDataDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SupplierDTO> list) {
        List<SupplierPO> newList = SupplierDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        supplierDataDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return supplierDataDao.deleteBatch(idList);
        }
        return supplierDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SupplierVO selectByPrimaryKey(String id) {
        SupplierPO po = supplierDataDao.selectByPrimaryKey(id);
        return SupplierDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_sup_supplier")
    public List<SupplierVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_sup_supplier")
    public List<SupplierVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SupplierVO> dataList = supplierDataDao.selectByCondition(sortParam, queryCriteriaParam);
        SupplierDataServiceImpl target = springBeanUtils.getBean(SupplierDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SupplierVO> selectByParams(Map<String, Object> params) {
        List<SupplierPO> list = supplierDataDao.selectByParams(params);
        return SupplierDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SupplierVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }


    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SUPPLIER.getCode();
    }

    @Override
    public List<SupplierVO> invocation(List<SupplierVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList) && params != null && !params.isEmpty()) {
            dataList = this.selectByParams(params);
        }
        // 冗余生产组织字段
        //productionOrganizationService.addOrganizationColumn(dataList, invocation, "setOrganization", "ids", "organizationId", "id");
        return dataList;
    }

    @Override
    public void addSupplierColumn(List<? extends BaseVO> dataList, String invocation, String fieldName, String paramName, String relationKey, String relationObjectColumn) {
        List<String> relationKeys = dataList.stream().map(item -> {
            try {
                return BeanUtils.getProperty(item, relationKey);
            } catch (Exception e) {
                throw new BusinessException(e.toString());
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(relationKeys)) {
            Map<String, Object> params = new HashMap<>();
            params.put(paramName, relationKeys);
            assembleSupplier(dataList, params, invocation, fieldName, relationKey, relationObjectColumn);
        }
    }

    private void assembleSupplier(List<? extends BaseVO> datas, Map<String, Object> params, String invocation, String fieldName, String relationKey, String relationObjectColumn) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }
        SupplierDataServiceImpl target = springBeanUtils.getBean("supplierDataServiceImpl");
        List<SupplierVO> supplierVOS = target.invocation(null, params, invocation + this.getInvocationName());
        if (CollectionUtils.isEmpty(supplierVOS)) {
            return;
        }
        Map<String, SupplierVO> supplierVOMap = supplierVOS.stream().collect(Collectors.toMap(item -> {
            try {
                return BeanUtils.getProperty(item, StringUtils.isEmpty(relationObjectColumn) ? relationKey : relationObjectColumn);
            } catch (Exception e) {
                throw new BusinessException(e.toString());
            }
        }, v -> v));
        Class<?> clazz = datas.get(0).getClass();
        Method setSupplier;
        try {
            setSupplier = clazz.getMethod(fieldName, SupplierVO.class);
            for (Object data : datas) {
                setSupplier.invoke(data, supplierVOMap.get(BeanUtils.getProperty(data, relationKey)));
            }
        } catch (Exception e) {
            throw new BusinessException(e.toString());
        }
    }

    @Override
    public void export(HttpServletResponse response) {
        List<SupplierVO> supplierVOS = supplierDataDao.selectByCondition(null, null);
        List<SupplierExportDTO> exportDTOS = new ArrayList<>();
        for (SupplierVO supplierVO : supplierVOS) {
            SupplierExportDTO exportDTO = new SupplierExportDTO();
            org.springframework.beans.BeanUtils.copyProperties(supplierVO, exportDTO);
            exportDTO.setEnabled(YesOrNoEnum.YES.getCode().equals(supplierVO.getEnabled()) ? YesOrNoEnum.YES.getDesc() : YesOrNoEnum.NO.getDesc());
            exportDTOS.add(exportDTO);
        }
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("供应商主数据.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), SupplierExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("供应商主数据")
                    .doWrite(exportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public BaseResponse<Void> syncSupplier(String tenantId) {
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> stockPointVOList =
                newStockPointService.selectByParams(
                        ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),"organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()));
        Map<String, NewStockPointVO> stockPointVOListMap = stockPointVOList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                        (value1, value2) -> value1));
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario.getData(),
                StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode(), "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        NewStockPointVO newStockPointVO = stockPointVOListMap.get(rangeData);
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("orgId", newStockPointVO.getOrganizeId());
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.SUPPLIER.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleSupplier(List<ErpSupplier> erpSuppliers) {
        if (CollectionUtils.isEmpty(erpSuppliers)) {
            return BaseResponse.success();
        }
        List<SupplierPO> insertPoS = new ArrayList<>();
        List<SupplierPO> updatePoS = new ArrayList<>();
        List<SupplierAddressDTO> insertAddressDTOS = new ArrayList<>();
        List<SupplierAddressDTO> updateAddressDTOS = new ArrayList<>();
        List<String> supplierSiteIds =
                erpSuppliers.stream().map(ErpSupplier::getSupplierSiteId).collect(Collectors.toList());

        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("supplierSiteIds", supplierSiteIds);
        List<SupplierAddressPO> supplierAddressPOS = supplierAddressDao.selectByParams(map);
        Map<String, SupplierAddressPO> supplierAddressPosMap = CollectionUtils.isEmpty(supplierAddressPOS) ?
                MapUtil.newHashMap() :
                supplierAddressPOS.stream().collect(
                        Collectors.toMap(SupplierAddressPO::getSupplierSiteId, Function.identity(), (v1, v2) -> v1));
        List<SupplierPO> oldPos = supplierDataDao.selectBySupplierIds(erpSuppliers);
        Map<String, SupplierPO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getOrganizationId() + "_" + t.getSupplierId(),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, SupplierPO> supplierPOMap = new HashMap<>();

        for (ErpSupplier erpSupplier : erpSuppliers) {
            SupplierPO po = new SupplierPO();
            SupplierAddressDTO supplierAddressDTO = new SupplierAddressDTO();
            String id =
                    erpSupplier.getOrgId() + "_" + erpSupplier.getSupplierId();

            if (oldPosMap.containsKey(id) && !supplierPOMap.containsKey(id)) {
                SupplierPO oldPo = oldPosMap.get(id);
                org.springframework.beans.BeanUtils.copyProperties(oldPo, po);
                generatePo(erpSupplier, po);
                supplierPOMap.put(id, po);
                updatePoS.add(po);
            } else if (!supplierPOMap.containsKey(id)) {
                generatePo(erpSupplier, po);
                po.setId(UUIDUtil.getUUID());
                supplierPOMap.put(id, po);
                insertPoS.add(po);
            }
            if (supplierAddressPosMap.containsKey(erpSupplier.getSupplierSiteId())) {
                SupplierAddressPO oldSupplierAddressPO = supplierAddressPosMap.get(erpSupplier.getSupplierSiteId());
                org.springframework.beans.BeanUtils.copyProperties(oldSupplierAddressPO, supplierAddressDTO);
                generateAddress(supplierPOMap, erpSupplier, supplierAddressDTO, id);
                updateAddressDTOS.add(supplierAddressDTO);
            } else {
                generateAddress(supplierPOMap, erpSupplier, supplierAddressDTO, id);
                insertAddressDTOS.add(supplierAddressDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertPoS)) {
            BasePOUtils.insertBatchFiller(insertPoS);
            supplierDataDao.insertBatchWithPrimaryKey(insertPoS);
        }
        if (CollectionUtils.isNotEmpty(updatePoS)) {
            BasePOUtils.updateBatchFiller(updatePoS);
            supplierDataDao.updateBatchSelective(updatePoS);
        }
        if (CollectionUtils.isNotEmpty(insertAddressDTOS)) {
            supplierAddressService.doCreateBatch(insertAddressDTOS);
        }
        if (CollectionUtils.isNotEmpty(updateAddressDTOS)) {
            supplierAddressService.doUpdateBatch(updateAddressDTOS);
        }
        return BaseResponse.success();
    }

    private void generateAddress(Map<String, SupplierPO> supplierPOMap, ErpSupplier erpSupplier, SupplierAddressDTO supplierAddressDTO, String id) {
        supplierAddressDTO.setAddress(erpSupplier.getSupplierSiteCode());
        supplierAddressDTO.setSupplierId(supplierPOMap.get(id).getId());
        supplierAddressDTO.setSupplierSiteId(erpSupplier.getSupplierSiteId());
        supplierAddressDTO.setSiteExpiryTime(erpSupplier.getSiteExpiration());
        supplierAddressDTO.setHeadExpiryTime(erpSupplier.getHeadExpiration());
        supplierAddressDTO.setPurchasingFlag("Y".equals(erpSupplier.getPurchasingFlag())?YesOrNoEnum.YES.getCode():
                YesOrNoEnum.NO.getCode());
    }

    private void generatePo(ErpSupplier erpSupplier, SupplierPO po) {
        po.setOrganizationId(erpSupplier.getOrgId());
        po.setSupplierCode(erpSupplier.getSupplierCode());
        po.setSupplierName(erpSupplier.getSupplierName());
        po.setHeadExpiryTime(erpSupplier.getHeadExpiration());
        po.setSupplierId(erpSupplier.getSupplierId());
        po.setSupplierAbbreviation(erpSupplier.getSupplierType());
    }


    @Override
    public List<LabelValue<String>> supplyDropdown() {
    	List<SupplierVO> supplierVOList = supplierDataDao.selectSeaTransportation();
        if (CollectionUtils.isNotEmpty(supplierVOList)) {
        	return supplierVOList.stream()
                    .map(x -> new LabelValue<>(x.getSupplierName(), x.getSupplierCode()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    @Override
    public List<SupplierVO> selectSupplierLike(String supplierName) {
        List<SupplierPO> list =
                supplierDataDao.selectSupplierLike(StringUtils.isNotEmpty(supplierName) ?
                        StringConvertUtils.convertToLike(supplierName) : null);
        return SupplierConvertor.INSTANCE.po2Vos(list);
    }
}