package com.yhl.scp.mds.box.controller;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.box.dto.BoxInfoDTO;
import com.yhl.scp.mds.box.service.BoxInfoService;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.util.LabelValueThree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>BoxInfoController</code>
 * <p>
 * 箱体信息控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:36
 */
@Slf4j
@Api(tags = "箱体信息控制器")
@RestController
@RequestMapping("boxInfo")
public class BoxInfoController extends BaseController {

    @Resource
    private BoxInfoService boxInfoService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<BoxInfoVO>> page() {
        List<BoxInfoVO> boxInfoList = boxInfoService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<BoxInfoVO> pageInfo = new PageInfo<>(boxInfoList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody BoxInfoDTO boxInfoDTO) {
        return boxInfoService.doCreate(boxInfoDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody BoxInfoDTO boxInfoDTO) {
        return boxInfoService.doUpdate(boxInfoDTO);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<BoxInfoVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, boxInfoService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        boxInfoService.deleteBatchVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "箱子类型下拉")
    @GetMapping(value = "boxType/dropDown")
    public BaseResponse<List<LabelValue<String>>> boxTypeDropDown() {
        List<LabelValue<String>> result = boxInfoService.listBoxTypeDropDown();
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "箱子编码下拉")
    @GetMapping(value = "boxCode/dropDown")
    public BaseResponse<List<LabelValue<String>>> boxCodeDropDown(@RequestParam("boxType") String boxType) {
        List<LabelValue<String>> result = boxInfoService.listBoxCodeDropDown(boxType);
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "箱子编码下拉(无传参)")
    @GetMapping(value = "boxCodeDropDown")
    public BaseResponse<List<LabelValueThree<String>>> boxCodeDropDown() {
        List<BoxInfoVO> boxInfoVOS = boxInfoService.boxCodeDropDown();
        List<LabelValueThree<String>> list = boxInfoVOS.stream().map(boxInfoVO -> {
            LabelValueThree<String> labelValueThree = new LabelValueThree<>(boxInfoVO.getBoxCode());
            labelValueThree.setLabel(boxInfoVO.getId());
            labelValueThree.setValue(boxInfoVO.getBoxCode());
            labelValueThree.setName(boxInfoVO.getBoxType());
            return labelValueThree;
        }).collect(Collectors.toList());
        return BaseResponse.success(list);
    }

    @ApiOperation(value = "箱体信息同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncBoxInfo() {
        return boxInfoService.syncStockPoints(SystemHolder.getTenantCode(),"324");
    }
}
