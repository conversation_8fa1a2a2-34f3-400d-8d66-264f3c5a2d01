FROM openjdk:8-alpine
LABEL maintainer="feiyuming"

# 更换镜像源，创建字体目录
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories \
    && mkdir -p /usr/share/fonts/wqy \
    && chmod 755 /usr/share/fonts/wqy  # 确保目录有执行权限

# 关键修复：使用raw链接下载真实字体文件（原链接是GitHub网页，非字体文件）
RUN wget -O /usr/share/fonts/wqy/wqy-microhei.ttc https://raw.githubusercontent.com/anthonyfok/fonts-wqy-microhei/master/wqy-microhei.ttc \
    && chmod 644 /usr/share/fonts/wqy/wqy-microhei.ttc  # 确保字体文件可读

# 安装依赖（保持原依赖，增加字体相关验证工具）
RUN apk update \
    && apk --no-cache add \
        curl \
        busybox-extras \
        tini \
        fontconfig \
        freetype \
        harfbuzz \
        ttf-dejavu \
        tzdata \
        libc6-compat \
        file  # 用于验证字体文件类型

# 验证字体文件是否正确（调试用，可保留）
RUN file /usr/share/fonts/wqy/wqy-microhei.ttc

# 重建字体缓存（确保在字体文件正确后执行）
RUN fc-cache -fv /usr/share/fonts \
    && fc-list | grep -i "wqy-microhei"  # 验证缓存是否生效

# 时区和兼容性配置（保持原样）
RUN ln -s /lib/libc.musl-x86_64.so.1 /lib/ld-linux-x86-64.so.2 \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

ENV LANG=zh_CN.UTF-8 \
    TZ=Asia/Shanghai

VOLUME /tmp
WORKDIR /home/<USER>
COPY --from=hengyunabc/arthas:latest /opt/arthas opt/arthas
ENTRYPOINT ["/sbin/tini", "--"]