---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpim-xxx-service
  namespace: bpim-xxx-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bpim-xxx-service
  template:
    metadata:
      name: bpim-xxx-service
      labels:
        app: bpim-xxx-service
        tier: backend
      namespace: bpim-xxx-namespace
    spec:
      terminationGracePeriodSeconds: 240
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values: [ "bpim-xxx-service" ]
                  - key: tier
                    operator: In
                    values: [ "backend" ]
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: bpim-xxx-service
          image: bpim-xxx-image
          resources:
            requests:
              memory: "k8s-request-memory"
              cpu: "k8s-request-cpu"
            limits:
              memory: "k8s-limits-memory"
              cpu: "k8s-limits-cpu"
          imagePullPolicy: Always
          ports:
            - name: server-port
              containerPort: 8760
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 600
            periodSeconds: 30
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 90
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
      volumes:
        - name: sw-agent
          emptyDir: { }
        - name: bpim-data-volume
          persistentVolumeClaim:
            claimName: bpim-xxx-pvc
      imagePullSecrets:
        - name: harbor-registry
---
apiVersion: v1
kind: Service
metadata:
  namespace: bpim-xxx-namespace
  name: bpim-xxx-service
  labels:
    app: bpim-xxx-service
spec:
  ports:
    - name: server
      protocol: TCP
      port: 8760
      targetPort: 8760
  selector:
    app: bpim-xxx-service
---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: back-ingress
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: 10240m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-next-upstream: "off"
  namespace: bpim-xxx-namespace
spec:
  rules:
    - host: bpim-xxx-host
      http:
        paths:
          - path: /
            backend:
              serviceName: bpim-xxx-service
              servicePort: 8760
          - path: /job
            backend:
              serviceName: bpim-job-server
              servicePort: 8080