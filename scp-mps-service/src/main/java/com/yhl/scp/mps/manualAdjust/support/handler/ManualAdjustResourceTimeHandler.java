package com.yhl.scp.mps.manualAdjust.support.handler;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.basic.resource.vo.PhysicalResourceBasicVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.enums.ManualAdjustTypeEnum;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustSource;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustTarget;
import com.yhl.scp.mps.manualAdjust.support.ManualAdjustSupport;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.vo.OperationSubTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 手动调整资源时间处理器
 *
 * <AUTHOR>
 */
@Component
public class ManualAdjustResourceTimeHandler extends ManualAdjustSupport {
  @Override
  protected String getCommand() {
    return ManualAdjustTypeEnum.ADJUST_RESOURCE_TIME.getCode();
  }

  @Override
  protected BaseResponse<Void> verify(ManualAdjustParam param) {
    ManualAdjustSource sourceInfo = param.getSourceInfo();
    List<String> operationIds = param.getOperationIds();
    if (CollectionUtils.isEmpty(operationIds)) {
      return BaseResponse.error("工序ID集合参数错误");
    }
    if (Objects.isNull(sourceInfo)) {
      return BaseResponse.error("工序源信息参数错误");
    }
    ManualAdjustTarget targetInfo = param.getTargetInfo();
    if (Objects.isNull(targetInfo)) {
      return BaseResponse.error("工序目标信息参数错误");
    }
    String sourceInfoResourceId = sourceInfo.getResourceId();
    if (StringUtils.isBlank(sourceInfoResourceId)) {
      return BaseResponse.error("工序源资源ID参数错误");
    }
    String sourceInfoOperationId = sourceInfo.getOperationId();
    if (StringUtils.isBlank(sourceInfoOperationId)) {
      return BaseResponse.error("工序源工序ID参数错误");
    }
    String sourcePosition = sourceInfo.getPosition();
    if (StringUtils.isBlank(sourcePosition)) {
      return BaseResponse.error("工序源位置参数错误");
    }
    String targetInfoResourceId = targetInfo.getResourceId();
    if (StringUtils.isBlank(targetInfoResourceId)) {
      return BaseResponse.error("工序目标资源ID参数错误");
    }
    String appointStartTime = targetInfo.getAppointStartTime();
    if (StringUtils.isBlank(appointStartTime)) {
      return BaseResponse.error("指定开始时间参数错误");
    }
    if (existCompleteWorkOrder(targetInfo)) {
      return BaseResponse.error("指定时间后有完工工序，禁止操作");
    }
    return BaseResponse.success("校验成功");
  }

  /**
   * 判断目指定时间后是否有完工工序
   *
   * @param targetInfo
   * @return
   */
  private Boolean existCompleteWorkOrder(ManualAdjustTarget targetInfo) {
    String resourceId = targetInfo.getResourceId();
    List<OperationSubTaskVO> subTasks =
        manualAdjustHandleDao.selectSubProcessTaskByResourceId(resourceId);
    if (CollectionUtils.isEmpty(subTasks)) {
      return false;
    }
    Date appointStartTime =
        DateUtils.stringToDate(targetInfo.getAppointStartTime(), DateUtils.COMMON_DATE_STR1);
    PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
    Date planStartTime = planningHorizon.getPlanStartTime();
    if (appointStartTime.before(planStartTime)) {
      appointStartTime = planStartTime;
      targetInfo.setAppointStartTime(
          DateUtils.dateToString(appointStartTime, DateUtils.COMMON_DATE_STR1));
    }
    Date finalAppointStartTime = appointStartTime;
    long completeCount =
        subTasks.stream()
            .filter(x -> !x.getStartTime().before(finalAppointStartTime))
            .filter(x -> PlannedStatusEnum.FINISHED.getCode().equals(x.getRemark()))
            .count();
    return completeCount > 0;
  }

  @Override
  protected BaseResponse<Void> executeBackEnd(ManualAdjustParam param) {
    //    ManualAdjustSource sourceInfo = param.getSourceInfo();
    //    // 源工序
    //    String sourceOperationId = sourceInfo.getOperationId();
    //    ManualAdjustTarget targetInfo = param.getTargetInfo();
    //    String targetResourceId = targetInfo.getResourceId();
    //    // 目标指定时间
    //    String appointStartTime = targetInfo.getAppointStartTime();
    //    String targetOperationId =
    //        manualAdjustHandleDao.selectFirstOperationByAppointStartTime(
    //            targetResourceId, appointStartTime);
    //    boolean existTargetOperation = StringUtils.isNotBlank(targetOperationId);
    //    if (!existTargetOperation) {
    //      targetOperationId =
    // manualAdjustHandleDao.selectLastOperationByResourceId(targetResourceId);
    //    }
    //    Map<String, Object> workOrderParam = MapUtil.newHashMap();
    //    List<WorkOrderPO> workOrders = workOrderDao.selectByParams(workOrderParam);
    //    Map<String, WorkOrderPO> workOrderMap =
    //        CollectionUtils.isEmpty(workOrders)
    //            ? MapUtil.newHashMap()
    //            : workOrders.stream()
    //                .collect(Collectors.toMap(WorkOrderPO::getId, Function.identity(), (v1, v2) ->
    // v1));
    //    // 调整工序
    //    List<String> operationIds = param.getOperationIds();
    //    // 获取大小片的关联工序
    //    operationIds = manualAdjustAlgorithmService.getBigAndSmallOperationIds(operationIds);
    //    List<String> totalOperationIds = Lists.newArrayList();
    //    totalOperationIds.addAll(operationIds);
    //    if (StringUtils.isNotBlank(sourceOperationId)) {
    //      totalOperationIds.add(sourceOperationId);
    //    }
    //    if (StringUtils.isNotBlank(targetOperationId)) {
    //      totalOperationIds.add(targetOperationId);
    //    }
    //    List<OperationPO> operations = operationDao.selectByPrimaryKeys(totalOperationIds);
    //    Map<String, OperationPO> operationMap =
    //        CollectionUtils.isEmpty(operations)
    //            ? MapUtil.newHashMap()
    //            : operations.stream()
    //                .collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) ->
    // v1));
    //    // 获取最小的订单顺序
    //    AtomicInteger minOrderSequence = new AtomicInteger(Integer.MAX_VALUE);
    //    List<WorkOrderPO> adjustWorkOrders = Lists.newArrayList();
    //    for (String calcOperationId : totalOperationIds) {
    //      OperationPO calcOperation = operationMap.get(calcOperationId);
    //      String calcOrderId = calcOperation.getOrderId();
    //      WorkOrderPO calcWorkOrder = workOrderMap.get(calcOrderId);
    //      Integer calcOrderSequence = calcWorkOrder.getOrderSequence();
    //      if (calcOrderSequence < minOrderSequence.get()) {
    //        minOrderSequence.set(calcOrderSequence);
    //      }
    //      if (operationIds.contains(calcOperationId)) {
    //        adjustWorkOrders.add(calcWorkOrder);
    //      }
    //    }
    //    // 所有受影响的订单
    //    List<WorkOrderPO> filterWorkOrders =
    //        workOrders.stream()
    //            .filter(x -> x.getOrderSequence() >= minOrderSequence.get())
    //            .sorted(Comparator.comparing(WorkOrderPO::getOrderSequence))
    //            .collect(Collectors.toList());
    //    if (CollectionUtils.isEmpty(filterWorkOrders)) {
    //      return BaseResponse.error("拖拽筛选订单异常");
    //    }
    //    // 去除调整工序
    //    filterWorkOrders.removeAll(adjustWorkOrders);
    //    OperationPO targetOperation = operationMap.get(targetOperationId);
    //    String targetOrderId = targetOperation.getOrderId();
    //    WorkOrderPO targetWorkOrder = workOrderMap.get(targetOrderId);
    //    int index = filterWorkOrders.indexOf(targetWorkOrder);
    //    // 剩余订单重新调整顺序
    //    filterWorkOrders.addAll(existTargetOperation ? index : index + 1, adjustWorkOrders);
    //    for (WorkOrderPO filterWorkOrder : filterWorkOrders) {
    //      filterWorkOrder.setOrderSequence(minOrderSequence.getAndIncrement());
    //    }
    //    List<List<WorkOrderPO>> partitions =
    //        com.google.common.collect.Lists.partition(filterWorkOrders, 2000);
    //    for (List<WorkOrderPO> partition : partitions) {
    //      manualAdjustHandleDao.updateWorkOrderSequences(partition);
    //    }
    return BaseResponse.success("指定资源时间成功");
  }

  @Override
  protected List<AdjustmentParam> executeAlgorithm(
      PlanningHorizonVO planningHorizon, ManualAdjustParam param) {
    Map<String, PhysicalResourceVO> physicalResourceVOMap = MapUtil.newHashMap();
    Map<String, StandardResourceVO> standardResourceVOMap = MapUtil.newHashMap();
    this.setResourceMap(param, physicalResourceVOMap, standardResourceVOMap);
    OperationPO adjustOperation = operationDao.selectByPrimaryKey(param.getOperationIds().get(0));
    // 被调整的是已计划的连续炉父工序
    if (manualAdjustAlgorithmService.checkHWOperation(
        param, adjustOperation, physicalResourceVOMap, standardResourceVOMap)) {
      List<RzzAdjustmentParam> adjustParams =
          manualAdjustAlgorithmService.getPlannedAdjustParams(adjustOperation, param);
      return manualAdjustAlgorithmService.getHandworkScheduleBatchParams(
          planningHorizon, adjustParams);
    }
    return this.getHandworkScheduleParams(toRzzAdjustmentParam(adjustOperation, param));
  }

  /**
   * 将手动调整参数转化为RzzAdjustmentParam
   *
   * @param adjustOperation
   * @param param
   * @return
   */
  private RzzAdjustmentParam toRzzAdjustmentParam(
      OperationPO adjustOperation, ManualAdjustParam param) {
    RzzAdjustmentParam adjustmentParam = new RzzAdjustmentParam();
    adjustmentParam.setOperationId(adjustOperation.getId());
    adjustmentParam.setQty(adjustOperation.getQuantity());
    adjustmentParam.setSourceResourceId(adjustOperation.getPlannedResourceId());
    adjustmentParam.setTargetResourceId(param.getTargetInfo().getResourceId());
    adjustmentParam.setAppointStartTime(param.getTargetInfo().getAppointStartTime());
    adjustmentParam.setAdjustQuantityFlag(false);
    return adjustmentParam;
  }

  public List<AdjustmentParam> getHandworkScheduleParams(RzzAdjustmentParam adjustmentParam) {
    String subOperationId = adjustmentParam.getOperationId();
    // 查询原来的subOperation
    OperationVO subOperationVO = operationService.selectVOByPrimaryKey(subOperationId);
    // 查询原来的operationTask,上面有排产资源
    String parentId = subOperationVO.getParentId();
    List<AdjustmentParam> adjustmentParamList = Lists.newArrayList();
    List<OperationVO> adjustOperationList = Lists.newArrayList();
    PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
    List<String> operationIds = null;
    // 多层bom,大小片要一起进行手工调整
    WorkOrderVO workOrderVO = workOrderService.selectByPrimaryKey(subOperationVO.getOrderId());
    if (StringUtils.isNotBlank(workOrderVO.getTopOrderId())) {
      List<WorkOrderVO> workOrderVOS =
          workOrderService.selectByParentIds(
              Collections.singletonList(workOrderVO.getTopOrderId()));
      WorkOrderVO orderVO =
          workOrderVOS.stream()
              .filter(t -> !workOrderVO.getId().equals(t.getId()))
              .findFirst()
              .orElse(null);
      if (orderVO != null) {
        List<OperationVO> operationVOS =
            operationService.selectByWorkOrderIds(Collections.singletonList(orderVO.getId()));
        List<OperationVO> collect =
            operationVOS.stream()
                .filter(
                    t ->
                        subOperationVO
                            .getRoutingStepSequenceNo()
                            .equals(t.getRoutingStepSequenceNo()))
                .collect(Collectors.toList());
        if (StringUtils.isBlank(subOperationVO.getParentId())) {
          collect =
              collect.stream()
                  .filter(t -> StringUtils.isBlank(t.getParentId()))
                  .collect(Collectors.toList());
        } else {
          collect =
              collect.stream()
                  .filter(t -> StringUtils.isNotBlank(t.getParentId()))
                  .collect(Collectors.toList());
        }
        operationIds = collect.stream().map(OperationVO::getId).collect(Collectors.toList());
        operationIds.add(subOperationVO.getId());
        adjustOperationList.addAll(collect);
      }
    }
    // 调整的是连续炉
    if (StringUtils.isBlank(parentId)
        && PlannedStatusEnum.PLANNED.getCode().equals(subOperationVO.getPlanStatus())) {
      List<OperationVO> unPlanParams =
          manualAdjustAlgorithmService.getLxlPlanParams(
              adjustmentParam, subOperationVO, adjustmentParamList);
      // adjustOperationList.addAll(unPlanParams);
      operationIds = unPlanParams.stream().map(OperationVO::getId).collect(Collectors.toList());
    }
    boolean qtyChangeFlag = false;
    boolean timeOrResourceChangeFlag = true;
    // 排产数量发生了变化
    if (adjustmentParam.getQty().compareTo(subOperationVO.getQuantity()) != 0) {
      qtyChangeFlag = true;
    }
    OperationVO operationVO =
        adjustOperationList.stream()
            .filter(t -> subOperationVO.getId().equals(t.getId()))
            .findFirst()
            .orElse(null);
    if (operationVO == null) {
      adjustOperationList.add(subOperationVO);
    }
    if (CollectionUtils.isEmpty(operationIds)) {
      operationIds = Collections.singletonList(subOperationVO.getId());
    }
    // 走手工调整
    adjustmentParamList =
        manualAdjustAlgorithmService.getAdjustmentParams(
            planningHorizon,
            adjustmentParam,
            operationIds,
            subOperationVO,
            adjustmentParamList,
            qtyChangeFlag,
            timeOrResourceChangeFlag);
    return manualAdjustAlgorithmService.mergeAdjustmentParamList(adjustmentParamList);
  }

  private void setResourceMap(
      ManualAdjustParam manualAdjustParam,
      Map<String, PhysicalResourceVO> physicalResourceVOMap,
      Map<String, StandardResourceVO> standardResourceVOMap) {
    List<String> resourceIds = Lists.newArrayList();
    if (StringUtils.isNotBlank(manualAdjustParam.getSourceInfo().getResourceId())) {
      resourceIds.add(manualAdjustParam.getSourceInfo().getResourceId());
    }
    if (StringUtils.isNotBlank(manualAdjustParam.getTargetInfo().getResourceId())) {
      resourceIds.add(manualAdjustParam.getTargetInfo().getResourceId());
    }
    resourceIds = operationTaskExtDao.selectPhysicalResourceByOperationTask();
    if (CollectionUtils.isEmpty(resourceIds)) {
      return;
    }
    List<PhysicalResourceVO> physicalResourceVOS =
        newMdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), resourceIds);
    physicalResourceVOMap.putAll(
        physicalResourceVOS.stream()
            .collect(
                Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1)));
    List<String> standardResourceIds =
        physicalResourceVOS.stream()
            .map(PhysicalResourceBasicVO::getStandardResourceId)
            .distinct()
            .collect(Collectors.toList());
    List<StandardResourceVO> standardResourceVOS =
        newMdsFeign.selectStandardResourceVOSByParams(
            SystemHolder.getScenario(), ImmutableMap.of("ids", standardResourceIds));
    standardResourceVOMap.putAll(
        standardResourceVOS.stream()
            .collect(
                Collectors.toMap(StandardResourceVO::getId, Function.identity(), (v1, v2) -> v1)));
  }
}
