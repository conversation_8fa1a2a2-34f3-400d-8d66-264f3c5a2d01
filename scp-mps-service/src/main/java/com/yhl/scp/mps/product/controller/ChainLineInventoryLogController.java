package com.yhl.scp.mps.product.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.service.ChainLineInventoryLogService;
import com.yhl.scp.mps.product.vo.ChainLineInventoryLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>ChainLineInventoryLogController</code>
 * <p>
 * 链式生产线_中间表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-08 16:28:15
 */
@Slf4j
@Api(tags = "链式生产线_中间表控制器")
@RestController
@RequestMapping("chainLineInventoryLog")
public class ChainLineInventoryLogController extends BaseController {

    @Resource
    private ChainLineInventoryLogService chainLineInventoryLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ChainLineInventoryLogVO>> page() {
        List<ChainLineInventoryLogVO> chainLineInventoryLogList = chainLineInventoryLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ChainLineInventoryLogVO> pageInfo = new PageInfo<>(chainLineInventoryLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ChainLineInventoryLogDTO chainLineInventoryLogDTO) {
        return chainLineInventoryLogService.doCreate(chainLineInventoryLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ChainLineInventoryLogDTO chainLineInventoryLogDTO) {
        return chainLineInventoryLogService.doUpdate(chainLineInventoryLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        chainLineInventoryLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ChainLineInventoryLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, chainLineInventoryLogService.selectByPrimaryKey(id));
    }


    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncChainLine() {
        return chainLineInventoryLogService.syncChainLine(SystemHolder.getTenantCode());
    }
}
