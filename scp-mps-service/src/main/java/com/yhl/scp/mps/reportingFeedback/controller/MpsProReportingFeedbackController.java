package com.yhl.scp.mps.reportingFeedback.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mps.reportingFeedback.convertor.MpsProReportingFeedbackConvertor;
import com.yhl.scp.mps.reportingFeedback.dto.MpsProReportingFeedbackDTO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.mps.reportingFeedback.vo.MpsProReportingFeedbackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MpsProReportingFeedbackController</code>
 * <p>
 * 生产报工反馈控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-03 10:32:00
 */
@Slf4j
@Api(tags = "生产报工反馈控制器")
@RestController
@RequestMapping("mpsProReportingFeedback")
public class MpsProReportingFeedbackController extends BaseController {

    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MpsProReportingFeedbackVO>> page() {
        List<MpsProReportingFeedbackVO> mpsProReportingFeedbackList = mpsProReportingFeedbackService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MpsProReportingFeedbackVO> pageInfo = new PageInfo<>(mpsProReportingFeedbackList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MpsProReportingFeedbackDTO mpsProReportingFeedbackDTO) {
        return mpsProReportingFeedbackService.doCreate(mpsProReportingFeedbackDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MpsProReportingFeedbackDTO mpsProReportingFeedbackDTO) {
        return mpsProReportingFeedbackService.doUpdate(mpsProReportingFeedbackDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        mpsProReportingFeedbackService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MpsProReportingFeedbackVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mpsProReportingFeedbackService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "MES数据接入")
    @PostMapping(value = "mesDataAccess")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> insertBatch(@RequestBody List<MpsProReportingFeedbackDTO> mesProFeck) {
        mpsProReportingFeedbackService.doInsertBatch(mesProFeck);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "生产报工同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncProReportingFeedback() {
        return mpsProReportingFeedbackService.syncProReportingFeedback(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "转换方法测试")
    @PostMapping(value = "doConvert")
    public BaseResponse<Void> doConvert() {
        List<MpsProReportingFeedbackVO> mpsProReportingFeedbackVOS = mpsProReportingFeedbackService.selectAll();
        List<MpsProReportingFeedbackDTO> mpsProReportingFeedbackDTOS = MpsProReportingFeedbackConvertor.INSTANCE.vo2Dtos(mpsProReportingFeedbackVOS);
        mpsProReportingFeedbackService.doConvert(SystemHolder.getScenario(), mpsProReportingFeedbackDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
