package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.calendar.dto.ResourceCalendarRangeDTO;
import com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipExceptionDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmDataService;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.support.CapacityBalanceSpecialSupport;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.infrastructure.dao.OutsourceTransferSummaryDao;
import com.yhl.scp.mps.demand.vo.OutsourceTransferSummaryVO;
import com.yhl.scp.mps.enums.OutsourceStatusEnum;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.scp.mps.enums.ChangeTypeEnum.DEMAND_OUTSOURCING;
import static com.yhl.scp.mps.enums.ChangeTypeEnum.PROCESS_OUTSOURCING;

/**
 * <code>CapacityDayBalanceAlgorithmDataServiceImpl</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-31 12:35:06
 */
@Slf4j
@Service("CapacityDayBalanceAlgorithmDataServiceImpl")
public class CapacityDayBalanceAlgorithmDataServiceImpl extends CapacityBalanceBaseSupport implements CapacityBalanceAlgorithmDataService {

    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private CapacityLoadService capacityLoadService;
    @Resource
    private OutsourceTransferSummaryDao outsourceTransferSummaryDao;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    private CapacityBalanceSpecialSupport capacityBalanceSpecialSupport;


    private final static String pattern = "yyyy-MM-dd";
    private final static String MONTH_PATTERN = DateUtils.YEAR_MONTH;



    @Override
    public CapacityBalanceAlgorithmDataDTO capacityBalanceAlgorithmData(String scenario, String capacityPeriod, boolean changeSynchro, List<String> logList) {
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipDao.selectLockOrOutData();
        String LatestDataPlanPeriod = null;
        if (CollectionUtils.isNotEmpty(capacitySupplyRelationshipVOS)){
            LatestDataPlanPeriod = capacitySupplyRelationshipVOS.get(0).getPlanPeriod();
        }

        //capacityPeriod是展望期，即我们要计算的月份的跨度，它的取值可能是12，36（月）
        int capacityPeriodNum = Integer.parseInt(capacityPeriod);
        if (capacityPeriodNum <= 0){
            throw new BusinessException("展望期不能小于等于0");
        }
        Date lastMonth = getLastMonth(capacityPeriodNum);
        String endTime = DateUtils.dateToString(lastMonth, "yyyy-MM-dd");
        CapacityBalanceAlgorithmDataDTO algorithmDataDTO = new CapacityBalanceAlgorithmDataDTO();
        PlanningHorizonVO planningHorizonVO = getPlanningHorizonVO(scenario);
        //获取最新发货计划和一致性业务预测数据
        Date now = new Date();
        //发货计划是按天维度
        String planPeriod = DateUtils.dateToString(now, MONTH_PATTERN);
        List<DeliveryPlanVO2> deliveryPlanDataList = getDeliveryPlanData(planningHorizonVO);
        //一致性业务预测是按月维度
        if (changeSynchro && !planPeriod.equals(LatestDataPlanPeriod)){
            throw new BusinessException("一致性业务预测计划周期发生变化，请执行产能平衡计算！");
        }
        //planPeriod = "202409";
        algorithmDataDTO.setPlanPeriod(planPeriod);
        List<DeliveryPlanVO2> cleanForecastDataList = dfpFeign.selectConsistenceDemandForecastDataByPlanPeriod(scenario, planPeriod,null, endTime);
        //过滤计划期间开始时间之前的数据
        cleanForecastDataList = cleanForecastFilterData(cleanForecastDataList, planningHorizonVO);
        //发货计划的时间范围（按天）
        List<String> demandTimeDayList = new ArrayList<>();
        //发货计划的时间范围（按月）
        List<String> demandTimeMonthList = new ArrayList<>();
        //发货计划分解到每月
        Map<String, List<DeliveryPlanVO2>> demandTimeMonthMap = new HashMap<>();
        //发货计划分解到每天
        Map<String, List<DeliveryPlanVO2>> demandTimeDayMap = new HashMap<>();

        //一致性业务预测版本号
        String versionCode;
        algorithmDataDTO.setType(CapacityBalanceTypeEnum.DAY.getCode());

        if (CollectionUtils.isNotEmpty(deliveryPlanDataList)){
            deliveryPlanDataList = deliveryPlanDataList.stream().sorted(Comparator.comparing(DeliveryPlanVO2::getDemandTime)).collect(Collectors.toList());
            List<String> oemCodeList = deliveryPlanDataList.stream().map(DeliveryPlanVO2::getOemCode).distinct().collect(Collectors.toList());

            deliveryPlanDataHandle(deliveryPlanDataList, demandTimeDayList, demandTimeMonthList, demandTimeMonthMap, demandTimeDayMap);

            //获取该发货计划覆盖的产品
            List<String> prouctCodeList = deliveryPlanDataList.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cleanForecastDataList)){
                //获取主机厂编码
                oemCodeList.addAll(cleanForecastDataList.stream().map(DeliveryPlanVO2::getOemCode).distinct().collect(Collectors.toList()));
                oemCodeList = oemCodeList.stream().distinct().collect(Collectors.toList());
                versionCode = cleanForecastDataList.get(0).getVersionCode();
                algorithmDataDTO.setVersionCode(versionCode);
                prouctCodeList.addAll(cleanForecastDataList.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList()));
                prouctCodeList = prouctCodeList.stream().distinct().collect(Collectors.toList());
            }
            //本厂编码-主机厂编码对应关系
            Map<String, List<String>> productOfOemMap;
            List<DeliveryPlanVO2> allDataList = new ArrayList<>();
            allDataList.addAll(deliveryPlanDataList);
            allDataList.addAll(cleanForecastDataList);
            productOfOemMap = allDataList.stream()
                    .collect(Collectors.groupingBy(
                            DeliveryPlanVO2::getProductCode,
                            Collectors.mapping(DeliveryPlanVO2::getOemCode, Collectors.toSet())
                    ))
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> new ArrayList<>(entry.getValue())
                    ));
            algorithmDataDTO.setProductOfOemMap(productOfOemMap);
            //获取主机厂信息
            getOemInfo(oemCodeList, scenario, algorithmDataDTO);

            //确定发货计划的时间范围
            Collections.sort(demandTimeDayList);
            Collections.sort(demandTimeMonthList);
            String deliveryPlanStart = demandTimeDayList.get(0);
            Date deliveryPlanStartDate = DateUtils.stringToDate(deliveryPlanStart, pattern);
            String deliveryPlanEnd = demandTimeDayList.get(demandTimeDayList.size()-1);
            //整个发货计划横跨的天
            List<String> demandTimeList = getDemandTimeList(deliveryPlanStart, deliveryPlanEnd, pattern);
            //产能平衡要计算的时间范围(月)
            List<String> monthList = new ArrayList<>(demandTimeMonthList);
            //产能平衡要计算的时间范围(月)
            List<String> dayList = new ArrayList<>(demandTimeDayList);
            //全是发货计划的月份,demandTimeMonthList前面排过序
            List<String> allDeliveryPlanList = new ArrayList<>(monthList);

            //构建发货计划每天每个产品的数量
            Map<String, Map<String, Integer>> deliveryPlanDataMap = getDeliveryPlanDataMap(demandTimeList, demandTimeDayMap, prouctCodeList);

            //该map存的就是日期和该日期下产品的数量
            Map<String, Map<String, Integer>> newQty = new HashMap<>(deliveryPlanDataMap);

            //发货计划的次月就是全是预测数据的开始月份
            Date stringToDate = DateUtils.stringToDate(demandTimeMonthList.get(demandTimeMonthList.size() - 1), MONTH_PATTERN);
            Date moveMonth = DateUtils.moveMonth(stringToDate, 1);
            String allCleanForecastStart = DateUtils.dateToString(moveMonth, MONTH_PATTERN);

            List<String> allallCleanForecastMonthList = new ArrayList<>();
            //业务预测分解到每月
            Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap = new HashMap<>();
            cleanForecastDataHandle(cleanForecastDataList, cleanForecastDataMap, allCleanForecastStart, allallCleanForecastMonthList);
            //获取装车日历
            Map<String, Map<String, List<String>>> oemCodeOfCalendarMap = getOemCalendarNew(cleanForecastDataMap, oemCodeList, scenario);

            //发货计划最后一个月
            //确定要补充的业务预测时间范围，发货计划最后一天加一天开始，获取最后一月的最后一天作为结束
            Date date = DateUtils.stringToDate(deliveryPlanEnd, pattern);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date time = calendar.getTime();
            String lastDayOfMonth = getLastDayOfMonth(date);
            //需要需求补充的月份
            String needMoth;
            logList.add("发货计划覆盖范围" + deliveryPlanStart + '-' + deliveryPlanEnd);
            //正好发货日期是这个月的最后一天，就不存在一个月既有发货计划又有预测数据的情况
            if (!deliveryPlanEnd.equals(lastDayOfMonth)){
                //这个既有发货计划，又有预测数据的月份在前面已经加到monthList里了，不用再添加了
                needMoth = DateUtils.dateToString(time, MONTH_PATTERN);
            } else {
                needMoth = null;
            }
            if (needMoth != null){
                allDeliveryPlanList.remove(needMoth);
            }
            log.info("产能平衡计算deliveryPlanEnd{},lastDayOfMonth{},needMoth{}",deliveryPlanEnd, lastDayOfMonth, needMoth);
            logList.add("补充月份" + needMoth);
            //这个map存的是业务预测月份产品对应的每天的量
            Map<String,Map<String, Integer>> monthQty = new HashMap<>();
            //算出业务预测每月的拆分后每天的需求量
            for (Map.Entry<String, List<DeliveryPlanVO2>> entry : cleanForecastDataMap.entrySet()) {
                String month = entry.getKey();
                log.info("产能平衡计算拆分业务预测month{}",month);
                logList.add("产能平衡计算拆分业务预测月份" + month);
                getQtyByMonth(cleanForecastDataMap, month, prouctCodeList, oemCodeOfCalendarMap, monthQty, logList);
            }
            // 使用预测数据补充发货计划数据
            supplementaryData(logList, needMoth, deliveryPlanEnd, dayList, prouctCodeList, productOfOemMap, oemCodeOfCalendarMap, newQty, cleanForecastDataMap, deliveryPlanDataMap);

            // 补充展望期其他月份数据
            supplementTheDataOfTheOutlookPeriod(allallCleanForecastMonthList, dayList, prouctCodeList, productOfOemMap, oemCodeOfCalendarMap, newQty, cleanForecastDataMap);
            //到这里qty已经将发货计划，业务预测都加进来了，汇总成天-产品-需求量的维度
            dayList = dayList.stream().distinct().sorted().collect(Collectors.toList());
            algorithmDataDTO.setDayList(dayList);
            monthList.addAll(allallCleanForecastMonthList);
            monthList = monthList.stream().distinct().sorted().collect(Collectors.toList());
            algorithmDataDTO.setMonthList(monthList);
            //安全库存水位
            getSafetyStockLevel(scenario, prouctCodeList, monthList, newQty, deliveryPlanDataMap, monthQty, allDeliveryPlanList, needMoth, algorithmDataDTO, logList);
            //将数据量汇总到每周第一天，以减少数据量
            summarizeToEachWeek(monthList, newQty, deliveryPlanStartDate, deliveryPlanStart);
            //获取产能供应关系中被锁定的数据，在计算中要保留这些已锁定的数据，锁定这部分数据，提前把产能扣掉，然后把这些数据直接加到规则1得到的结果里
            Map<String, BigDecimal> lockQtyData  = getLockQtyData(capacitySupplyRelationshipVOS, algorithmDataDTO, newQty);

            //工序净需求拆解
            processNetDemandDisassembly(scenario, prouctCodeList, newQty, lockQtyData, algorithmDataDTO, logList);

            //获取设备资源相关信息
            capacityLoadService.getResourceNew(scenario, dayList, algorithmDataDTO, pattern);
            //获取设备生产资源关系，生产节拍，资源优先级
            getCandidateResourceTimeNew(algorithmDataDTO, scenario, logList);
            //获取设置的算法执行规则 (只有一条)
            getRule(algorithmDataDTO);

            //获取零件风险等级
            getMaterialRiskLevel(scenario, prouctCodeList, algorithmDataDTO);

            //处理特殊工艺
            capacityBalanceSpecialSupport.getSpecialOperation(prouctCodeList, scenario, algorithmDataDTO.getDayList(), newQty, algorithmDataDTO, pattern);
        }

        return algorithmDataDTO;
    }

    private void supplementTheDataOfTheOutlookPeriod(List<String> allallCleanForecastMonthList,
                                                     List<String> dayList,
                                                     List<String> prouctCodeList,
                                                     Map<String, List<String>> productOfOemMap,
                                                     Map<String, Map<String, List<String>>> oemCodeOfCalendarMap,
                                                     Map<String, Map<String, Integer>> newQty,
                                                     Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap) {
        //到这里为止qty里存的是发货计划，填充的预测数据的月，全是预测数据的月份还没加进来
        //这里处理的是只有预测数据的情况，按天补充
        if (CollectionUtils.isNotEmpty(allallCleanForecastMonthList)){
            for (String month : allallCleanForecastMonthList) {
                List<String> days = getDaysInMonth(month);
                dayList.addAll(days);

                // 获取该月每个产品的总需求量
                Map<String, Integer> productTotalDemandMap = cleanForecastDataMap.get(month).stream()
                        .collect(Collectors.groupingBy(
                                DeliveryPlanVO2::getProductCode,
                                Collectors.summingInt(vo -> Optional.ofNullable(vo.getDemandQuantity()).orElse(0))
                        ));

                for (String productCode : prouctCodeList) {
                    Integer totalMonthlyDemand = productTotalDemandMap.getOrDefault(productCode, 0);

                    // 获取该产品在该月份的工作日
                    List<String> workDays = days.stream()
                            .filter(day -> isContainsOemCodeOfCalendar(productOfOemMap.get(productCode), month, day, oemCodeOfCalendarMap))
                            .collect(Collectors.toList());

                    if (!workDays.isEmpty() && totalMonthlyDemand > 0) {
                        // 使用精确分配法确保总量不变
                        int workDayCount = workDays.size();
                        // 计算每天的基础分配量
                        int baseAllocation = totalMonthlyDemand / workDayCount;
                        // 计算需要额外分配的余量
                        int remaining = totalMonthlyDemand % workDayCount;

                        // 分配需求量到每一天
                        for (int i = 0; i < workDayCount; i++) {
                            String day = workDays.get(i);
                            // 前remaining天会得到额外的1个单位，确保总量不变
                            int dailyQty = baseAllocation + (i < remaining ? 1 : 0);

                            Map<String, Integer> productDayQty = newQty.computeIfAbsent(day, k -> new HashMap<>());
                            productDayQty.put(productCode, dailyQty);
                        }
                    } else {
                        // 没有工作日或需求为0的情况下，将0分配给所有天
                        for (String day : days) {
                            Map<String, Integer> productDayQty = newQty.computeIfAbsent(day, k -> new HashMap<>());
                            productDayQty.put(productCode, 0);
                        }
                    }
                }
            }
        }
    }

    private void supplementaryData(List<String> logList, String needMoth, String deliveryPlanEnd, List<String> dayList, List<String> prouctCodeList, Map<String, List<String>> productOfOemMap, Map<String, Map<String, List<String>>> oemCodeOfCalendarMap, Map<String, Map<String, Integer>> newQty, Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap, Map<String, Map<String, Integer>> deliveryPlanDataMap) {
        //这里处理的是既有发货计划，又有预测数据的情况
        if (needMoth !=null){
            // 获取整个月的所有天数
            List<String> allDaysInMonth = getDaysInMonth(needMoth);
            // 待补充的是发货计划结束日期之后的天数
            List<String> daysToFill = allDaysInMonth.stream()
                    .filter(t->DateUtils.stringToDate(t, pattern).after(DateUtils.stringToDate(deliveryPlanEnd, pattern)))
                    .sorted()
                    .collect(Collectors.toList());
            log.info("产能平衡开始补充发货计划月份，待补充天：{}", JSON.toJSONString(daysToFill));
            logList.add("产能平衡开始补充发货计划月份，待补充天" + JSON.toJSONString(daysToFill));
            dayList.addAll(daysToFill);

            // 获取该月每个产品的总需求量（预测）
            Map<String, Integer> productTotalForecastMap = cleanForecastDataMap.get(needMoth).stream()
                    .collect(Collectors.groupingBy(
                            DeliveryPlanVO2::getProductCode,
                            Collectors.summingInt(vo -> Optional.ofNullable(vo.getDemandQuantity()).orElse(0))
                    ));

            // 获取该月每个产品的总发货计划量
            Map<String, Integer> productTotalDeliveryPlanMap = new HashMap<>();
            for (Map.Entry<String, Map<String, Integer>> entry : deliveryPlanDataMap.entrySet()) {
                String day = entry.getKey();
                // 只统计该月的发货计划
                if (day.startsWith(needMoth)) {
                    Map<String, Integer> productMap = entry.getValue();
                    for (Map.Entry<String, Integer> productEntry : productMap.entrySet()) {
                        String productCode = productEntry.getKey();
                        Integer quantity = productEntry.getValue();
                        productTotalDeliveryPlanMap.merge(productCode, quantity, Integer::sum);
                    }
                }
            }

            for (String productCode : prouctCodeList) {
                Integer totalForecastDemand = productTotalForecastMap.getOrDefault(productCode, 0);
                Integer totalDeliveryPlan = productTotalDeliveryPlanMap.getOrDefault(productCode, 0);

                // 计算需要补充的量：预测总量 - 发货计划总量
                int needToFillQuantity = totalForecastDemand - totalDeliveryPlan;

                // 如果需要补充的量小于等于0，则不需要补充
                if (needToFillQuantity <= 0) {
                    // 没有需要补充的量，将0分配给所有待补充的天
                    for (String day : daysToFill) {
                        Map<String, Integer> productDayQty = newQty.computeIfAbsent(day, k -> new HashMap<>());
                        productDayQty.put(productCode, 0);
                    }
                    logList.add(productCode + "预测总量" + totalForecastDemand + ", 发货计划总量" + totalDeliveryPlan + ", 无需补充");
                    continue;
                }

                // 获取该产品在待补充日期范围内的工作日（只需要关注待补充的几天）
                List<String> workDaysToFill = daysToFill.stream()
                        .filter(day -> isContainsOemCodeOfCalendar(productOfOemMap.get(productCode), needMoth, day, oemCodeOfCalendarMap))
                        .collect(Collectors.toList());

                if (!workDaysToFill.isEmpty() && needToFillQuantity > 0) {
                    // 使用精确分配法确保总量不变，基于待补充期间的工作日数计算
                    int workDayCount = workDaysToFill.size();
                    // 计算每天的基础分配量
                    int baseAllocation = needToFillQuantity / workDayCount;
                    // 计算需要额外分配的余量
                    int remaining = needToFillQuantity % workDayCount;

                    // 将需要补充的量分配到待补充的工作日中
                    for (int i = 0; i < workDayCount; i++) {
                        String day = workDaysToFill.get(i);
                        // 前remaining天会得到额外的1个单位，确保总量不变
                        int dailyQty = baseAllocation + (i < remaining ? 1 : 0);

                        Map<String, Integer> productDayQty = newQty.computeIfAbsent(day, k -> new HashMap<>());
                        productDayQty.put(productCode, dailyQty);
                        logList.add(productCode + "预测补充" + day + "-" + dailyQty + ", 需补充总量:" + needToFillQuantity);
                    }
                } else {
                    // 没有工作日或需求为0的情况下，将0分配给所有待补充的天
                    for (String day : daysToFill) {
                        Map<String, Integer> productDayQty = newQty.computeIfAbsent(day, k -> new HashMap<>());
                        productDayQty.put(productCode, 0);
                    }
                }
            }
        }
    }

    /**
     * 一致性需求预测数据分解到月
     */
    private static void cleanForecastDataHandle(List<DeliveryPlanVO2> cleanForecastDataList,
                                                Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap,
                                                String allCleanForecastStart,
                                                List<String> allallCleanForecastMonthList) {
        if (CollectionUtils.isNotEmpty(cleanForecastDataList)){
            cleanForecastDataList = cleanForecastDataList.stream().sorted(Comparator.comparing(DeliveryPlanVO2::getDemandTime)).collect(Collectors.toList());
            for (DeliveryPlanVO2 deliveryPlanVO2 : cleanForecastDataList) {
                String month = DateUtils.dateToString(deliveryPlanVO2.getDemandTime(), MONTH_PATTERN);
                if (!cleanForecastDataMap.containsKey(month)){
                    List<DeliveryPlanVO2> dataList = new ArrayList<>();
                    dataList.add(deliveryPlanVO2);
                    cleanForecastDataMap.put(month, dataList);
                }else {
                    cleanForecastDataMap.get(month).add(deliveryPlanVO2);
                }
                if (month.compareTo(allCleanForecastStart) >= 0){
                    if (!allallCleanForecastMonthList.contains(month)){
                        allallCleanForecastMonthList.add(month);
                    }
                }
            }
        }
    }

    /**
     * 将发货计划分别分解到月和天
     */
    private static void deliveryPlanDataHandle(List<DeliveryPlanVO2> deliveryPlanDataList,
                                               List<String> demandTimeDayList,
                                               List<String> demandTimeMonthList,
                                               Map<String, List<DeliveryPlanVO2>> demandTimeMonthMap,
                                               Map<String, List<DeliveryPlanVO2>> demandTimeDayMap) {
        for (DeliveryPlanVO2 deliveryPlanVO2 : deliveryPlanDataList) {
            String demandTime = DateUtils.dateToString(deliveryPlanVO2.getDemandTime(), pattern);
            String month = DateUtils.dateToString(deliveryPlanVO2.getDemandTime(), MONTH_PATTERN);
            if (!demandTimeDayList.contains(demandTime)){
                demandTimeDayList.add(demandTime);
            }
            //保存发货计划的月份
            if (!demandTimeMonthList.contains(month)){
                demandTimeMonthList.add(month);
            }

            if (!demandTimeMonthMap.containsKey(month)){
                List<DeliveryPlanVO2> dataList = new ArrayList<>();
                dataList.add(deliveryPlanVO2);
                demandTimeMonthMap.put(month, dataList);
            }else {
                demandTimeMonthMap.get(month).add(deliveryPlanVO2);
            }
            if (!demandTimeDayMap.containsKey(demandTime)){
                List<DeliveryPlanVO2> dataList = new ArrayList<>();
                dataList.add(deliveryPlanVO2);
                demandTimeDayMap.put(demandTime, dataList);
            }else {
                demandTimeDayMap.get(demandTime).add(deliveryPlanVO2);
            }
        }
    }

    /**
     * 数据汇总到每周第一天
     */
    private static void summarizeToEachWeek(List<String> monthList, Map<String, Map<String, Integer>> newQty, Date deliveryPlanStartDate, String deliveryPlanStart) {

        if (!monthList.isEmpty()){
            for (String month : monthList) {
                //newQty的key是yyyy-MM-dd的形式，这里把超过三个月的数据按周汇总到每周第一天
                Map<String, Map<String, Integer>> monthMap = newQty.entrySet().stream()
                        .filter(t -> t.getKey().contains(month)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                for (Map.Entry<String, Map<String, Integer>> entry : monthMap.entrySet()) {
                    String day = entry.getKey();
                    Map<String, Integer> value = entry.getValue();
                    Date dayDate = DateUtils.stringToDate(day, pattern);
                    //周一对应日期
                    DateTime dateTime = DateUtil.beginOfWeek(dayDate);
                    String weekDay = DateUtils.dateToString(dateTime, pattern);
                    //当前第一周在发货计划开始时间之前，则把日期改为发货计划开始时间
                    String firstOfWeek;
                    if (dateTime.before(deliveryPlanStartDate)) {
                        firstOfWeek = deliveryPlanStart;
                    } else if (!weekDay.contains(month)) {
                        //这一周的第一天跨月了
                        firstOfWeek = month + "-01";
                    } else {
                        firstOfWeek = weekDay;
                    }
                    if (!day.equals(firstOfWeek)) {
                        Map<String, Integer> map = newQty.get(firstOfWeek);
                        for (Map.Entry<String, Integer> integerEntry : map.entrySet()) {
                            String productCode = integerEntry.getKey();
                            integerEntry.setValue(integerEntry.getValue() + value.get(productCode));
                            value.put(productCode, 0);
                        }
                    }
                }
            }
        }
        for (Map.Entry<String, Map<String, Integer>> entry : newQty.entrySet()) {
            entry.getValue().entrySet().removeIf(innerEntry -> innerEntry.getValue() == 0);
        }
    }

    private boolean isContainsOemCodeOfCalendar(List<String> oemCodeList,
                                                String needMoth,
                                                String day,
                                                Map<String, Map<String, List<String>>> oemCodeOfCalendarMap) {
        boolean flag = false;
        for (String oemCode : oemCodeList) {
            Map<String, List<String>> calendarMap = oemCodeOfCalendarMap.getOrDefault(oemCode, new HashMap<>());
            List<String> monthList = calendarMap.getOrDefault(needMoth, new ArrayList<>());
            if (monthList.contains(day)) {
                flag = true;
                break;
            }
        }
        return flag;
    }
    /**
     * 获取装车日历
     * @param cleanForecastDataMap
     * @param oemCodeList
     * @param dfpScenario
     * @return 主机厂编码-月份-有装车日历的天
     */
    private Map<String, Map<String, List<String>>> getOemCalendarNew(Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap, List<String> oemCodeList, String dfpScenario) {
        List<String> cleanForecastMonth = cleanForecastDataMap.keySet().stream().sorted().collect(Collectors.toList());

        Map<String, Map<String, List<String>>> oemCodeOfCalendarMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cleanForecastMonth)){
            String startMonthStr = cleanForecastMonth.get(0);
            String endMonthStr = cleanForecastMonth.get(cleanForecastMonth.size()-1);
            Date startDay = DateUtils.stringToDate(startMonthStr, MONTH_PATTERN);//按月创建默认是当月1号
            Date endMonth = DateUtils.stringToDate(endMonthStr, MONTH_PATTERN);//改成结束月份的最后一天
            endMonthStr = getLastDayOfMonth(endMonth);
            Date endDay = DateUtils.stringToDate(endMonthStr, pattern);
            ResourceCalendarRangeDTO rangeDTO = new ResourceCalendarRangeDTO();
            rangeDTO.setStartDate(startDay);
            rangeDTO.setEndDate(endDay);
            rangeDTO.setStandardResourceId(String.join(",", oemCodeList));
            List<WorkHourStatisticsVO> workHourStatisticsVOS = dfpFeign.dfpResourceCalendarCalWorkHour(dfpScenario, rangeDTO);
            //将装车日历按主机厂分组

            if (CollectionUtils.isNotEmpty(workHourStatisticsVOS)){
                Map<String, List<WorkHourStatisticsVO>> oemMap = workHourStatisticsVOS.stream().collect(Collectors.groupingBy(WorkHourStatisticsVO::getStandardResourceId));
                for (String oemCode : oemCodeList) {
                    Map<String, List<String>> monthOfDayMap = new HashMap<>();
                    if (oemMap.containsKey(oemCode)){

                        Map<String, BigDecimal> totalDateMap = oemMap.get(oemCode).get(0).getTotalDateMap();
                        totalDateMap = totalDateMap.entrySet()
                                .stream()
                                .filter(t -> !t.getValue().equals(BigDecimal.ZERO))
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                        Set<String> days = totalDateMap.keySet();

                        for (String day : days) { //2024-11-13#2024-11-13
                            String[] split = day.split("#");

                            String result = split[0].substring(0, split[0].lastIndexOf("-")).replace("-", "");//202411
                            if (monthOfDayMap.containsKey(result)){
                                monthOfDayMap.get(result).add(split[0]);
                            }else {
                                List<String> dayStr = new ArrayList<>();
                                dayStr.add(split[0]);
                                monthOfDayMap.put(result, dayStr);
                            }
                        }
                        //补充装车日历缺失的月份，默认为当月的天数
                        replenishmentOemCalendarNew(cleanForecastMonth, oemCodeOfCalendarMap, oemCode, monthOfDayMap);
                    }else {
                        replenishmentOemCalendarNew(cleanForecastMonth, oemCodeOfCalendarMap, oemCode, monthOfDayMap);
                    }
                }
            }else {
                //没有装车日历，统一按每月天数补充
                for (String oemCode : oemCodeList) {
                    Map<String, List<String>> monthOfDayMap = new HashMap<>();
                    replenishmentOemCalendarNew(cleanForecastMonth, oemCodeOfCalendarMap, oemCode, monthOfDayMap);
                }
            }
        }
        return oemCodeOfCalendarMap;
    }

    /**
     * 补充装车日历
     * @param cleanForecastMonth
     * @param oemCodeOfCalendarMap
     * @param oemCode
     * @param monthOfDayMap
     */
    private void replenishmentOemCalendar(List<String> cleanForecastMonth, Map<String, Map<String, Integer>> oemCodeOfCalendarMap, String oemCode, Map<String, Integer> monthOfDayMap) {
        for (String month : cleanForecastMonth) {
            if (!monthOfDayMap.containsKey(month)){
                int monthDayCount = DateUtils.getMonthDayCount(Integer.parseInt(month.substring(0, 4)), Integer.parseInt(month.substring(4, 6)));
                monthOfDayMap.put(month, monthDayCount);
            }
        }
        oemCodeOfCalendarMap.put(oemCode, monthOfDayMap);
    }

    /**
     * 补充装车日历-到天
     * @param cleanForecastMonth
     * @param oemCodeOfCalendarMap
     * @param oemCode
     * @param monthOfDayMap
     */
    private void replenishmentOemCalendarNew(List<String> cleanForecastMonth, Map<String, Map<String, List<String>>> oemCodeOfCalendarMap, String oemCode, Map<String, List<String>> monthOfDayMap) {
        for (String month : cleanForecastMonth) {
            if (!monthOfDayMap.containsKey(month)){

                monthOfDayMap.put(month, getDaysInMonth(month));
            }
        }
        oemCodeOfCalendarMap.put(oemCode, monthOfDayMap);
    }




    private void getSafetyStockLevel(String scenario,
                                     List<String> prouctCodeList,
                                     List<String> monthList,
                                     Map<String, Map<String, Integer>> qty,
                                     Map<String, Map<String, Integer>> deliveryPlanDataMap,
                                     Map<String, Map<String, Integer>> monthQty,
                                     List<String> allDeliveryPlanList,
                                     String needMonth,
                                     CapacityBalanceAlgorithmDataDTO algorithmDataDTO,
                                     List<String> logList) {

        List<NewStockPointVO> newStockPointVOList = getStockPointMap(scenario);
        Map<String, String> newStockPointVOMap = newStockPointVOList.stream().filter(t->t.getStockPointType()!=null).collect(Collectors.toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getStockPointType));
        //保存月份-本厂编码-安全库存
        Map<String, Map<String, Integer>> endingInventoryMap = new HashMap<>();
        List<String> safetyProductCodeList = algorithmDataDTO.getSafetyProductCodeList();
        List<String> needProductCodeList = prouctCodeList.stream().filter(t -> !safetyProductCodeList.contains(t)).collect(Collectors.toList());
        List<SafetyStockLevelVO> safetyStockLevelVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(needProductCodeList)) {
            //查询安全库存水位
            safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(scenario, needProductCodeList);
        }
        if (CollectionUtils.isNotEmpty(safetyStockLevelVOS)){

            Map<String, List<SafetyStockLevelVO>> safetyStockMap = safetyStockLevelVOS.stream().collect(Collectors.groupingBy(SafetyStockLevelVO::getProductCode));


            for (String month : monthList) {
                Date monthDate = DateUtils.stringToDate(month, MONTH_PATTERN);
                Date beforeDate = DateUtils.moveMonth(monthDate, -1);
                Date afterDate = DateUtils.moveMonth(monthDate, 1);
                String afterMonth = DateUtils.dateToString(afterDate, MONTH_PATTERN);
                //本月最后一天
                String lastDayOfMonth = getLastDayOfMonth(monthDate);
                Map<String, Integer> productQty = qty.get(lastDayOfMonth);
                Map<String, Integer> endingInventoryOfProductMap = new HashMap<>();
                for (String productCode : prouctCodeList) {
                    Integer totalQty = 0;
                    //该产品有安全库存数据
                    if (safetyStockMap.containsKey(productCode)){
                        List<SafetyStockLevelVO> safetyStockLevelVOList = safetyStockMap.get(productCode);
                        for (SafetyStockLevelVO safetyStockLevelVO : safetyStockLevelVOList) {
                            //根据库存点类型判断计算方式，整个计算时间分成3段
                            String stockPointType = newStockPointVOMap.get(safetyStockLevelVO.getStockCode());
                            //安全水位天数
                            BigDecimal standardStockDay = safetyStockLevelVO.getStandardStockDay();
                            if (StockPointTypeEnum.BC.getCode().equals(stockPointType)){
                                //这个月全是发货计划
                                totalQty = getTotalQty(deliveryPlanDataMap, qty, afterMonth, productCode, totalQty, standardStockDay, logList, month);
                            }else if(!allDeliveryPlanList.contains(month) && monthQty.containsKey(afterMonth) && needMonth != null) {//中转库类型
                                //全是发货计划且跨月的月份不考虑中转库
                                totalQty = getTotalQty(deliveryPlanDataMap, qty, afterMonth, productCode, totalQty, standardStockDay, logList, month);
                            }
                        }
                    }
                    //循环完所有安全库存水位，把计算出来的本厂期末库存+中转库期末库存的结果存起来
                    endingInventoryOfProductMap.put(productCode, totalQty);

                    //每个月的数据加上当月的安全库存，不在这里减上月的安全库存了，改为在实时库存扣减那里
                    int newQty = productQty.get(productCode) + totalQty;
                    productQty.put(productCode, newQty);
                }
                endingInventoryMap.put(month, endingInventoryOfProductMap);
            }
        }
        algorithmDataDTO.setEndingInventoryMap(endingInventoryMap);
    }



    @Override
    public Map<String, BigDecimal> getLockQtyData(List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS,
                                                  CapacityBalanceAlgorithmDataDTO algorithmDataDTO,
                                                  Map<String,Map<String,Integer>> qty){
        Map<String, BigDecimal> lockQtyData = new HashMap<>();
        Map<String, List<CapacitySupplyRelationshipVO>> lockData = new HashMap<>();
        for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : capacitySupplyRelationshipVOS) {
            String month;
            if (SupplyModelEnum.OUTSOURCED.getCode().equals(capacitySupplyRelationshipVO.getSupplyModel())){
                month = DateUtils.dateToString(capacitySupplyRelationshipVO.getForecastTime(), pattern);
            }else {
                month = DateUtils.dateToString(capacitySupplyRelationshipVO.getSupplyTime(), pattern);
            }
            String demandMonth = DateUtils.dateToString(capacitySupplyRelationshipVO.getForecastTime(), pattern);
            String key = demandMonth+"-"+capacitySupplyRelationshipVO.getProductCode()+"-"+capacitySupplyRelationshipVO.getOperationCode();
            if (qty != null && qty.containsKey(demandMonth)){
                //本次计算的当月需求量
                Integer integer = qty.get(demandMonth).get(capacitySupplyRelationshipVO.getProductCode());
                if (integer != null && BigDecimal.valueOf(integer).compareTo(capacitySupplyRelationshipVO.getDemandQuantity()) < 0){
                    capacitySupplyRelationshipVO.setDemandQuantity(BigDecimal.valueOf(integer));
                    if (BigDecimal.valueOf(integer).compareTo(capacitySupplyRelationshipVO.getSupplyQuantity()) < 0){
                        capacitySupplyRelationshipVO.setSupplyQuantity(BigDecimal.valueOf(integer));
                    }
                }
            }
            if (lockQtyData.containsKey(key)){
                lockQtyData.put(key, lockQtyData.get(key).add(capacitySupplyRelationshipVO.getSupplyQuantity()));
            }else {
                lockQtyData.put(key, capacitySupplyRelationshipVO.getSupplyQuantity());
            }
            if (!lockData.containsKey(month)){
                List<CapacitySupplyRelationshipVO> vos = new ArrayList<>();
                vos.add(capacitySupplyRelationshipVO);
                lockData.put(month, vos);
            }else {
                lockData.get(month).add(capacitySupplyRelationshipVO);
            }
        }
        algorithmDataDTO.setLockData(lockData);
        return lockQtyData;
    }




    private void processNetDemandDisassembly(String scenario,
                                             List<String> prouctCodeList,
                                             Map<String, Map<String, Integer>> qty,
                                             Map<String, BigDecimal> lockQtyData,
                                             CapacityBalanceAlgorithmDataDTO algorithmDataDTO,
                                             List<String> logList) {
        //算法重要参数
        Map<String, Integer> operationQty = new HashMap<>();
        //每道工序的原始需求量
        Map<String, Integer> primitiveOperationQty = new HashMap<>();
        Map<String, Set<String>> operationOfProductMap = new HashMap<>();
        Map<String, NewProductStockPointVO> productCodeMap = new HashMap<>();
        List<String> routingStepIds = new ArrayList<>();
        //记录物品和工艺路径之间的对应关系
        Map<String, String> routingIdOfProductCodeMap = new HashMap<>();
        //记录工艺路径步骤和工艺路径之间的对应关系
        Map<String, List<RoutingStepVO>> routingStepOfProductMap = new HashMap<>();
        //记录每道工序输入物品和原物品之间的关系
        Map<String, List<String>> sourceProductCodeListMap = new HashMap<>();
        List<String> allInputProductIdList = new ArrayList<>();
        Map<String, String> productIdMap = new HashMap<>();
        //记录半成品和成品的关系
        Map<String, List<String>> map = prouctCodeList.stream().collect(Collectors.groupingBy(t -> t, Collectors.mapping(t->t, Collectors.toList())));
        // 工艺路径步骤（物料编码，工艺路径步骤id）
        Map<String, String> routingStepIdMap = new HashMap<>();

        //递归查询工艺路径
        getRoutingNew(scenario,prouctCodeList, null, productCodeMap, productIdMap,
                allInputProductIdList, routingIdOfProductCodeMap, routingStepOfProductMap, sourceProductCodeListMap,
                algorithmDataDTO.getExceptionPOList());
        allInputProductIdList = allInputProductIdList.stream().distinct().collect(Collectors.toList());
        //根据递归查询到的结果，重新补充qty
        replenishmentQty(qty, routingIdOfProductCodeMap, sourceProductCodeListMap, allInputProductIdList, productIdMap, map);
        for (Map.Entry<String, Map<String, Integer>> entry : qty.entrySet()) {
            entry.getValue().entrySet().removeIf(innerEntry -> innerEntry.getValue() == 0);
        }
        qty.entrySet().removeIf(innerEntry -> CollectionUtils.isEmpty(innerEntry.getValue()));
        List<String> dayList = qty.keySet().stream().sorted().collect(Collectors.toList());
        algorithmDataDTO.setDayList(dayList);
        List<String> removeProductCodeList = algorithmDataDTO.getExceptionPOList().stream().filter(t -> t.getOperationCode() == null)
                .map(CapacitySupplyRelationshipExceptionDTO::getProductCode).distinct().collect(Collectors.toList());
        prouctCodeList = qty.entrySet().stream().flatMap(t1 -> t1.getValue().keySet().stream())
                .distinct().filter(t->!removeProductCodeList.contains(t)).collect(Collectors.toList());
        // 这里面是没有工艺路径或者不存在的产品编码

        algorithmDataDTO.setProductCodeList(prouctCodeList);
        algorithmDataDTO.setProductOfSourceProductListMap(map);
        //获取委外数据
        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", prouctCodeList);
        params.put("outsourceStatus", OutsourceStatusEnum.ISSUED.getCode());
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<OutsourceTransferSummaryVO> outsourceTransferSummaryVOList = outsourceTransferSummaryDao.selectVOByParams(params);
        params = new HashMap<>();
        params.put("changeAfterCodeList", prouctCodeList);
        params.put("outsourceStatus", OutsourceStatusEnum.ISSUED.getCode());
        params.put("enabled", YesOrNoEnum.YES.getCode());
        outsourceTransferSummaryVOList.addAll(outsourceTransferSummaryDao.selectVOByParams(params));

        List<String> finalProuctCodeList = prouctCodeList;
        outsourceTransferSummaryVOList = outsourceTransferSummaryVOList.stream().filter(t -> finalProuctCodeList.contains(t.getProductCode())).collect(Collectors.toList());
        List<String> ouChangeAfterCodeList = new ArrayList<>();
        Map<String, List<OutsourceTransferSummaryVO>> productCodeOfOutMap = new HashMap<>();
        //记录每个产品对应的委外产品编码（原编码+转产后编码）
        Map<String, List<String>> outProductCodeOfOutsourceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outsourceTransferSummaryVOList)){
            ouChangeAfterCodeList = outsourceTransferSummaryVOList.stream().map(OutsourceTransferSummaryVO::getChangeAfterCode).filter(Objects::nonNull).collect(Collectors.toList());
            //将数据分别按原编码和转产后编码分组
            Map<String, List<OutsourceTransferSummaryVO>> outProductCodeMap = outsourceTransferSummaryVOList.stream().collect(Collectors.groupingBy(OutsourceTransferSummaryVO::getProductCode));
            Map<String, List<OutsourceTransferSummaryVO>> changeAfterCodeMap = outsourceTransferSummaryVOList.stream().filter(t -> t.getChangeAfterCode() != null).collect(Collectors.groupingBy(OutsourceTransferSummaryVO::getChangeAfterCode));
            for (String productCode : prouctCodeList) {
                //遍历本厂编码，找到原编码和转产后编码是该本厂编码的数据
                List<OutsourceTransferSummaryVO> summaryVOS = outProductCodeMap.getOrDefault(productCode, new ArrayList<>());
                summaryVOS.addAll(changeAfterCodeMap.getOrDefault(productCode, new ArrayList<>()));
                List<OutsourceTransferSummaryVO> outsourceTransferSummaryVOS = summaryVOS.stream().distinct().collect(Collectors.toList());
                productCodeOfOutMap.put(productCode, outsourceTransferSummaryVOS);
                List<String> collect = outsourceTransferSummaryVOS.stream().map(OutsourceTransferSummaryVO::getChangeAfterCode).filter(Objects::nonNull).collect(Collectors.toList());
                collect.addAll(outsourceTransferSummaryVOS.stream().map(OutsourceTransferSummaryVO::getProductCode).collect(Collectors.toList()));
                collect = collect.stream().distinct().collect(Collectors.toList());
                outProductCodeOfOutsourceMap.put(productCode, collect);
            }
        }
        List<String> productCodesInventoryRealList = new ArrayList<>();
        productCodesInventoryRealList.addAll(prouctCodeList);
        productCodesInventoryRealList.addAll(ouChangeAfterCodeList);
        productCodesInventoryRealList = productCodesInventoryRealList.stream().distinct().collect(Collectors.toList());
        List<NewStockPointVO> newStockPointVOList = getStockPointMap(scenario);
        //获取实时库存
        getInventory(scenario, qty, algorithmDataDTO, productCodesInventoryRealList, outProductCodeOfOutsourceMap, newStockPointVOList, logList);
        //需求委外天数， key = productCode
        Map<String, List<String>> demandOutsourcingPercentDays = new HashMap<>();
        //工序委外天数百分比， key = productCode+operationCode
        Map<String, List<String>> processOutsourcingPercentDays= new HashMap<>();
        //处理委外数据，根据委外转产时间范围按占月总天数百分比得到当月该扣减的需求量
        for (Map.Entry<String, List<OutsourceTransferSummaryVO>> entry : productCodeOfOutMap.entrySet()) {
            String productCode = entry.getKey();
            //将委外数据按开始时间排序
            List<OutsourceTransferSummaryVO> outsourceTransferSummaryVOS = entry.getValue().stream().sorted(Comparator.comparing(OutsourceTransferSummaryVO::getChangeStartDate)).collect(Collectors.toList());
            for (OutsourceTransferSummaryVO outsourceTransferSummaryVO : outsourceTransferSummaryVOS) {
                Map<String, BigDecimal> dateMap = getDateMap(outsourceTransferSummaryVO.getChangeStartDate(), outsourceTransferSummaryVO.getChangeEndDate());
                String changeStartDateStr = DateUtils.dateToString(outsourceTransferSummaryVO.getChangeStartDate(), pattern);
                String changeEndDateStr = DateUtils.dateToString(outsourceTransferSummaryVO.getChangeEndDate(), pattern);
                List<String> demandTimeList = getDemandTimeList(changeStartDateStr, changeEndDateStr, pattern);
                //需求委外
                if (DEMAND_OUTSOURCING.getCode().equals(outsourceTransferSummaryVO.getChangeType())){
                    demandOutsourcingPercentDays.put(productCode, demandTimeList);
                }else if (PROCESS_OUTSOURCING.getCode().equals(outsourceTransferSummaryVO.getChangeType())){
                    processOutsourcingPercentDays.put(productCode + "-" + outsourceTransferSummaryVO.getOperationCode(), demandTimeList);
                }
            }
        }
        algorithmDataDTO.setProcessOutsourcingPercentDays(demandOutsourcingPercentDays);
        algorithmDataDTO.setDemandOutsourcingPercentDays(processOutsourcingPercentDays);
        List<CapacitySupplyRelationshipVO> outDataList = new ArrayList<>();
        //组装产品工序(从工序拆分计算里拆出来的，为了先给operationOfProductMap赋值（周产能平衡先要用）)
        Map<String, RoutingStepVO> routingStepVOMap = getOperationOfProductMap(prouctCodeList, routingIdOfProductCodeMap, routingStepOfProductMap, routingStepIds, operationOfProductMap);
        algorithmDataDTO.setRoutingStepVOMap(routingStepVOMap);
        // 获取标准工艺上的良品率
        Map<String, StandardStepVO> standardStepMap = getStandardStepMap(scenario);


        //工序拆分计算
        decompositionProcessDay(algorithmDataDTO.getDayList(), routingStepIds, qty, operationQty, primitiveOperationQty,
                algorithmDataDTO.getOperationInventoryMap(), lockQtyData, routingIdOfProductCodeMap, routingStepOfProductMap, demandOutsourcingPercentDays,
                processOutsourcingPercentDays, productCodeMap, outDataList, false, null, algorithmDataDTO.getNow(), algorithmDataDTO.getDeliveryPlanStart(),
                routingStepIdMap, standardStepMap, logList);


        //根据工序步骤id获取候选资源
        List<String> routingStepResourceIds = getRoutingStepResource(routingStepIds, scenario, algorithmDataDTO);
        algorithmDataDTO.setProductMap(productCodeMap);
        algorithmDataDTO.setOperationOfProductMap(operationOfProductMap);
        algorithmDataDTO.setOperationQty(operationQty);
        algorithmDataDTO.setPrimitiveOperationQty(primitiveOperationQty);
        algorithmDataDTO.setOutShipData(outDataList);
        algorithmDataDTO.setRoutingStepResourceIds(routingStepResourceIds);
        // 工艺路径步骤
        algorithmDataDTO.setRoutingStepIdMap(routingStepIdMap);
    }

    private void getInventory(String dfpScenario, Map<String, Map<String, Integer>> qty,
                              CapacityBalanceAlgorithmDataDTO algorithmDataDTO,
                              List<String> productCodesInventoryRealList,
                              Map<String, List<String>> outProductCodeOfOutsourceMap,
                              List<NewStockPointVO> newStockPoints, List<String> logList) {
        //查询实时库存
        Map<String, Integer> operationInventoryMap = new HashMap<>();
        Map<String, Integer> productInventoryMap = new HashMap<>();
        List<NewStockPointVO> bcStockPoints = newStockPoints.stream().filter(t ->
                StockPointTypeEnum.BC.getCode().equals(t.getStockPointType())).collect(Collectors.toList());
        //工序在制只扣本厂的库存
        List<String> bcStockPointCodes =
                bcStockPoints.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        //获取本厂销售组织类型的仓库(成品库存点)
        List<String> saleOrganizations = bcStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        //获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = bcStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 实时库存数据
        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(dfpScenario,
                productCodesInventoryRealList, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointCodes
                .contains(p.getStockPointCode())).collect(Collectors.toList());
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = org.apache.commons.collections4.CollectionUtils.isEmpty(spaceList) ?
                new HashMap<>()
                : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode())
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
        String rangeData = getRangeData(dfpScenario);
        if (CollectionUtils.isNotEmpty(inventoryBatchDetails)){
            // 实时库存Map
            productInventoryMap = inventoryBatchDetails.stream()
                    .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                            && saleOrganizations.contains(t.getStockPointCode())
                            && rangeData.equals(t.getSubinventory()))
                    .filter(t -> {
                        String freightSpace = t.getFreightSpace();
                        SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                        if (null == subInventoryCargoLocationVO) {
                            return false;
                        }
                        return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                    })
                    .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode,
                            Collectors.reducing(0,
                                    t -> new BigDecimal(t.getCurrentQuantity()).intValue(),
                                    Integer::sum
                            )
                    ));
            // 工序在制只扣本厂的库存
            operationInventoryMap = inventoryBatchDetails.stream()
                    .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                    .filter(t -> {
                        String freightSpace = t.getFreightSpace();
                        SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                        if (null == subInventoryCargoLocationVO) {
                            return false;
                        }
                        return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                    })
                    .collect(Collectors.groupingBy( t -> String.join("-",
                            t.getProductCode(), t.getOperationCode()),
                            Collectors.reducing(0,
                                    t -> new BigDecimal(t.getCurrentQuantity()).intValue(),
                                    Integer::sum
                            )
                    ));
            //不止在第一个月扣减，一直把实时库存扣光为止
            List<String> dayList = algorithmDataDTO.getDayList();
            List<String> monthList = algorithmDataDTO.getMonthList();
            Map<String, Map<String, Integer>> endingInventoryMap = algorithmDataDTO.getEndingInventoryMap();
            //这里改成天了 20241204
            String firstMonth = monthList.get(0);
            List<String> productCodeList = algorithmDataDTO.getProductCodeList();
            String beforeMonth = firstMonth;
            for (String month : monthList) {
                List<String> currMonthOfDays = dayList.stream().filter(t -> t.contains(month)).sorted().collect(Collectors.toList());
                for (String productCode : productCodeList) {
                    Integer inventory = productInventoryMap.getOrDefault(productCode, 0);
                    Map<String, Integer> currMonthEndingInventoryMap = endingInventoryMap.getOrDefault(beforeMonth, new HashMap<>());
                    Integer endingInventory = currMonthEndingInventoryMap.getOrDefault(productCode, 0);
                    for (String day : currMonthOfDays) {
                        Integer demandQuantity = qty.getOrDefault(day, new HashMap<>()).getOrDefault(productCode, 0);
                        logList.add("安全库存扣减需求量" + productCode + "#" + day + "#" + demandQuantity);
                        if (firstMonth.equals(month)){
                            int num = inventory;
                            //扣减对应库存
                            demandQuantity = productInventory(productInventoryMap, demandQuantity, productCode);
                            logList.add(productCode + "#" + day +"第一个月扣减实时库存后数量" + demandQuantity + "成品实时库存为" + num);
                            inventory = productInventoryMap.getOrDefault(productCode, 0);
                        }else {
                            //从第二个月开始，比较上个月安全库存水位和实时库存大小，扣大的
                            logList.add(productCode + "#" + day +"实时库存" + inventory + ", 安全库存水位" + endingInventory);
                            if (inventory > endingInventory){
                                logList.add(productCode + "-" + day +"实时库存" + inventory + ", 当前数量" + demandQuantity);
                                if (demandQuantity > inventory){

                                    demandQuantity = demandQuantity - inventory;
                                    productInventoryMap.put(productCode, 0);
                                    //安全库存水位和实时库存其中任意一个被扣光，另一个也设置为0
                                    inventory = 0;
                                    endingInventory = 0;
                                }else {
                                    inventory = inventory - demandQuantity;
                                    productInventoryMap.put(productCode, inventory);
                                    endingInventory = Math.max(endingInventory - demandQuantity, 0);
                                    demandQuantity = 0;
                                }
                            }else {
                                logList.add(productCode + "#" + day +"安全库存" + endingInventory + ", 当前数量" + demandQuantity);
                                if (demandQuantity > endingInventory){
                                    demandQuantity = demandQuantity - endingInventory;
                                    productInventoryMap.put(productCode, 0);
                                    //安全库存水位和实时库存其中任意一个被扣光，另一个也设置为0
                                    endingInventory = 0;
                                    inventory = 0;
                                }else {
                                    endingInventory = endingInventory - demandQuantity;
                                    inventory = Math.max(inventory - demandQuantity, 0);
                                    productInventoryMap.put(productCode, inventory);
                                    demandQuantity = 0;
                                }
                                currMonthEndingInventoryMap.put(productCode, endingInventory);
                                endingInventoryMap.put(beforeMonth, currMonthEndingInventoryMap);
                            }
                        }
                        qty.get(day).put(productCode, demandQuantity);
                        if (inventory <=0 && endingInventory <=0){
                            break;
                        }
                    }
                }
                beforeMonth = month;
            }
        }
        algorithmDataDTO.setOperationInventoryMap(operationInventoryMap);
        algorithmDataDTO.setProductInventoryMap(productInventoryMap);
    }

    private Integer getTotalQty(Map<String, Map<String, Integer>> deliveryPlanDataMap,
                                Map<String, Map<String, Integer>> qty,
                                String afterMonth,
                                String productCode,
                                Integer totalQty,
                                BigDecimal standardStockDay,
                                List<String> logList,
                                String month) {
        //安全库存水位是3天，那就取次月1，2，3号的数量相加就是安全库存
        Date startDate = DateUtils.stringToDate(afterMonth, MONTH_PATTERN);
        BigDecimal day = new BigDecimal(standardStockDay.intValue());
        Integer safeQty = 0;
        while (standardStockDay.compareTo(BigDecimal.ZERO) > 0){
            BigDecimal rate = standardStockDay.subtract(BigDecimal.ONE).compareTo(BigDecimal.ZERO) >= 0 ? BigDecimal.ONE : BigDecimal.ONE.subtract(standardStockDay);
            String startDay = DateUtils.dateToString(startDate, pattern);
            if (deliveryPlanDataMap.containsKey(startDay)){
                safeQty = safeQty + BigDecimal.valueOf(deliveryPlanDataMap.get(startDay).get(productCode)).multiply(rate).setScale(0, RoundingMode.CEILING).intValue();
            }else {//20260101
                int dayDty = qty.get(startDay) == null ? 0 : qty.get(startDay).get(productCode);
                safeQty = safeQty + BigDecimal.valueOf(dayDty).multiply(rate).setScale(0, RoundingMode.CEILING).intValue();
            }
            Calendar dayCalendar = Calendar.getInstance();
            dayCalendar.setTime(startDate);
            dayCalendar.add(Calendar.DAY_OF_MONTH, 1); //20241001->20241002
            startDate = dayCalendar.getTime();
            standardStockDay = standardStockDay.subtract(BigDecimal.ONE);
        }
        logList.add(productCode + "-" + month + "月安全库存水位天数" + day + "-安全库存为" + safeQty);
        return totalQty + safeQty;
    }


    /**
     * 根据装车日历和月份获取对应月的需求量（每天）
     *
     * @param cleanForecastDataMap
     * @param needMoth
     * @param prouctCodeList
     * @param monthQty
     * @param logList
     */
    private void getQtyByMonth(Map<String, List<DeliveryPlanVO2>> cleanForecastDataMap,
                               String needMoth,
                               List<String> prouctCodeList,
                               Map<String, Map<String, List<String>>> oemCodeOfCalendarMap,
                               Map<String, Map<String, Integer>> monthQty,
                               List<String> logList) {
        //获取需要补充的月份的数据
        List<DeliveryPlanVO2> deliveryPlanVO2s = cleanForecastDataMap.get(needMoth);
        //构建产品与主机厂的关系
        Map<String, DeliveryPlanVO2> productOfOemMap = deliveryPlanVO2s.stream().collect(Collectors.toMap(DeliveryPlanVO2::getProductCode, Function.identity(), (v1, v2) -> v1));
        Map<String, Integer> dayOfQtyMap = new HashMap<>();
        for (String productCode : prouctCodeList) {
            boolean flag = false;
            //获取该主机厂对应的装车日历
            if (productOfOemMap.containsKey(productCode)){
                String oemCode = productOfOemMap.get(productCode).getOemCode();
                Map<String, List<String>> oemCodeOfMonthMap = oemCodeOfCalendarMap.get(oemCode);
                if (oemCodeOfMonthMap != null){
                    List<String> workDays = oemCodeOfMonthMap.get(needMoth);
                    if (workDays != null && !workDays.isEmpty()){
                        //工作日数量
                        int workDayCount = workDays.size();
                        int demandQuantity = Optional.ofNullable(productOfOemMap.get(productCode))
                                .map(DeliveryPlanVO2::getDemandQuantity)
                                .orElse(0);

                        // 使用更合理的计算方式避免小数量被放大
                        int dailyQty;
                        if (demandQuantity > 0) {
                            // 当需求量小于工作日数时，每天分配1个单位（避免向上取整造成的放大）
                            // 当需求量大于等于工作日数时，正常计算平均值
                            if (demandQuantity < workDayCount) {
                                dailyQty = 1;
                            } else {
                                // 使用ROUND_HALF_UP而不是向上取整
                                dailyQty = new BigDecimal(demandQuantity)
                                        .divide(new BigDecimal(workDayCount), 0, RoundingMode.HALF_UP)
                                        .intValue();
                            }
                        } else {
                            dailyQty = 0;
                        }

                        dayOfQtyMap.put(productCode, dailyQty);
                        logList.add(productCode + "#" + needMoth + "月总需求量" + demandQuantity + ", 工作天数" + workDayCount + ", 每天分配" + dailyQty);
                        flag = true;
                    }
                }
            }
            if (!flag){
                //如果该月没有该产品的预测数据，则该月该产品的需求数量为0
                dayOfQtyMap.put(productCode, 0);
                logList.add(productCode + "#" + needMoth + "月平均每天需求量0");
            }
        }
        monthQty.put(needMoth, dayOfQtyMap);
    }



    /**
     * 根据时间获取当前月最后一天，例：20240823 -> 20240831
     * @param date
     * @return
     */
    private String getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return DateUtils.dateToString(calendar.getTime(), pattern);
    }

    /**
     * 根据展望期确定查询发货计划和需求计划的时间范围，例如展望期i是12，该方法返回现在的时间加上12个月的日期的23:59:59
     * @param i
     * @return
     */
    private Date getLastMonth(Integer i){
        // 获取当前日期的时间
        Calendar calendar = Calendar.getInstance();

        // 设置时分秒为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0); // 清零毫秒
        calendar.add(Calendar.MONTH, i);
        // 获取Date对象
        return calendar.getTime();
    }

    /**
     * 用于获取两个时间的时间范围内所占月份的百分比
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return key-月份，value-转产天数占当月总天数百分比
     */
    private static Map<String, BigDecimal> getDateMap(Date startTime, Date endTime) {
        Map<String, BigDecimal> dateMap = new HashMap<>();
        LocalDate start = startTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

        while (!start.isAfter(end)) {
            int daysInMonth = start.lengthOfMonth();
            LocalDate monthEnd = start.withDayOfMonth(daysInMonth);
            if (monthEnd.isAfter(end)) {
                monthEnd = end;
            }
            long days = ChronoUnit.DAYS.between(start, monthEnd) + 1;
            BigDecimal percentage = BigDecimal.valueOf(days).divide(BigDecimal.valueOf(daysInMonth), 4, RoundingMode.HALF_UP);
            String key = start.getYear() + String.format("%02d", start.getMonthValue());
            dateMap.put(key, percentage);
            start = monthEnd.plusDays(1);
        }

        return dateMap;
    }

    public static List<String> getDaysInMonth(String month) {
        List<String> daysInMonth = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate startDate = LocalDate.parse(month + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            daysInMonth.add(date.format(DateTimeFormatter.ofPattern(pattern)));
        }

        return daysInMonth;
    }

}
