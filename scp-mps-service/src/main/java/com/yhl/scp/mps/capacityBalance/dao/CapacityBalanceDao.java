package com.yhl.scp.mps.capacityBalance.dao;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>CapacityBalanceDao</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-31 14:16:06
 */
public interface CapacityBalanceDao {

    /**
     * 根据开始时间查询发货计划发布表数据
     *
     * @param demandTimeStart
     * @return
     */
    List<DeliveryPlanVO2> selectDeliveryPlanPublished(@Param("demandTimeStart") String demandTimeStart);

    /**
     * 根据物料编码查询外部库存数据
     * @param productCodeList
     * @return
     */
    List<InventoryBatchDetailVO> selectExternalInventoryByProductCodeList(@Param("productCodeList") List<String> productCodeList);
}
