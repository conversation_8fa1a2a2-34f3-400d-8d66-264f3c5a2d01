package com.yhl.scp.mps.plan.support;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.plan.service.impl.MasterPlanServiceImpl;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MasterPlanInventorySupport</code>
 * <p>
 * 封装库存查询相关逻辑
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-21 11:11:42
 */
@Component
@Slf4j
public class MasterPlanInventorySupport {
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    public static final String PARAM_STOCK_POINT_TYPE = "stockPointType";
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    protected IpsFeign ipsFeign;

    /**
     * 获取库存点数据
     * @return
     */
    public List<NewStockPointVO> getStockPoint() {
        // 查询库存点数据，用于过滤非本厂库存
        return newMdsFeign.selectStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));
    }

    public List<String> getStockPointCodeListByType(List<NewStockPointVO> newStockPoints, String stockPointType) {
        return newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && stockPointType.equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    }

    /**
     * 获取库存数据
     * @param bcStockPointList
     * @param productCodes
     * @return
     */
    public List<InventoryBatchDetailVO> getInventoryBatchDetail(List<String> bcStockPointList, List<String> productCodes) {
        return dfpFeign.selectInventoryDataByProductCodes(SystemHolder.getScenario(),
                productCodes, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointList
                .contains(p.getStockPointCode())).collect(Collectors.toList());
    }

    /**
     * 获取可用本厂有效货位库存
     * @param inventoryBatchDetails
     * @return
     */

    public Map<String, SubInventoryCargoLocationVO> getSubInventoryCargoLocationMap(List<InventoryBatchDetailVO> inventoryBatchDetails) {
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        return CollectionUtils.isEmpty(spaceList) ?
                new HashMap<>()
                : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode())
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 获取成品库存
     * @param inventoryBatchDetails
     * @param newStockPoints
     * @return
     */
    public Map<String, List<InventoryBatchDetailVO>> getFinishInventoryMap(List<InventoryBatchDetailVO> inventoryBatchDetails,
                                                                           List<NewStockPointVO> newStockPoints) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(), "SUB_INVENTORY", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        //获取本厂销售组织类型的仓库(成品库存点)
        List<String> saleOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 成品库存
        return inventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && rangeData.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
    }

    /**
     * 获取工序在制
     * @param inventoryBatchDetails
     * @param newStockPoints
     * @return
     */
    public Map<String, List<InventoryBatchDetailVO>> getOperationInventoryMap(List<InventoryBatchDetailVO> inventoryBatchDetails,
                                                                           List<NewStockPointVO> newStockPoints) {
        //获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 成品库存
        return inventoryBatchDetails.stream()
                .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(),
                        p.getProductCode(), p.getOperationCode())));
    }

    /**
     * 获取半成品库存
     * @param inventoryBatchDetails
     * @param newStockPoints
     * @return
     */
    public Map<String, List<InventoryBatchDetailVO>> getSemiFinishInventoryMap(List<InventoryBatchDetailVO> inventoryBatchDetails,
                                                                           List<NewStockPointVO> newStockPoints) {

        //获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        return inventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p ->
                        String.join("-", p.getStockPointCode(), p.getProductCode())));
    }

    /**
     * 半成品BOM数据
     * @param productCodeList
     * @return
     */
    public Map<String, List<BomRoutingStepInputVO>> getSemiBomMap(List<String> productCodeList) {
        Map<String, Object> params = new HashMap<>();
        params.put("productType", "SA");
        params.put("productCodeList", productCodeList);
        List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(SystemHolder.getScenario(), params);

        return StreamUtils.mapListByColumn(bomRoutingStepInputVOS,
                BomRoutingStepInputVO::getSourceProductCode);
    }


    /**
     * 标准工艺数据
     * @return
     */
    public Map<String, String> getStepMap(){
        List<StandardStepVO> standardSteps = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        return standardSteps.stream().collect(Collectors
                .toMap(p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
    }

    public String getSpecialStockPoint() {
        return ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
    }

    public String getSpecialStockPoint2() {
        return ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
    }

    public String getSemiInventory(String unPlanProductCode, Map<String, List<BomRoutingStepInputVO>> semiBomMap, Map<String, NewProductStockPointVO> productionProductCodeMap, Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap, Map<String, SubInventoryCargoLocationVO> cargoLocationMap, Map<String, String> stepMap, Map<String, List<InventoryBatchDetailVO>> operationInventoryMap, String specialStockPoint, String specialStockPoint2, NewProductStockPointVO productItem) {
        String semiInventory = "0";
        if (semiBomMap.containsKey(unPlanProductCode)) {
            List<BomRoutingStepInputVO> semiBomList = semiBomMap.get(unPlanProductCode);
            List<Double> semiValue = new ArrayList<>();
            for (BomRoutingStepInputVO bomRoutingStepInputVO : semiBomList) {
                String semiProductCode = bomRoutingStepInputVO.getProductCode();
                // 半品物料
                NewProductStockPointVO semiProductCodeItem = productionProductCodeMap.get(semiProductCode);
                if (Objects.isNull(semiProductCodeItem)) {
                    continue;
                }
                String semiProductCodeItemProductCode = semiProductCodeItem.getProductCode();
                String stockPointCode = semiProductCodeItem.getStockPointCode();
                String semiKey = stockPointCode + "-" + semiProductCodeItemProductCode;
                // 维护产品编码对应的半品库存
                List<InventoryBatchDetailVO> semiFinishList = MasterPlanServiceImpl.getFinishInventory(semiFinishInventoryMap
                        .get(semiKey), cargoLocationMap);
                BigDecimal semiFinishInventory = semiFinishList.stream().map(t ->
                        new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(semiProductCodeItem, stepMap,
                        operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, unPlanProductCode);
                // semiBomInventory = semiBomInventory + semiFinishInventory.doubleValue();
                semiValue.add(semiBomInventory);
            }
            // 发货计划总览的成型后库存，应该是大小片各自的半品库存，然后取小
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(semiValue)) {
                semiValue.sort(Comparator.comparing(Double::doubleValue));
                semiInventory = new BigDecimal(String.valueOf(semiValue.get(0))).stripTrailingZeros().toPlainString();
            }
        } else {
            double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(productItem, stepMap,
                    operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, unPlanProductCode);
            semiInventory = new BigDecimal(String.valueOf(semiBomInventory)).stripTrailingZeros().toPlainString();
        }
        return semiInventory;
    }
}
