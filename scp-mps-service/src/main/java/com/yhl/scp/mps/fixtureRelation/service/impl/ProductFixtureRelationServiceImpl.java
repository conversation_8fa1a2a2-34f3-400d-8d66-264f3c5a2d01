package com.yhl.scp.mps.fixtureRelation.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.fixtureRelation.convertor.ProductFixtureRelationConvertor;
import com.yhl.scp.mps.fixtureRelation.domain.entity.ProductFixtureRelationDO;
import com.yhl.scp.mps.fixtureRelation.domain.service.ProductFixtureRelationDomainService;
import com.yhl.scp.mps.fixtureRelation.dto.FixtureMoldToolingDTO;
import com.yhl.scp.mps.fixtureRelation.dto.FixtureStockPointProductDTO;
import com.yhl.scp.mps.fixtureRelation.dto.ProductFixtureRelationDTO;
import com.yhl.scp.mps.fixtureRelation.infrastructure.dao.ProductFixtureRelationDao;
import com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO;
import com.yhl.scp.mps.fixtureRelation.service.ProductFixtureRelationService;
import com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductFixtureRelationServiceImpl</code>
 * <p>
 * 产品与工装关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-07 11:14:57
 */
@Slf4j
@Service
public class ProductFixtureRelationServiceImpl extends AbstractService implements ProductFixtureRelationService {

    private static final String TOOL = "TOOL";
    @Resource
    private ProductFixtureRelationDao productFixtureRelationDao;
    @Resource
    private ProductFixtureRelationDomainService productFixtureRelationDomainService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private ManualAdjustHandleDao manualAdjustHandleDao;

    @Override
    public BaseResponse<Void> doCreate(ProductFixtureRelationDTO productFixtureRelationDTO) {
        // 0.数据转换
        ProductFixtureRelationDO productFixtureRelationDO = ProductFixtureRelationConvertor.INSTANCE.dto2Do(productFixtureRelationDTO);
        ProductFixtureRelationPO productFixtureRelationPO = ProductFixtureRelationConvertor.INSTANCE.dto2Po(productFixtureRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productFixtureRelationDomainService.validation(productFixtureRelationDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productFixtureRelationPO);
        productFixtureRelationDao.insert(productFixtureRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductFixtureRelationDTO productFixtureRelationDTO) {
        // 0.数据转换
        ProductFixtureRelationDO productFixtureRelationDO = ProductFixtureRelationConvertor.INSTANCE.dto2Do(productFixtureRelationDTO);
        ProductFixtureRelationPO productFixtureRelationPO = ProductFixtureRelationConvertor.INSTANCE.dto2Po(productFixtureRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productFixtureRelationDomainService.validation(productFixtureRelationDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productFixtureRelationPO);
        productFixtureRelationDao.update(productFixtureRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductFixtureRelationDTO> list) {
        List<ProductFixtureRelationPO> newList = ProductFixtureRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productFixtureRelationDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductFixtureRelationDTO> list) {
        List<ProductFixtureRelationPO> newList = ProductFixtureRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        productFixtureRelationDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productFixtureRelationDao.deleteBatch(idList);
        }
        return productFixtureRelationDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductFixtureRelationVO selectByPrimaryKey(String id) {
        ProductFixtureRelationPO po = productFixtureRelationDao.selectByPrimaryKey(id);
        return ProductFixtureRelationConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_FIXTURE_RELATION")
    public List<ProductFixtureRelationVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_FIXTURE_RELATION")
    public List<ProductFixtureRelationVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductFixtureRelationVO> dataList = productFixtureRelationDao.selectByCondition(sortParam, queryCriteriaParam);
        ProductFixtureRelationServiceImpl target = SpringBeanUtils.getBean(ProductFixtureRelationServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductFixtureRelationVO> selectByParams(Map<String, Object> params) {
        List<ProductFixtureRelationPO> list = productFixtureRelationDao.selectByParams(params);
        return ProductFixtureRelationConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductFixtureRelationVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<LabelValue<String>> getStandardResource(String organizationId, String standardResourceCode) {
        return newMdsFeign.selectMoldToolingGroupByParams(SystemHolder.getScenario(), organizationId, standardResourceCode);
    }

    @Override
    public List<LabelValue<String>> getPhysicalResourceById(String standardResourceId) {
        if (StringUtils.isBlank(standardResourceId)) {
            throw new BusinessException("标准资源ID不能为空");
        }
        return newMdsFeign.selectMoldToolingByStandResourceId(SystemHolder.getScenario(), standardResourceId);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_FIXTURE_RELATION.getCode();
    }

    @Override
    public List<ProductFixtureRelationVO> invocation(List<ProductFixtureRelationVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> synMoldToolingGroupDir(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        BaseResponse<String> externalApiResp = newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MOLD_TOOLING_GROUP_DIR.getCode(), params);
        if (!externalApiResp.getSuccess()) {
            return BaseResponse.error(externalApiResp.getMsg());
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroupDir(List<MesMoldToolingGroupDir> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<ProductFixtureRelationDTO> insertDtos = Lists.newArrayList();
        List<String> kids = list.stream().map(MesMoldToolingGroupDir::getToolingGroupId).distinct().collect(Collectors.toList());
        List<FixtureMoldToolingDTO> fixtureMoldToolingList = manualAdjustHandleDao.selectFixtureMoldToolingByKids(kids);
        List<String> organizationIds = fixtureMoldToolingList.stream().map(FixtureMoldToolingDTO::getOrganizationId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(organizationIds)) {
            log.info("同步系统模具工装族与目录号关系数据的------组织不存在");
            return BaseResponse.success();
        }
        Map<String, List<FixtureMoldToolingDTO>> fixtureMoldToolingMap = fixtureMoldToolingList.stream().collect(Collectors.groupingBy(FixtureMoldToolingDTO::getKid));
        List<String> directoryCodes = list.stream().map(MesMoldToolingGroupDir::getDirectoryCode).distinct().collect(Collectors.toList());
        List<FixtureStockPointProductDTO> fixtureStockPointProductDTOS = manualAdjustHandleDao.selectProductCodesByDirNums(directoryCodes);
        if (CollectionUtils.isEmpty(fixtureStockPointProductDTOS)) {
            log.info("同步系统模具工装族与目录号关系数据的------物品不存在");
            return BaseResponse.success();
        }
        Map<String, List<String>> dirNumProductMap = fixtureStockPointProductDTOS.stream().collect(Collectors.groupingBy(FixtureStockPointProductDTO::getDirNum, Collectors.mapping(FixtureStockPointProductDTO::getProductCode, Collectors.toList())));
        List<String> productCodes = fixtureStockPointProductDTOS.stream().map(FixtureStockPointProductDTO::getProductCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            log.info("同步系统模具工装族与目录号关系数据的------物品不存在");
            return BaseResponse.success();
        }
        List<FixtureStockPointProductDTO> productDTOS = manualAdjustHandleDao.selectProductsByProductCodes(organizationIds, productCodes);
        Map<String, List<FixtureStockPointProductDTO>> productCodeMap = productDTOS.stream().collect(Collectors.groupingBy(FixtureStockPointProductDTO::getProductCode));
        Map<String, Object> relationParams = MapUtil.newHashMap();
        List<ProductFixtureRelationPO> oldProductFixtureRelations = productFixtureRelationDao.selectByParams(relationParams);
        Map<String, ProductFixtureRelationPO> fixtureRelationMap = oldProductFixtureRelations.stream().collect(
                Collectors.toMap(x -> String.join("&", x.getStockPointCode(), x.getProductCode(), x.getStandardResourceId(), x.getPhysicalResourceId()),
                        Function.identity(),
                        (v1, v2) -> v1));

        for (MesMoldToolingGroupDir toolingGroupDir : list) {
            String kid = toolingGroupDir.getToolingGroupId();
            // 获取资源
            List<FixtureMoldToolingDTO> moldToolingDTOS = fixtureMoldToolingMap.get(kid);
            if (CollectionUtils.isEmpty(moldToolingDTOS)) {
                continue;
            }
            String directoryCode = toolingGroupDir.getDirectoryCode();
            List<String> matchProductCodes = dirNumProductMap.get(directoryCode);
            if (CollectionUtils.isEmpty(matchProductCodes)) {
                continue;
            }
            for (FixtureMoldToolingDTO moldToolingDTO : moldToolingDTOS) {
                String organizationId = moldToolingDTO.getOrganizationId();
                if (StringUtils.isBlank(organizationId)) {
                    continue;
                }
                String standardResourceId = moldToolingDTO.getStandardResourceId();
                String physicalResourceId = moldToolingDTO.getPhysicalResourceId();
                for (String matchProductCode : matchProductCodes) {
                    List<FixtureStockPointProductDTO> matchProducts = productCodeMap.get(matchProductCode);
                    if (CollectionUtils.isEmpty(matchProducts)) {
                        continue;
                    }
                    for (FixtureStockPointProductDTO matchProduct : matchProducts) {
                        String productOrganizationId = matchProduct.getOrganizationId();
                        if (!productOrganizationId.equals(organizationId)) {
                            continue;
                        }
                        String key = String.join("&", matchProduct.getStockPointCode(), matchProduct.getProductCode(), standardResourceId, physicalResourceId);
                        if (fixtureRelationMap.containsKey(key)) {
                            continue;
                        }
                        insertDtos.add(ProductFixtureRelationDTO
                                .builder()
                                .id(UUIDUtil.getUUID())
                                .stockPointCode(matchProduct.getStockPointCode())
                                .productCode(matchProduct.getProductCode())
                                .standardResourceId(standardResourceId)
                                .toolingType("TOOLING_MOLD")
                                .physicalResourceId(physicalResourceId).build());
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertDtos)) {
            List<List<ProductFixtureRelationDTO>> partitions = Lists.partition(insertDtos, 2000);
            for (List<ProductFixtureRelationDTO> partition : partitions) {
                doCreateBatch(partition);
            }
        }
        return BaseResponse.success("同步成功");
    }
}
