package com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;


public class SubInventoryCargoLocationPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 221654125097240492L;

        /**
     * 公司
     */
        private String corporationCode;
        /**
     * 库存点代码
     */
        private String factoryCode;
        /**
     * 库存点名称
     */
        private String factoryName;
        /**
     * 仓库
     */
        private String stashCode;
        /**
     * 仓库描述
     */
        private String stashName;
        /**
     * 货位
     */
        private String freightSpaceCode;
        /**
     * 货位描述
     */
        private String freightSpaceName;
        /**
     * 来源
     */
        private String source;
        /**
     * 是否有效
     */
        private String valid;
        /**
     * ERP货位
     */
        private String erpFreightSpaceCode;
        /**
     * 更新时间
     */
        private Date updateTime;
        /**
     * 版本号
     */
        private Integer versionValue;

    public String getCorporationCode() {
        return corporationCode;
    }

    public void setCorporationCode(String corporationCode) {
        this.corporationCode = corporationCode;
    }

    public String getFactoryCode() {
        return factoryCode;
    }

    public void setFactoryCode(String factoryCode) {
        this.factoryCode = factoryCode;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getStashCode() {
        return stashCode;
    }

    public void setStashCode(String stashCode) {
        this.stashCode = stashCode;
    }

    public String getStashName() {
        return stashName;
    }

    public void setStashName(String stashName) {
        this.stashName = stashName;
    }

    public String getFreightSpaceCode() {
        return freightSpaceCode;
    }

    public void setFreightSpaceCode(String freightSpaceCode) {
        this.freightSpaceCode = freightSpaceCode;
    }

    public String getFreightSpaceName() {
        return freightSpaceName;
    }

    public void setFreightSpaceName(String freightSpaceName) {
        this.freightSpaceName = freightSpaceName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public String getErpFreightSpaceCode() {
        return erpFreightSpaceCode;
    }

    public void setErpFreightSpaceCode(String erpFreightSpaceCode) {
        this.erpFreightSpaceCode = erpFreightSpaceCode;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
