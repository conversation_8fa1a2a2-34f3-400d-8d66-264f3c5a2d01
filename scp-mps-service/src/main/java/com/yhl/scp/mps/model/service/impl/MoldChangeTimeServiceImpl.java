package com.yhl.scp.mps.model.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.model.convertor.MoldChangeTimeConvertor;
import com.yhl.scp.mps.model.domain.entity.MoldChangeTimeDO;
import com.yhl.scp.mps.model.domain.service.MoldChangeTimeDomainService;
import com.yhl.scp.mps.model.dto.MoldChangeTimeDTO;
import com.yhl.scp.mps.model.dto.MoldChangeTimeExportDTO;
import com.yhl.scp.mps.model.infrastructure.dao.MoldChangeTimeDao;
import com.yhl.scp.mps.model.infrastructure.po.MoldChangeTimePO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.resource.service.ResourceService;
import com.yhl.scp.mps.resource.vo.ResourceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MoldChangeTimeServiceImpl</code>
 * <p>
 * 换模换型时间应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:20:44
 */
@Slf4j
@Service
public class MoldChangeTimeServiceImpl extends AbstractService implements MoldChangeTimeService {

    @Resource
    private MoldChangeTimeDao moldChangeTimeDao;

    @Resource
    private MoldChangeTimeDomainService moldChangeTimeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private ResourceService resourceService;

    @Resource
    private MoldChangeTimeService moldChangeTimeService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MoldChangeTimeDTO moldChangeTimeDTO) {
        // 0.数据转换
        MoldChangeTimeDO moldChangeTimeDO = MoldChangeTimeConvertor.INSTANCE.dto2Do(moldChangeTimeDTO);
        MoldChangeTimePO moldChangeTimePO = MoldChangeTimeConvertor.INSTANCE.dto2Po(moldChangeTimeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldChangeTimeDomainService.validation(moldChangeTimeDO);
        setResource(moldChangeTimePO);
        // 2.数据持久化
        BasePOUtils.insertFiller(moldChangeTimePO);
        moldChangeTimeDao.insert(moldChangeTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    private void setResource(MoldChangeTimePO moldChangeTimePO) {
        Map<String, Object> params = new HashMap<>();
        params.put("resourceCode", moldChangeTimePO.getResourceCode());
        List<ResourceVO> resourceVOList = resourceService.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(resourceVOList)) {
            Map<String, ResourceVO> resourceVOMap = resourceVOList.stream().filter(t -> StringUtils.isNotEmpty(t.getResourceCode())).collect(Collectors.toMap(ResourceVO::getResourceCode, Function.identity(), (v1, v2) -> v2));
            if (resourceVOMap.containsKey(moldChangeTimePO.getResourceCode())) {
                moldChangeTimePO.setResourceName(resourceVOMap.get(moldChangeTimePO.getResourceCode()).getResourceName());
            }
        }
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MoldChangeTimeDTO moldChangeTimeDTO) {
        // 0.数据转换
        MoldChangeTimeDO moldChangeTimeDO = MoldChangeTimeConvertor.INSTANCE.dto2Do(moldChangeTimeDTO);
        MoldChangeTimePO moldChangeTimePO = MoldChangeTimeConvertor.INSTANCE.dto2Po(moldChangeTimeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        moldChangeTimeDomainService.validation(moldChangeTimeDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(moldChangeTimePO);
        moldChangeTimeDao.update(moldChangeTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MoldChangeTimeDTO> list) {
        List<MoldChangeTimePO> newList = MoldChangeTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        moldChangeTimeDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MoldChangeTimeDTO> list) {
        List<MoldChangeTimePO> newList = MoldChangeTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        moldChangeTimeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return moldChangeTimeDao.deleteBatch(idList);
        }
        return moldChangeTimeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MoldChangeTimeVO selectByPrimaryKey(String id) {
        MoldChangeTimePO po = moldChangeTimeDao.selectByPrimaryKey(id);
        return MoldChangeTimeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MOLD_CHANGE_TIME")
    public List<MoldChangeTimeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MOLD_CHANGE_TIME")
    public List<MoldChangeTimeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MoldChangeTimeVO> dataList = moldChangeTimeDao.selectByCondition(sortParam, queryCriteriaParam);
        MoldChangeTimeServiceImpl target = springBeanUtils.getBean(MoldChangeTimeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MoldChangeTimeVO> selectByParams(Map<String, Object> params) {
        List<MoldChangeTimePO> list = moldChangeTimeDao.selectByParams(params);
        return MoldChangeTimeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MoldChangeTimeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void export(HttpServletResponse response) {
        List<MoldChangeTimePO> moldChangeTimePOS = moldChangeTimeDao.selectByParams(new HashMap<>(2));
        List<MoldChangeTimeExportDTO> moldChangeTimeExportDTOS = MoldChangeTimeConvertor.INSTANCE.po2ExportDtos(moldChangeTimePOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("换模换型时间.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), MoldChangeTimeExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("换模换型时间")
                    .doWrite(moldChangeTimeExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void loseEffectivenessBatch(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<MoldChangeTimePO> moldChangeTimePOS = moldChangeTimeDao.selectByPrimaryKeys(ids);
        if (CollectionUtils.isNotEmpty(moldChangeTimePOS)) {
            moldChangeTimePOS.forEach(t -> t.setEnabled(YesOrNoEnum.NO.getCode()));
            BasePOUtils.updateBatchFiller(moldChangeTimePOS);
            moldChangeTimeDao.updateBatch(moldChangeTimePOS);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MOLD_CHANGE_TIME.getCode();
    }

    @Override
    public List<MoldChangeTimeVO> invocation(List<MoldChangeTimeVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> syncMoldChangeTime(String scenario, List<MesMoldChangeTime> moldChangeTimes) {
        // 检查输入列表是否为空
        if (CollectionUtils.isEmpty(moldChangeTimes)) {
            return BaseResponse.success();
        }
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario,
                "PRODUCT_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> rangeList = Arrays.asList(rangeData.split(","));
        List<MesMoldChangeTime> filteredMoldChangeTimes = moldChangeTimes.stream()
                .filter(mct -> rangeList.contains(mct.getPlantCode()))
                .collect(Collectors.toList());

        // 如果过滤后没有数据，直接返回成功
        if (CollectionUtils.isEmpty(filteredMoldChangeTimes)) {
            return BaseResponse.success();
        }

        // 设置动态数据源
        DynamicDataSourceContextHolder.setDataSource(scenario);

        // 从过滤后的数据中提取不同的kid、operationNames和resourceCodes
        List<String> kids = filteredMoldChangeTimes.stream().map(MesMoldChangeTime::getKid).distinct().collect(Collectors.toList());
        List<String> operationNames = filteredMoldChangeTimes.stream().map(MesMoldChangeTime::getSequenceDesc).distinct().collect(Collectors.toList());
        List<String> resourceCodes = filteredMoldChangeTimes.stream().map(MesMoldChangeTime::getProdLineCode).distinct().collect(Collectors.toList());

        // 准备查询参数并获取旧数据
        Map<String, Object> params = new HashMap<>();
        params.put("kids", kids);
        params.put("operationNames", operationNames);
        params.put("resourceCodes", resourceCodes);
        List<MoldChangeTimeVO> oldPOList = moldChangeTimeService.selectByKidOrOperationNameOrResource(params);
        Map<String, MoldChangeTimeVO> oldPOMap = oldPOList.stream()
                .collect(Collectors.toMap(t -> t.getKid() + "|" + t.getOperationName(), Function.identity(), (v1, v2) -> v1));

        // 初始化插入和更新的DTO列表
        List<MoldChangeTimeDTO> insertMoldChangeTimeDTOS = new ArrayList<>();
        List<MoldChangeTimeDTO> updateMoldChangeTimeDTOS = new ArrayList<>();

        // 遍历过滤后的moldChangeTimes进行数据同步
        for (MesMoldChangeTime mesMoldChangeTime : filteredMoldChangeTimes) {
            String dataKey = mesMoldChangeTime.getKid() + "|" + mesMoldChangeTime.getSequenceDesc();
            MoldChangeTimeDTO moldChangeTimeDTO = new MoldChangeTimeDTO();

            if (oldPOMap.containsKey(dataKey)) {
                MoldChangeTimeVO oldPO = oldPOMap.get(dataKey);
                BeanUtils.copyProperties(oldPO, moldChangeTimeDTO);
                if (Objects.nonNull(mesMoldChangeTime.getEnableFlag())) {
                    moldChangeTimeDTO.setEnabled("Y".equals(mesMoldChangeTime.getEnableFlag()) ? "YES" :
                            "N".equals(mesMoldChangeTime.getEnableFlag()) ? "NO" : mesMoldChangeTime.getEnableFlag());
                }
                moldChangeTimeDTO.setOutsideDieChangeTime(oldPO.getOutsideDieChangeTime());
                if (Objects.nonNull(mesMoldChangeTime.getPlantCode())) {
                    moldChangeTimeDTO.setStockPointCode(mesMoldChangeTime.getPlantCode());
                }
                if (Objects.nonNull(mesMoldChangeTime.getSequenceCode())) {
                    moldChangeTimeDTO.setOperationCode(mesMoldChangeTime.getSequenceCode());
                }
                if (Objects.nonNull(mesMoldChangeTime.getSequenceDesc())) {
                    moldChangeTimeDTO.setOperationName(mesMoldChangeTime.getSequenceDesc());
                }
                if (Objects.nonNull(mesMoldChangeTime.getItemCode())) {
                    moldChangeTimeDTO.setProductCode(mesMoldChangeTime.getItemCode());
                }
                if (Objects.nonNull(mesMoldChangeTime.getItemDesc())) {
                    moldChangeTimeDTO.setProductName(mesMoldChangeTime.getItemDesc());
                }
                if (Objects.nonNull(mesMoldChangeTime.getProdLineCode())) {
                    moldChangeTimeDTO.setResourceCode(mesMoldChangeTime.getProdLineCode());
                }
                if (Objects.nonNull(mesMoldChangeTime.getProdLineDesc())) {
                    moldChangeTimeDTO.setResourceName(mesMoldChangeTime.getProdLineDesc());
                }
                if (Objects.nonNull(mesMoldChangeTime.getBeatHour())) {
                    moldChangeTimeDTO.setDieChangeTime(mesMoldChangeTime.getBeatHour());
                } else {
                    moldChangeTimeDTO.setDieChangeTime(0L); // 为空的话传0
                }

                updateMoldChangeTimeDTOS.add(moldChangeTimeDTO);
            } else {
                moldChangeTimeDTO.setEnabled(Objects.nonNull(mesMoldChangeTime.getEnableFlag())
                        ? ("Y".equals(mesMoldChangeTime.getEnableFlag()) ? "YES"
                        : "N".equals(mesMoldChangeTime.getEnableFlag()) ? "NO" : mesMoldChangeTime.getEnableFlag())
                        : String.valueOf(Boolean.TRUE));
                moldChangeTimeDTO.setOutsideDieChangeTime(null);
                moldChangeTimeDTO.setStockPointCode(mesMoldChangeTime.getPlantCode());
                moldChangeTimeDTO.setOperationCode(mesMoldChangeTime.getSequenceCode());
                moldChangeTimeDTO.setOperationName(mesMoldChangeTime.getSequenceDesc());
                moldChangeTimeDTO.setProductCode(mesMoldChangeTime.getItemCode());
                moldChangeTimeDTO.setProductName(mesMoldChangeTime.getItemDesc());
                moldChangeTimeDTO.setKid(mesMoldChangeTime.getKid());
                if (Objects.nonNull(mesMoldChangeTime.getProdLineCode())) {
                    moldChangeTimeDTO.setResourceCode(mesMoldChangeTime.getProdLineCode());
                }
                if (Objects.nonNull(mesMoldChangeTime.getProdLineDesc())) {
                    moldChangeTimeDTO.setResourceName(mesMoldChangeTime.getProdLineDesc());
                }
                if (Objects.nonNull(mesMoldChangeTime.getBeatHour())) {
                    moldChangeTimeDTO.setDieChangeTime(mesMoldChangeTime.getBeatHour());
                } else {
                    moldChangeTimeDTO.setDieChangeTime(0L); // 为空的话传0
                }

                insertMoldChangeTimeDTOS.add(moldChangeTimeDTO);
            }
        }

        // 批量插入和更新操作
        if (CollectionUtils.isNotEmpty(insertMoldChangeTimeDTOS)) {
            List<List<MoldChangeTimeDTO>> partitionInsert = com.google.common.collect.Lists.partition(insertMoldChangeTimeDTOS, 3000);
            partitionInsert.forEach(poDtos -> moldChangeTimeService.doCreateBatch(poDtos));
        }

        if (CollectionUtils.isNotEmpty(updateMoldChangeTimeDTOS)) {
            List<List<MoldChangeTimeDTO>> partitionUpdate = com.google.common.collect.Lists.partition(updateMoldChangeTimeDTOS, 3000);
            partitionUpdate.forEach(poDtos -> moldChangeTimeService.doUpdateBatch(poDtos));
        }

        DynamicDataSourceContextHolder.clearDataSource();

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<MoldChangeTimeVO> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步换模换型时间接口");
            if (StringUtils.isEmpty(scenario)) {
                scenario = SystemHolder.getScenario();
                tenantCode = SystemHolder.getTenantId();
            }
            HashMap<String, Object> map = MapUtil.newHashMap();

            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.MOLD_CHANGE_TIME.getCode(), map);

        } catch (Exception e) {
            log.error("同步换模换型时间数据报错,{}", e.getMessage());
            throw new BusinessException("同步换模换型时间数据报错", e.getMessage());
        }
        return BaseResponse.success("同步操作完成");
    }

    @Override
    public List<MoldChangeTimeVO> selectByKidOrOperationNameOrResource(Map<String, Object> params) {
        return MoldChangeTimeConvertor.INSTANCE.po2Vos(moldChangeTimeDao.selectByKidOrOperationNameOrResource(params));
    }

    @Override
    public List<MoldChangeTimeVO> selectTime() {
        List<MoldChangeTimePO> list = moldChangeTimeDao.selectTime();
        return MoldChangeTimeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MoldChangeTimeVO> selectKeyOperationTime() {
        List<MoldChangeTimePO> list = moldChangeTimeDao.selectKeyOperationTime();
        return MoldChangeTimeConvertor.INSTANCE.po2Vos(list);
    }
}
