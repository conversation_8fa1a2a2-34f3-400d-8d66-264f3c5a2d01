package com.yhl.scp.mps.proabnormalfeedback.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.calendar.dto.CalendarForFeedbackDTO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.proabnormalfeedback.convertor.ProAbnormalFeedbackConvertor;
import com.yhl.scp.mps.proabnormalfeedback.domain.entity.ProAbnormalFeedbackDO;
import com.yhl.scp.mps.proabnormalfeedback.domain.service.ProAbnormalFeedbackDomainService;
import com.yhl.scp.mps.proabnormalfeedback.dto.ProAbnormalFeedbackDTO;
import com.yhl.scp.mps.proabnormalfeedback.infrastructure.dao.ProAbnormalFeedbackDao;
import com.yhl.scp.mps.proabnormalfeedback.infrastructure.po.ProAbnormalFeedbackPO;
import com.yhl.scp.mps.proabnormalfeedback.service.ProAbnormalFeedbackService;
import com.yhl.scp.mps.proabnormalfeedback.vo.ProAbnormalFeedbackVO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.mps.schedule.service.AbnormalAdjustScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ProAbnormalFeedbackServiceImpl</code>
 * <p>
 * 生产异常反馈应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-22 16:54:04
 */
@Slf4j
@Service
public class ProAbnormalFeedbackServiceImpl extends AbstractService implements ProAbnormalFeedbackService {

    public static final String USER_CHECK_MSG = "当前用户无权限提交,请先维护账户信息";

    @Resource
    private ProAbnormalFeedbackDao proAbnormalFeedbackDao;

    @Resource
    private ProAbnormalFeedbackDomainService proAbnormalFeedbackDomainService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private MdsFeign mdsFeign;

    @Resource
    private AbnormalAdjustScheduleService abnormalAdjustScheduleService;

    @Override
    public BaseResponse<Void> doCreate(ProAbnormalFeedbackDTO proAbnormalFeedbackDTO) {
        // 0.数据转换
        ProAbnormalFeedbackDO proAbnormalFeedbackDO = ProAbnormalFeedbackConvertor.INSTANCE.dto2Do(proAbnormalFeedbackDTO);
        ProAbnormalFeedbackPO proAbnormalFeedbackPO = ProAbnormalFeedbackConvertor.INSTANCE.dto2Po(proAbnormalFeedbackDTO);
        // 1.数据校验
        proAbnormalFeedbackDomainService.validation(proAbnormalFeedbackDO);
        proAbnormalFeedbackDomainService.getOrgInfo(proAbnormalFeedbackPO);
        // 校验当前用户
        String userName = proAbnormalFeedbackDTO.getUserName();
        User user = null;
        if (StringUtils.isNotEmpty(userName)) {
            user = ipsNewFeign.getUserByUserName(userName);
            if (Objects.isNull(user)) {
                throw new BusinessException(USER_CHECK_MSG);
            }
        }

        // 2.维护对应异常日历, 开始时间必须晚于当前时间，且预计关闭时间必须晚于开始时间
        if (proAbnormalFeedbackPO.getForecastCloseTime() != null && proAbnormalFeedbackPO.getForecastCloseTime().after(proAbnormalFeedbackPO.getSubmissionTime())) {
            // 开始时间，结束时间，设备编码，已绑定规则id，返回班次规则ID,
            CalendarForFeedbackDTO calendarForFeedbackDTO = CalendarForFeedbackDTO.builder().startTime(proAbnormalFeedbackDTO.getSubmissionTime()).endTime(proAbnormalFeedbackDTO.getForecastCloseTime()).resourceCode(proAbnormalFeedbackDTO.getResourceCode()).organizationCode(proAbnormalFeedbackDTO.getOrganizationCode()).build();
            List<String> calendarRuleIds = newMdsFeign.doResourceCalendarForFeedback(getMdsScenario(), calendarForFeedbackDTO);
            proAbnormalFeedbackPO.setCalendarRuleIds(String.join(",", calendarRuleIds));
        }
        // 3.数据持久化
        if (StringUtils.isNotEmpty(userName) && !Objects.isNull(user)) {
            proAbnormalFeedbackPO.setEnabled(YesOrNoEnum.YES.getCode());
            proAbnormalFeedbackPO.setCreator(user.getId());
            proAbnormalFeedbackPO.setCreateTime(new Date());
            proAbnormalFeedbackPO.setModifier(user.getId());
            proAbnormalFeedbackPO.setModifyTime(new Date());
            proAbnormalFeedbackPO.setVersionValue(1);
        } else {
            BasePOUtils.insertFiller(proAbnormalFeedbackPO);
        }
        // 生产异常反馈id给你随机值
        proAbnormalFeedbackPO.setAbnormalFeedbackId(UUIDUtil.getUUID());
        proAbnormalFeedbackDao.insert(proAbnormalFeedbackPO);
        // 执行异常日历算法
        BaseResponse<Void> check = abnormalAdjustScheduleService.doAbnormalAdjustSchedule(ProAbnormalFeedbackConvertor.INSTANCE.po2Dto(proAbnormalFeedbackPO));
        if (!check.getSuccess()) {
            return check;
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProAbnormalFeedbackDTO proAbnormalFeedbackDTO) {
        // 0.数据转换
        ProAbnormalFeedbackDO proAbnormalFeedbackDO = ProAbnormalFeedbackConvertor.INSTANCE.dto2Do(proAbnormalFeedbackDTO);
        ProAbnormalFeedbackPO proAbnormalFeedbackPO = ProAbnormalFeedbackConvertor.INSTANCE.dto2Po(proAbnormalFeedbackDTO);
        // 1.数据校验
        proAbnormalFeedbackDomainService.validation(proAbnormalFeedbackDO);
        proAbnormalFeedbackDomainService.getOrgInfo(proAbnormalFeedbackPO);
        ProAbnormalFeedbackPO currentPo = proAbnormalFeedbackDao.selectByPrimaryKey(proAbnormalFeedbackDTO.getId());

        String userName = proAbnormalFeedbackDTO.getUserName();
        User user = null;
        if (StringUtils.isNotEmpty(userName)) {
            user = ipsNewFeign.getUserByUserName(userName);
            if (Objects.isNull(user)) {
                throw new BusinessException("USER_CHECK_MSG");
            }
            if (!Objects.equals(user.getId(), currentPo.getCreator())) {
                throw new BusinessException("当前记录非本人提交，无权修改!");
            }
        }
        // 2.如果开始时间，预计结束时间发生变化，更新对应的异常日历，维护了实际结束时间;
        if (proAbnormalFeedbackDTO.getAchieveCloseTime() != null) {
            // 执行删除逻辑
            String calendarRuleIds = currentPo.getCalendarRuleIds();
            if (StringUtils.isNotEmpty(calendarRuleIds)) {
                CalendarForFeedbackDTO calendarForFeedbackDTO = CalendarForFeedbackDTO.builder().onlyDeleteCalendarRuleIds(calendarRuleIds).build();
                newMdsFeign.doResourceCalendarForFeedback(getMdsScenario(), calendarForFeedbackDTO);
            }
        } else {
            // 判断开始时间和预计关闭时间是否发生变化
            String submissionTime = DateUtils.dateToString(proAbnormalFeedbackDTO.getSubmissionTime(), DateUtils.COMMON_DATE_STR1);
            String forecastCloseTime = DateUtils.dateToString(proAbnormalFeedbackDTO.getForecastCloseTime(), DateUtils.COMMON_DATE_STR1);
            String currentSubmissionTime = DateUtils.dateToString(currentPo.getSubmissionTime(), DateUtils.COMMON_DATE_STR1);
            String currentForecastCloseTime = DateUtils.dateToString(currentPo.getForecastCloseTime(), DateUtils.COMMON_DATE_STR1);
            if (!Objects.equals(submissionTime, forecastCloseTime) || !Objects.equals(currentSubmissionTime, currentForecastCloseTime)) {
                // 开始时间，结束时间，设备编码，已绑定规则id，返回班次规则ID,
                CalendarForFeedbackDTO calendarForFeedbackDTO = CalendarForFeedbackDTO.builder().startTime(proAbnormalFeedbackDTO.getSubmissionTime()).endTime(proAbnormalFeedbackDTO.getForecastCloseTime()).resourceCode(proAbnormalFeedbackDTO.getResourceCode()).calendarRuleIds(currentPo.getCalendarRuleIds()).build();
                if (proAbnormalFeedbackDTO.getAchieveCloseTime() != null) {
                    calendarForFeedbackDTO.setEndTime(proAbnormalFeedbackDTO.getAchieveCloseTime());
                }
                List<String> calendarRuleIds = newMdsFeign.doResourceCalendarForFeedback(getMdsScenario(), calendarForFeedbackDTO);
                proAbnormalFeedbackPO.setCalendarRuleIds(String.join(",", calendarRuleIds));
            }
        }
        // 3.数据持久化
        proAbnormalFeedbackPO.setAbnormalFeedbackId(currentPo.getAbnormalFeedbackId());
        if (StringUtils.isNotEmpty(userName) && !Objects.isNull(user)) {
            proAbnormalFeedbackPO.setModifyTime(new Date());
            proAbnormalFeedbackPO.setVersionValue(currentPo.getVersionValue());
            proAbnormalFeedbackDao.updateSelective(proAbnormalFeedbackPO);
        } else {
            BasePOUtils.updateFiller(proAbnormalFeedbackPO);
            proAbnormalFeedbackDao.update(proAbnormalFeedbackPO);
        }
        // 执行异常日历算法
        BaseResponse<Void> check = abnormalAdjustScheduleService.doAbnormalAdjustSchedule(ProAbnormalFeedbackConvertor.INSTANCE.po2Dto(proAbnormalFeedbackPO));
        if (!check.getSuccess()) {
            return check;
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProAbnormalFeedbackDTO> list) {
        List<ProAbnormalFeedbackPO> newList = ProAbnormalFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        proAbnormalFeedbackDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProAbnormalFeedbackDTO> list) {
        List<ProAbnormalFeedbackPO> newList = ProAbnormalFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        proAbnormalFeedbackDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        // 删除对应班次，日历规则
        List<ProAbnormalFeedbackPO> deleteList = proAbnormalFeedbackDao.selectByPrimaryKeys(idList);
        StringBuilder allCalendarRuleIds = new StringBuilder();
        for (ProAbnormalFeedbackPO proAbnormalFeedbackPO : deleteList) {
            String calendarRuleIds = proAbnormalFeedbackPO.getCalendarRuleIds();
            if (StringUtils.isNotEmpty(calendarRuleIds)) {
                if (StringUtils.isEmpty(allCalendarRuleIds)) {
                    allCalendarRuleIds = new StringBuilder(calendarRuleIds);
                } else {
                    allCalendarRuleIds = allCalendarRuleIds.append(",").append(calendarRuleIds);
                }
            }
        }
        if (StringUtils.isNotEmpty(allCalendarRuleIds)) {
            CalendarForFeedbackDTO calendarForFeedbackDTO = CalendarForFeedbackDTO.builder().onlyDeleteCalendarRuleIds(allCalendarRuleIds.toString()).build();
            newMdsFeign.doResourceCalendarForFeedback(getMdsScenario(), calendarForFeedbackDTO);
        }
        if (idList.size() > 1) {
            return proAbnormalFeedbackDao.deleteBatch(idList);
        }
        return proAbnormalFeedbackDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProAbnormalFeedbackVO selectByPrimaryKey(String id) {
        ProAbnormalFeedbackPO po = proAbnormalFeedbackDao.selectByPrimaryKey(id);
        return ProAbnormalFeedbackConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "ABNORMAL_FEEDBACK")
    public List<ProAbnormalFeedbackVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "ABNORMAL_FEEDBACK")
    public List<ProAbnormalFeedbackVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProAbnormalFeedbackVO> dataList = proAbnormalFeedbackDao.selectByCondition(sortParam, queryCriteriaParam);
        ProAbnormalFeedbackServiceImpl target = SpringBeanUtils.getBean(ProAbnormalFeedbackServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProAbnormalFeedbackVO> selectByParams(Map<String, Object> params) {
        List<ProAbnormalFeedbackPO> list = proAbnormalFeedbackDao.selectByParams(params);
        return ProAbnormalFeedbackConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProAbnormalFeedbackVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<LabelValue<String>> queryFailureType() {
        List<ProAbnormalFeedbackVO> abnormalFeedbackVOList = this.selectAll();
        return abnormalFeedbackVOList.stream().map(x -> new LabelValue<>(x.getFailureType(), x.getFailureCode())).collect(Collectors.toList());
    }

    @Override
    public void insertFieldById(String id, String achieveTime) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(achieveTime, formatter);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("dateTime", dateTime);
        proAbnormalFeedbackDao.insertFieldById(params);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ABNORMAL_FEEDBACK.getCode();
    }

    @Override
    public List<ProAbnormalFeedbackVO> invocation(List<ProAbnormalFeedbackVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<LabelValue<String>> selectProductOrderCodes(String productOrderCode) {
        List<String> productOrderCodes = mpsProReportingFeedbackService.selectProductOrderCodes(productOrderCode);
        return productOrderCodes.stream().filter(StringUtils::isNotEmpty).map(x -> new LabelValue<>(x, x)).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> selectPhysicalResource(String orgCode, String resourceCode) {
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(orgCode, null, null);
        if (StringUtils.isNotEmpty(resourceCode)) {
            physicalResourceVOS = physicalResourceVOS.stream().filter(e -> Objects.equals(resourceCode, e.getPhysicalResourceCode())).collect(Collectors.toList());
        }
        return physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode() + "-" + item.getPhysicalResourceName(), item.getPhysicalResourceCode())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> selectPhysicalResourceByUserName(String userName) {
        User userByUserName = ipsNewFeign.getUserByUserName(userName);
        if (Objects.isNull(userByUserName)) {
            return new ArrayList<>();
        }
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResourceByUserId(userByUserName.getId());
        return physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode() + "-" + item.getPhysicalResourceName(), item.getPhysicalResourceCode())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> org() {
        List<ProductionOrganizationVO> productionOrganizationVOS = newMdsFeign.selectProductionOrganizeAll();
        return productionOrganizationVOS.stream().map(item -> new LabelValue<>(item.getOrganizationName(), item.getOrganizationCode())).collect(Collectors.toList());
    }

    private String getMdsScenario() {
        return SystemHolder.getScenario();
    }

    @Override
    public List<ProAbnormalFeedbackVO> selectEffectiveAbnormalFeedback(String resourceCode, List<String> ids, String submissionStartTime, String submissionEndTime) {
        if (StringUtils.isNotEmpty(submissionStartTime)) {
            submissionStartTime = submissionStartTime + " 00:00:00";
        }
        if (StringUtils.isNotEmpty(submissionEndTime)) {
            submissionEndTime = submissionEndTime + " 23:59:59";
        }

        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date startForecastCloseTime = DateUtils.getDayFirstTime(planStartTime);
        return proAbnormalFeedbackDao.selectEffectiveAbnormalFeedback(resourceCode, ids, submissionStartTime, submissionEndTime, startForecastCloseTime);
    }

    @Override
    public void doDeleteById(String id, String userName) {
        User user = ipsNewFeign.getUserByUserName(userName);
        if (Objects.isNull(user)) {
            throw new BusinessException("USER_CHECK_MSG");
        }
        ProAbnormalFeedbackPO currentPo = proAbnormalFeedbackDao.selectByPrimaryKey(id);
        if (!Objects.equals(user.getId(), currentPo.getCreator())) {
            throw new BusinessException("当前记录非本人提交，无权删除!");
        }
        this.doDelete(Collections.singletonList(id));
    }

    @Override
    public List<ProAbnormalFeedbackVO> selectByList(ProAbnormalFeedbackDTO proAbnormalFeedbackDTO) {
        if (StringUtils.isNotEmpty(proAbnormalFeedbackDTO.getUserName())) {
            User user = ipsNewFeign.getUserByUserName(proAbnormalFeedbackDTO.getUserName());
            if (Objects.isNull(user)) {
                throw new BusinessException("未获取到当前用户信息,请先维护账户信息");
            }
            proAbnormalFeedbackDTO.setCreator(user.getId());
        }
        return proAbnormalFeedbackDao.selectByList(proAbnormalFeedbackDTO);
    }

}