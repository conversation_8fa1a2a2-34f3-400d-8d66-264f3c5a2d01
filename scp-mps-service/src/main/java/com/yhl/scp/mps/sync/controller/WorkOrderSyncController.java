package com.yhl.scp.mps.sync.controller;

import cn.hutool.core.util.StrUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>WorkOrderSyncController</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-12 10:02:36
 */
@Api(tags = "MPS-制造订单展开控制器")
@RestController
@RequestMapping("/workOrderSync")
@Slf4j
public class WorkOrderSyncController extends BaseController {

    @Resource
    private IWorkOrderSync workOrderSync;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationService operationService;
    @Resource
    IAmsSchedule amsSchedule;
    @Resource
    private NewMdsFeign newMdsFeign;

    @ApiOperation(value = "制造订单展开-执行")
    @GetMapping(value = "/execute")
    @Transactional
    public BaseResponse execute(@RequestParam(value = "remark") String remark,
                                @RequestParam(value = "userId") String userId) {
        // TODO 查询制造订单
        List<WorkOrderVO> workOrderVOS = workOrderService.selectAll();
        List<WorkOrderVO> syncWorkOrder = workOrderVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getRemark()) && p.getRemark().equals(remark)).collect(Collectors.toList());
        List<String> ids = syncWorkOrder.stream().map(WorkOrderVO::getId).collect(Collectors.toList());
        RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        rzzMpsAlgorithmOutput.setWorkOrderIds(ids);
        workOrderSync.doSyncOrder(rzzMpsAlgorithmOutput);
        // 自动排程
        MpsAnalysisContext mpsAnalysisContext = new MpsAnalysisContext();
        mpsAnalysisContext.setWorkOrderIds(ids);
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId("hands");
        algorithmLog.setCreator(userId);
        mpsAnalysisContext.setAlgorithmLog(algorithmLog);
        mpsAnalysisContext.setAlgorithmStepLogDTOList(new ArrayList<>());
        amsSchedule.doAmsSchedule(algorithmLog, mpsAnalysisContext);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "制造订单展开-按制造订单id")
    @PostMapping(value = "/executeByWorkOrderIds")
    @Transactional
    public BaseResponse executeByWorkOrderIds(@RequestBody List<String> workOrderIds) {

        RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        rzzMpsAlgorithmOutput.setWorkOrderIds(workOrderIds);
        workOrderSync.doSyncOrder(rzzMpsAlgorithmOutput);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }


    @ApiOperation(value = "制造订单展开-测试方法")
    @GetMapping(value = "/test")
    public BaseResponse<Void> test() {
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(new HashMap<>());
        List<String> productIds = workOrderVOS.stream().map(WorkOrderVO::getProductId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(), productIds);
        List<Pair<NewProductStockPointVO, NewProductStockPointVO>> pairedProducts = findPairedProducts(productStockPointVOS);
        List<Pair<WorkOrderVO, WorkOrderVO>> pairedWorkOrders = findPairedWorkOrders(workOrderVOS, pairedProducts);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 根据产品代码中的"DR"部分查找配对的左右片
     *
     * @param productList 产品列表
     * @return 配对的左右片列表
     */
    public static List<Pair<NewProductStockPointVO, NewProductStockPointVO>> findPairedProducts(List<NewProductStockPointVO> productList) {
        // 过滤出productCode包含"DR"的数据
        List<NewProductStockPointVO> drProducts = productList.stream()
                .filter(p -> p.getProductCode() != null && p.getProductCode().contains("DR"))
                .collect(Collectors.toList());

        // 以"DR"前的字符串进行分组，并过滤掉只有一个元素的组
        Map<String, List<NewProductStockPointVO>> groupedProducts = drProducts.stream()
                .collect(Collectors.groupingBy(p -> {
                    int drIndex = p.getProductCode().indexOf("DR");
                    return p.getProductCode().substring(0, drIndex);
                }))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().size() > 1)  // 过滤掉只有一个元素的组
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 存储配对结果
        List<Pair<NewProductStockPointVO, NewProductStockPointVO>> pairedList = new ArrayList<>();

        // 遍历每组数据
        for (Map.Entry<String, List<NewProductStockPointVO>> entry : groupedProducts.entrySet()) {
            List<NewProductStockPointVO> group = entry.getValue();

            // 创建两个Map分别存储左片和右片
            Map<Integer, NewProductStockPointVO> leftMap = new HashMap<>();
            Map<Integer, NewProductStockPointVO> rightMap = new HashMap<>();

            // 遍历组内元素，识别左右片
            for (NewProductStockPointVO product : group) {
                int drIndex = product.getProductCode().indexOf("DR");
                String afterDr = product.getProductCode().substring(drIndex + 2); // "DR"之后的部分

                // 提取DR后的数字（1位或2位）
                if (!afterDr.isEmpty()) {
                    int number = -1;
                    // 如果第一位不是数字，跳过
                    if (Character.isDigit(afterDr.charAt(0))) {
                        // 如果有第二位且是数字，则取两位数字
                        if (afterDr.length() >= 2 && Character.isDigit(afterDr.charAt(1))) {
                            String numberStr = afterDr.substring(0, 2);
                            number = Integer.parseInt(numberStr);
                        } else {
                            // 否则只取第一位数字
                            String numberStr = afterDr.substring(0, 1);
                            number = Integer.parseInt(numberStr);
                        }
                    }

                    // 如果成功解析出数字
                    if (number != -1) {
                        if (number % 2 == 1) {
                            // 奇数为左片
                            leftMap.put(number, product);
                        } else {
                            // 偶数为右片
                            rightMap.put(number, product);
                        }
                    }
                }
            }

            // 配对左右片
            for (Map.Entry<Integer, NewProductStockPointVO> leftEntry : leftMap.entrySet()) {
                int leftNumber = leftEntry.getKey();
                int rightNumber = leftNumber + 1;

                if (rightMap.containsKey(rightNumber)) {
                    // 找到配对，添加到结果列表
                    pairedList.add(Pair.of(leftEntry.getValue(), rightMap.get(rightNumber)));
                }
            }
        }
        return pairedList;
    }

    /**
     * 查找配对的工单（交期差在3天内的左右片工单）
     *
     * @param workOrderList 所有工单列表
     * @param pairedProducts 配对的左右片产品列表
     * @return 配对的左右片工单列表
     */
    public static List<Pair<WorkOrderVO, WorkOrderVO>> findPairedWorkOrders(List<WorkOrderVO> workOrderList,
                                                                            List<Pair<NewProductStockPointVO, NewProductStockPointVO>> pairedProducts) {
        // 将工单列表转换为以productId为key的Map，方便快速查找
        Map<String, List<WorkOrderVO>> workOrderByProductId = workOrderList.stream()
                .filter(workOrder -> workOrder.getProductId() != null && workOrder.getDueDate() != null)
                .collect(Collectors.groupingBy(WorkOrderVO::getProductId));

        // 存储配对的工单结果
        List<Pair<WorkOrderVO, WorkOrderVO>> pairedWorkOrders = new ArrayList<>();

        // 遍历配对的产品
        for (Pair<NewProductStockPointVO, NewProductStockPointVO> productPair : pairedProducts) {
            NewProductStockPointVO leftProduct = productPair.getLeft();
            NewProductStockPointVO rightProduct = productPair.getRight();

            // 查找对应产品的工单列表
            List<WorkOrderVO> leftWorkOrders = workOrderByProductId.get(leftProduct.getId());
            List<WorkOrderVO> rightWorkOrders = workOrderByProductId.get(rightProduct.getId());

            // 如果左右片产品都有对应的工单
            if (leftWorkOrders != null && rightWorkOrders != null) {
                // 创建一个映射来跟踪已配对的工单，避免重复配对
                Set<String> pairedLeftWorkOrderIds = new HashSet<>();
                Set<String> pairedRightWorkOrderIds = new HashSet<>();

                // 按交期排序
                leftWorkOrders.sort(Comparator.comparing(WorkOrderVO::getDueDate));
                rightWorkOrders.sort(Comparator.comparing(WorkOrderVO::getDueDate));

                // 遍历左右片工单，查找交期差在3天内的配对
                for (WorkOrderVO leftWorkOrder : leftWorkOrders) {
                    // 如果该左工单已配对过，则跳过
                    if (pairedLeftWorkOrderIds.contains(leftWorkOrder.getId())) {
                        continue;
                    }
                    // 找到与当前左工单交期差小于3天且交期最早的右工单
                    WorkOrderVO earliestMatchRightWorkOrder = findFistRightWorkOrder(leftWorkOrder, rightWorkOrders, pairedRightWorkOrderIds);

                    // 如果找到了匹配的右工单，则添加到结果列表并标记为已配对
                    if (earliestMatchRightWorkOrder != null) {
                        pairedWorkOrders.add(Pair.of(leftWorkOrder, earliestMatchRightWorkOrder));
                        pairedLeftWorkOrderIds.add(leftWorkOrder.getId());
                        pairedRightWorkOrderIds.add(earliestMatchRightWorkOrder.getId());
                    }
                }
            }
        }
        pairedWorkOrders.forEach(t -> {
            WorkOrderVO key = t.getKey();
            WorkOrderVO value = t.getValue();
            String productId = key.getProductId();
            key.setSymmetryCode(productId);
            value.setSymmetryCode(productId);
        });

        return pairedWorkOrders;
    }

    private static WorkOrderVO findFistRightWorkOrder(WorkOrderVO leftWorkOrder, List<WorkOrderVO> rightWorkOrders, Set<String> pairedRightWorkOrderIds) {
        WorkOrderVO earliestMatchRightWorkOrder = null;

        for (WorkOrderVO rightWorkOrder : rightWorkOrders) {
            // 如果该右工单已配对过，则跳过
            if (pairedRightWorkOrderIds.contains(rightWorkOrder.getId())) {
                continue;
            }

            // 计算交期差（绝对值）
            long diffInMilliseconds = Math.abs(leftWorkOrder.getDueDate().getTime() - rightWorkOrder.getDueDate().getTime());
            long diffInDays = diffInMilliseconds / (24 * 60 * 60 * 1000);

            // 如果交期差在3天内
            if (diffInDays <= 3) {
                // 如果这是第一个匹配的右工单，或者这个右工单的交期更早
                if (earliestMatchRightWorkOrder == null ||
                        rightWorkOrder.getDueDate().before(earliestMatchRightWorkOrder.getDueDate())) {
                    earliestMatchRightWorkOrder = rightWorkOrder;
                }
            }
        }
        return earliestMatchRightWorkOrder;
    }


}
