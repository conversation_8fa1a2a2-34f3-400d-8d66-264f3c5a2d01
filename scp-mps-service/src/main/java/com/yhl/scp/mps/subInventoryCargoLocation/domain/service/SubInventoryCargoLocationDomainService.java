package com.yhl.scp.mps.subInventoryCargoLocation.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mps.subInventoryCargoLocation.domain.entity.SubInventoryCargoLocationDO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class SubInventoryCargoLocationDomainService {

    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;

    /**
     * 数据校验
     *
     * @param subInventoryCargoLocationDO 领域对象
     */
    public void validation(SubInventoryCargoLocationDO subInventoryCargoLocationDO) {
        checkNotNull(subInventoryCargoLocationDO);
        checkUniqueCode(subInventoryCargoLocationDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param subInventoryCargoLocationDO 领域对象
     */
    private void checkNotNull(SubInventoryCargoLocationDO subInventoryCargoLocationDO) {
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getCorporationCode())) {
            throw new BusinessException("公司代码，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getFactoryCode())) {
            throw new BusinessException("库存点代码，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getFactoryName())) {
            throw new BusinessException("库存点名称，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getStashCode())) {
            throw new BusinessException("仓库代码，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getStashName())) {
            throw new BusinessException("仓库名称，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getFreightSpaceCode())) {
            throw new BusinessException("货位代码，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getFreightSpaceName())) {
            throw new BusinessException("货位名称，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getSource())) {
            throw new BusinessException("来源名称，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getValid())) {
            throw new BusinessException("是否有效，不能为空");
        }
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getErpFreightSpaceCode())) {
            throw new BusinessException("erp货位代码，不能为空");
        }

    }

    /**
     * 唯一性校验
     *
     * @param subInventoryCargoLocationDO 领域对象
     */
    private void checkUniqueCode(SubInventoryCargoLocationDO subInventoryCargoLocationDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("CorporationCode", subInventoryCargoLocationDO.getCorporationCode());
        params.put("FactoryCode", subInventoryCargoLocationDO.getFactoryCode());
        params.put("StashCode", subInventoryCargoLocationDO.getStashCode());
        params.put("FreightSpaceCode", subInventoryCargoLocationDO.getFreightSpaceCode());
        if (StringUtils.isBlank(subInventoryCargoLocationDO.getId())) {
            List<SubInventoryCargoLocationPO> list = subInventoryCargoLocationDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，公司代码, " + subInventoryCargoLocationDO.getCorporationCode(),
                        "工厂代码" + subInventoryCargoLocationDO.getFactoryCode(),
                        "仓库代码" + subInventoryCargoLocationDO.getStashCode(),
                        "货位代码已存在： " + subInventoryCargoLocationDO.getFreightSpaceCode());
            }
        }
    }

}
