<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.capacityBalance.dao.CapacityBalanceDao">
    <select id="selectDeliveryPlanPublished" resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2">
        select t.oem_code as oemCode,
               t.product_code as productCode,
               t.demand_time as demandTime,
               t.demand_quantity as demandQuantity
        from v_fdp_delivery_plan_published t
        <where>
            <if test="demandTimeStart != null">
                and demand_time >= #{demandTimeStart,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectExternalInventoryByProductCodeList" resultType="com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO">
        SELECT
            t.product_code as productCode,
            sum(t.current_quantity) as currentQuantity
        FROM
            `v_fdp_inventory_real_time_data` t,
            mds_stock_point msp
        WHERE
            t.stock_point_code = msp.stock_point_code
          AND msp.plan_area = 'FYSH'
          AND msp.stock_point_type != 'BC'
        <if test="productCodeList != null and productCodeList.size() > 0">
            AND t.product_code IN
            <foreach item="item" collection="productCodeList" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by t.product_code
    </select>
</mapper>
