# 需求同步重复数据问题修复说明

## 问题描述

用户测试发现每次点击同步需求的时候，都会产生一次需求提报的数据，这样会导致数据重复。正确的做法应该是如果该版本对应的主机厂和产品编码的需求提报原来已经存在，则进行更新就可以了，直接删除数据库对应的需求提报明细，再写入新的需求提报明细，如果原来就不存在，则直接进行写入。

## 问题根本原因分析

通过深入分析代码，发现问题的根本原因在于：

1. **删除逻辑不够彻底**：在`syncDemand`方法中，虽然有删除旧明细数据的逻辑，但删除操作基于`submissionIdsToDeleteDetailsFor`，只删除了本次同步涉及的产品的明细数据。

2. **数据一致性问题**：每次同步时，系统会：
   - 先删除本次同步涉及产品的旧明细数据
   - 然后创建新的明细数据
   - 但如果同一个版本、同一个主机厂、同一个产品的需求提报头已经存在，系统会复用这个头，但仍然会插入新的明细数据

3. **并发和异常处理不足**：如果在删除和创建之间有任何异常或并发问题，就可能导致数据重复。

## 解决方案

### 1. 核心修改

**文件**: `scp-dfp-service/src/main/java/com/yhl/scp/dfp/loading/service/impl/LoadingDemandSubmissionServiceImpl.java`

#### 1.1 添加事务注解
```java
@Override
@Transactional(rollbackFor = Exception.class)
public BaseResponse<Void> syncDemand(LoadingDemandSubmissionReqDTO reqDTO,Scenario scenario) {
```

**目的**: 确保整个同步过程在一个事务中执行，保证数据一致性。

#### 1.2 简化删除逻辑
**修改前**:
```java
// 复杂的查询和过滤逻辑
for (String submissionId : submissionIdsToDeleteDetailsFor) {
    // 复杂的查询逻辑...
}
```

**修改后**:
```java
// 直接删除这些提报头下的所有明细数据，确保不会有重复数据
// deleteBySubmissionIds方法会删除指定提报头ID下的所有明细数据
int deletedCount = loadingDemandSubmissionDetailService.deleteBySubmissionIds(submissionIdsToDeleteDetailsFor);
log.info("成功删除 {} 条明细数据", deletedCount);
```

**目的**: 
- 简化删除逻辑，直接删除指定提报头下的所有明细数据
- 避免复杂的查询和过滤可能导致的遗漏
- 确保删除操作的彻底性

#### 1.3 增强日志记录
```java
log.info("开始同步需求，版本ID: {}, 主机厂数量: {}", reqDTO.getOriginVersionId(), 
        reqDTO.getDetailDTOList() != null ? reqDTO.getDetailDTOList().size() : 0);

log.info("同步开始，为版本 {} 的主机厂 {} 精准删除 {} 个提报头下的所有明细数据", 
        context.getOriginVersionId(), oemCode, submissionIdsToDeleteDetailsFor.size());
log.debug("待删除明细数据的提报头IDs: {}", submissionIdsToDeleteDetailsFor);

log.info("成功删除 {} 条明细数据", deletedCount);

log.info("开始创建 {} 条新的明细数据", container.getInsertDetailList().size());
log.info("成功创建 {} 条新的明细数据", container.getInsertDetailList().size());

log.info("需求同步完成，版本ID: {}, 处理主机厂数量: {}", reqDTO.getOriginVersionId(), 
        context.getEdiOemList().size());
```

**目的**: 
- 提供详细的操作日志，便于问题追踪
- 记录删除和创建的数据量，便于验证操作正确性

### 2. 数据库层面的保证

#### 2.1 删除操作的SQL实现
**文件**: `scp-dfp-service/src/main/java/com/yhl/scp/dfp/loading/infrastructure/dao/LoadingDemandSubmissionDetailDao.xml`

```xml
<delete id="deleteBySubmissionIds">
    delete
    from fdp_loading_demand_submission_detail
    where submission_id in
    <foreach collection="submissionIds" item="item" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
    </foreach>
</delete>
```

**说明**: 这个SQL会删除指定提报头ID下的所有明细数据，确保删除的彻底性。

### 3. 测试验证

创建了测试类 `LoadingDemandSubmissionServiceImplTest.java` 来验证修改的正确性：

#### 3.1 幂等性测试
- 多次调用同步需求方法
- 验证不会产生重复数据

#### 3.2 并发测试
- 模拟并发调用同步需求
- 验证数据一致性

## 修改的安全性保证

### 1. 精准删除
- 只删除本次同步涉及的产品对应的提报头下的明细数据
- 不会影响其他版本或其他主机厂的数据
- 不会删除不相关的数据

### 2. 事务保证
- 整个同步过程在一个事务中执行
- 如果出现异常，所有操作都会回滚
- 保证数据的一致性

### 3. 日志追踪
- 详细的日志记录便于问题追踪
- 记录删除和创建的数据量
- 便于验证操作的正确性

## 预期效果

1. **解决重复数据问题**: 每次同步需求时，会先删除相关的旧明细数据，再创建新的明细数据，确保不会产生重复。

2. **保证数据一致性**: 通过事务控制，确保删除和创建操作的原子性。

3. **提高系统稳定性**: 简化了删除逻辑，减少了出错的可能性。

4. **便于问题排查**: 增强的日志记录便于问题追踪和性能监控。

## 部署建议

1. **测试环境验证**: 在测试环境充分验证修改的正确性
2. **数据备份**: 生产环境部署前做好数据备份
3. **监控部署**: 部署后密切监控系统运行情况和日志
4. **回滚准备**: 准备好回滚方案，以防出现意外问题
